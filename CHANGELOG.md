# 更新日志

## [1.2.0] - 2025-01-20

### 🆕 新增功能

#### **服务层重构**
- **模块化架构**: 将原有的单个大文件拆分为多个专职模块
- **事件处理模块**: 独立的事件处理、导航、自定义事件、预设操作模块
- **路径解析器**: 独立的路径解析工具类
- **向后兼容**: 完全兼容的包装器设计，现有代码无需修改

#### **智能路由系统**
- **动态路由解析**: 根据页面配置自动选择原生路由或低代码路由
- **应用ID动态获取**: 从当前路由上下文获取，支持用户自定义AppID
- **配置驱动**: 完全基于配置，消除硬编码
- **路径验证**: 完整的路径格式验证和错误处理

#### **统一事件系统**
- **统一事件架构**: 所有组件使用统一的 `click` 事件
- **预设操作**: 7种常用预设操作（showMessage、showConfirm、copyText等）
- **自定义代码**: 安全的代码执行环境，丰富的上下文对象
- **智能导航**: 支持页面、外部链接、WebView、返回等多种导航类型

### 🔧 改进优化

#### **代码质量**
- **职责分离**: 每个模块职责单一，代码量适中
- **类型安全**: 完整的TypeScript类型定义
- **错误处理**: 统一的错误处理和日志记录
- **性能优化**: 支持按需导入，减少包体积

#### **开发体验**
- **清晰的模块边界**: 更好的代码可读性和可维护性
- **工厂函数**: 提供便捷的实例创建方法
- **完整文档**: 详细的架构文档和使用指南

### 🐛 修复问题

- **硬编码问题**: 消除路由映射中的硬编码
- **导入导出错误**: 修复模块导入导出问题
- **路径解析错误**: 修复路径匹配逻辑
- **事件配置匹配**: 修复事件配置匹配逻辑

### 📚 文档更新

- **架构文档**: 新增服务层重构详细文档
- **路由文档**: 更新智能路由系统文档
- **事件文档**: 新增统一事件系统文档
- **开发总结**: 更新项目开发进度和状态

---

## [1.1.0] - 2025-01-15

### 🆕 新增功能

#### **PC端设计器**
- **页面发布功能**: 支持将设计的页面发布到API服务器
- **事件配置**: 完善的事件配置界面
- **实时预览**: 设计器中的实时预览功能

#### **H5端**
- **动态页面**: 完善的动态页面渲染系统
- **应用认证**: 应用级别的认证和权限控制
- **页面缓存**: 动态页面的缓存机制

### 🔧 改进优化

- **组件系统**: 优化组件注册和渲染机制
- **路由管理**: 改进路由配置和导航逻辑
- **错误处理**: 完善错误处理和用户提示

---

## [1.0.0] - 2025-01-10

### 🎉 首次发布

#### **基础架构**
- **Monorepo架构**: 基于pnpm的Monorepo项目结构
- **TypeScript支持**: 完整的类型安全保障
- **构建系统**: 基于Vite的现代化构建流程

#### **H5端应用**
- **Vue 3框架**: 基于Vue 3 Composition API
- **Vant UI**: 移动端UI组件库
- **路由系统**: Vue Router 4路由管理
- **状态管理**: Pinia状态管理

#### **PC端设计器**
- **Vue 3框架**: 基于Vue 3 Composition API
- **Ant Design Vue**: PC端UI组件库
- **可视化设计**: 拖拽式组件设计器
- **属性配置**: 组件属性配置面板

#### **核心包**
- **应用类型管理**: 统一的应用类型定义和管理
- **组件系统**: 可复用的组件库
- **工具函数**: 核心工具函数库

#### **API服务**
- **Express框架**: 基于Express的API服务
- **页面配置**: 页面配置的存储和管理
- **应用管理**: 应用信息的管理接口

---

## 版本说明

### 版本号规则
- **主版本号**: 重大架构变更或不兼容更新
- **次版本号**: 新功能添加或重要改进
- **修订版本号**: 问题修复和小幅优化

### 更新类型
- 🆕 **新增功能**: 全新的功能特性
- 🔧 **改进优化**: 现有功能的改进和优化
- 🐛 **修复问题**: 问题修复和错误处理
- 📚 **文档更新**: 文档的新增和更新
- ⚠️ **破坏性变更**: 不兼容的变更（需要特别注意）

### 兼容性说明
- **v1.2.0**: 完全向后兼容v1.1.0和v1.0.0
- **v1.1.0**: 完全向后兼容v1.0.0

---

**维护者**: 安生团队  
**项目地址**: [GitHub Repository]  
**文档地址**: [Documentation Site]
