# 生产环境部署指南

## 📋 部署概述

本低代码平台包含三个核心服务，需要分别部署：

- **H5应用**：移动端用户界面 (端口3000)
- **PC设计器**：管理端设计界面 (端口3001)  
- **API服务**：后端API接口 (端口3002)

## 🏗️ 构建准备

### 1. 环境要求
- Node.js >= 18.0.0
- pnpm >= 8.0.0
- 服务器支持静态文件托管

### 2. 构建命令
```bash
# 安装依赖
pnpm install

# 构建所有项目
pnpm build

# 或分别构建
pnpm build:h5        # 构建H5应用
pnpm build:designer  # 构建PC设计器
pnpm build:api       # 构建API服务
```

## 📦 部署结构

```
production/
├── h5/              # H5应用静态文件
├── designer/        # PC设计器静态文件
└── api/             # API服务
```

## 🌐 H5应用部署

### 构建输出
```bash
apps/h5/dist/        # 构建后的静态文件
```

### Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-h5-domain.com;
    root /path/to/h5/dist;
    index index.html;

    # 支持Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:3002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 外部API代理
    location /Fan/ {
        proxy_pass https://ml.liangaiyun.com/java/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /Lin/ {
        proxy_pass https://ml.liangaiyun.com/py/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 💻 PC设计器部署

### 构建输出
```bash
apps/designer/dist/  # 构建后的静态文件
```

### Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-designer-domain.com;
    root /path/to/designer/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:3002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔧 API服务部署

### 使用PM2部署
```bash
# 安装PM2
npm install -g pm2

# 启动API服务
cd apps/api
pm2 start ecosystem.config.js

# 查看状态
pm2 status
pm2 logs lowcode-api
```

### PM2配置文件 (ecosystem.config.js)
```javascript
module.exports = {
  apps: [{
    name: 'lowcode-api',
    script: 'dist/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3002
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
```

## 🔐 环境变量配置

### H5应用环境变量
```bash
# .env.production
VITE_API_BASE_URL=https://your-api-domain.com
VITE_APP_ENV=production
```

### PC设计器环境变量
```bash
# .env.production
VITE_API_BASE_URL=https://your-api-domain.com
VITE_H5_BASE_URL=https://your-h5-domain.com
```

### API服务环境变量
```bash
# .env.production
NODE_ENV=production
PORT=3002
```

## 🚀 部署流程

### 1. 自动化部署脚本
```bash
#!/bin/bash
# deploy.sh

echo "开始部署低代码平台..."

# 构建项目
pnpm build

# 部署H5应用
rsync -av apps/h5/dist/ /var/www/h5/

# 部署PC设计器
rsync -av apps/designer/dist/ /var/www/designer/

# 重启API服务
pm2 restart lowcode-api

# 重载Nginx配置
nginx -s reload

echo "部署完成！"
```

### 2. Docker部署 (可选)
```dockerfile
# Dockerfile.api
FROM node:18-alpine
WORKDIR /app
COPY apps/api/package*.json ./
RUN npm ci --only=production
COPY apps/api/dist ./dist
EXPOSE 3002
CMD ["node", "dist/index.js"]
```

## 📊 监控和维护

### 1. 日志监控
```bash
# 查看API日志
pm2 logs lowcode-api

# 查看Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### 2. 性能监控
- 使用PM2 Plus进行应用监控
- 配置Nginx状态页面
- 设置服务器资源监控

### 3. 备份策略
- 定期备份应用配置
- 备份用户创建的应用数据
- 备份数据库（如果使用）

## ⚠️ 注意事项

1. **HTTPS配置**：生产环境建议启用HTTPS
2. **跨域配置**：确保API服务正确配置CORS
3. **缓存策略**：配置适当的静态资源缓存
4. **安全配置**：设置防火墙和访问控制
5. **域名配置**：确保PC端能正确生成H5链接

## 🔧 故障排查

### 常见问题
1. **路由404**：检查Nginx的try_files配置
2. **API请求失败**：检查代理配置和CORS设置
3. **静态资源加载失败**：检查资源路径和缓存配置
4. **登录跳转异常**：检查token存储和路由守卫配置

### 调试命令
```bash
# 检查服务状态
pm2 status
systemctl status nginx

# 检查端口占用
netstat -tlnp | grep :3002

# 检查日志
journalctl -u nginx -f
```
