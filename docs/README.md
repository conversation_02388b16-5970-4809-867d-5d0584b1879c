# 安生低代码平台文档

## 📚 文档目录

### **🏗️ 架构文档**
- [系统架构概览](./architecture/overview.md) - 整体架构设计
- [应用类型管理](./architecture/application-types.md) - 应用类型和路由管理系统
- [模块化设计](./architecture/modules.md) - 模块化架构设计
- 🆕 [服务层重构](./architecture/services-refactor.md) - 服务层模块化重构详解

### **🚀 快速开始**
- [环境搭建](./getting-started/setup.md) - 开发环境配置
- [项目结构](./getting-started/structure.md) - 项目目录结构说明
- [开发指南](./getting-started/development.md) - 开发流程和规范

### **📱 H5端文档**
- [H5应用架构](./h5/architecture.md) - H5端架构设计
- [组件开发](./h5/components.md) - 组件开发指南
- 🆕 [路由管理](./h5/routing.md) - 智能路由系统和路径解析

### **💻 PC端设计器**
- [设计器架构](./designer/architecture.md) - PC端设计器架构
- [组件库](./designer/components.md) - 设计器组件库
- 🆕 [事件系统](./designer/events.md) - 统一事件系统和预设操作

### **📦 核心包文档**
- [核心包概览](./core/overview.md) - 核心包功能介绍
- [应用类型系统](./core/application-types.md) - 应用类型管理详解
- [工具函数](./core/utils.md) - 核心工具函数

### **🎨 UI组件库**
- [组件库概览](./ui/overview.md) - UI组件库介绍
- [组件开发规范](./ui/development.md) - 组件开发指南
- [主题系统](./ui/theming.md) - 主题配置和定制

### **🔧 API文档**
- [API概览](./api/overview.md) - API服务架构
- [接口文档](./api/endpoints.md) - 具体接口说明
- [数据模型](./api/models.md) - 数据结构定义

### **📋 更新日志**
- [版本历史](./changelog.md) - 版本更新记录
- [迁移指南](./migration.md) - 版本迁移说明

## 🎯 项目概述

安生低代码平台是一个基于 Vue 3 的现代化低代码开发平台，支持多种应用类型的快速开发和部署。

### **核心特性**
- 🎨 **可视化设计** - 拖拽式组件设计器
- 📱 **多端支持** - H5端和PC端设计器
- 🔧 **应用类型管理** - 支持设备充值端、商城端等多种应用类型
- 🎯 **组件化架构** - 高度模块化的组件系统
- 🚀 **TypeScript支持** - 完整的类型安全保障

### **技术栈**
- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **UI组件**: Ant Design Vue + Vant
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **包管理**: pnpm + Monorepo

### **项目结构**
```
lowcode/
├── apps/                    # 应用目录
│   ├── h5/                 # H5端应用
│   ├── designer/           # PC端设计器
│   └── api/                # API服务
├── packages/               # 共享包
│   ├── core/              # 核心包
│   ├── ui/                # UI组件库
│   └── hooks/             # 共享Hooks
└── docs/                  # 文档目录
```

## 🚀 快速开始

### **环境要求**
- Node.js >= 16
- pnpm >= 7

### **安装依赖**
```bash
pnpm install
```

### **启动开发服务器**
```bash
# 启动所有服务
pnpm dev

# 或单独启动
pnpm dev:h5        # H5端
pnpm dev:designer  # PC端设计器
pnpm dev:api       # API服务
```

### **访问地址**
- H5端: http://localhost:3000 (自动分配端口)
- PC端设计器: http://localhost:3001 (自动分配端口)
- API服务: http://localhost:3002 (自动分配端口)

> 💡 **端口说明**: 如果默认端口被占用，Vite会自动分配可用端口

## 📞 联系我们

如有问题或建议，请联系开发团队。

---

**版本**: v1.2.0
**最后更新**: 2025-01-20

## 🆕 最新更新 (v1.2.0)

### **服务层重构**
- 完全模块化的服务架构
- 事件处理、导航、自定义代码分离
- 向后兼容的包装器设计

### **智能路由系统**
- 动态路由解析和自动路由选择
- 应用ID动态获取，支持用户自定义
- 完全配置驱动，无硬编码

### **统一事件系统**
- 统一的click事件架构
- 丰富的预设操作（7种）
- 安全的自定义代码执行环境
