# 事件系统文档

## 概述

🆕 **v1.2.0** 引入了全新的统一事件系统，支持模块化的事件处理、智能路由导航和丰富的预设操作。

## 🎯 核心特性

- **统一事件架构**：所有组件使用统一的 `click` 事件
- **模块化处理**：事件处理、导航、自定义代码分离
- **智能路由**：自动选择原生路由或低代码路由
- **预设操作**：丰富的预设操作，开箱即用
- **向后兼容**：完全兼容旧版本事件格式

## 📋 事件类型

### **1. 导航事件 (navigate)**
用于页面跳转和导航操作

```json
{
  "type": "navigate",
  "elementType": "button",
  "elementId": "more-button",
  "navigateType": "page",
  "target": "/device/PackageList"
}
```

### **2. 自定义事件 (custom)**
用于执行预设操作或自定义代码

```json
{
  "type": "custom",
  "elementType": "button", 
  "elementId": "refresh-button",
  "preset": "refreshDevice",
  "message": "正在刷新设备信息..."
}
```

## 🚀 导航类型

### **页面导航 (page)**
智能页面跳转，自动选择路由类型

```json
{
  "navigateType": "page",
  "target": "/device/PackageList"
}
```

**处理流程**：
1. 解析目标路径
2. 检查页面是否可配置
3. 动态获取应用ID
4. 选择原生路由或低代码路由

### **外部链接 (external)**
打开外部网页

```json
{
  "navigateType": "external", 
  "target": "https://example.com"
}
```

### **返回导航 (back)**
返回上一页

```json
{
  "navigateType": "back"
}
```

### **WebView导航 (webview)**
在内嵌WebView中打开页面

```json
{
  "navigateType": "webview",
  "target": "https://example.com",
  "webviewTitle": "外部页面",
  "webviewOptions": ["showNavBar", "showBackButton"]
}
```

## 🎨 预设操作

### **显示消息 (showMessage)**
```json
{
  "preset": "showMessage",
  "message": "操作成功"
}
```

### **确认对话框 (showConfirm)**
```json
{
  "preset": "showConfirm",
  "title": "确认操作",
  "message": "确定要执行此操作吗？",
  "onConfirm": {
    "type": "navigate",
    "target": "/device/success"
  }
}
```

### **复制文本 (copyText)**
```json
{
  "preset": "copyText",
  "text": "要复制的文本内容"
}
```

### **下载文件 (downloadFile)**
```json
{
  "preset": "downloadFile",
  "fileUrl": "https://example.com/file.pdf",
  "fileName": "document.pdf"
}
```

### **打开弹窗 (openModal)**
```json
{
  "preset": "openModal",
  "modalTitle": "提示",
  "modalContent": "这是弹窗内容"
}
```

### **切换显示 (toggleComponent)**
```json
{
  "preset": "toggleComponent",
  "targetId": "component-id"
}
```

### **刷新设备信息 (refreshDevice)**
```json
{
  "preset": "refreshDevice",
  "message": "正在刷新设备信息..."
}
```

## 🔧 自定义代码

### **代码执行环境**
提供安全的代码执行环境，包含以下上下文：

```javascript
// 可用的上下文对象
{
  // 数据上下文
  data,           // 事件数据
  component,      // 组件实例
  
  // 路由相关
  router,         // Vue Router实例
  
  // UI相关
  showToast,      // 显示提示
  showDialog,     // 显示对话框
  
  // 网络请求
  axios,          // HTTP客户端
  
  // 工具方法
  utils: {
    showMessage: (msg) => showToast(msg),
    navigate: (path) => router.push(path),
    goBack: () => router.back()
  },
  
  // API相关
  api: {
    get: (url, config) => axios.get(url, config),
    post: (url, data, config) => axios.post(url, data, config)
  }
}
```

### **代码示例**
```javascript
// 显示消息并跳转
utils.showMessage('操作成功')
setTimeout(() => {
  utils.navigate('/device/success')
}, 1000)

// 发送API请求
api.post('/api/device/refresh', { deviceId: data.deviceId })
  .then(response => {
    utils.showMessage('刷新成功')
  })
  .catch(error => {
    utils.showMessage('刷新失败')
  })
```

## 📱 事件配置

### **单个事件配置**
```json
{
  "click": {
    "type": "navigate",
    "elementType": "button",
    "elementId": "more-button", 
    "target": "/device/PackageList"
  }
}
```

### **多个事件配置**
```json
{
  "click": [
    {
      "type": "custom",
      "elementId": "refresh",
      "preset": "refreshDevice"
    },
    {
      "type": "navigate", 
      "elementId": "package",
      "target": "/device/PackageList"
    }
  ]
}
```

## 🔄 向后兼容

### **旧事件格式自动转换**
```javascript
// 旧格式
'menuClick' → { elementType: 'menu', elementId: data.id }
'buttonClick' → { elementType: 'button', elementId: data.type }
'actionClick' → { elementType: 'action', elementId: data.type }
```

### **兼容性保证**
- 现有代码无需修改
- 自动转换旧事件格式
- 保持原有接口不变

## 🛠️ 开发指南

### **添加新的预设操作**
```typescript
// 在 PresetActions.ts 中添加
case 'newAction':
  await this.handleNewAction(config)
  break

private async handleNewAction(config: PresetActionConfig): Promise<void> {
  // 实现新的预设操作
}
```

### **自定义事件处理器**
```typescript
import { CustomEventHandler } from '@/services'

const customHandler = new CustomEventHandler(router)
await customHandler.handleCustomEvent(eventConfig, data, component)
```

### **扩展导航类型**
```typescript
// 在 NavigationHandler.ts 中添加
case 'newNavigationType':
  await this.handleNewNavigation(target, config)
  break
```

## 🧪 测试

### **事件处理测试**
```typescript
import { EventHandlerService } from '@/services'

const eventHandler = new EventHandlerService(mockRouter)
await eventHandler.handleComponentEvent(mockComponent, 'click', mockData)
```

### **预设操作测试**
```typescript
import { PresetActions } from '@/services'

const presetActions = new PresetActions()
await presetActions.executePresetAction({
  preset: 'showMessage',
  message: 'Test message'
}, mockData, mockComponent)
```

## 📊 性能监控

### **事件执行时间**
```javascript
console.time('event-execution')
await eventHandler.handleComponentEvent(comp, 'click', data)
console.timeEnd('event-execution')
```

### **错误监控**
```javascript
try {
  await eventHandler.handleComponentEvent(comp, 'click', data)
} catch (error) {
  console.error('事件处理失败:', error)
  // 发送错误报告
}
```

## 📚 相关文档

- [服务层重构](../architecture/services-refactor.md)
- [路由管理](../h5/routing.md)
- [组件开发](../h5/components.md)

---

**版本**: v1.2.0  
**最后更新**: 2025-01-20
