# 预设操作配置指南

## 📋 概述

🆕 **v1.2.0** 重构了预设操作系统，将操作分为两大类：**UI操作**和**API操作**，提供更好的分类管理和参数验证。

## 🎨 UI操作类

### **showMessage - 显示消息**
显示简单的提示消息

**配置参数**：
```json
{
  "preset": "showMessage",
  "message": "操作成功！"
}
```

**参数说明**：
- `message` (必填): 要显示的消息内容

---

### **showConfirm - 确认对话框**
显示确认对话框，用户确认后执行后续操作

**配置参数**：
```json
{
  "preset": "showConfirm",
  "title": "确认操作",
  "message": "确定要删除这个项目吗？",
  "onConfirm": {
    "type": "navigate",
    "target": "/device/success"
  }
}
```

**参数说明**：
- `title` (可选): 对话框标题，默认"确认"
- `message` (必填): 确认消息内容
- `onConfirm` (可选): 用户确认后执行的操作

---

### **copyText - 复制文本**
复制指定文本到剪贴板

**配置参数**：
```json
{
  "preset": "copyText",
  "text": "要复制的文本内容"
}
```

**参数说明**：
- `text` (必填): 要复制的文本内容

---

### **downloadFile - 下载文件**
下载指定文件

**配置参数**：
```json
{
  "preset": "downloadFile",
  "fileUrl": "https://example.com/file.pdf",
  "fileName": "document.pdf"
}
```

**参数说明**：
- `fileUrl` (必填): 文件下载地址
- `fileName` (可选): 下载文件名，默认"download"

---

### **openModal - 打开模态框**
打开信息展示模态框

**配置参数**：
```json
{
  "preset": "openModal",
  "modalTitle": "详细信息",
  "modalContent": "这里是详细的信息内容..."
}
```

**参数说明**：
- `modalTitle` (可选): 模态框标题，默认"提示"
- `modalContent` (必填): 模态框内容

---

### **toggleComponent - 切换组件显示**
切换指定组件的显示/隐藏状态

**配置参数**：
```json
{
  "preset": "toggleComponent",
  "targetId": "component-id"
}
```

**参数说明**：
- `targetId` (必填): 目标组件的ID

## 🔌 API操作类

### **refreshDevice - 刷新设备信息**
调用后端API刷新设备信息

**配置参数**：
```json
{
  "preset": "refreshDevice",
  "message": "正在刷新设备信息...",
  "onSuccess": {
    "type": "custom",
    "preset": "showMessage",
    "message": "刷新完成"
  },
  "onError": {
    "type": "custom", 
    "preset": "showMessage",
    "message": "刷新失败，请重试"
  }
}
```

**参数说明**：
- `message` (可选): 加载提示消息
- `onSuccess` (可选): 成功后执行的操作
- `onError` (可选): 失败后执行的操作

---

### **syncData - 同步数据**
同步数据到服务器（开发中）

**配置参数**：
```json
{
  "preset": "syncData",
  "message": "正在同步数据...",
  "params": {
    "dataType": "device",
    "syncAll": true
  }
}
```

**参数说明**：
- `message` (可选): 加载提示消息
- `params` (必填): 同步参数

---

### **updateStatus - 更新状态**
更新设备或数据状态（开发中）

**配置参数**：
```json
{
  "preset": "updateStatus",
  "message": "正在更新状态...",
  "params": {
    "status": "active",
    "deviceId": "device-001"
  }
}
```

**参数说明**：
- `message` (可选): 加载提示消息
- `params` (必填): 更新参数，必须包含`status`字段

## 🛠️ PC端配置界面

### **操作分类选择**
在PC端设计器中，预设操作按类型分组显示：

```
预设操作
├── 🎨 UI操作
│   ├── 显示消息 (showMessage)
│   ├── 确认对话框 (showConfirm)
│   ├── 复制文本 (copyText)
│   ├── 下载文件 (downloadFile)
│   ├── 打开模态框 (openModal)
│   └── 切换显示 (toggleComponent)
└── 🔌 API操作
    ├── 刷新设备 (refreshDevice)
    ├── 同步数据 (syncData)
    └── 更新状态 (updateStatus)
```

### **参数配置表单**
根据选择的操作类型，动态显示对应的参数配置表单：

#### **UI操作表单**
- 简单的文本输入框
- 文件选择器
- 组件ID选择器

#### **API操作表单**
- 参数对象编辑器
- 成功/失败回调配置
- 加载消息配置

## 🔧 参数验证

### **UI操作验证**
- `message`、`text`、`modalContent` 不能为空
- `fileUrl` 必须是有效的URL
- `targetId` 必须是有效的组件ID

### **API操作验证**
- `refreshDevice`: 无额外验证
- `syncData`: 必须提供 `params` 参数
- `updateStatus`: `params` 中必须包含 `status` 字段

## 🚀 使用示例

### **简单消息提示**
```json
{
  "type": "custom",
  "preset": "showMessage",
  "message": "保存成功！"
}
```

### **确认删除操作**
```json
{
  "type": "custom",
  "preset": "showConfirm",
  "title": "删除确认",
  "message": "确定要删除这个项目吗？此操作不可撤销。",
  "onConfirm": {
    "type": "navigate",
    "target": "/device/delete-success"
  }
}
```

### **设备信息刷新**
```json
{
  "type": "custom",
  "preset": "refreshDevice",
  "message": "正在刷新设备信息，请稍候...",
  "onSuccess": {
    "type": "custom",
    "preset": "showMessage", 
    "message": "设备信息已更新"
  }
}
```

## 🐛 常见问题

### **参数传递问题**
- **问题**: 弹窗参数传递不正确
- **解决**: 检查参数名称是否正确，确保必填参数不为空

### **组件ID找不到**
- **问题**: `toggleComponent` 找不到目标组件
- **解决**: 确保组件ID正确，检查组件是否已渲染

### **API调用失败**
- **问题**: `refreshDevice` 调用失败
- **解决**: 检查网络连接，查看控制台错误信息

## 📚 扩展开发

### **添加新的UI操作**
1. 在 `UIActions.ts` 中添加新的操作方法
2. 更新 `isUIAction` 方法
3. 在PC端添加对应的配置界面

### **添加新的API操作**
1. 在 `APIActions.ts` 中添加新的操作方法
2. 更新 `getSupportedActions` 方法
3. 添加参数验证逻辑
4. 在PC端添加对应的配置界面

---

**版本**: v1.2.0  
**最后更新**: 2025-01-20
