# AppID管理系统

## 概述

AppID管理系统是H5端的核心安全机制，确保用户只能访问其对应的应用，防止应用间的误操作和数据泄露。

## 🔐 安全机制

### **AppID验证流程**
1. **获取AppID**：从URL参数、本地存储或用户输入获取
2. **验证有效性**：调用 `/api/public/app/{appId}` 验证AppID是否存在
3. **设置缓存**：验证成功后存储到 `localStorage`
4. **路由保护**：所有需要AppID的页面都会验证其有效性

### **防护措施**
- ✅ **登录页面保护**：无有效AppID无法进入登录页面
- ✅ **应用隔离**：设备用户无法访问商城应用，反之亦然
- ✅ **实时验证**：AppID在使用前都会验证有效性
- ✅ **自动清理**：无效AppID会被自动清除

## 🚀 使用方式

### **1. 通过URL参数设置**
```
https://your-domain.com/?appid=ansheng
https://your-domain.com/#/?appid=ansheng
```

### **2. 通过AppID缺失页面设置**
访问 `/app-id-missing` 页面，手动输入AppID

### **3. 程序化设置**
```typescript
import { setCurrentAppId } from '@/services/SmartAppIdManager'

// 设置AppID（会自动验证）
setCurrentAppId('ansheng', 'manual')
```

## 📋 API接口

### **验证AppID**
```http
GET /api/public/app/{appId}

# 成功响应
{
  "id": "ansheng",
  "name": "安生应用",
  "type": "device"
}

# 失败响应
{
  "message": "应用不存在",
  "error": "Not Found",
  "statusCode": 404
}
```

## 🔧 配置说明

### **本地存储键名**
- `current-app-id`: 当前应用ID

### **默认行为**
- 无AppID时跳转到 `/app-id-missing`
- 无效AppID会被自动清除
- 登录页面强制验证AppID有效性

## 🛠️ 开发调试

### **调试信息**
```typescript
import { smartAppIdManager } from '@/services/SmartAppIdManager'

// 获取调试信息
const debugInfo = smartAppIdManager.getDebugInfo()
console.log('AppID调试信息:', debugInfo)
```

### **清除缓存**
```typescript
// 清除AppID缓存
smartAppIdManager.clearCache()

// 重置到默认状态
smartAppIdManager.resetToDefault()
```

## 🚨 注意事项

1. **AppID格式**：建议使用字母、数字、下划线，避免特殊字符
2. **大小写敏感**：AppID区分大小写
3. **唯一性**：每个AppID对应唯一的应用实例
4. **安全性**：不要在客户端硬编码敏感的AppID

## 📈 最佳实践

### **生产环境**
- 通过服务端接口动态获取可用的AppID列表
- 实现AppID的权限控制和访问日志
- 定期清理无效的AppID缓存

### **开发环境**
- 使用测试专用的AppID
- 启用详细的调试日志
- 提供快速切换AppID的开发工具
