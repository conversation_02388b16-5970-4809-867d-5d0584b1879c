# H5端路由管理

## 概述

H5端采用 Vue Router 进行路由管理，支持多种应用类型的动态路由配置。🆕 **v1.3.0** 引入了智能AppID验证系统，确保路由安全性和应用隔离。

## 🆕 智能AppID验证系统

### **核心特性**
- **AppID有效性验证**：所有AppID在使用前都会验证其有效性
- **应用隔离**：防止用户误入其他应用的页面
- **智能路径解析**：统一处理带前缀和不带前缀的路径
- **安全登录**：登录页面强制验证AppID有效性

### **路由类型**
1. **原生路由**：`/device/PageName` - 用于 `configurable: false` 的页面
2. **低代码路由**：`/PageName` 或 `/device/PageName` - 用于 `configurable: true` 的页面
3. **系统路由**：`/app-id-missing` - AppID管理页面

## 🔄 路由流程

### **1. 根路径访问 (`/`)**
```typescript
// 检查本地AppID
const storedAppId = localStorage.getItem('current-app-id')

if (storedAppId && storedAppId.trim() !== '') {
  // 有AppID：跳转到应用首页
  const homePath = getAppHomePath(defaultAppType)  // /device/home
  return homePath
} else {
  // 无AppID：跳转到AppID管理页面
  return '/app-id-missing'
}
```

### **2. AppID管理页面 (`/app-id-missing`)**
```typescript
// URL参数检测
const urlAppId = new URLSearchParams(location.search).get('appid')

if (urlAppId) {
  // 🎯 验证AppID有效性
  const isValid = await validateAppId(urlAppId)

  if (isValid) {
    setCurrentAppId(urlAppId)
    router.push(getHomePath())  // 跳转到应用首页
  } else {
    message.error(`应用ID "${urlAppId}" 不存在或无效`)
  }
}
```

### **3. 登录页面验证 (`/device/login`)**
```typescript
// 强制验证AppID
const appId = localStorage.getItem('current-app-id')

if (!appId || appId.trim() === '') {
  router.replace('/app-id-missing')  // 跳转到AppID管理页面
  return
}

// 验证AppID有效性
const response = await fetch(`/api/public/app/${appId}`)
if (!response.ok) {
  localStorage.removeItem('current-app-id')  // 清除无效AppID
  router.replace('/app-id-missing')
  return
}
```

## 🎯 智能路径解析

### **统一路径处理**
系统智能处理带前缀和不带前缀的路径，确保API请求一致：

```typescript
// 输入路径示例
'/home'                    → 页面ID: ansheng_home
'/device/home'             → 页面ID: ansheng_home
'/BalanceDetails'          → 页面ID: ansheng_BalanceDetails
'/device/BalanceDetails'   → 页面ID: ansheng_BalanceDetails

// 智能提取页面名称
function buildPageId(appInfo: AppInfo, pagePath: string): string {
  const parts = pagePath.split('/').filter(part => part.length > 0)
  const normalizedPath = parts[parts.length - 1] || 'home'
  return `${appInfo.appId}_${normalizedPath}`
}
```

### **路由生成策略**
```typescript
// 只为非可配置页面生成原生路由
routes.filter(route => route.enabled && !route.configurable)

// 可配置页面由 DynamicPage 统一处理
{
  path: '/:pagePath+',
  name: 'DynamicPage',
  component: () => import('../views/DynamicPage.vue')
}
```

## 🚀 路由生成器

### **RouteGenerator 类**
```typescript
import { RouteGenerator } from '@lowcode/core'

// 生成设备端路由
const deviceRoutes = RouteGenerator.generateRoutes('device')

// 生成所有应用类型的路由
const allRoutes = RouteGenerator.generateAllRoutes()
```

### **路由配置**
```typescript
// 路由配置示例
const routeConfig = {
  path: '/PackageList',
  name: 'PackageList',
  configurable: true,  // 可配置页面
  meta: {
    title: '套餐列表',
    requiresAuth: true
  }
}
```

## 🔄 事件驱动导航

### **NavigationHandler**
```typescript
import { NavigationHandler } from '@/services'

const navigationHandler = new NavigationHandler(router)

// 处理导航事件
await navigationHandler.handleNavigation({
  target: '/device/PackageList',
  navigateType: 'page'
})
```

### **导航类型**
- `page` - 页面导航（智能路由选择）
- `external` - 外部链接
- `back` - 返回上一页
- `webview` - WebView导航

## 📱 AppID验证系统

### **AppID有效性验证**
```typescript
// 验证AppID是否存在
async function validateAppId(appId: string): Promise<boolean> {
  try {
    const response = await fetch(`/api/public/app/${appId}`)
    return response.ok
  } catch (error) {
    return false
  }
}
```

### **SmartAppIdManager**
```typescript
import { getCurrentAppId, setCurrentAppId, hasValidAppId } from '@/services/SmartAppIdManager'

// 获取当前AppID
const appId = getCurrentAppId()

// 检查是否有有效AppID
const isValid = hasValidAppId()

// 设置AppID（会自动验证）
setCurrentAppId('ansheng', 'manual')
```

## 🛡️ 路由守卫

### **认证守卫**
```typescript
router.beforeEach(async (to, from, next) => {
  // 检查应用认证
  const isAuthenticated = await checkAppAuthentication(to)
  
  if (!isAuthenticated) {
    // 重定向到登录页
    next('/device/login')
  } else {
    next()
  }
})
```

### **应用验证**
```typescript
// 验证应用是否存在
const appExists = await validateApplication(appId)
if (!appExists) {
  // 跳转到404页面
  next('/404')
}
```

## 🔧 配置管理

### **路由配置**
```typescript
// packages/core/src/router/config.ts
export const ROUTE_PATTERNS = {
  LOWCODE: '/app/:appId',
  NATIVE: '/:appType',
  DYNAMIC: '/app/:appId/:pagePath+',
  WEBVIEW: '/webview/:url+'
}
```

### **特殊路径**
```typescript
export const SPECIAL_PATHS = {
  HOME: '/home',
  ROOT: '/',
  NOT_FOUND: '/404'
}
```

## 📊 性能优化

### **路由懒加载**
```typescript
const DynamicPage = () => import('@/views/DynamicPage.vue')
```

### **路由缓存**
```typescript
// 使用 keep-alive 缓存动态页面
<keep-alive>
  <router-view />
</keep-alive>
```

## 🧪 测试

### **路由测试**
```typescript
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: testRoutes
})

const wrapper = mount(App, {
  global: {
    plugins: [router]
  }
})
```

## 📚 相关文档

- [服务层重构](../architecture/services-refactor.md)
- [事件系统](../designer/events.md)
- [应用类型管理](../core/application-types.md)

---

**版本**: v1.2.0  
**最后更新**: 2025-01-20
