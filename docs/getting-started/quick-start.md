# 快速开始指南

## 🚀 5分钟快速体验

### **第一步：环境准备**
```bash
# 检查Node.js版本（需要 >= 16）
node --version

# 检查pnpm版本（需要 >= 7）
pnpm --version

# 如果没有pnpm，请先安装
npm install -g pnpm
```

### **第二步：克隆并启动项目**
```bash
# 克隆项目
git clone <repository-url>
cd lowcode_as-main

# 安装依赖
pnpm install

# 启动所有服务
pnpm dev
```

### **第三步：访问应用**
- **H5端**: http://localhost:3000
- **PC端设计器**: http://localhost:3001  
- **API服务**: http://localhost:3002

> 💡 如果端口被占用，Vite会自动分配可用端口

## 🎨 创建第一个应用

### **1. 进入PC端设计器**
访问 http://localhost:3001，进入可视化设计器

### **2. 创建新应用**
1. 点击"创建应用"按钮
2. 填写应用信息：
   - **应用名称**: 我的第一个应用
   - **应用ID**: my-first-app
   - **应用类型**: device（设备端）
   - **描述**: 这是我的第一个低代码应用

### **3. 设计页面**
1. 从左侧组件库拖拽组件到画布
2. 在右侧属性面板配置组件属性
3. 配置组件事件（点击、导航等）
4. 实时预览设计效果

### **4. 发布应用**
1. 点击"保存"按钮保存页面配置
2. 点击"发布"按钮发布到H5端
3. 获取H5访问链接

### **5. 在H5端查看**
访问生成的H5链接，查看你的应用效果

## 🔧 核心概念

### **应用类型**
- **device**: 设备充值端应用
- **mall**: 商城端应用（开发中）
- **crm**: CRM系统应用（计划中）

### **页面类型**
- **可配置页面**: 通过设计器配置的低代码页面
- **原生页面**: 传统Vue组件开发的页面

### **路由系统**
- **低代码路由**: `/app/{应用ID}/{页面路径}`
- **原生路由**: `/{应用类型}/{页面路径}`
- **智能解析**: 系统自动选择合适的路由类型

## 🎯 常用操作

### **配置组件事件**
```json
{
  "type": "navigate",
  "elementType": "button",
  "elementId": "my-button",
  "navigateType": "page",
  "target": "/device/PackageList"
}
```

### **使用预设操作**
```json
{
  "type": "custom",
  "preset": "showMessage",
  "message": "操作成功！"
}
```

### **自定义代码**
```javascript
// 显示消息并跳转
utils.showMessage('处理中...')

// 发送API请求
api.post('/api/device/action', { id: data.id })
  .then(response => {
    utils.showMessage('操作成功')
    utils.navigate('/device/success')
  })
  .catch(error => {
    utils.showMessage('操作失败')
  })
```

## 📱 H5端访问方式

### **🆕 AppID管理访问**
- 首页: http://localhost:3000 (自动检查AppID)
- AppID设置: http://localhost:3000/#/app-id-missing
- 带AppID参数: http://localhost:3000/?appid=ansheng

### **应用页面访问**
- 设备应用首页: http://localhost:3000/#/device/home
- 余额明细: http://localhost:3000/#/device/BalanceDetails
- 简化路径: http://localhost:3000/#/BalanceDetails

### **安全特性**
- ✅ 自动验证AppID有效性
- ✅ 防止应用间误操作
- ✅ 登录页面AppID保护

## 🛠️ 开发模式

### **启动单个服务**
```bash
# 只启动H5端
pnpm dev:h5

# 只启动PC端设计器
pnpm dev:designer

# 只启动API服务
pnpm dev:api
```

### **构建生产版本**
```bash
# 构建所有项目
pnpm build

# 构建单个项目
pnpm build:h5
pnpm build:designer
```

### **代码检查**
```bash
# 检查代码规范
pnpm lint

# 自动修复
pnpm lint:fix

# TypeScript类型检查
pnpm type-check
```

## 🔍 故障排查

### **常见问题**

#### **端口被占用**
```bash
# 查看端口占用
lsof -i :3000
lsof -i :3001
lsof -i :3002

# 杀死进程
kill -9 <PID>
```

#### **依赖安装失败**
```bash
# 清理缓存
pnpm store prune

# 重新安装
rm -rf node_modules
pnpm install
```

#### **构建失败**
```bash
# 清理构建产物
pnpm clean

# 重新构建
pnpm build
```

### **调试技巧**

#### **查看日志**
- 浏览器开发者工具 Console
- 终端输出日志
- 网络请求面板

#### **热重载**
- 修改代码后自动刷新
- 保持开发服务器运行
- 检查文件保存是否成功

## 📚 下一步

### **深入学习**
- [架构文档](../architecture/) - 了解系统架构
- [组件开发](../h5/components.md) - 开发自定义组件
- [事件系统](../designer/events.md) - 掌握事件配置

### **扩展功能**
- 添加新的组件类型
- 配置复杂的事件逻辑
- 集成第三方服务

### **部署上线**
- 配置生产环境
- 设置CI/CD流程
- 监控和维护

## 💡 小贴士

- **保存习惯**: 经常保存页面配置，避免丢失
- **预览测试**: 设计完成后在H5端测试效果
- **版本管理**: 重要变更前备份页面配置
- **性能优化**: 避免过多复杂组件影响性能

---

**需要帮助？** 查看 [完整文档](../README.md) 或联系开发团队
