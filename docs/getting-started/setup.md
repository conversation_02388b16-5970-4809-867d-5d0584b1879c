# 环境搭建

## 📋 环境要求

### **系统要求**
- **操作系统**: macOS, Windows, Linux
- **Node.js**: >= 16.0.0
- **pnpm**: >= 7.0.0
- **Git**: 最新版本

### **推荐开发工具**
- **IDE**: VS Code
- **浏览器**: Chrome >= 90
- **终端**: iTerm2 (macOS) / Windows Terminal (Windows)

## 🚀 快速安装

### **1. 克隆项目**

```bash
git clone <repository-url>
cd lowcode
```

### **2. 安装 pnpm**

如果还没有安装 pnpm：

```bash
# 使用 npm 安装
npm install -g pnpm

# 或使用 curl 安装
curl -fsSL https://get.pnpm.io/install.sh | sh -

# 验证安装
pnpm --version
```

### **3. 安装依赖**

```bash
# 安装所有依赖
pnpm install

# 验证安装
pnpm list
```

### **4. 启动开发服务器**

```bash
# 启动所有服务
pnpm dev

# 或单独启动服务
pnpm dev:h5        # H5端 (端口: 3000)
pnpm dev:designer  # PC端设计器 (端口: 3001)
pnpm dev:api       # API服务 (端口: 3002)
```

### **5. 验证安装**

访问以下地址验证服务是否正常启动：

- **H5端**: http://localhost:3000
- **PC端设计器**: http://localhost:3001
- **API服务**: http://localhost:3002/health

## 🔧 开发环境配置

### **VS Code 扩展推荐**

在项目根目录的 `.vscode/extensions.json` 中已配置推荐扩展：

```json
{
  "recommendations": [
    "Vue.volar",
    "Vue.vscode-typescript-vue-plugin",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

### **VS Code 设置**

在 `.vscode/settings.json` 中配置：

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "vue.codeActions.enabled": true
}
```

### **环境变量配置**

在各应用目录下创建 `.env.local` 文件：

#### **H5端 (apps/h5/.env.local)**
```env
# API 基础地址
VITE_API_BASE_URL=http://localhost:3000

# 应用标题
VITE_APP_TITLE=安生低代码平台

# 开发模式
VITE_DEV_MODE=true
```

#### **PC端设计器 (apps/designer/.env.local)**
```env
# API 基础地址
VITE_API_BASE_URL=http://localhost:3000

# 应用标题
VITE_APP_TITLE=安生低代码设计器

# 开发模式
VITE_DEV_MODE=true
```

#### **API服务 (apps/api/.env.local)**
```env
# 服务端口
PORT=3000

# 数据库连接
DATABASE_URL=sqlite:./data/dev.db

# JWT 密钥
JWT_SECRET=your-secret-key

# 开发模式
NODE_ENV=development
```

## 🛠️ 常用命令

### **开发命令**

```bash
# 启动开发服务器
pnpm dev                    # 启动所有服务
pnpm dev:h5                # 启动H5端
pnpm dev:designer          # 启动PC端设计器
pnpm dev:api               # 启动API服务

# 构建项目
pnpm build                 # 构建所有项目
pnpm build:h5              # 构建H5端
pnpm build:designer        # 构建PC端设计器
pnpm build:api             # 构建API服务

# 代码检查
pnpm lint                  # 检查所有项目
pnpm lint:fix              # 自动修复代码问题
pnpm type-check            # TypeScript 类型检查

# 测试
pnpm test                  # 运行所有测试
pnpm test:unit             # 运行单元测试
pnpm test:e2e              # 运行端到端测试
```

### **包管理命令**

```bash
# 安装依赖
pnpm install               # 安装所有依赖
pnpm add <package>         # 添加依赖到根目录
pnpm add <package> --filter @lowcode/ui  # 添加依赖到特定包

# 更新依赖
pnpm update                # 更新所有依赖
pnpm update <package>      # 更新特定依赖

# 清理
pnpm clean                 # 清理构建产物
pnpm clean:deps            # 清理依赖
```

## 🔍 故障排除

### **常见问题**

#### **1. 端口被占用**

```bash
# 查看端口占用
lsof -i :3000
lsof -i :3001
lsof -i :3002

# 杀死进程
kill -9 <PID>
```

#### **2. 依赖安装失败**

```bash
# 清理缓存
pnpm store prune

# 删除 node_modules 重新安装
rm -rf node_modules
rm -rf apps/*/node_modules
rm -rf packages/*/node_modules
pnpm install
```

#### **3. TypeScript 错误**

```bash
# 重新生成类型文件
pnpm type-check

# 重启 TypeScript 服务 (VS Code)
Ctrl/Cmd + Shift + P -> "TypeScript: Restart TS Server"
```

#### **4. 构建失败**

```bash
# 清理构建缓存
pnpm clean

# 重新构建
pnpm build
```

### **调试技巧**

#### **1. 浏览器调试**

- 使用 Chrome DevTools
- 安装 Vue DevTools 扩展
- 启用 Source Maps

#### **2. VS Code 调试**

在 `.vscode/launch.json` 中配置调试：

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug H5 App",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:3000",
      "webRoot": "${workspaceFolder}/apps/h5/src"
    }
  ]
}
```

#### **3. 网络调试**

```bash
# 查看网络请求
curl -X GET http://localhost:3002/health

# 使用代理工具
# Charles, Fiddler, Postman
```

## 📚 下一步

环境搭建完成后，建议阅读以下文档：

1. [项目结构说明](./structure.md)
2. [开发指南](./development.md)
3. [系统架构概览](../architecture/overview.md)
4. [应用类型管理](../architecture/application-types.md)

---

**版本**: v1.0.0  
**最后更新**: 2024-01-17
