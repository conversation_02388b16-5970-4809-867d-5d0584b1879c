# 项目结构说明

## 📁 总体结构

```
lowcode/
├── 📱 apps/                        # 应用目录
│   ├── h5/                        # H5端应用
│   ├── designer/                  # PC端设计器
│   └── api/                       # API服务
├── 📦 packages/                   # 共享包
│   ├── core/                     # 核心包
│   ├── ui/                       # UI组件库
│   └── hooks/                    # 共享Hooks
├── 📚 docs/                      # 文档目录
├── 🛠️ tools/                     # 工具配置
├── 📜 scripts/                   # 构建脚本
├── 🔧 package.json              # 根配置文件
├── 🔧 pnpm-workspace.yaml       # pnpm工作空间配置
└── 🔧 turbo.json                # Turbo构建配置
```

## 📱 Apps 目录

### **H5端应用 (apps/h5/)**

```
apps/h5/
├── public/                       # 静态资源
├── src/
│   ├── components/              # 页面组件
│   │   ├── common/             # 通用组件
│   │   └── layout/             # 布局组件
│   ├── views/                  # 页面视图
│   │   ├── Home.vue           # 首页
│   │   ├── PackageList.vue    # 套餐列表
│   │   └── ...                # 其他页面
│   ├── stores/                 # 状态管理
│   │   ├── app.ts             # 应用状态
│   │   ├── user.ts            # 用户状态
│   │   └── index.ts           # 状态入口
│   ├── router/                 # 路由配置
│   │   ├── index.ts           # 路由主文件
│   │   └── guards.ts          # 路由守卫
│   ├── utils/                  # 工具函数
│   │   ├── api.ts             # API请求
│   │   ├── storage.ts         # 本地存储
│   │   └── helpers.ts         # 辅助函数
│   ├── styles/                 # 样式文件
│   │   ├── global.scss        # 全局样式
│   │   └── variables.scss     # 样式变量
│   ├── App.vue                 # 根组件
│   └── main.ts                 # 应用入口
├── index.html                  # HTML模板
├── vite.config.ts             # Vite配置
└── package.json               # 包配置
```

### **PC端设计器 (apps/designer/)**

```
apps/designer/
├── public/                     # 静态资源
├── src/
│   ├── components/            # 设计器组件
│   │   ├── layout/           # 布局组件
│   │   ├── canvas/           # 画布组件
│   │   ├── properties/       # 属性面板
│   │   ├── library/          # 组件库
│   │   └── events/           # 事件配置
│   ├── views/                 # 设计器页面
│   │   ├── AppManager.vue    # 应用管理
│   │   ├── Designer.vue      # 设计器主页
│   │   └── Preview.vue       # 预览页面
│   ├── stores/                # 状态管理
│   │   ├── designer.ts       # 设计器状态
│   │   ├── app.ts            # 应用状态
│   │   └── index.ts          # 状态入口
│   ├── utils/                 # 工具函数
│   │   ├── api.ts            # API请求
│   │   ├── canvas.ts         # 画布工具
│   │   └── export.ts         # 导出工具
│   ├── styles/                # 样式文件
│   ├── App.vue                # 根组件
│   └── main.ts                # 应用入口
├── index.html                 # HTML模板
├── vite.config.ts            # Vite配置
└── package.json              # 包配置
```

### **API服务 (apps/api/)**

```
apps/api/
├── src/
│   ├── routes/                # 路由定义
│   │   ├── apps.ts           # 应用相关API
│   │   ├── pages.ts          # 页面相关API
│   │   └── index.ts          # 路由入口
│   ├── middleware/            # 中间件
│   │   ├── auth.ts           # 认证中间件
│   │   ├── cors.ts           # 跨域中间件
│   │   └── logger.ts         # 日志中间件
│   ├── utils/                 # 工具函数
│   │   ├── database.ts       # 数据库工具
│   │   ├── validation.ts     # 数据验证
│   │   └── response.ts       # 响应工具
│   ├── types/                 # 类型定义
│   └── app.ts                 # 应用主文件
├── package.json               # 包配置
└── tsconfig.json             # TypeScript配置
```

## 📦 Packages 目录

### **核心包 (packages/core/)**

```
packages/core/
├── src/
│   ├── applications/          # 应用类型管理
│   │   ├── index.ts          # 统一导出
│   │   ├── device/           # 设备充值端
│   │   │   └── index.ts      # 设备应用配置
│   │   └── mall/             # 商城端
│   │       └── index.ts      # 商城应用配置
│   ├── config/                # 配置文件
│   │   ├── constants.ts      # 常量定义
│   │   └── themes.ts         # 主题配置
│   ├── types/                 # 类型定义
│   │   ├── application.ts    # 应用类型
│   │   ├── component.ts      # 组件类型
│   │   └── index.ts          # 类型导出
│   └── index.ts               # 包入口
├── package.json               # 包配置
└── tsconfig.json             # TypeScript配置
```

### **UI组件库 (packages/ui/)**

```
packages/ui/
├── src/
│   ├── components/            # 组件实现
│   │   ├── Home/             # 首页组件
│   │   │   ├── HomeBasic/    # 基础信息组件
│   │   │   ├── HomeMore/     # 更多功能组件
│   │   │   └── index.ts      # 组件导出
│   │   ├── Layout/           # 布局组件
│   │   ├── Form/             # 表单组件
│   │   └── index.ts          # 组件库导出
│   ├── styles/                # 样式文件
│   │   ├── components/       # 组件样式
│   │   ├── themes/           # 主题样式
│   │   └── index.scss        # 样式入口
│   ├── utils/                 # 组件工具
│   │   ├── events.ts         # 事件工具
│   │   ├── props.ts          # 属性工具
│   │   └── index.ts          # 工具导出
│   └── index.ts               # 包入口
├── package.json               # 包配置
└── tsconfig.json             # TypeScript配置
```

### **共享Hooks (packages/hooks/)**

```
packages/hooks/
├── src/
│   ├── hooks/                 # Hook实现
│   │   ├── useApi.ts         # API请求Hook
│   │   ├── useStorage.ts     # 存储Hook
│   │   ├── useDevice.ts      # 设备信息Hook
│   │   └── index.ts          # Hook导出
│   ├── utils/                 # Hook工具
│   │   ├── request.ts        # 请求工具
│   │   ├── cache.ts          # 缓存工具
│   │   └── index.ts          # 工具导出
│   └── index.ts               # 包入口
├── package.json               # 包配置
└── tsconfig.json             # TypeScript配置
```

## 🔧 配置文件

### **根目录配置**

- **package.json** - 项目主配置，定义脚本和依赖
- **pnpm-workspace.yaml** - pnpm工作空间配置
- **turbo.json** - Turbo构建配置
- **tsconfig.json** - TypeScript根配置
- **.gitignore** - Git忽略文件配置
- **.eslintrc.js** - ESLint代码规范配置
- **.prettierrc** - Prettier代码格式化配置

### **工具配置 (tools/)**

```
tools/
├── eslint-config/             # ESLint配置包
│   ├── index.js              # ESLint规则
│   └── package.json          # 配置包信息
└── tsconfig/                  # TypeScript配置包
    ├── base.json             # 基础TS配置
    ├── vue.json              # Vue项目TS配置
    └── package.json          # 配置包信息
```

## 📚 文档结构 (docs/)

```
docs/
├── README.md                  # 文档首页
├── architecture/              # 架构文档
│   ├── overview.md           # 系统架构概览
│   ├── application-types.md  # 应用类型管理
│   └── modules.md            # 模块化设计
├── getting-started/           # 快速开始
│   ├── setup.md              # 环境搭建
│   ├── structure.md          # 项目结构
│   └── development.md        # 开发指南
├── h5/                        # H5端文档
├── designer/                  # PC端设计器文档
├── core/                      # 核心包文档
├── ui/                        # UI组件库文档
├── api/                       # API文档
├── changelog.md               # 更新日志
└── migration.md               # 迁移指南
```

## 🔗 依赖关系

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   H5 App    │    │  Designer   │    │  API Server │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       └───────────────────┼───────────────────┘
                           │
              ┌─────────────▼─────────────┐
              │      Shared Packages      │
              ├─────────────┬─────────────┤
              │    Core     │     UI      │
              └─────────────┴─────────────┘
                           │
              ┌─────────────▼─────────────┐
              │         Hooks            │
              └─────────────────────────────┘
```

### **依赖说明**

- **Apps** 依赖 **Packages** 中的共享代码
- **Core** 包提供基础功能和类型定义
- **UI** 包提供可复用的组件
- **Hooks** 包提供共享的业务逻辑
- **Tools** 提供开发和构建工具配置

## 💡 设计原则

### **1. 单一职责**
每个包和模块都有明确的职责边界

### **2. 依赖倒置**
高层模块不依赖低层模块，都依赖抽象

### **3. 开放封闭**
对扩展开放，对修改封闭

### **4. 接口隔离**
使用小而专一的接口

### **5. 依赖注入**
通过依赖注入实现松耦合

---

**版本**: v1.0.0  
**最后更新**: 2024-01-17
