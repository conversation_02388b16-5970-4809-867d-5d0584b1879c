# 服务层重构架构文档

## 📋 概述

本文档详细介绍了H5端服务层的模块化重构，包括架构设计、模块职责、使用方式等。

## 🎯 重构目标

### **问题背景**
- 原有的 `EventHandlerService.ts` 文件过大（500+ 行）
- 职责混乱，包含事件处理、导航、自定义代码等多种功能
- 硬编码问题，路由映射不够灵活
- 难以维护和扩展

### **重构目标**
- ✅ **模块化**：按职责分离，每个模块职责单一
- ✅ **可维护性**：清晰的模块边界，易于理解和修改
- ✅ **可扩展性**：新功能可以独立添加
- ✅ **向后兼容**：现有代码无需修改
- ✅ **配置驱动**：消除硬编码，完全配置驱动

## 📁 新架构设计

### **目录结构**
```
apps/h5/src/services/
├── index.ts                    # 统一导出
├── LegacyEventHandler.ts       # 兼容包装器
├── AppIdResolver.ts            # 应用ID解析
├── PageConfigService.ts        # 页面配置
├── RequestManager.ts           # 请求管理
├── RouteResolver.ts            # 路由解析
├── events/                     # 事件处理模块
│   ├── EventHandlerService.ts  # 事件处理主服务
│   ├── NavigationHandler.ts    # 导航处理
│   ├── CustomEventHandler.ts   # 自定义事件
│   └── PresetActions.ts        # 预设操作
└── utils/                      # 工具类
    └── PathResolver.ts         # 路径解析
```

### **模块职责**

#### **1. EventHandlerService** (主服务)
- 统一的事件处理入口
- 事件分发和路由
- 向后兼容处理

#### **2. NavigationHandler** (导航处理)
- 页面导航逻辑
- 外部链接处理
- WebView导航
- 返回导航

#### **3. CustomEventHandler** (自定义事件)
- 自定义代码执行
- 预设操作调用
- 安全执行环境

#### **4. PresetActions** (预设操作)
- 预设操作实现
- 消息显示
- 文件操作
- UI交互

#### **5. PathResolver** (路径解析)
- 智能路径解析
- 路由类型判断
- AppID动态获取

## 🔧 核心功能

### **1. 智能路由解析**

```typescript
// 自动识别页面类型并选择正确的路由
const pathResolver = new PathResolver(router)
const finalPath = pathResolver.resolveTargetPath('/device/PackageList')

// 流程：
// 1. 解析应用类型和页面路径
// 2. 检查页面是否可配置
// 3. 动态获取当前AppID
// 4. 返回正确的路由路径
```

### **2. 事件处理流程**

```typescript
// 统一的事件处理入口
eventHandler.handleComponentEvent(component, 'click', eventData)

// 流程：
// 1. 查找匹配的事件配置
// 2. 根据事件类型分发到对应处理器
// 3. 执行具体的事件逻辑
// 4. 错误处理和日志记录
```

### **3. 预设操作系统**

```typescript
// 支持的预设操作
const presetActions = new PresetActions()

await presetActions.executePresetAction({
  preset: 'showMessage',
  message: '操作成功'
}, data, component)
```

## 🚀 使用方式

### **方式1：兼容导入（推荐）**
```typescript
import { EventHandlerService } from '@/services'

// 现有代码无需修改
const eventHandler = new EventHandlerService(router)
await eventHandler.handleComponentEvent(comp, 'click', data)
```

### **方式2：模块化导入**
```typescript
import { 
  NavigationHandler,
  CustomEventHandler,
  PathResolver 
} from '@/services'

// 按需使用特定模块
const navigationHandler = new NavigationHandler(router)
await navigationHandler.handleNavigation(config)
```

### **方式3：工厂函数**
```typescript
import { 
  createEventHandlerService,
  createNavigationHandler 
} from '@/services'

// 使用工厂函数创建实例
const eventHandler = createEventHandlerService(router)
const navigationHandler = createNavigationHandler(router)
```

## 🔄 向后兼容

### **兼容策略**
- 保留原有的 `EventHandlerService` 接口
- 内部委托给新的模块化服务
- 自动转换旧版本事件格式
- 完全透明的迁移过程

### **旧事件格式转换**
```typescript
// 旧格式自动转换为新格式
'menuClick' → { elementType: 'menu', elementId: data.id }
'buttonClick' → { elementType: 'button', elementId: data.type }
'actionClick' → { elementType: 'action', elementId: data.type }
```

## 📈 性能优化

### **按需加载**
- 只导入需要的模块
- 减少包体积
- 更好的Tree Shaking

### **代码分割**
- 事件处理模块独立
- 工具类模块独立
- 支持动态导入

## 🧪 测试策略

### **单元测试**
- 每个模块独立测试
- 模拟依赖注入
- 覆盖核心功能

### **集成测试**
- 事件处理流程测试
- 路由解析测试
- 向后兼容测试

## 🔮 未来规划

### **短期目标**
- 完善单元测试覆盖
- 性能监控和优化
- 文档完善

### **长期目标**
- 插件化架构
- 更多预设操作
- 可视化配置工具

## 📚 相关文档

- [事件系统文档](../designer/events.md)
- [路由管理文档](../h5/routing.md)
- [组件开发指南](../h5/components.md)

---

**版本**: v1.2.0  
**最后更新**: 2025-01-20
