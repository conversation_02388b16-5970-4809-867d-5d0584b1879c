# 应用类型管理系统

## 🎯 系统概述

应用类型管理系统是安生低代码平台的核心功能之一，提供了统一的应用类型定义、路由管理和页面配置能力。

## 📁 文件结构

```
packages/core/src/applications/
├── index.ts                    # 统一导出和工具函数
├── device/                     # 设备充值端
│   └── index.ts               # 设备应用配置
└── mall/                      # 商城端
    └── index.ts               # 商城应用配置
```

## 🔧 核心功能

### **1. 应用类型定义**

每个应用类型包含以下信息：

```typescript
interface ApplicationType {
  id: string              // 应用类型ID
  name: string           // 应用类型名称
  description: string    // 应用类型描述
  icon: string          // 应用图标
  color: string         // 应用颜色主题
  enabled: boolean      // 是否启用
  order: number         // 排序权重
  defaultPage: string   // 默认页面路径
  features: string[]    // 支持的功能特性
}
```

### **2. 页面路由配置**

每个页面路由包含以下配置：

```typescript
interface PageRoute {
  path: string              // 路由路径
  name: string             // 路由名称
  title: string            // 页面标题
  description?: string     // 页面描述
  category: string         // 页面分类
  requiresAuth: boolean    // 是否需要认证
  configurable: boolean    // 是否可配置（低代码）
  icon?: string           // 页面图标
  params?: PageParam[]    // 参数说明
}
```

### **3. 页面分类系统**

页面按功能分为以下类别：

- **auth** - 认证页面（登录、注册等）
- **home** - 首页类页面
- **business** - 业务功能页面
- **payment** - 支付相关页面
- **user** - 用户中心页面
- **system** - 系统功能页面

## 🎨 支持的应用类型

### **设备充值端 (device)**

```typescript
export const DEVICE_APPLICATION: ApplicationType = {
  id: 'device',
  name: '设备充值端',
  description: '设备管理和充值业务应用',
  icon: 'smartphone',
  color: '#1890ff',
  enabled: true,
  order: 1,
  defaultPage: '/home',
  features: ['device-management', 'package-purchase', 'balance-recharge', 'real-name-auth']
}
```

**主要页面**：
- 首页 (`/home`) - 设备管理主页
- 套餐列表 (`/PackageList`) - 查看和购买套餐
- 余额充值 (`/BalanceList`) - 账户余额充值
- 实名认证 (`/RealName`) - 实名认证页面
- 设备设置 (`/EditDevice`) - 设备参数设置

### **商城端 (mall)**

```typescript
export const MALL_APPLICATION: ApplicationType = {
  id: 'mall',
  name: '商城端',
  description: '电商购物业务应用',
  icon: 'shopping-cart',
  color: '#52c41a',
  enabled: false, // 暂未开发完成
  order: 2,
  defaultPage: '/mall/home',
  features: ['product-catalog', 'shopping-cart', 'order-management', 'payment']
}
```

**主要页面**：
- 商城首页 (`/mall/home`) - 商城主页
- 商品列表 (`/mall/products`) - 商品列表页面
- 商品详情 (`/mall/product/:id`) - 商品详情页面
- 购物车 (`/mall/cart`) - 购物车页面
- 订单列表 (`/mall/orders`) - 用户订单列表

## 🛠️ 工具函数

### **应用类型管理**

```typescript
// 获取所有启用的应用类型
getEnabledApplicationTypes(): ApplicationType[]

// 获取应用类型信息
getApplicationType(appType: string): ApplicationType | undefined

// 获取应用配置
getApplicationConfig(appType: string): ApplicationConfig | undefined
```

### **路由管理**

```typescript
// 根据应用类型获取路由列表
getRoutesByApplicationType(appType: string): PageRoute[]

// 根据应用类型和分类获取路由
getRoutesByCategory(appType: string, category: string): PageRoute[]

// 获取可配置的路由（用于低代码）
getConfigurableRoutes(appType: string): PageRoute[]

// 获取分组后的路由（用于PC端选择器）
getGroupedRoutes(appType: string): Record<string, PageRoute[]>
```

### **路由验证**

```typescript
// 验证路由是否存在
isValidRoute(appType: string, path: string): boolean

// 获取路由的显示名称
getRouteDisplayName(appType: string, path: string): string

// 搜索路由
searchRoutes(appType: string, keyword: string): PageRoute[]
```

## 🎯 使用场景

### **1. PC端设计器 - 新建应用**

```typescript
import { getEnabledApplicationTypes } from '@lowcode/core'

const AppTypeSelector = () => {
  const applicationTypes = getEnabledApplicationTypes()
  
  return (
    <Select placeholder="选择应用类型">
      {applicationTypes.map(type => (
        <Option key={type.id} value={type.id}>
          <div className="flex items-center">
            <Icon name={type.icon} color={type.color} />
            <div>
              <div className="font-medium">{type.name}</div>
              <div className="text-gray-500 text-sm">{type.description}</div>
            </div>
          </div>
        </Option>
      ))}
    </Select>
  )
}
```

### **2. PC端设计器 - 页面跳转配置**

```typescript
import { getGroupedRoutes, getPageCategories } from '@lowcode/core'

const PageSelector = ({ appType }) => {
  const groupedRoutes = getGroupedRoutes(appType)
  const categories = getPageCategories()

  return (
    <Select placeholder="选择跳转页面">
      {categories.map(category => {
        const routes = groupedRoutes[category.id] || []
        return (
          <OptGroup key={category.id} label={category.name}>
            {routes.map(route => (
              <Option key={route.path} value={route.path}>
                {route.title} - {route.path}
              </Option>
            ))}
          </OptGroup>
        )
      })}
    </Select>
  )
}
```

### **3. H5端 - 路由验证**

```typescript
import { isValidRoute, getRouteDisplayName } from '@lowcode/core'

// 在路由守卫中验证
const validateRoute = (appType: string, path: string) => {
  if (!isValidRoute(appType, path)) {
    console.warn(`无效的路由: ${path} for app type: ${appType}`)
    return false
  }
  return true
}

// 获取页面标题
const getPageTitle = (appType: string, path: string) => {
  return getRouteDisplayName(appType, path)
}
```

## 🚀 扩展新应用类型

### **1. 创建应用配置文件**

```bash
# 创建新应用目录
mkdir packages/core/src/applications/crm

# 创建配置文件
touch packages/core/src/applications/crm/index.ts
```

### **2. 定义应用配置**

```typescript
// packages/core/src/applications/crm/index.ts
export const CRM_APPLICATION: ApplicationType = {
  id: 'crm',
  name: 'CRM系统',
  description: '客户关系管理系统',
  icon: 'users',
  color: '#722ed1',
  enabled: true,
  order: 3,
  defaultPage: '/crm/dashboard',
  features: ['customer-management', 'sales-tracking']
}

export const CRM_ROUTES: Record<string, PageRoute> = {
  dashboard: {
    path: '/crm/dashboard',
    name: 'CrmDashboard',
    title: 'CRM仪表板',
    category: 'home',
    requiresAuth: true,
    configurable: true,
    icon: 'dashboard'
  },
  // ... 更多路由
}

export default {
  application: CRM_APPLICATION,
  routes: CRM_ROUTES
}
```

### **3. 更新统一导出**

```typescript
// packages/core/src/applications/index.ts
import crmConfig from './crm'

const APPLICATION_CONFIGS: Record<string, ApplicationConfig> = {
  device: deviceConfig,
  mall: mallConfig,
  crm: crmConfig  // 新增
}
```

### **4. 自动生效**

新增应用类型后，以下功能自动生效：
- ✅ PC端设计器自动显示新的应用类型选项
- ✅ 页面选择器自动包含新应用的路由
- ✅ H5端自动支持新应用的路由验证
- ✅ 所有工具函数自动支持新应用类型

## 💡 最佳实践

1. **命名规范** - 使用清晰的命名约定
2. **分类组织** - 合理使用页面分类
3. **图标选择** - 选择合适的图标表示应用类型
4. **路径设计** - 设计清晰的路径结构
5. **文档维护** - 及时更新相关文档

---

**版本**: v1.0.0  
**最后更新**: 2024-01-17
