# 系统架构概览

## 🏗️ 整体架构

安生低代码平台采用现代化的前端架构设计，基于 Monorepo 模式组织代码，支持多端应用开发。

```
┌─────────────────────────────────────────────────────────────┐
│                    安生低代码平台                              │
├─────────────────────────────────────────────────────────────┤
│  Apps Layer (应用层)                                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │   H5端      │  │  PC端设计器  │  │  API服务    │          │
│  │             │  │             │  │             │          │
│  │ Vue 3 + TS  │  │ Vue 3 + TS  │  │ Express.js  │          │
│  │ Vant UI     │  │ Ant Design  │  │ TypeScript  │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  Packages Layer (共享包层)                                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │  Core包     │  │   UI包      │  │  Hooks包    │          │
│  │             │  │             │  │             │          │
│  │ 应用类型管理 │  │ 组件库      │  │ 共享Hooks   │          │
│  │ 路由管理    │  │ 主题系统    │  │ 工具函数    │          │
│  │ 工具函数    │  │ 事件系统    │  │             │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  Infrastructure Layer (基础设施层)                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │ 构建工具    │  │ 开发工具    │  │ 部署工具    │          │
│  │ Vite        │  │ TypeScript  │  │ Docker      │          │
│  │ Turbo       │  │ ESLint      │  │ CI/CD       │          │
│  │ pnpm        │  │ Prettier    │  │             │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 📦 项目结构

```
lowcode/
├── apps/                           # 应用目录
│   ├── h5/                        # H5端应用
│   │   ├── src/
│   │   │   ├── components/        # H5组件
│   │   │   ├── views/            # 页面视图
│   │   │   ├── stores/           # 状态管理
│   │   │   ├── utils/            # 工具函数
│   │   │   └── router/           # 路由配置
│   │   └── package.json
│   │
│   ├── designer/                  # PC端设计器
│   │   ├── src/
│   │   │   ├── components/       # 设计器组件
│   │   │   ├── views/           # 设计器页面
│   │   │   ├── stores/          # 状态管理
│   │   │   └── utils/           # 工具函数
│   │   └── package.json
│   │
│   └── api/                      # API服务
│       ├── src/
│       │   ├── routes/          # 路由定义
│       │   ├── middleware/      # 中间件
│       │   └── utils/           # 工具函数
│       └── package.json
│
├── packages/                     # 共享包
│   ├── core/                    # 核心包
│   │   ├── src/
│   │   │   ├── applications/    # 应用类型管理
│   │   │   ├── config/         # 配置文件
│   │   │   └── types/          # 类型定义
│   │   └── package.json
│   │
│   ├── ui/                      # UI组件库
│   │   ├── src/
│   │   │   ├── components/     # 组件实现
│   │   │   ├── styles/        # 样式文件
│   │   │   └── utils/         # 组件工具
│   │   └── package.json
│   │
│   └── hooks/                   # 共享Hooks
│       ├── src/
│       │   ├── hooks/          # Hook实现
│       │   └── utils/          # Hook工具
│       └── package.json
│
├── docs/                        # 文档目录
├── tools/                       # 工具配置
├── scripts/                     # 构建脚本
└── package.json                 # 根配置文件
```

## 🎯 核心模块

### **1. 应用类型管理系统**

负责管理不同类型的应用（设备充值端、商城端等）：

- **应用类型定义** - 定义应用的基本信息和特性
- **路由管理** - 管理每种应用类型的页面路由
- **页面分类** - 按功能对页面进行分类组织
- **工具函数** - 提供路由查询、验证等功能

### **2. 组件系统**

基于 Vue 3 的组件化架构：

- **UI组件库** - 可复用的UI组件
- **业务组件** - 特定业务场景的组件
- **低代码组件** - 支持可视化配置的组件
- **事件系统** - 组件间的事件通信机制

### **3. 状态管理**

使用 Pinia 进行状态管理：

- **应用状态** - 当前应用信息和配置
- **设计器状态** - 设计器的编辑状态
- **用户状态** - 用户信息和权限
- **数据状态** - 业务数据的管理

### **4. 路由系统**

基于 Vue Router 的路由管理：

- **动态路由** - 根据应用类型动态生成路由
- **路由守卫** - 权限验证和页面拦截
- **路由配置** - 统一的路由配置管理
- **页面缓存** - 页面状态的保持和恢复

## 🔄 数据流

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   用户操作   │───▶│  组件事件   │───▶│  状态更新   │
└─────────────┘    └─────────────┘    └─────────────┘
       ▲                                      │
       │                                      ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   UI更新    │◀───│  响应式更新  │◀───│  数据变化   │
└─────────────┘    └─────────────┘    └─────────────┘
```

### **事件流程**

1. **用户交互** - 用户在界面上进行操作
2. **事件触发** - 组件触发相应的事件
3. **状态更新** - 通过 Pinia 更新应用状态
4. **响应式更新** - Vue 的响应式系统自动更新UI
5. **界面刷新** - 用户看到最新的界面状态

## 🛠️ 技术栈

### **前端技术**

- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **包管理**: pnpm + Monorepo
- **状态管理**: Pinia
- **路由**: Vue Router
- **UI组件**: Ant Design Vue (PC端) + Vant (H5端)
- **样式**: SCSS + CSS Modules
- **图标**: Iconify

### **开发工具**

- **代码规范**: ESLint + Prettier
- **类型检查**: TypeScript
- **构建优化**: Turbo
- **版本管理**: Git
- **包管理**: pnpm workspace

### **部署工具**

- **容器化**: Docker
- **CI/CD**: GitHub Actions
- **静态托管**: Nginx
- **API服务**: Node.js + Express

## 🔧 核心特性

### **1. 模块化架构**

- **高内聚低耦合** - 模块间依赖关系清晰
- **可插拔设计** - 支持功能模块的动态加载
- **统一接口** - 标准化的模块接口设计

### **2. 类型安全**

- **TypeScript支持** - 完整的类型定义
- **编译时检查** - 在编译阶段发现类型错误
- **IDE支持** - 完善的代码提示和检查

### **3. 响应式设计**

- **多端适配** - 支持PC端和移动端
- **弹性布局** - 基于Flexbox和Grid的布局
- **主题系统** - 支持主题定制和切换

### **4. 性能优化**

- **代码分割** - 按需加载减少初始包大小
- **缓存策略** - 合理的缓存机制
- **懒加载** - 组件和路由的懒加载

## 🚀 扩展性

### **1. 应用类型扩展**

- 新增应用类型只需添加配置文件
- 自动集成到设计器和H5端
- 支持自定义页面和路由

### **2. 组件扩展**

- 标准化的组件开发规范
- 支持第三方组件集成
- 可视化配置能力

### **3. 功能扩展**

- 插件化的功能模块
- 标准化的API接口
- 灵活的配置系统

---

**版本**: v1.0.0  
**最后更新**: 2024-01-17
