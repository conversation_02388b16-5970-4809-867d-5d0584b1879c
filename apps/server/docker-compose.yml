version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: lowcode-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD:-lowcode123}
      MYSQL_DATABASE: ${DB_DATABASE:-lowcode_platform}
      MYSQL_USER: ${DB_USERNAME:-lowcode}
      MYSQL_PASSWORD: ${DB_PASSWORD:-lowcode123}
    ports:
      - "${DB_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database-init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - lowcode-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: lowcode-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - lowcode-network

  # 低代码服务器
  lowcode-server:
    build: .
    container_name: lowcode-server
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3002
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USERNAME: ${DB_USERNAME:-lowcode}
      DB_PASSWORD: ${DB_PASSWORD:-lowcode123}
      DB_DATABASE: ${DB_DATABASE:-lowcode_platform}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET}
    ports:
      - "3002:3002"
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      - mysql
      - redis
    networks:
      - lowcode-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3002/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
  redis_data:

networks:
  lowcode-network:
    driver: bridge