# 环境变量安全配置指南

## 🔐 安全密钥配置

### 重要提醒
**⚠️ 绝对不要在代码中硬编码任何密钥或敏感信息！**

所有密钥都应该通过环境变量进行配置，并且在生产环境中使用强密钥。

## 📋 必需的环境变量

### 服务端 (NestJS)

```bash
# apps/server/.env

# JWT密钥（必须设置，用于JWT token签名）
JWT_SECRET=your-super-secure-jwt-secret-key-here

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your-db-password
DB_DATABASE=lowcode_platform

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
```

### 前端应用 (H5/Designer)

```bash
# apps/h5/.env.production
# apps/designer/.env.production

# 认证密钥（必须设置，用于API认证）
VITE_AUTH_SECRET=your-super-secure-auth-secret-key-here

# API地址
VITE_DEVICE_AN_API_URL=https://your-api-domain.com
VITE_LOWCODE_API_URL=https://your-lowcode-api.com
```

## 🔑 密钥生成建议

### JWT密钥生成
```bash
# 生成256位随机密钥
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# 或使用openssl
openssl rand -hex 32
```

### 认证密钥生成
```bash
# 生成复杂密钥
node -e "console.log(require('crypto').randomBytes(64).toString('base64'))"
```