#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 构建独立可执行文件...');

// 1. 构建项目
console.log('📦 构建NestJS项目...');
execSync('npm run build', { stdio: 'inherit' });

// 2. 创建启动脚本
const startScript = `#!/usr/bin/env node

// 设置环境变量默认值
process.env.NODE_ENV = process.env.NODE_ENV || 'production';
process.env.PORT = process.env.PORT || '3002';

// 启动应用
require('./dist/main');
`;

// 3. 创建部署包
const deployDir = './deploy';
if (!fs.existsSync(deployDir)) {
  fs.mkdirSync(deployDir);
}

// 复制必要文件
console.log('📁 复制文件...');
execSync(`cp -r dist ${deployDir}/`);
execSync(`cp -r node_modules ${deployDir}/`);
execSync(`cp package.json ${deployDir}/`);
execSync(`cp .env.example ${deployDir}/.env`);
execSync(`cp database-init.sql ${deployDir}/`);

// 创建启动脚本
fs.writeFileSync(path.join(deployDir, 'start.js'), startScript);
fs.chmodSync(path.join(deployDir, 'start.js'), '755');

// 创建启动脚本 (Unix)
const unixStartScript = `#!/bin/bash
export NODE_ENV=production
export PORT=3002
node start.js
`;
fs.writeFileSync(path.join(deployDir, 'start.sh'), unixStartScript);
fs.chmodSync(path.join(deployDir, 'start.sh'), '755');

// 创建启动脚本 (Windows)
const winStartScript = `@echo off
set NODE_ENV=production
set PORT=3002
node start.js
`;
fs.writeFileSync(path.join(deployDir, 'start.bat'), winStartScript);