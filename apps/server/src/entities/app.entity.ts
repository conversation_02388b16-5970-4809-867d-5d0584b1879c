import {
  Entity,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { PageConfig } from './page-config.entity';

@Entity('lowcode_apps')
export class App {
  @PrimaryColumn({ length: 36, comment: '应用ID（UUID格式）' })
  id: string;

  @Column({ length: 50, unique: true, nullable: true, comment: '友好URL标识' })
  slug: string;

  @Column({ length: 100, comment: '应用名称' })
  name: string;

  @Column({ length: 500, nullable: true, comment: '应用描述' })
  description: string;

  @Column({ length: 200, nullable: true, comment: '应用图标' })
  icon: string;

  @Column({
    name: 'app_type',
    length: 50,
    default: 'device',
    comment: '应用类型：device-设备端，mall-商城端'
  })
  appType: string;

  @Column({
    name: 'default_home_page',
    length: 100,
    nullable: true,
    comment: '默认首页'
  })
  defaultHomePage: string;

  @Column({
    name: 'tab_bar',
    type: 'json',
    nullable: true,
    comment: 'TabBar配置JSON'
  })
  tabBar: any;

  @Column({
    type: 'tinyint',
    default: 0,
    comment: '是否发布：0-未发布，1-已发布'
  })
  published: number;

  @Column({
    name: 'publish_time',
    type: 'timestamp',
    nullable: true,
    comment: '发布时间'
  })
  publishTime: Date | null;

  @Column({ name: 'creator_id', comment: '创建者ID' })
  creator_id: number;

  @CreateDateColumn({ name: 'create_time', comment: '创建时间' })
  createTime: Date;

  @UpdateDateColumn({ name: 'update_time', comment: '更新时间' })
  updateTime: Date;

  // 关联关系
  @OneToMany(() => PageConfig, pageConfig => pageConfig.app)
  pageConfigs: PageConfig[];
}