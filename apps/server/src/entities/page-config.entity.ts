import {
  Entity,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { App } from './app.entity';

@Entity('lowcode_page_configs')
export class PageConfig {
  @PrimaryColumn({ length: 36, comment: '页面ID（UUID格式）' })
  id: string;

  @Column({ name: 'app_id', length: 36, comment: '应用ID' })
  appId: string;

  @Column({ length: 50, nullable: true, comment: '友好URL标识' })
  slug: string;

  @Column({ length: 100, comment: '页面名称' })
  name: string;

  @Column({ length: 200, comment: '页面路径' })
  path: string;

  @Column({ length: 100, comment: '页面标题' })
  title: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: '页面布局配置JSON'
  })
  layout: any;

  @Column({
    type: 'json',
    nullable: true,
    comment: '页面样式配置JSON'
  })
  style: any;

  @Column({
    type: 'json',
    comment: '页面组件配置JSON'
  })
  components: any;

  @Column({
    name: 'data_source',
    type: 'json',
    nullable: true,
    comment: '数据源配置JSON'
  })
  dataSource: any;

  @Column({
    type: 'json',
    nullable: true,
    comment: '页面事件配置JSON'
  })
  events: any;

  @Column({
    type: 'tinyint',
    default: 0,
    comment: '是否发布：0-未发布，1-已发布'
  })
  published: number;

  @Column({
    name: 'publish_time',
    type: 'timestamp',
    nullable: true,
    comment: '发布时间'
  })
  publishTime: Date | null;

  @CreateDateColumn({ name: 'create_time', comment: '创建时间' })
  createTime: Date;

  @UpdateDateColumn({ name: 'update_time', comment: '更新时间' })
  updateTime: Date;

  // 关联关系
  @ManyToOne(() => App, app => app.pageConfigs)
  @JoinColumn({ name: 'app_id' })
  app: App;
}