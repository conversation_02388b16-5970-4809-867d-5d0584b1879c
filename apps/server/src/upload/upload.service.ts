import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class UploadService {
  constructor(private configService: ConfigService) {}

  handleUpload(file: Express.Multer.File) {
    if (!file) {
      throw new Error('没有上传文件');
    }

    const baseUrl = this.configService.get('BASE_URL', 'http://localhost:3002');
    const fileUrl = `${baseUrl}/uploads/${file.filename}`;

    return {
      filename: file.filename,
      originalname: file.originalname,
      size: file.size,
      mimetype: file.mimetype,
      url: fileUrl,
      path: `/uploads/${file.filename}`,
    };
  }
}