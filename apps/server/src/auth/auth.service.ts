import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class AuthService {
  constructor(
    private jwtService: JwtService,
  ) {}

  // 简化认证：只验证JWT token，不查询数据库
  // 因为您的线上系统已经处理了用户认证
  async validateToken(token: string) {
    try {
      const payload = this.jwtService.verify(token);
      // 确保token包含必要字段
      if (payload.id && payload.userAccount) {
        return payload;
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  // 如果需要生成token（测试用）
  async generateToken(user: any) {
    const payload = {
      userAccount: user.userAccount,
      authority: user.authority || 0,
      id: user.id,
      userName: user.userName,
      pay_authority: user.pay_authority || 0,
      invitation_code: user.invitation_code || null,
    };

    return {
      access_token: this.jwtService.sign(payload),
      user: payload,
    };
  }
}