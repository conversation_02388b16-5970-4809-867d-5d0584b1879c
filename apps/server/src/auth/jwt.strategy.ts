import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

export interface JwtPayload {
  userAccount: string;
  authority: string;
  id: string;
  userName: string;
  pay_authority: string;
  invitation_code: string;
  exp: number;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    configService: ConfigService,
  ) {
    super({
      // 自定义token提取器，直接从Authorization头获取token，不需要Bearer前缀
      jwtFromRequest: (request: Request) => {
        const authHeader = request.headers.authorization;
        if (authHeader) {
          // 如果有Bearer前缀，去掉它；如果没有，直接返回
          return authHeader.startsWith('Bearer ')
            ? authHeader.substring(7)
            : authHeader;
        }
        return null;
      },
      ignoreExpiration: false,
      secretOrKey: configService.get('JWT_SECRET'),
    });
  }

  async validate(payload: JwtPayload) {
    // 简化验证：只检查token格式，不查询数据库
    // 因为您的线上系统已经处理了用户认证
    if (!payload.id || !payload.userAccount) {
      throw new UnauthorizedException('Token格式无效');
    }

    // 直接返回token中的用户信息
    return {
      id: parseInt(payload.id),
      userAccount: payload.userAccount,
      userName: payload.userName,
      authority: parseInt(payload.authority),
      pay_authority: parseInt(payload.pay_authority || '0'),
      invitation_code: payload.invitation_code,
    };
  }
}