import { Controller, Post, UseGuards, Request, Get } from '@nestjs/common';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';

@Controller('api/auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  getProfile(@Request() req: any) {
    return {
      code: 1,
      msg: 'success',
      data: req.user
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout')
  logout() {
    return {
      code: 1,
      msg: '退出登录成功',
      data: null
    };
  }
}