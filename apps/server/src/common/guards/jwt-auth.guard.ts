import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Observable } from 'rxjs';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    if (err || !user) {
      // 返回标准的NOT_LOGIN响应格式
      const response = context.switchToHttp().getResponse();
      response.status(200).json({
        code: 0,
        msg: 'NOT_LOGIN'
      });
      return;
    }
    return user;
  }
}