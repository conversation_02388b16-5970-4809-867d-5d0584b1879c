import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { AppsService } from './apps.service';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { AdminGuard } from '../common/guards/admin.guard';
import { CreateAppDto, UpdateAppDto } from './dto/app.dto';

@Controller('api/app')
@UseGuards(JwtAuthGuard)
export class AppsController {
  constructor(private readonly appsService: AppsService) {}

  @Get()
  @UseGuards(AdminGuard) // 只有管理员才能获取应用列表
  async findAll(@Query() query: any) {
    return this.appsService.findAll(query);
  }

  @Get(':identifier')
  async findOne(@Param('identifier') identifier: string) {
    return this.appsService.findBySlugOrId(identifier);
  }

  @Get(':identifier/pages')
  async getAppPages(@Param('identifier') identifier: string) {
    // 获取应用的所有页面配置
    const app = await this.appsService.findBySlugOrId(identifier);
    const pages = app.pageConfigs || [];
    return {
      code: 1,
      msg: 'success',
      data: {
        list: pages,
        total: pages.length,
        page: 1,
        pageSize: pages.length
      }
    };
  }

  @Post()
  async create(@Body() createAppDto: CreateAppDto, @Request() req: any) {
    return this.appsService.create(createAppDto, req.user.id);
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateAppDto: UpdateAppDto,
    @Request() req: any,
  ) {
    return this.appsService.update(id, updateAppDto, req.user.id);
  }

  @Delete(':id')
  async remove(@Param('id') id: string, @Request() req: any) {
    return this.appsService.remove(id, req.user.id);
  }

  @Post(':id/publish')
  async publish(@Param('id') id: string, @Request() req: any) {
    const result = await this.appsService.publish(id, req.user.id);
    // 🔧 修复：统一返回格式，确保前端能正确解析
    return {
      code: 1,
      msg: result.message,
      data: result.data
    };
  }

  @Post(':id/unpublish')
  async unpublish(@Param('id') id: string, @Request() req: any) {
    const result = await this.appsService.unpublish(id, req.user.id);
    // 🔧 修复：统一返回格式，确保前端能正确解析
    return {
      code: 1,
      msg: result.message,
      data: result.data
    };
  }
}