import { IsString, IsOptional, IsObject, IsEnum, IsNumber } from 'class-validator';

export enum AppType {
  DEVICE = 'device',
  MALL = 'mall',
}

export class CreateAppDto {
  @IsOptional()
  @IsString()
  id?: string; // 如果不提供，将自动生成UUID

  @IsOptional()
  @IsString()
  slug?: string; // 友好URL标识，可选

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  icon?: string;

  @IsEnum(AppType)
  @IsOptional()
  appType?: AppType = AppType.DEVICE;

  @IsOptional()
  @IsString()
  defaultHomePage?: string;

  @IsOptional()
  @IsObject()
  tabBar?: any;
}

export class UpdateAppDto {
  @IsOptional()
  @IsString()
  id?: string; // 允许传递ID，但会被忽略

  @IsOptional()
  @IsString()
  slug?: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  icon?: string;

  @IsOptional()
  @IsEnum(AppType)
  appType?: AppType;

  @IsOptional()
  @IsString()
  defaultHomePage?: string;

  @IsOptional()
  @IsObject()
  tabBar?: any;

  @IsOptional()
  @IsNumber()
  published?: number;

  @IsOptional()
  createTime?: any; // 允许传递，但会被忽略

  @IsOptional()
  updateTime?: any; // 允许传递，但会被忽略
}