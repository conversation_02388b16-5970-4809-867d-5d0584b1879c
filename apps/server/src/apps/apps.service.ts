import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { App } from '../entities/app.entity';
import { CreateAppDto, UpdateAppDto } from './dto/app.dto';

@Injectable()
export class AppsService {
  constructor(
    @InjectRepository(App)
    private appRepository: Repository<App>,
  ) {}

  async findAll(query: any) {
    const { page = 1, limit = 10, appType, published } = query;
    const queryBuilder = this.appRepository.createQueryBuilder('app')
      .leftJoinAndSelect('app.pageConfigs', 'pageConfigs');

    if (appType) {
      queryBuilder.andWhere('app.appType = :appType', { appType });
    }

    if (published !== undefined) {
      queryBuilder.andWhere('app.published = :published', { published });
    }

    const [apps, total] = await queryBuilder
      .orderBy('app.createTime', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      list: apps,
      total,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string) {
    const app = await this.appRepository.findOne({
      where: { id },
      relations: ['pageConfigs'],
    });

    if (!app) {
      throw new NotFoundException('应用不存在');
    }

    return app;
  }

  async findBySlugOrId(identifier: string) {
    // 先尝试通过slug查找，如果找不到再通过ID查找
    let app = await this.appRepository.findOne({
      where: { slug: identifier },
      relations: ['pageConfigs'],
    });

    if (!app) {
      app = await this.appRepository.findOne({
        where: { id: identifier },
        relations: ['pageConfigs'],
      });
    }

    if (!app) {
      throw new NotFoundException('应用不存在');
    }

    return app;
  }

  async create(createAppDto: CreateAppDto, creatorId: number) {
    // 如果没有提供ID，自动生成UUID
    const id = createAppDto.id || uuidv4();

    // 检查slug是否已存在（如果提供了slug）
    if (createAppDto.slug) {
      const existingApp = await this.appRepository.findOne({
        where: { slug: createAppDto.slug }
      });
      if (existingApp) {
        throw new ForbiddenException('友好URL标识已存在');
      }
    }

    const app = this.appRepository.create({
      ...createAppDto,
      id,
      creator_id: creatorId,
    });

    return this.appRepository.save(app);
  }

  async update(id: string, updateAppDto: UpdateAppDto, userId: number) {
    const app = await this.findOne(id);

    // 检查权限：只有创建者可以修改
    if (app.creator_id !== userId) {
      throw new ForbiddenException('只有应用创建者可以修改应用');
    }

    // 🔧 修复：过滤掉不应该更新的属性
    const { id: _, createTime: __, updateTime: ___, ...updateData } = updateAppDto;

    Object.assign(app, updateData);
    return this.appRepository.save(app);
  }

  async remove(id: string, userId: number) {
    const app = await this.findOne(id);

    // 检查权限：只有创建者可以删除
    if (app.creator_id !== userId) {
      throw new ForbiddenException('只有应用创建者可以删除应用');
    }

    await this.appRepository.remove(app);
    return { message: '应用删除成功' };
  }

  async publish(id: string, userId: number) {
    const app = await this.findOne(id);

    if (app.creator_id !== userId) {
      throw new ForbiddenException('只有应用创建者可以发布应用');
    }

    app.published = 1;
    app.publishTime = new Date();
    const updatedApp = await this.appRepository.save(app);

    // 🔧 修复：返回更新后的应用数据，确保前端状态同步
    return {
      message: '应用发布成功',
      data: updatedApp
    };
  }

  async unpublish(id: string, userId: number) {
    const app = await this.findOne(id);

    if (app.creator_id !== userId) {
      throw new ForbiddenException('只有应用创建者可以取消发布应用');
    }

    app.published = 0;
    app.publishTime = null;
    const updatedApp = await this.appRepository.save(app);

    // 🔧 修复：返回更新后的应用数据，确保前端状态同步
    return {
      message: '应用取消发布成功',
      data: updatedApp
    };
  }
}