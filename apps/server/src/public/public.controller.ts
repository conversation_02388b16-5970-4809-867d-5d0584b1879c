import {
  Controller,
  Get,
  Param,
  Query,
  NotFoundException,
} from '@nestjs/common';
import { AppsService } from '../apps/apps.service';
import { PagesService } from '../pages/pages.service';

@Controller('api/public')
export class PublicController {
  constructor(
    private readonly appsService: AppsService,
    private readonly pagesService: PagesService,
  ) {}

  // 获取已发布的应用信息（H5端使用）
  @Get('app/:identifier')
  async getPublishedApp(@Param('identifier') identifier: string) {
    try {
      const app = await this.appsService.findBySlugOrId(identifier);

      // 只返回已发布的应用
      if (!app.published) {
        throw new NotFoundException('应用存在但未发布，请联系管理员发布应用');
      }

      return app;
    } catch (error) {
      // 如果是我们主动抛出的NotFoundException，直接重新抛出
      if (error instanceof NotFoundException) {
        throw error;
      }

      // 其他错误（如数据库查询失败、应用不存在等）
      throw new NotFoundException('应用不存在，请检查应用ID是否正确');
    }
  }

  // 获取已发布应用的页面配置（H5端使用）
  @Get('app/:identifier/pages')
  async getPublishedAppPages(@Param('identifier') identifier: string) {
    try {
      const app = await this.appsService.findBySlugOrId(identifier);

      // 只返回已发布的应用
      if (!app.published) {
        throw new NotFoundException('应用存在但未发布，请联系管理员发布应用');
      }

      // 只返回已发布的页面配置
      const publishedPages = app.pageConfigs?.filter(page => page.published === 1) || [];

      return publishedPages;
    } catch (error) {
      // 如果是我们主动抛出的NotFoundException，直接重新抛出
      if (error instanceof NotFoundException) {
        throw error;
      }

      // 其他错误（如数据库查询失败、应用不存在等）
      throw new NotFoundException('应用不存在，请检查应用ID是否正确');
    }
  }

  // 获取已发布的页面配置（H5端使用）
  @Get('page/:identifier')
  async getPublishedPage(@Param('identifier') identifier: string) {
    try {
      const page = await this.pagesService.findBySlugOrId(identifier);

      // 🔧 优化：返回更详细的错误信息，便于前端处理
      if (page.published !== 1) {
        const errorResponse = {
          statusCode: 404,
          message: '页面存在但未发布，请联系管理员发布页面',
          error: 'Page Not Published',
          errorType: 'page-not-published',
          pageInfo: {
            id: page.id,
            name: page.name || page.title,
            appId: page.app.id,
            appName: page.app.name
          }
        };

        // 🔧 使用HttpException来返回自定义错误响应
        throw new NotFoundException(errorResponse);
      }

      // 检查关联的应用是否已发布
      if (!page.app.published) {
        const errorResponse = {
          statusCode: 404,
          message: '页面所属应用未发布，请联系管理员发布应用',
          error: 'App Not Published',
          errorType: 'app-not-published',
          appInfo: {
            id: page.app.id,
            name: page.app.name
          }
        };

        throw new NotFoundException(errorResponse);
      }

      return page;
    } catch (error) {
      // 如果是我们主动抛出的NotFoundException，直接重新抛出
      if (error instanceof NotFoundException) {
        throw error;
      }

      // 其他错误（如数据库查询失败、页面不存在等）
      const errorResponse = {
        statusCode: 404,
        message: '页面不存在，请检查页面ID是否正确',
        error: 'Page Not Found',
        errorType: 'page-not-found'
      };

      throw new NotFoundException(errorResponse);
    }
  }
}
