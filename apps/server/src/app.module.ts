import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheModule } from '@nestjs/cache-manager';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { APP_INTERCEPTOR } from '@nestjs/core';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { getDatabaseConfig } from './config/database.config';
import { ResponseInterceptor } from './common/interceptors/response.interceptor';

// 实体导入
import { App } from './entities/app.entity';
import { PageConfig } from './entities/page-config.entity';

// 模块导入
import { AuthModule } from './auth/auth.module';
import { AppsModule } from './apps/apps.module';
import { PagesModule } from './pages/pages.module';
import { UploadModule } from './upload/upload.module';
import { PublicModule } from './public/public.module';
// import { SiteConfigModule } from './site-config/site-config.module'; // 注释掉，使用现有系统的站点配置

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: getDatabaseConfig,
      inject: [ConfigService],
    }),

    // 缓存模块（使用内存缓存，生产环境可配置Redis）
    CacheModule.register({
      ttl: 300, // 5分钟默认过期时间
      max: 100, // 最大缓存项数
      isGlobal: true,
    }),

    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN', '7d'),
        },
      }),
      inject: [ConfigService],
      global: true,
    }),

    // Passport模块
    PassportModule,

    // 实体模块
    TypeOrmModule.forFeature([App, PageConfig]),

    // 业务模块
    AuthModule,
    AppsModule,
    PagesModule,
    UploadModule,
    PublicModule, // 公开API模块，不需要认证
    // SiteConfigModule, // 注释掉，使用现有系统的站点配置
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
  ],
})
export class AppModule {}
