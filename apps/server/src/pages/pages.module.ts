import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PagesController } from './pages.controller';
import { PagesService } from './pages.service';
import { PageConfig } from '../entities/page-config.entity';
import { App } from '../entities/app.entity';

@Module({
  imports: [TypeOrmModule.forFeature([PageConfig, App])],
  controllers: [PagesController],
  providers: [PagesService],
  exports: [PagesService],
})
export class PagesModule {}