import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { PagesService } from './pages.service';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { CreatePageConfigDto, UpdatePageConfigDto } from './dto/page-config.dto';

@Controller('api/page-config')
@UseGuards(JwtAuthGuard)
export class PagesController {
  constructor(private readonly pagesService: PagesService) {}

  @Get()
  async findAll(@Query() query: any) {
    return this.pagesService.findAll(query);
  }

  @Get(':identifier')
  async findOne(@Param('identifier') identifier: string) {
    return this.pagesService.findBySlugOrId(identifier);
  }

  @Get('app/:appId')
  async findByApp(@Param('appId') appId: string, @Query() query: any) {
    return this.pagesService.findByApp(appId, query);
  }

  @Post()
  async create(@Body() createPageConfigDto: CreatePageConfigDto, @Request() req: any) {
    try {
      console.log('📝 创建页面配置请求:', JSON.stringify(createPageConfigDto, null, 2));
      return this.pagesService.create(createPageConfigDto, req.user.id);
    } catch (error) {
      console.error('❌ 创建页面配置失败:', error);
      throw error;
    }
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updatePageConfigDto: UpdatePageConfigDto,
    @Request() req: any,
  ) {
    try {
      console.log('📝 更新页面配置请求:', id, JSON.stringify(updatePageConfigDto, null, 2));
      return this.pagesService.update(id, updatePageConfigDto, req.user.id);
    } catch (error) {
      console.error('❌ 更新页面配置失败:', error);
      throw error;
    }
  }

  @Delete(':id')
  async remove(@Param('id') id: string, @Request() req: any) {
    return this.pagesService.remove(id, req.user.id);
  }

  @Post(':id/publish')
  async publish(@Param('id') id: string, @Request() req: any) {
    return this.pagesService.publish(id, req.user.id);
  }

  // 🔧 新增：取消发布页面配置
  @Post(':id/unpublish')
  async unpublish(@Param('id') id: string, @Request() req: any) {
    return this.pagesService.unpublish(id, req.user.id);
  }
}