import { IsString, IsOptional, IsObject, IsNumber, IsArray } from 'class-validator';

export class CreatePageConfigDto {
  @IsOptional()
  @IsString()
  id?: string; // 如果不提供，将自动生成UUID

  @IsString()
  appId: string;

  @IsOptional()
  @IsString()
  slug?: string; // 友好URL标识，可选

  @IsString()
  name: string;

  @IsString()
  path: string;

  @IsString()
  title: string;

  @IsOptional()
  @IsObject()
  layout?: any;

  @IsOptional()
  @IsObject()
  style?: any;

  @IsArray()
  components: any[];

  @IsOptional()
  @IsObject()
  dataSource?: any;

  @IsOptional()
  @IsObject()
  events?: any;

  @IsOptional()
  @IsNumber()
  published?: number;

  @IsOptional()
  publishTime?: any; // 允许传递，但会被忽略

  @IsOptional()
  createTime?: any; // 允许传递，但会被忽略

  @IsOptional()
  updateTime?: any; // 允许传递，但会被忽略

  @IsOptional()
  app?: any; // 允许传递，但会被忽略
}

export class UpdatePageConfigDto {
  @IsOptional()
  @IsString()
  id?: string; // 允许传递ID，但会被忽略

  @IsOptional()
  @IsString()
  appId?: string; // 允许传递appId，但会被忽略

  @IsOptional()
  @IsString()
  slug?: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  path?: string;

  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsObject()
  layout?: any;

  @IsOptional()
  @IsObject()
  style?: any;

  @IsOptional()
  @IsArray()
  components?: any[];

  @IsOptional()
  @IsObject()
  dataSource?: any;

  @IsOptional()
  @IsObject()
  events?: any;

  @IsOptional()
  @IsNumber()
  published?: number;

  @IsOptional()
  publishTime?: any; // 允许传递，但会被忽略

  @IsOptional()
  createTime?: any; // 允许传递，但会被忽略

  @IsOptional()
  updateTime?: any; // 允许传递，但会被忽略

  @IsOptional()
  app?: any; // 允许传递，但会被忽略
}