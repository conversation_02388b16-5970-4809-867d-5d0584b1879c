import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { PageConfig } from '../entities/page-config.entity';
import { App } from '../entities/app.entity';
import { CreatePageConfigDto, UpdatePageConfigDto } from './dto/page-config.dto';

@Injectable()
export class PagesService {
  constructor(
    @InjectRepository(PageConfig)
    private pageConfigRepository: Repository<PageConfig>,
    @InjectRepository(App)
    private appRepository: Repository<App>,
  ) {}

  async findAll(query: any) {
    const { page = 1, limit = 10, app_id, status } = query;
    const queryBuilder = this.pageConfigRepository.createQueryBuilder('page')
      .leftJoinAndSelect('page.app', 'app')
      .select([
        'page.id',
        'page.page_name',
        'page.page_path',
        'page.title',
        'page.status',
        'page.sort_order',
        'page.created_at',
        'page.updated_at',
        'app.id',
        'app.name'
      ]);

    if (app_id) {
      queryBuilder.andWhere('page.app_id = :app_id', { app_id });
    }

    if (status !== undefined) {
      queryBuilder.andWhere('page.status = :status', { status });
    }

    const [pages, total] = await queryBuilder
      .orderBy('page.sort_order', 'ASC')
      .addOrderBy('page.created_at', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      pages,
      total,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string) {
    const pageConfig = await this.pageConfigRepository.findOne({
      where: { id },
      relations: ['app'],
    });

    if (!pageConfig) {
      throw new NotFoundException('页面配置不存在');
    }

    return pageConfig;
  }

  async findBySlugOrId(identifier: string) {
    // 先尝试通过slug查找，如果找不到再通过ID查找
    let pageConfig = await this.pageConfigRepository.findOne({
      where: { slug: identifier },
      relations: ['app'],
    });

    if (!pageConfig) {
      pageConfig = await this.pageConfigRepository.findOne({
        where: { id: identifier },
        relations: ['app'],
      });
    }

    if (!pageConfig) {
      throw new NotFoundException('页面配置不存在');
    }

    return pageConfig;
  }

  async findByApp(appId: string, query: any) {
    const { published } = query;
    const queryBuilder = this.pageConfigRepository.createQueryBuilder('page')
      .where('page.appId = :appId', { appId });

    if (published !== undefined) {
      queryBuilder.andWhere('page.published = :published', { published });
    }

    const pages = await queryBuilder
      .orderBy('page.createTime', 'DESC')
      .getMany();

    return pages;
  }

  async create(createPageConfigDto: CreatePageConfigDto, userId: number) {
    // 验证应用是否存在且用户有权限
    const app = await this.appRepository.findOne({
      where: { id: createPageConfigDto.appId },
    });

    if (!app) {
      throw new NotFoundException('应用不存在');
    }

    // 简化权限检查：只检查应用创建者ID
    if (app.creator_id !== userId) {
      throw new ForbiddenException('只有应用创建者可以添加页面配置');
    }

    // 如果没有提供ID，自动生成UUID
    const id = createPageConfigDto.id || uuidv4();

    // 检查slug是否已存在（如果提供了slug）
    if (createPageConfigDto.slug) {
      const existingPage = await this.pageConfigRepository.findOne({
        where: { slug: createPageConfigDto.slug, appId: createPageConfigDto.appId }
      });
      if (existingPage) {
        throw new ForbiddenException('友好URL标识在该应用中已存在');
      }
    }

    // 过滤掉不应该保存的属性
    const { publishTime, createTime, updateTime, app: _app, ...createData } = createPageConfigDto;

    const pageConfig = this.pageConfigRepository.create({
      ...createData,
      id,
    });
    return this.pageConfigRepository.save(pageConfig);
  }

  async update(id: string, updatePageConfigDto: UpdatePageConfigDto, userId: number) {
    const pageConfig = await this.findOne(id);

    // 检查权限：只有应用创建者可以修改
    if (pageConfig.app.creator_id !== userId) {
      throw new ForbiddenException('只有应用创建者可以修改页面配置');
    }

    // 过滤掉不应该更新的属性
    const { id: _, appId: __, publishTime: ___, createTime: ____, updateTime: _____, app: ______, ...updateData } = updatePageConfigDto;

    Object.assign(pageConfig, updateData);
    return this.pageConfigRepository.save(pageConfig);
  }

  async remove(id: string, userId: number) {
    const pageConfig = await this.findOne(id);

    // 检查权限：只有应用创建者可以删除
    if (pageConfig.app.creator_id !== userId) {
      throw new ForbiddenException('只有应用创建者可以删除页面配置');
    }

    await this.pageConfigRepository.remove(pageConfig);
    return { message: '页面配置删除成功' };
  }

  async publish(id: string, userId: number) {
    // 🔧 修复：使用findBySlugOrId支持通过slug或ID查找页面
    const pageConfig = await this.findBySlugOrId(id);

    // 检查权限：只有应用创建者可以发布页面配置
    if (pageConfig.app.creator_id !== userId) {
      throw new ForbiddenException('只有应用创建者可以发布页面配置');
    }

    // 设置页面为已发布状态
    pageConfig.published = 1;
    pageConfig.publishTime = new Date();

    const updatedPageConfig = await this.pageConfigRepository.save(pageConfig);
    return {
      message: '页面配置发布成功',
      data: updatedPageConfig
    };
  }

  // 🔧 新增：取消发布页面配置
  async unpublish(id: string, userId: number) {
    // 使用findBySlugOrId支持通过slug或ID查找页面
    const pageConfig = await this.findBySlugOrId(id);

    // 检查权限：只有应用创建者可以取消发布页面配置
    if (pageConfig.app.creator_id !== userId) {
      throw new ForbiddenException('只有应用创建者可以取消发布页面配置');
    }

    // 设置页面为未发布状态
    pageConfig.published = 0;
    pageConfig.publishTime = null;

    const updatedPageConfig = await this.pageConfigRepository.save(pageConfig);
    return {
      message: '页面配置取消发布成功',
      data: updatedPageConfig
    };
  }
}