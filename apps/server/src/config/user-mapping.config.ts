/**
 * 用户表字段映射配置
 * 根据您现有的用户表结构调整这些映射
 */
export const USER_FIELD_MAPPING = {
  // 如果您的用户表字段名不同，请在这里修改
  id: 'id',                           // 用户ID字段
  userAccount: 'userAccount',         // 用户账号字段，可能是 'username', 'account', 'email' 等
  userName: 'userName',               // 用户名字段，可能是 'name', 'nickname', 'display_name' 等
  password: 'password',               // 密码字段
  authority: 'authority',             // 权限字段，可能是 'role', 'level', 'permission' 等
  pay_authority: 'pay_authority',     // 支付权限字段，如果没有可以设为 null
  invitation_code: 'invitation_code', // 邀请码字段，如果没有可以设为 null
  status: 'status',                   // 状态字段，可能是 'active', 'enabled' 等
  created_at: 'created_at',           // 创建时间字段
  updated_at: 'updated_at',           // 更新时间字段
};

/**
 * 权限值映射
 * 根据您现有系统的权限值调整
 */
export const AUTHORITY_MAPPING = {
  ADMIN: 1,        // 管理员权限值，可能是 'admin', 'super', 1, 'root' 等
  USER: 0,         // 普通用户权限值，可能是 'user', 'member', 0, 'normal' 等
};

/**
 * 状态值映射
 * 根据您现有系统的状态值调整
 */
export const STATUS_MAPPING = {
  ACTIVE: 1,       // 激活状态值，可能是 'active', 'enabled', 1, true 等
  INACTIVE: 0,     // 非激活状态值，可能是 'inactive', 'disabled', 0, false 等
};