-- 低代码平台数据库初始化脚本
-- 注意：此脚本只创建低代码平台特有的表，不会影响现有的 users 和 site_configs 表
-- 请在您现有的数据库中执行此脚本

-- 如果需要创建独立数据库，取消下面的注释
-- CREATE DATABASE IF NOT EXISTS lowcode_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE lowcode_platform;

-- 确保使用您现有的数据库
-- USE your_existing_database;

-- 创建低代码应用表
CREATE TABLE IF NOT EXISTS lowcode_apps (
  id VARCHAR(36) PRIMARY KEY COMMENT '应用ID（UUID格式）',
  slug VARCHAR(50) UNIQUE NULL COMMENT '友好URL标识（可选，如device-manager）',
  name VARCHAR(100) NOT NULL COMMENT '应用名称',
  description VARCHAR(500) COMMENT '应用描述',
  icon VARCHAR(200) COMMENT '应用图标',
  app_type VARCHAR(50) DEFAULT 'device' COMMENT '应用类型：device-设备端，mall-商城端',
  default_home_page VARCHAR(100) COMMENT '默认首页',
  tab_bar JSON COMMENT 'TabBar配置JSON',
  published TINYINT DEFAULT 0 COMMENT '是否发布：0-未发布，1-已发布',
  publish_time TIMESTAMP NULL COMMENT '发布时间',
  creator_id INT NOT NULL COMMENT '创建者ID（关联现有用户表）',
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_creator_id (creator_id),
  INDEX idx_app_type (app_type),
  INDEX idx_published (published),
  INDEX idx_slug (slug)
);

-- 创建低代码页面配置表
CREATE TABLE IF NOT EXISTS lowcode_page_configs (
  id VARCHAR(36) PRIMARY KEY COMMENT '页面ID（UUID格式）',
  app_id VARCHAR(36) NOT NULL COMMENT '应用ID',
  slug VARCHAR(50) NULL COMMENT '友好URL标识（可选，如home）',
  name VARCHAR(100) NOT NULL COMMENT '页面名称',
  path VARCHAR(200) NOT NULL COMMENT '页面路径',
  title VARCHAR(100) NOT NULL COMMENT '页面标题',
  layout JSON COMMENT '页面布局配置JSON',
  style JSON COMMENT '页面样式配置JSON',
  components JSON NOT NULL COMMENT '页面组件配置JSON',
  data_source JSON COMMENT '数据源配置JSON',
  events JSON COMMENT '页面事件配置JSON',
  published TINYINT DEFAULT 0 COMMENT '是否发布：0-未发布，1-已发布',
  publish_time TIMESTAMP NULL COMMENT '发布时间',
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (app_id) REFERENCES lowcode_apps(id) ON DELETE CASCADE,
  INDEX idx_app_id (app_id),
  INDEX idx_published (published),
  INDEX idx_path (path),
  INDEX idx_slug (slug)
);

-- 插入示例应用数据
INSERT INTO lowcode_apps (id, slug, name, description, icon, app_type, default_home_page, tab_bar, published, publish_time, creator_id) VALUES
('550e8400-e29b-41d4-a716-************', 'device-manager', '设备管理应用', '用于管理设备信息、套餐和余额的移动应用', 'mdi:router-wireless', 'device', 'home',
'{"enabled": true, "type": "default", "tabs": [{"id": "home-tab", "name": "home", "label": "首页", "icon": "solar:home-bold|#971212", "path": "/home", "pageId": "home", "active": true}, {"id": "package-tab", "name": "package", "label": "套餐", "icon": "mdi:package-variant", "path": "/device/PackageList", "pageId": "package-list"}, {"id": "balance-tab", "name": "balance", "label": "余额", "icon": "mdi:wallet", "path": "/device/BalanceList", "pageId": "balance-list"}, {"id": "profile-tab", "name": "profile", "label": "我的", "icon": "mdi:account", "path": "/profile", "pageId": "profile"}], "style": {"backgroundColor": "#ffffff", "activeColor": "#1890ff", "inactiveColor": "#666666", "height": "50px", "iconTextGap": "2", "borderRadius": "0", "showText": true}}',
1, NOW(), 1),

('550e8400-e29b-41d4-a716-************', 'demo-app', '演示应用', '用于演示低代码平台功能的示例应用', 'mdi:application', 'device', 'demo-home',
'{"enabled": false, "tabs": []}',
0, NULL, 1);

-- 插入示例页面配置数据
INSERT INTO lowcode_page_configs (id, app_id, slug, name, path, title, layout, style, components, published, create_time) VALUES
('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'home', '首页', '/home', '设备管理',
'{"type": "flex", "direction": "column", "padding": 16, "gap": 16}',
'{"backgroundColor": "#f0f8ff", "backgroundImage": "url(\\"https://img.freepik.com/free-vector/gradient-network-connection-background_23-**********.jpg\\")", "backgroundSize": "cover", "backgroundPosition": "center"}',
'[{"id": "home_basic", "type": "HomeBasic", "props": {"title": "设备管理", "subtitle": "智能设备一站式管理平台"}}]',
1, NOW()),

('650e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'package-list', '套餐列表', '/device/PackageList', '套餐管理',
'{"type": "flex", "direction": "column", "padding": 16}',
'{"backgroundColor": "#ffffff"}',
'[{"id": "package_list", "type": "PackageList", "props": {"config": {"showValidity": true, "showDescription": true, "showPopularTag": true}}}]',
1, NOW()),

('650e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-************', 'balance-list', '余额列表', '/device/BalanceList', '余额管理',
'{"type": "flex", "direction": "column", "padding": 16}',
'{"backgroundColor": "#ffffff"}',
'[{"id": "balance_list", "type": "BalanceList", "props": {"config": {"showChart": true, "showHistory": true}}}]',
1, NOW());