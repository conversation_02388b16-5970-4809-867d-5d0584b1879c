{"name": "@lowcode/server", "version": "0.1.0", "description": "Low-code platform server with NestJS", "author": "", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "type-check": "tsc --noEmit", "pkg:build": "npm run build && pkg dist/main.js --targets node18-linux-x64,node18-win-x64,node18-macos-arm64 --out-path ./binary", "pkg:linux": "npm run build && pkg dist/main.js --targets node18-linux-x64 --out-path ./binary", "pkg:win": "npm run build && pkg dist/main.js --targets node18-win-x64 --out-path ./binary", "pkg:mac": "npm run build && pkg dist/main.js --targets node18-macos-arm64 --out-path ./binary", "pkg:mac-intel": "npm run build && pkg dist/main.js --targets node18-macos-x64 --out-path ./binary", "nexe:build": "npm run build && nexe dist/main.js --build --output ./binary/lowcode-server", "nexe:mac": "npm run build && nexe dist/main.js --target mac-arm64-18.20.4 --output ./binary/lowcode-server-mac", "nexe:mac-intel": "npm run build && nexe dist/main.js --target mac-x64-18.20.4 --output ./binary/lowcode-server-mac-intel", "nexe:linux": "npm run build && nexe dist/main.js --target linux-x64-18.20.4 --output ./binary/lowcode-server-linux", "nexe:win": "npm run build && nexe dist/main.js --target win-x64-18.20.4 --output ./binary/lowcode-server-win.exe", "build:standalone": "node scripts/build-standalone.js", "docker:build": "docker build -t lowcode-server .", "docker:run": "docker-compose up -d", "docker:stop": "docker-compose down"}, "dependencies": {"@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "bcrypt": "^6.0.0", "cache-manager": "^7.0.1", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compression": "^1.7.4", "helmet": "^7.1.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "redis": "^5.6.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.25", "uuid": "^9.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^6.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.11", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.8", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0", "pkg": "^5.8.1"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "pkg": {"scripts": ["dist/**/*.js"], "assets": ["node_modules/@nestjs/microservices/package.json", "node_modules/@nestjs/websockets/package.json"], "targets": ["node18-linux-x64", "node18-win-x64", "node18-macos-x64"], "outputPath": "./binary"}}