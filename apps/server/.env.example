# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=
DB_DATABASE=lowcode_platform

# 数据库同步设置（生产环境建议设为false）
DB_SYNCHRONIZE=false

# JWT配置
JWT_SECRET=django-insecure-_=0rpi4cfhzdus5ih*4^8p%j)zdg%y2i^_d6_tbe(z$tfk!yp%
JWT_EXPIRES_IN=7d

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 应用配置
NODE_ENV=production
PORT=3002

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# 基础URL配置
BASE_URL=http://localhost:3002