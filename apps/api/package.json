{"name": "@lowcode/api", "version": "0.1.0", "description": "Low-code platform API service", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "type-check": "tsc --noEmit"}, "dependencies": {"express": "^4.19.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.8", "typescript": "^5.4.0", "tsx": "^4.7.2"}}