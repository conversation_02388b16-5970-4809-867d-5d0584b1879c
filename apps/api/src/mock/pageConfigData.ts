// 页面配置Mock数据

export const mockPageConfigs: Record<string, any> = {
  'home': {
    id: 'home',
    appId: 'device-manager',
    name: '首页',
    path: '/home',
    title: '设备管理',
    layout: {
      type: 'flex',
      direction: 'column',
      padding: 16,
      gap: 16
    },
    style: {
      backgroundColor: '#f0f8ff',
      backgroundImage: 'url("https://img.freepik.com/free-vector/gradient-network-connection-background_23-2148865392.jpg")',
      backgroundSize: 'cover',
      backgroundPosition: 'center'
    },
    components: [
      {
        id: 'package_list_home',
        type: 'PackageList',
        props: {
          config: {
            showValidity: true,
            showDescription: true,
            showPopularTag: true,
            popularText: '热门',
            flowLabel: '流量',
            validityLabel: '有效期'
          }
        },
        dataSource: {
          type: 'api',
          source: '/api/packages',
          cache: true,
          refresh: 30000
        },
        events: {
          packageSelect: {
            type: 'navigate',
            target: '/package-payment'
          },
          select: {
            type: 'navigate',
            target: '/package-payment'
          }
        },
        visible: true,
        editable: true
      },
      {
        id: 'payment_box_demo',
        type: 'PaymentBox',
        props: {
          Payment: {
            type: 'wechat',
            name: '微信支付'
          },
          Active: false,
          Right: 'Select'
        },
        events: {
          click: {
            type: 'custom',
            handler: 'selectPayment'
          }
        },
        visible: true,
        editable: true
      }
    ],
    editable: true,
    published: true,
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2024-01-15'),
    publishTime: new Date('2024-01-15')
  },

  'package-list': {
    id: 'package-list',
    appId: 'device-manager',
    name: '套餐列表',
    path: '/package-list',
    title: '订购套餐',
    layout: {
      type: 'flex',
      direction: 'column',
      padding: 16,
      gap: 16
    },
    components: [
      {
        id: 'current_package_info',
        type: 'CurrentPackageInfo',
        props: {},
        dataSource: {
          type: 'api',
          source: '/api/device/details'
        },
        visible: true,
        editable: true
      },
      {
        id: 'package_list',
        type: 'PackageList',
        props: {
          config: {
            allowMultiSelect: false,
            showPopularTag: true,
            showDescription: true
          }
        },
        dataSource: {
          type: 'api',
          source: '/api/package/list',
          cache: true
        },
        events: {
          packageSelect: {
            type: 'navigate',
            target: '/package-payment',
            params: {
              id: '${eventData}'
            }
          }
        },
        visible: true,
        editable: true
      }
    ],
    editable: true,
    published: true,
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2024-01-15'),
    publishTime: new Date('2024-01-15')
  },

  'balance-list': {
    id: 'balance-list',
    appId: 'device-manager',
    name: '余额管理',
    path: '/balance-list',
    title: '账户余额',
    layout: {
      type: 'flex',
      direction: 'column',
      padding: 0
    },
    components: [
      {
        id: 'balance_display',
        type: 'BalanceDisplay',
        props: {
          config: {
            showBalance: true,
            showStats: true,
            showRechargeOptions: true,
            showTransactions: true,
            showActions: true
          }
        },
        dataSource: {
          type: 'api',
          source: '/api/balance/details',
          cache: true,
          refresh: 30000
        },
        events: {
          recharge: {
            type: 'navigate',
            target: '/balance-payment',
            params: {
              id: '${eventData}'
            }
          },
          viewAllTransactions: {
            type: 'navigate',
            target: '/balance-details'
          }
        },
        visible: true,
        editable: true
      }
    ],
    editable: true,
    published: true,
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2024-01-15'),
    publishTime: new Date('2024-01-15')
  },

  'balance-details': {
    id: 'balance-details',
    appId: 'device-manager',
    name: '余额明细',
    path: '/balance-details',
    title: '余额明细',
    layout: {
      type: 'flex',
      direction: 'column',
      padding: 16
    },
    components: [
      {
        id: 'balance_summary',
        type: 'BalanceSummary',
        props: {},
        dataSource: {
          type: 'api',
          source: '/api/balance/month-data'
        },
        visible: true,
        editable: true
      },
      {
        id: 'transaction_list',
        type: 'TransactionList',
        props: {
          config: {
            pageSize: 20,
            showPagination: true
          }
        },
        dataSource: {
          type: 'api',
          source: '/api/balance/details',
          params: {
            page: 1,
            pageSize: 20
          }
        },
        visible: true,
        editable: true
      }
    ],
    editable: true,
    published: false,
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2024-01-15')
  },

  'profile': {
    id: 'profile',
    appId: 'device-manager',
    name: '个人中心',
    path: '/profile',
    title: '我的',
    layout: {
      type: 'flex',
      direction: 'column',
      padding: 16,
      gap: 16
    },
    components: [
      {
        id: 'device-info-1',
        type: 'HomeBasic',
        props: {
          config: {
            networkTitle: '网络连接',
            renewText: '立即续费',
            balanceLabel: '账户余额',
            rechargeText: '充值'
          }
        },
        dataSource: {
          type: 'api',
          source: '/api/device/details'
        },
        events: {
          more: {
            type: 'navigate',
            target: '/EditDevice'
          },
          renew: {
            type: 'navigate',
            target: '/PackageList'
          },
          recharge: {
            type: 'navigate',
            target: '/BalanceList'
          }
        },
        visible: true,
        editable: true
      }
    ],
    editable: true,
    published: true,
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2024-01-15'),
    publishTime: new Date('2024-01-15')
  },

  // 默认设备应用首页配置
  'default-device-app_home': {
    id: 'default-device-app_home',
    appId: 'default-device-app',
    name: '默认设备应用首页',
    path: '/home',
    title: '设备管理',
    layout: {
      type: 'flex',
      direction: 'column',
      padding: 16,
      gap: 16
    },
    style: {
      backgroundColor: '#f0f8ff',
      minHeight: '100vh'
    },
    components: [
      {
        id: 'home_basic_default',
        type: 'HomeBasic',
        props: {
          config: {
            networkTitle: '网络连接',
            renewText: '立即续费',
            balanceLabel: '账户余额',
            rechargeText: '充值'
          }
        },
        dataSource: {
          type: 'api',
          source: '/api/device/details',
          cache: true,
          refresh: 30000
        },
        events: {
          more: {
            type: 'navigate',
            target: '/EditDevice'
          },
          renew: {
            type: 'navigate',
            target: '/PackageList'
          },
          recharge: {
            type: 'navigate',
            target: '/BalanceList'
          }
        },
        visible: true,
        editable: true
      }
    ],
    editable: true,
    published: true,
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2024-01-15'),
    publishTime: new Date('2024-01-15')
  },

  // Device-An应用首页配置
  'device-an_home': {
    id: 'device-an_home',
    appId: 'device-an',
    name: 'Device-An首页',
    path: '/home',
    title: '设备管理',
    layout: {
      type: 'flex',
      direction: 'column',
      padding: 16,
      gap: 16
    },
    style: {
      backgroundColor: '#f0f8ff',
      minHeight: '100vh'
    },
    components: [
      {
        id: 'home_basic',
        type: 'HomeBasic',
        props: {
          config: {
            networkTitle: '网络连接',
            renewText: '立即续费',
            balanceLabel: '账户余额',
            rechargeText: '充值'
          }
        },
        dataSource: {
          type: 'api',
          source: '/api/device/details',
          cache: true,
          refresh: 30000
        },
        events: {
          more: {
            type: 'navigate',
            target: '/EditDevice'
          },
          renew: {
            type: 'navigate',
            target: '/PackageList'
          },
          recharge: {
            type: 'navigate',
            target: '/BalanceList'
          }
        },
        visible: true,
        editable: true
      },
      {
        id: 'home_network',
        type: 'HomeNetWork',
        props: {
          config: {
            showSwitchButton: true,
            showRechargeButton: true
          }
        },
        dataSource: {
          type: 'api',
          source: '/api/device/details',
          cache: true,
          refresh: 30000
        },
        events: {
          'switch-network': {
            type: 'custom',
            handler: 'console.log("切换网络:", eventData)'
          },
          recharge: {
            type: 'navigate',
            target: '/PackageList'
          }
        },
        visible: true,
        editable: true
      },
      {
        id: 'home_more',
        type: 'HomeMore',
        props: {
          config: {
            menuItems: [
              { id: 'package', name: '套餐管理', icon: 'mdi:package-variant' },
              { id: 'balance', name: '余额充值', icon: 'mdi:wallet' },
              { id: 'wifi', name: 'Wi-Fi设置', icon: 'mdi:wifi' },
              { id: 'history', name: '使用记录', icon: 'mdi:history' }
            ]
          }
        },
        events: {
          package: {
            type: 'navigate',
            target: '/PackageList'
          },
          balance: {
            type: 'navigate',
            target: '/BalanceList'
          },
          wifi: {
            type: 'navigate',
            target: '/EditDevice'
          },
          history: {
            type: 'navigate',
            target: '/BalanceDetails'
          }
        },
        visible: true,
        editable: true
      }
    ],
    editable: true,
    published: true,
    createTime: new Date('2024-01-01'),
    updateTime: new Date('2024-01-15'),
    publishTime: new Date('2024-01-15')
  }
}
