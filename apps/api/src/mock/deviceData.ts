// 设备相关Mock数据

export const mockDeviceData = {
  // 设备详情
  details: {
    id: 1,
    becomedueDatetime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后到期
    packageName: '基础套餐 10GB',
    deviceNo: 'DEV001',
    imeiNo: 123456789012345,
    vTotalFlow: 10240, // 10GB in MB
    vUseFlow: 3072,    // 3GB used
    vResidueFlow: 7168, // 7GB remaining
    vDayFlow: 512,     // 512MB today
    currentCardId: 1,
    devicePackageGroupId: 1,
    deviceConfigId: 1,
    userId: 1,
    balance: 125.50,
    currentNetwork: 4, // 4G
    powerOnStatus: 1,  // 开机
    presence: 1,       // 在线
    status: 3,         // 已激活
    nameStatus: 2,     // 已实名
    restartTime: '2024-01-15 10:30:00',
    reseTime: '2024-01-15 10:35:00',
    recentHeartbeat: '2024-01-15 15:45:00',
    updateTime: '2024-01-15 15:45:00',
    activationDatetime: '2024-01-01 09:00:00',
    currentBatteryLevel: 85,
    currentSignal: 4,  // 信号强度
    cardSlot1Id: 1,
    cardSlot2Id: 2,
    cardSlot3Id: null,
    cardSlot4Id: null,
    prestoreId: 1,
    phone: '13800138000',
    wifiName: 'MyDevice_WiFi',
    wifiPwd: 'password123',
    hideStatus: 0,
    wifiLike: 1,
    wifi5gName: 'MyDevice_5G',
    wifi5gPwd: 'password123',
    wifi5gLike: 1,
    hideStatus5g: 0
  },

  // 设备卡片信息
  cards: [
    {
      id: 1,
      iccid: '89860000000000000001',
      msisdn: '13800138001',
      network: 4, // 4G
      operator: 1, // 中国移动
      cardType: 1,
      supports5g: 0
    },
    {
      id: 2,
      iccid: '89860000000000000002',
      msisdn: '13800138002',
      network: 5, // 5G
      operator: 2, // 中国联通
      cardType: 1,
      supports5g: 1
    }
  ],

  // 实名认证卡片
  realNameCards: [
    {
      number: 1,
      cardName: 2, // 已实名
      iccid: '89860000000000000001',
      msisdn: '13800138001',
      network: 4,
      operator: 1
    },
    {
      number: 2,
      cardName: 2, // 已实名
      iccid: '89860000000000000002',
      msisdn: '13800138002',
      network: 5,
      operator: 2
    }
  ]
}
