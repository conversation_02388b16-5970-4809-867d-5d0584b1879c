// 应用配置接口
export interface AppConfig {
  id: string
  name: string
  description: string
  icon: string
  defaultHomePage: string
  appType?: string  // 应用类型，可选字段以保持向后兼容
  tabBar?: {
    enabled: boolean
    type?: string  // TabBar类型：default, rounded, minimal, floating
    tabs: TabConfig[]
    style?: {
      backgroundColor?: string
      activeColor?: string
      inactiveColor?: string
      height?: string
      iconTextGap?: string  // 图标文字间距
      borderRadius?: string  // 圆角大小
      showText?: boolean  // 是否显示文字
    }
  }
  createTime: Date | string
  updateTime: Date | string
  published: boolean
  publishTime?: Date | string
}

export interface TabConfig {
  id: string
  name: string
  label: string
  icon: string
  path: string
  pageId: string
  active?: boolean
}

// 模拟应用数据
export const mockApps: Record<string, AppConfig> = {
  'device-manager': {
    id: 'device-manager',
    name: '设备管理应用',
    description: '用于管理设备信息、套餐和余额的移动应用',
    icon: 'mdi:router-wireless',
    defaultHomePage: 'home',
    appType: 'device',
    tabBar: {
      enabled: true,
      type: 'default',  // 新增：TabBar类型
      tabs: [
        {
          id: 'home-tab',
          name: 'home',
          label: '首页',
          icon: 'solar:home-bold|#971212',  // 带颜色的图标
          path: '/home',
          pageId: 'home',
          order: 1,
          active: true
        },
        {
          id: 'package-tab',
          name: 'package',
          label: '套餐',
          icon: 'mdi:package-variant',
          path: '/device/PackageList',
          pageId: 'package-list',
          order: 2
        },
        {
          id: 'balance-tab',
          name: 'balance',
          label: '余额',
          icon: 'mdi:wallet',
          path: '/device/BalanceList',
          pageId: 'balance-list',
          order: 3
        },
        {
          id: 'profile-tab',
          name: 'profile',
          label: '我的',
          icon: 'mdi:account',
          path: '/profile',
          pageId: 'profile',
          order: 4
        }
      ],
      style: {
        backgroundColor: '#ffffff',
        activeColor: '#1890ff',
        inactiveColor: '#666666',
        height: '50px',
        iconTextGap: '2',  // 新增：图标文字间距
        borderRadius: '0',  // 新增：圆角大小
        showText: true  // 新增：是否显示文字
      }
    },
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: '2024-01-15T00:00:00.000Z',
    published: true,
    publishTime: '2024-01-15T00:00:00.000Z'
  },
  
  'demo-app': {
    id: 'demo-app',
    name: '演示应用',
    description: '用于演示低代码平台功能的示例应用',
    icon: 'mdi:application',
    defaultHomePage: 'demo-home',
    appType: 'device',
    tabBar: {
      enabled: false,
      tabs: []
    },
    createTime: '2024-01-01T00:00:00.000Z',
    updateTime: '2024-01-15T00:00:00.000Z',
    published: false
  }
}
