// 套餐相关Mock数据

export const mockPackageData = {
  // 套餐列表
  packages: [
    {
      id: 1,
      name: '基础套餐 10GB',
      packageTotal: 10240, // 10GB in MB
      packageIntroduce: '适合轻度使用，包含10GB全国流量，月底清零',
      packagePrice: 29,
      popular: 1, // 热门
      packageType: 1, // 基础套餐
      packageValidity: 1, // 月底清零
      validityDays: 30
    },
    {
      id: 2,
      name: '标准套餐 20GB',
      packageTotal: 20480, // 20GB in MB
      packageIntroduce: '适合日常使用，包含20GB全国流量，月底清零',
      packagePrice: 49,
      popular: 1, // 热门
      packageType: 1, // 基础套餐
      packageValidity: 1, // 月底清零
      validityDays: 30
    },
    {
      id: 3,
      name: '高级套餐 50GB',
      packageTotal: 51200, // 50GB in MB
      packageIntroduce: '适合重度使用，包含50GB全国流量，月底清零',
      packagePrice: 99,
      popular: 0,
      packageType: 1, // 基础套餐
      packageValidity: 1, // 月底清零
      validityDays: 30
    },
    {
      id: 4,
      name: '无限流量套餐',
      packageTotal: 999999, // 无限流量
      packageIntroduce: '无限流量，不限速，适合超重度使用',
      packagePrice: 199,
      popular: 0,
      packageType: 1, // 基础套餐
      packageValidity: 1, // 月底清零
      validityDays: 30
    },
    {
      id: 5,
      name: '加油包 5GB',
      packageTotal: 5120, // 5GB in MB
      packageIntroduce: '流量不够用时的补充包',
      packagePrice: 15,
      popular: 0,
      packageType: 2, // 加油包
      packageValidity: 3, // 30天有效
      validityDays: 30
    },
    {
      id: 6,
      name: '日租卡 1GB/天',
      packageTotal: 1024, // 1GB in MB
      packageIntroduce: '按天计费，每天1GB流量',
      packagePrice: 3,
      popular: 0,
      packageType: 3, // 日租卡
      packageValidity: 3, // 1天有效
      validityDays: 1
    }
  ],

  // 套餐订单
  orders: [
    {
      id: 1,
      packageId: 1,
      packageName: '基础套餐 10GB',
      packagePrice: 29,
      paymentMethod: 'balance',
      status: 'paid',
      createTime: new Date('2024-01-10'),
      payTime: new Date('2024-01-10'),
      orderNumber: 'PKG_1705123456789'
    },
    {
      id: 2,
      packageId: 5,
      packageName: '加油包 5GB',
      packagePrice: 15,
      paymentMethod: 'wechat',
      status: 'paid',
      createTime: new Date('2024-01-12'),
      payTime: new Date('2024-01-12'),
      orderNumber: 'PKG_1705234567890'
    }
  ]
}
