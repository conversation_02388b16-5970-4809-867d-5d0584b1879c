// 余额相关Mock数据

export const mockBalanceData = {
  // 当前余额
  currentBalance: 125.50,

  // 充值选项
  rechargeOptions: [
    {
      id: 1,
      prestorePrice: 50,
      prestoreGive: 0
    },
    {
      id: 2,
      prestorePrice: 100,
      prestoreGive: 10
    },
    {
      id: 3,
      prestorePrice: 200,
      prestoreGive: 30
    },
    {
      id: 4,
      prestorePrice: 500,
      prestoreGive: 100
    },
    {
      id: 5,
      prestorePrice: 1000,
      prestoreGive: 250
    }
  ],

  // 余额变动记录
  transactions: [
    {
      id: 1,
      systemOrdernumber: 'BAL_1705123456789',
      balanceBefore: 100.50,
      balanceAfter: 125.50,
      balanceAmount: 25.00,
      unit: '+',
      balanceAlterationTime: new Date('2024-01-15 14:30:00'),
      balanceRemark: '充值余额'
    },
    {
      id: 2,
      systemOrdernumber: 'PKG_1705123456788',
      balanceBefore: 129.50,
      balanceAfter: 100.50,
      balanceAmount: 29.00,
      unit: '-',
      balanceAlterationTime: new Date('2024-01-14 10:15:00'),
      balanceRemark: '购买套餐 - 基础套餐 10GB'
    },
    {
      id: 3,
      systemOrdernumber: 'BAL_1705023456789',
      balanceBefore: 29.50,
      balanceAfter: 129.50,
      balanceAmount: 100.00,
      unit: '+',
      balanceAlterationTime: new Date('2024-01-12 16:20:00'),
      balanceRemark: '充值余额'
    },
    {
      id: 4,
      systemOrdernumber: 'PKG_1704923456789',
      balanceBefore: 44.50,
      balanceAfter: 29.50,
      balanceAmount: 15.00,
      unit: '-',
      balanceAlterationTime: new Date('2024-01-10 09:45:00'),
      balanceRemark: '购买套餐 - 加油包 5GB'
    },
    {
      id: 5,
      systemOrdernumber: 'BAL_1704823456789',
      balanceBefore: 0,
      balanceAfter: 44.50,
      balanceAmount: 44.50,
      unit: '+',
      balanceAlterationTime: new Date('2024-01-08 11:30:00'),
      balanceRemark: '首次充值'
    }
  ],

  // 充值订单
  rechargeOrders: [
    {
      id: 1,
      rechargeId: 1,
      amount: 50,
      giveAmount: 0,
      paymentMethod: 'wechat',
      status: 'paid',
      createTime: new Date('2024-01-15 14:25:00'),
      payTime: new Date('2024-01-15 14:30:00'),
      orderNumber: 'BAL_1705123456789'
    },
    {
      id: 2,
      rechargeId: 2,
      amount: 100,
      giveAmount: 10,
      paymentMethod: 'alipay',
      status: 'paid',
      createTime: new Date('2024-01-12 16:15:00'),
      payTime: new Date('2024-01-12 16:20:00'),
      orderNumber: 'BAL_1705023456789'
    }
  ]
}
