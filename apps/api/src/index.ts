import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import morgan from 'morgan'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

// 路由导入
import deviceRoutes from './routes/device.js'
import packageRoutes from './routes/package.js'
import balanceRoutes from './routes/balance.js'
import pageConfigRoutes from './routes/pageConfig.js'
import uploadRoutes from './routes/upload.js'
import appRoutes from './routes/app.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const app = express()
const PORT = 3002

// 中间件
app.use(helmet())
app.use(compression())
// app.use(cors({
//   origin: ['http://localhost:3000', 'http://localhost:3001','https://5v0r8k64-3000.asse.devtunnels.ms','https://5v0r8k64-3002.asse.devtunnels.ms'],
//   credentials: true
// }))
app.use(cors({
  origin: true,         // 自动反射请求源
  credentials: true     // 允许携带 cookie
}))

app.use(morgan('combined'))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// 静态文件服务
app.use('/uploads', express.static(join(__dirname, '../uploads')))

// API路由
app.use('/api/app', appRoutes)
app.use('/api/device', deviceRoutes)
app.use('/api/package', packageRoutes)
app.use('/api/balance', balanceRoutes)
app.use('/api/page-config', pageConfigRoutes)
app.use('/api/upload', uploadRoutes)

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  })
})

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: false,
    msg: 'API endpoint not found',
    data: null
  })
})

// 错误处理
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', err)
  res.status(500).json({
    code: false,
    msg: err.message || 'Internal server error',
    data: null
  })
})

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 Low-code API server running on port ${PORT}`)
  console.log(`📖 Health check: http://localhost:${PORT}/health`)
  console.log(`🔗 API base URL: http://localhost:${PORT}/api`)
})
