import express from 'express'
import { mockApps } from '../mock/appData.js'
import { mockPageConfigs } from '../mock/pageConfigData.js'

const router = express.Router()

// 获取可用应用列表（只返回已发布的应用）
router.get('/available', (req, res) => {
  try {
    const availableApps = Object.values(mockApps)
      .filter(app => app.published) // 只返回已发布的应用
      .map(app => ({
        id: app.id,
        name: app.name,
        description: app.description,
        icon: app.icon,
        appType: app.appType,
        defaultHomePage: app.defaultHomePage
      }))

    res.json({
      code: true,
      data: availableApps,
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: [],
      msg: 'Failed to get available apps'
    })
  }
})

// 获取应用列表
router.get('/list', (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query
    const start = (Number(page) - 1) * Number(pageSize)
    const end = start + Number(pageSize)
    
    const apps = Object.values(mockApps).slice(start, end)
    
    res.json({
      code: true,
      data: {
        list: apps,
        total: Object.keys(mockApps).length,
        page: Number(page),
        pageSize: Number(pageSize)
      },
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get app list'
    })
  }
})

// 获取单个应用
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params
    const app = mockApps[id]
    
    if (!app) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'App not found'
      })
    }
    
    res.json({
      code: true,
      data: app,
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get app'
    })
  }
})

// 获取应用的页面列表
router.get('/:id/pages', (req, res) => {
  try {
    const { id } = req.params
    const { page = 1, pageSize = 10 } = req.query
    
    if (!mockApps[id]) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'App not found'
      })
    }
    
    // 筛选属于该应用的页面
    const appPages = Object.values(mockPageConfigs).filter(pageConfig => pageConfig.appId === id)
    
    const start = (Number(page) - 1) * Number(pageSize)
    const end = start + Number(pageSize)
    const pages = appPages.slice(start, end)
    
    res.json({
      code: true,
      data: {
        list: pages,
        total: appPages.length,
        page: Number(page),
        pageSize: Number(pageSize)
      },
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get app pages'
    })
  }
})

// 创建应用
router.post('/', (req, res) => {
  try {
    const app = req.body
    
    if (!app.id || !app.name) {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'App must have id and name'
      })
    }
    
    if (mockApps[app.id]) {
      return res.status(409).json({
        code: false,
        data: null,
        msg: 'App already exists'
      })
    }
    
    // 添加时间戳
    app.createTime = new Date()
    app.updateTime = new Date()
    
    mockApps[app.id] = app
    
    res.json({
      code: true,
      data: app,
      msg: 'App created successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to create app'
    })
  }
})

// 更新应用
router.put('/:id', (req, res) => {
  try {
    const { id } = req.params
    const updates = req.body
    
    if (!mockApps[id]) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'App not found'
      })
    }
    
    // 更新应用
    mockApps[id] = {
      ...mockApps[id],
      ...updates,
      updateTime: new Date()
    }
    
    res.json({
      code: true,
      data: mockApps[id],
      msg: 'App updated successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to update app'
    })
  }
})

// 删除应用
router.delete('/:id', (req, res) => {
  try {
    const { id } = req.params
    
    if (!mockApps[id]) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'App not found'
      })
    }
    
    // 删除应用相关的页面
    Object.keys(mockPageConfigs).forEach(pageId => {
      if (mockPageConfigs[pageId].appId === id) {
        delete mockPageConfigs[pageId]
      }
    })
    
    delete mockApps[id]
    
    res.json({
      code: true,
      data: null,
      msg: 'App deleted successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to delete app'
    })
  }
})

// 发布应用
router.post('/:id/publish', (req, res) => {
  try {
    const { id } = req.params
    
    if (!mockApps[id]) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'App not found'
      })
    }
    
    // 模拟发布
    mockApps[id].published = true
    mockApps[id].publishTime = new Date()
    mockApps[id].updateTime = new Date()
    
    res.json({
      code: true,
      data: mockApps[id],
      msg: 'App published successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to publish app'
    })
  }
})

export default router
