import express from 'express'
import { mockPageConfigs } from '../mock/pageConfigData.js'

const router = express.Router()

// 获取页面配置列表
router.get('/list', (req, res) => {
  try {
    const { page = 1, pageSize = 10, appId } = req.query
    const start = (Number(page) - 1) * Number(pageSize)
    const end = start + Number(pageSize)

    let configs = Object.values(mockPageConfigs)

    // 如果指定了appId，则筛选该应用的页面
    if (appId) {
      configs = configs.filter(config => config.appId === appId)
    }

    const paginatedConfigs = configs.slice(start, end)

    res.json({
      code: true,
      data: {
        list: paginatedConfigs,
        total: configs.length,
        page: Number(page),
        pageSize: Number(pageSize)
      },
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get page config list'
    })
  }
})

// 获取单个页面配置
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params
    const config = mockPageConfigs[id]

    if (!config) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'Page config not found'
      })
    }

    res.json({
      code: true,
      data: config,
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get page config'
    })
  }
})

// 创建页面配置
router.post('/', (req, res) => {
  try {
    const config = req.body
    
    if (!config.id || !config.name || !config.path) {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'Page config must have id, name and path'
      })
    }
    
    if (mockPageConfigs[config.id]) {
      return res.status(409).json({
        code: false,
        data: null,
        msg: 'Page config already exists'
      })
    }
    
    // 添加时间戳
    config.createTime = new Date()
    config.updateTime = new Date()
    
    mockPageConfigs[config.id] = config
    
    res.json({
      code: true,
      data: config,
      msg: 'Page config created successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to create page config'
    })
  }
})

// 更新页面配置
router.put('/:id', (req, res) => {
  try {
    const { id } = req.params
    const updates = req.body
    
    if (!mockPageConfigs[id]) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'Page config not found'
      })
    }
    
    // 更新配置
    mockPageConfigs[id] = {
      ...mockPageConfigs[id],
      ...updates,
      updateTime: new Date()
    }
    
    res.json({
      code: true,
      data: mockPageConfigs[id],
      msg: 'Page config updated successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to update page config'
    })
  }
})

// 删除页面配置
router.delete('/:id', (req, res) => {
  try {
    const { id } = req.params
    
    if (!mockPageConfigs[id]) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'Page config not found'
      })
    }
    
    delete mockPageConfigs[id]
    
    res.json({
      code: true,
      data: null,
      msg: 'Page config deleted successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to delete page config'
    })
  }
})

// 复制页面配置
router.post('/:id/copy', (req, res) => {
  try {
    const { id } = req.params
    const { newId, newName, newPath } = req.body
    
    if (!mockPageConfigs[id]) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'Source page config not found'
      })
    }
    
    if (!newId || !newName || !newPath) {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'New id, name and path are required'
      })
    }
    
    if (mockPageConfigs[newId]) {
      return res.status(409).json({
        code: false,
        data: null,
        msg: 'Target page config already exists'
      })
    }
    
    // 复制配置
    const newConfig = {
      ...mockPageConfigs[id],
      id: newId,
      name: newName,
      path: newPath,
      createTime: new Date(),
      updateTime: new Date()
    }
    
    mockPageConfigs[newId] = newConfig
    
    res.json({
      code: true,
      data: newConfig,
      msg: 'Page config copied successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to copy page config'
    })
  }
})

// 发布页面配置
router.post('/:id/publish', (req, res) => {
  try {
    const { id } = req.params

    if (!mockPageConfigs[id]) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'Page config not found'
      })
    }

    // 模拟发布
    mockPageConfigs[id].published = true
    mockPageConfigs[id].publishTime = new Date()
    mockPageConfigs[id].updateTime = new Date()

    res.json({
      code: true,
      data: mockPageConfigs[id],
      msg: 'Page config published successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to publish page config'
    })
  }
})

// 🔧 新增：取消发布页面配置
router.post('/:id/unpublish', (req, res) => {
  try {
    const { id } = req.params

    if (!mockPageConfigs[id]) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'Page config not found'
      })
    }

    // 模拟取消发布
    mockPageConfigs[id].published = false
    mockPageConfigs[id].publishTime = null
    mockPageConfigs[id].updateTime = new Date()

    res.json({
      code: true,
      data: mockPageConfigs[id],
      msg: 'Page config unpublished successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to unpublish page config'
    })
  }
})

export default router
