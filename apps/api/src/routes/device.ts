import express from 'express'
import { mockDeviceData } from '../mock/deviceData.js'

const router = express.Router()

// 获取设备详情
router.get('/details', (req, res) => {
  try {
    res.json({
      code: true,
      data: mockDeviceData.details,
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get device details'
    })
  }
})

// 获取设备卡片信息
router.get('/cards', (req, res) => {
  try {
    res.json({
      code: true,
      data: mockDeviceData.cards,
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get device cards'
    })
  }
})

// 获取实名认证卡片
router.get('/real-name-cards', (req, res) => {
  try {
    res.json({
      code: true,
      data: mockDeviceData.realNameCards,
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get real name cards'
    })
  }
})

// 续费设备
router.post('/renew', (req, res) => {
  try {
    // 模拟续费逻辑
    const { packageId } = req.body
    
    if (!packageId) {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'Package ID is required'
      })
    }
    
    // 更新设备到期时间
    const newExpireDate = new Date()
    newExpireDate.setMonth(newExpireDate.getMonth() + 1)
    
    mockDeviceData.details.becomedueDatetime = newExpireDate
    
    res.json({
      code: true,
      data: {
        orderId: `ORDER_${Date.now()}`,
        expireDate: newExpireDate
      },
      msg: 'Device renewed successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to renew device'
    })
  }
})

// 更新设备配置
router.put('/config', (req, res) => {
  try {
    const { wifiName, wifiPwd, wifi5gName, wifi5gPwd } = req.body
    
    if (wifiName) mockDeviceData.details.wifiName = wifiName
    if (wifiPwd) mockDeviceData.details.wifiPwd = wifiPwd
    if (wifi5gName) mockDeviceData.details.wifi5gName = wifi5gName
    if (wifi5gPwd) mockDeviceData.details.wifi5gPwd = wifi5gPwd
    
    res.json({
      code: true,
      data: mockDeviceData.details,
      msg: 'Device config updated successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to update device config'
    })
  }
})

export default router
