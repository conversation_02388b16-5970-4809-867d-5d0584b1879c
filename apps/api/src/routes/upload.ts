import express from 'express'
import multer from 'multer'
import { v4 as uuidv4 } from 'uuid'
import { extname, join } from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const router = express.Router()

// 配置multer
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, join(__dirname, '../../uploads'))
  },
  filename: (req, file, cb) => {
    const ext = extname(file.originalname)
    const filename = `${uuidv4()}${ext}`
    cb(null, filename)
  }
})

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  },
  fileFilter: (req, file, cb) => {
    // 允许的文件类型
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/json',
      'text/plain'
    ]
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true)
    } else {
      cb(new Error('File type not allowed'))
    }
  }
})

// 单文件上传
router.post('/single', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'No file uploaded'
      })
    }
    
    const fileInfo = {
      id: uuidv4(),
      originalName: req.file.originalname,
      filename: req.file.filename,
      mimetype: req.file.mimetype,
      size: req.file.size,
      url: `/uploads/${req.file.filename}`,
      uploadTime: new Date()
    }
    
    res.json({
      code: true,
      data: fileInfo,
      msg: 'File uploaded successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'File upload failed'
    })
  }
})

// 多文件上传
router.post('/multiple', upload.array('files', 10), (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'No files uploaded'
      })
    }
    
    const files = (req.files as Express.Multer.File[]).map(file => ({
      id: uuidv4(),
      originalName: file.originalname,
      filename: file.filename,
      mimetype: file.mimetype,
      size: file.size,
      url: `/uploads/${file.filename}`,
      uploadTime: new Date()
    }))
    
    res.json({
      code: true,
      data: files,
      msg: 'Files uploaded successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Files upload failed'
    })
  }
})

// 图片上传（专用于组件图标等）
router.post('/image', upload.single('image'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'No image uploaded'
      })
    }
    
    // 验证是否为图片
    if (!req.file.mimetype.startsWith('image/')) {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'File must be an image'
      })
    }
    
    const imageInfo = {
      id: uuidv4(),
      originalName: req.file.originalname,
      filename: req.file.filename,
      mimetype: req.file.mimetype,
      size: req.file.size,
      url: `/uploads/${req.file.filename}`,
      width: 0, // 实际项目中可以使用sharp等库获取图片尺寸
      height: 0,
      uploadTime: new Date()
    }
    
    res.json({
      code: true,
      data: imageInfo,
      msg: 'Image uploaded successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Image upload failed'
    })
  }
})

// 页面配置文件上传
router.post('/page-config', upload.single('config'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'No config file uploaded'
      })
    }
    
    // 验证文件类型
    if (req.file.mimetype !== 'application/json') {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'Config file must be JSON format'
      })
    }
    
    const configInfo = {
      id: uuidv4(),
      originalName: req.file.originalname,
      filename: req.file.filename,
      size: req.file.size,
      url: `/uploads/${req.file.filename}`,
      uploadTime: new Date()
    }
    
    res.json({
      code: true,
      data: configInfo,
      msg: 'Config file uploaded successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Config file upload failed'
    })
  }
})

// 错误处理中间件
router.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'File size too large (max 10MB)'
      })
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'Too many files (max 10)'
      })
    }
  }
  
  if (error.message === 'File type not allowed') {
    return res.status(400).json({
      code: false,
      data: null,
      msg: 'File type not allowed'
    })
  }
  
  next(error)
})

export default router
