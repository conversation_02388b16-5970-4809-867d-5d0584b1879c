import express from 'express'
import { mockBalanceData } from '../mock/balanceData.js'

const router = express.Router()

// 获取余额列表（充值选项）
router.get('/list', (req, res) => {
  try {
    res.json({
      code: true,
      data: {
        list: mockBalanceData.rechargeOptions,
        currentBalance: mockBalanceData.currentBalance
      },
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get balance list'
    })
  }
})

// 获取余额明细（完整余额信息）
router.get('/details', (req, res) => {
  try {
    res.json({
      code: true,
      data: {
        balance: mockBalanceData.currentBalance,
        monthData: {
          monthConsumption: 89.00,
          monthRecharge: 200.00
        },
        rechargeOptions: mockBalanceData.rechargeOptions,
        transactions: mockBalanceData.transactions.slice(0, 5) // 只返回最近5条
      },
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get balance details'
    })
  }
})

// 获取余额交易记录（分页）
router.get('/transactions', (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query
    const start = (Number(page) - 1) * Number(pageSize)
    const end = start + Number(pageSize)

    const transactions = mockBalanceData.transactions.slice(start, end)

    res.json({
      code: true,
      data: {
        rows: transactions,
        total: mockBalanceData.transactions.length,
        page: Number(page),
        pageSize: Number(pageSize)
      },
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get balance transactions'
    })
  }
})

// 获取月度余额数据
router.get('/month-data', (req, res) => {
  try {
    const currentMonth = new Date().getMonth()
    const currentYear = new Date().getFullYear()
    
    // 计算当月消费和充值
    const monthTransactions = mockBalanceData.transactions.filter(t => {
      const transactionDate = new Date(t.balanceAlterationTime)
      return transactionDate.getMonth() === currentMonth && 
             transactionDate.getFullYear() === currentYear
    })
    
    const monthConsumption = monthTransactions
      .filter(t => t.unit === '-')
      .reduce((sum, t) => sum + t.balanceAmount, 0)
    
    const monthRecharge = monthTransactions
      .filter(t => t.unit === '+')
      .reduce((sum, t) => sum + t.balanceAmount, 0)
    
    res.json({
      code: true,
      data: {
        monthConsumption,
        monthRecharge,
        currentBalance: mockBalanceData.currentBalance
      },
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get month balance data'
    })
  }
})

// 创建充值订单
router.post('/recharge', (req, res) => {
  try {
    const { rechargeId, paymentMethod } = req.body
    
    if (!rechargeId) {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'Recharge ID is required'
      })
    }
    
    const rechargeOption = mockBalanceData.rechargeOptions.find(r => r.id === rechargeId)
    if (!rechargeOption) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'Recharge option not found'
      })
    }
    
    // 创建充值订单
    const order = {
      id: Date.now(),
      rechargeId,
      amount: rechargeOption.prestorePrice,
      giveAmount: rechargeOption.prestoreGive,
      paymentMethod: paymentMethod || 'wechat',
      status: 'pending',
      createTime: new Date(),
      orderNumber: `BAL_${Date.now()}`
    }
    
    mockBalanceData.rechargeOrders.push(order)
    
    res.json({
      code: true,
      data: order,
      msg: 'Recharge order created successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to create recharge order'
    })
  }
})

// 支付充值订单
router.post('/orders/:id/pay', (req, res) => {
  try {
    const { id } = req.params
    const order = mockBalanceData.rechargeOrders.find(o => o.id === Number(id))
    
    if (!order) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'Order not found'
      })
    }
    
    if (order.status !== 'pending') {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'Order cannot be paid'
      })
    }
    
    // 模拟支付成功
    order.status = 'paid'
    order.payTime = new Date()
    
    // 更新余额
    const totalAmount = order.amount + order.giveAmount
    const oldBalance = mockBalanceData.currentBalance
    mockBalanceData.currentBalance += totalAmount
    
    // 添加交易记录
    const transaction = {
      id: Date.now(),
      systemOrdernumber: order.orderNumber,
      balanceBefore: oldBalance,
      balanceAfter: mockBalanceData.currentBalance,
      balanceAmount: totalAmount,
      unit: '+',
      balanceAlterationTime: new Date(),
      balanceRemark: `充值余额 ${order.amount}元${order.giveAmount > 0 ? ` (赠送${order.giveAmount}元)` : ''}`
    }
    
    mockBalanceData.transactions.unshift(transaction)
    
    res.json({
      code: true,
      data: {
        order,
        newBalance: mockBalanceData.currentBalance,
        transaction
      },
      msg: 'Recharge successful'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Recharge failed'
    })
  }
})

// 获取充值订单列表
router.get('/orders/list', (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query
    const start = (Number(page) - 1) * Number(pageSize)
    const end = start + Number(pageSize)
    
    const orders = mockBalanceData.rechargeOrders.slice(start, end)
    
    res.json({
      code: true,
      data: {
        list: orders,
        total: mockBalanceData.rechargeOrders.length,
        page: Number(page),
        pageSize: Number(pageSize)
      },
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get recharge orders'
    })
  }
})

export default router
