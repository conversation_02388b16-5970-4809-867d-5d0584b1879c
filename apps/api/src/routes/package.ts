import express from 'express'
import { mockPackageData } from '../mock/packageData.js'

const router = express.Router()

// 获取套餐列表
router.get('/list', (req, res) => {
  try {
    const { type, popular } = req.query
    let packages = [...mockPackageData.packages]
    
    // 按类型过滤
    if (type) {
      packages = packages.filter(pkg => pkg.packageType === Number(type))
    }
    
    // 按热门过滤
    if (popular) {
      packages = packages.filter(pkg => pkg.popular === Number(popular))
    }
    
    res.json({
      code: true,
      data: packages,
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get package list'
    })
  }
})

// 获取套餐详情
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params
    const packageItem = mockPackageData.packages.find(pkg => pkg.id === Number(id))
    
    if (!packageItem) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'Package not found'
      })
    }
    
    res.json({
      code: true,
      data: packageItem,
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get package details'
    })
  }
})

// 创建套餐订单
router.post('/order', (req, res) => {
  try {
    const { packageId, paymentMethod } = req.body
    
    if (!packageId) {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'Package ID is required'
      })
    }
    
    const packageItem = mockPackageData.packages.find(pkg => pkg.id === packageId)
    if (!packageItem) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'Package not found'
      })
    }
    
    // 创建订单
    const order = {
      id: Date.now(),
      packageId,
      packageName: packageItem.name,
      packagePrice: packageItem.packagePrice,
      paymentMethod: paymentMethod || 'balance',
      status: 'pending',
      createTime: new Date(),
      orderNumber: `PKG_${Date.now()}`
    }
    
    mockPackageData.orders.push(order)
    
    res.json({
      code: true,
      data: order,
      msg: 'Package order created successfully'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to create package order'
    })
  }
})

// 获取订单列表
router.get('/orders/list', (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query
    const start = (Number(page) - 1) * Number(pageSize)
    const end = start + Number(pageSize)
    
    const orders = mockPackageData.orders.slice(start, end)
    
    res.json({
      code: true,
      data: {
        list: orders,
        total: mockPackageData.orders.length,
        page: Number(page),
        pageSize: Number(pageSize)
      },
      msg: 'success'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Failed to get order list'
    })
  }
})

// 支付订单
router.post('/orders/:id/pay', (req, res) => {
  try {
    const { id } = req.params
    const order = mockPackageData.orders.find(o => o.id === Number(id))
    
    if (!order) {
      return res.status(404).json({
        code: false,
        data: null,
        msg: 'Order not found'
      })
    }
    
    if (order.status !== 'pending') {
      return res.status(400).json({
        code: false,
        data: null,
        msg: 'Order cannot be paid'
      })
    }
    
    // 模拟支付
    order.status = 'paid'
    order.payTime = new Date()
    
    res.json({
      code: true,
      data: order,
      msg: 'Payment successful'
    })
  } catch (error) {
    res.status(500).json({
      code: false,
      data: null,
      msg: 'Payment failed'
    })
  }
})

export default router
