# H5应用环境变量配置示例

# ==================== API配置 ====================

# Device-An API地址 (开发环境使用代理路径，生产环境使用完整URL)
# 开发环境: /Fan (通过Vite代理到 https://ml.liangaiyun.com/java/)
# 生产环境: https://api.device-an.com
VITE_DEVICE_AN_API_URL=/Fan

# 低代码平台API地址
VITE_LOWCODE_API_URL=http://localhost:3002

# 其他项目API地址示例
VITE_PROJECT_A_API_URL=https://api.project-a.com
VITE_PROJECT_B_API_URL=https://api.project-b.com

# ==================== 安全配置 ====================

# 认证密钥（生产环境必须设置）
VITE_AUTH_SECRET=your-secret-key-here

# ==================== 应用配置 ====================

# 应用标题
VITE_APP_TITLE=低代码H5应用

# 应用版本
VITE_APP_VERSION=1.0.0

# 应用环境
VITE_APP_ENV=development

# ==================== 功能开关 ====================

# 是否启用调试模式
VITE_DEBUG_MODE=true

# 是否启用性能监控
VITE_ENABLE_PERFORMANCE_MONITOR=true

# 是否启用错误上报
VITE_ENABLE_ERROR_REPORTING=false

# ==================== 第三方服务 ====================

# 错误监控服务
VITE_SENTRY_DSN=

# 统计分析服务
VITE_ANALYTICS_ID=

# CDN地址
VITE_CDN_URL=

# ==================== 开发配置 ====================

# 开发服务器端口
VITE_DEV_PORT=3000

# 是否启用HTTPS
VITE_HTTPS=false

# 代理配置
VITE_PROXY_TARGET=http://localhost:3002
