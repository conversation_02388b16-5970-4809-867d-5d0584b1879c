<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0,maximum-scale=1.0,user-scalale=no;"
    />
    <title>设备</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>

<style>
  * {
    padding: 0;
    margin: 0;
  }

  :root {
    --max-width: 500px;
    --count: 20;
  }

  #app {
    max-width: var(--max-width);
    margin: 0 auto;
    position: absolute;
    height: 100%;
    inset: 0;
    font-size: calc(100vw / var(--count));
  }

  html {
    font-size: calc(100vw / var(--count));
  }

  @media (min-width: 500px) {
    #app {
      font-size: calc(var(--max-width) / var(--count));
    }

    html {
      font-size: calc(var(--max-width) / var(--count));
    }
  }
</style>
