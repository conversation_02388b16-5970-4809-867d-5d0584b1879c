import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { requestManager } from '../services/RequestManager'
import { DataAdapter } from '../services/DataAdapter'

// 页面配置接口
interface Page {
  id: string
  name: string
  path: string
  pageId: string
  components?: any[]
}

// 权限配置接口
interface AppPermissionConfig {
  enabled: boolean
  publicPages: string[]
  requireAuth: boolean
  defaultRole: string
}

// 应用配置接口
export interface AppConfig {
  id: string
  name: string
  description: string
  icon: string
  defaultHomePage: string
  appType?: string  // 应用类型，如 'device', 'mall'
  tabBar?: {
    enabled: boolean
    tabs: TabConfig[]
    style?: {
      backgroundColor?: string
      activeColor?: string
      inactiveColor?: string
      height?: string
    }
  }
  createTime: Date | string
  updateTime: Date | string
  published: boolean
  publishTime?: Date | string
  permissions?: AppPermissionConfig
}

export interface TabConfig {
  id: string
  name: string
  label: string
  icon: string
  path: string
  pageId: string
  active?: boolean
}

// 应用管理Store
export const useAppStore = defineStore('app', () => {
  const apps = ref<Map<string, AppConfig>>(new Map())
  const currentApp = ref<AppConfig | null>(null)
  const currentPage = ref<Page | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const appList = computed(() => Array.from(apps.value.values()))
  const hasCurrentApp = computed(() => !!currentApp.value)

  // 获取应用配置 (兼容原有接口)
  const getAppConfig = async (appId: string): Promise<AppConfig | null> => {
    return await getApp(appId)
  }

  // 获取单个应用
  const getApp = async (appId: string): Promise<AppConfig | null> => {
    // 先从缓存中查找
    if (apps.value.has(appId)) {
      return apps.value.get(appId)!
    }

    loading.value = true
    error.value = null

    try {
      console.log(`🔄 H5端获取应用配置: ${appId}`)

      const app = await requestManager.request<{ code: boolean | number, data: AppConfig, msg: string }>({
        url: `/api/public/app/${appId}`,
        method: 'GET',
        cache: {
          key: `app_${appId}`,
          ttl: 10 * 60 * 1000, // 10分钟缓存
          storage: 'memory'
        }
      })

      console.log('API响应数据:', app)

      // 使用数据适配器检查响应和提取数据
      const rawData = DataAdapter.extractResponseData(app)

      if (rawData) {
        // 适配数据格式
        const adaptedData = DataAdapter.adaptAppConfig(rawData)
        console.log(`✅ 应用配置获取成功: ${appId}`, adaptedData)
        apps.value.set(appId, adaptedData)
        return adaptedData
      } else {
        console.warn(`API返回错误: ${app.msg}`, app)
        return null
      }
    } catch (err) {
      console.error(`获取应用配置失败: ${appId}`, err)
      error.value = err instanceof Error ? err.message : '获取应用配置失败'
      return null
    } finally {
      loading.value = false
    }
  }

  // 设置当前应用
  const setCurrentApp = async (appId: string): Promise<boolean> => {
    const app = await getApp(appId)
    if (app) {
      currentApp.value = app
      return true
    }
    return false
  }

  // 获取页面详情
  const getPage = async (appId: string, pageId: string): Promise<Page | null> => {
    try {
      const page = await requestManager.request<Page>({
        url: `/api/apps/${appId}/pages/${pageId}`,
        method: 'GET',
        cache: {
          key: `page_${appId}_${pageId}`,
          ttl: 5 * 60 * 1000, // 5分钟缓存
          storage: 'memory'
        }
      })

      return page
    } catch (err) {
      console.error('Get page failed:', err)
      return null
    }
  }

  // 设置当前页面
  const setCurrentPage = async (appId: string, pageId: string): Promise<boolean> => {
    const page = await getPage(appId, pageId)
    if (page) {
      currentPage.value = page
      return true
    }
    return false
  }

  // 获取应用权限配置
  const getAppPermissionConfig = async (appId: string): Promise<AppPermissionConfig | null> => {
    const app = await getApp(appId)
    return app?.permissions || null
  }

  // 检查页面是否为公开页面
  const isPublicPage = async (appId: string, pageId: string): Promise<boolean> => {
    const config = await getAppPermissionConfig(appId)
    if (!config || !config.enabled) {
      return true // 如果没有启用权限控制，则认为是公开的
    }

    return config.publicPages.includes(pageId)
  }

  // 根据应用ID检查认证状态
  const checkAppAuthentication = async (appId: string): Promise<{ isAuthenticated: boolean; appType: string; loginPath?: string }> => {
    const app = await getApp(appId)
    if (!app) {
      return { isAuthenticated: false, appType: 'device' }
    }

    const appType = app.appType || 'device'

    // 根据应用类型检查对应的token
    const tokenKey = appType === 'device' ? 'ZX-DEVICE-TOKEN' :
                     appType === 'mall' ? 'ZX-MALL-TOKEN' :
                     'ZX-DEVICE-TOKEN' // 默认

    const token = localStorage.getItem(tokenKey)
    const isAuthenticated = !!(token && token !== 'NOT_LOGIN')

    if (!isAuthenticated) {
      // 构建登录页路径
      const loginPath = `/${appType}/login`
      return { isAuthenticated: false, appType, loginPath }
    }

    return { isAuthenticated: true, appType }
  }

  // 清空应用配置
  const clearApp = () => {
    currentApp.value = null
    currentPage.value = null
    error.value = null
  }

  // 清除缓存
  const clearCache = () => {
    apps.value.clear()
    clearApp()
    requestManager.invalidateCache('app_')
    requestManager.invalidateCache('page_')
  }

  return {
    // 状态
    apps,
    currentApp,
    currentPage,
    loading,
    error,

    // 计算属性
    appList,
    hasCurrentApp,

    // 方法
    getAppConfig, // 兼容原有接口
    getApp,
    setCurrentApp,
    getPage,
    setCurrentPage,
    getAppPermissionConfig,
    isPublicPage,
    checkAppAuthentication,
    clearApp,
    clearCache
  }
})
