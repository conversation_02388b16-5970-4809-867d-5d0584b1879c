// ✅ 完全复制device-an的mask store
import { ref, type Component } from 'vue'
import { defineStore } from 'pinia'
import { toTime } from '../utils'
import ErrorBox from '../components/ErrorBox.vue'
import { closeToast } from 'vant'
import { useDeviceStore } from '../modules/device/stores/device'

export const useMaskStore = defineStore(
  'mask',
  () => {
    const show = ref(false)

    const MaskComp = ref<Component>()

    const NoticeTime = ref('')

    const Notice = ref({
      title: '',
      content: '',
      time: ''
    })

    const Error = ref({
      detail: '',
      time: ''
    })

    const OpenErrorBox = (detail: string) => {
      const useDevice = useDeviceStore()
      closeToast()
      show.value = false
      setTimeout(() => {
        useDevice.loading = false
        Error.value = {
          detail,
          time: toTime(new Date(), 'YYYY年MM月DD日 HH:mm:ss')!
        }
        MaskComp.value = ErrorBox
        show.value = true
      }, 200)
    }

    return {
      show,
      MaskComp,
      Notice,
      NoticeTime,
      Error,
      OpenErrorBox
    }
  },
  {
    persist: [
      {
        key: 'ZX-DEVICE-NOTICE-TIME',
        paths: ['NoticeTime']
      }
    ]
  }
)
