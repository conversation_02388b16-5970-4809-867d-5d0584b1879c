import { ref } from 'vue'
import { defineStore } from 'pinia'
import type { PageConfig } from '@lowcode/aslib/core'
import { appDataManager } from '../services/AppDataManager'
import { DataAdapter } from '../services/DataAdapter'

// 页面配置管理Store
export const usePageConfigStore = defineStore('pageConfig', () => {
  const configs = ref<Map<string, PageConfig>>(new Map())
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 获取页面配置
  const getPageConfig = async (pageId: string): Promise<PageConfig | null> => {
    // 先从缓存中获取
    if (configs.value.has(pageId)) {
      return configs.value.get(pageId)!
    }

    loading.value = true
    error.value = null

    try {
      // 从API或本地配置获取页面配置
      const config = await fetchPageConfig(pageId)
      if (config) {
        configs.value.set(pageId, config)
        return config
      }
      return null
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取页面配置失败'
      return null
    } finally {
      loading.value = false
    }
  }

  // 设置页面配置
  const setPageConfig = (pageId: string, config: PageConfig) => {
    configs.value.set(pageId, config)
  }

  // 更新页面配置
  const updatePageConfig = (pageId: string, updates: Partial<PageConfig>) => {
    const existing = configs.value.get(pageId)
    if (existing) {
      const updated = { ...existing, ...updates }
      configs.value.set(pageId, updated)
    }
  }

  // 删除页面配置
  const removePageConfig = (pageId: string) => {
    configs.value.delete(pageId)
  }

  // 清空所有配置
  const clearConfigs = () => {
    configs.value.clear()
  }

  // 获取所有页面配置列表
  const getPageConfigList = async () => {
    loading.value = true
    error.value = null

    try {
      // 这个方法暂时不实现，因为我们使用 AppDataManager 来管理页面配置
      console.warn('getAllPageConfigs 方法暂未实现，请使用 AppDataManager')
      return []
    } catch (err: any) {
      error.value = err.message || '获取页面配置列表失败'
      console.error('获取页面配置列表失败:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  // 强制刷新页面配置（清除缓存后重新获取）
  const refreshPageConfig = async (pageId: string) => {
    configs.value.delete(pageId)
    return await getPageConfig(pageId)
  }

  return {
    configs,
    loading,
    error,
    getPageConfig,
    setPageConfig,
    updatePageConfig,
    removePageConfig,
    clearConfigs,
    getPageConfigList,
    refreshPageConfig
  }
})

// 获取页面配置的具体实现
async function fetchPageConfig(pageId: string): Promise<PageConfig | null> {
  try {
    console.log('🌐 请求页面配置:', pageId)

    // 使用统一的应用数据管理器获取页面配置
    const result = await appDataManager.getPageConfigWithErrorHandling(pageId)

    if (!result.config) {
      console.warn(`页面配置获取失败: ${pageId}`, result.errorMessage)

      // 🔧 优化：根据错误类型采用不同的处理策略
      console.log('🔍 页面配置获取失败，错误类型:', result.errorType)

      // 🔧 临时调试：如果错误消息包含"未发布"，强制返回未发布页面配置
      if (result.errorType === 'page-not-published' ||
          (result.errorMessage && result.errorMessage.includes('未发布'))) {
        // 页面未发布：返回特殊的未发布页面配置，而不是跳转
        console.log('📄 页面未发布，返回未发布页面组件')
        const notPublishedConfig = createNotPublishedPageConfig(pageId, result.errorMessage, result.errorDetails)
        console.log('📄 生成的未发布页面配置:', notPublishedConfig)
        return notPublishedConfig
      } else {
        // 其他错误：跳转到错误页面
        const appId = pageId.includes('_') ? pageId.substring(0, pageId.lastIndexOf('_')) : pageId
        const errorUrl = `/app-error/${result.errorType}?appId=${appId}&message=${encodeURIComponent(result.errorMessage || '')}`
        console.log('🔄 跳转到错误页面:', errorUrl, '提取的AppID:', appId)

        // 使用 window.location 进行跳转
        if (typeof window !== 'undefined') {
          window.location.href = `${window.location.origin}/#${errorUrl}`
        }

        return null
      }
    }

    console.log('🔍 获取到的页面配置:', result.config)

    // 适配页面配置数据
    const adaptedData = DataAdapter.adaptPageConfig(result.config)
    console.log(`✅ 从AppDataManager获取页面配置: ${pageId}`)
    return adaptedData
  } catch (error) {
    console.error(`获取页面配置失败: ${pageId}`, error)
    return null
  }
}

// 创建页面未发布的特殊页面配置
function createNotPublishedPageConfig(pageId: string, errorMessage?: string, errorDetails?: any): PageConfig {
  const pageInfo = errorDetails?.pageInfo

  return {
    id: pageId,
    name: pageInfo?.name || '页面未发布',
    path: `/${pageId}`,
    title: pageInfo?.name || '页面未发布',
    layout: {
      type: 'flex',
      direction: 'column',
      style: {
        minHeight: '100vh',
        backgroundColor: '#f5f5f5'
      }
    },
    components: [
      {
        id: 'not-published-notice',
        type: 'PageNotPublished',
        props: {
          errorMessage: errorMessage,
          pageInfo: pageInfo
        },
        style: {
          width: '100%',
          height: '100vh'
        }
      }
    ],
    published: false,
    editable: false
  } as PageConfig
}

// 本地页面配置已删除，完全依赖 API 获取数据
