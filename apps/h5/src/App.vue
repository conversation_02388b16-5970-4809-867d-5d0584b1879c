<template>
  <div id="app">
    <!-- 添加路由过渡动画和缓存 -->
    <div class="main-content" :class="{ 'has-tabbar': hasTabBar }">
      <router-view v-slot="{ Component, route }">
        <transition
          :name="getTransitionName(route)"
          mode="out-in"
          @before-enter="onBeforeEnter"
          @after-enter="onAfterEnter"
        >
          <keep-alive :include="getCacheableComponents(route)">
            <component :is="Component" :key="route.path" />
          </keep-alive>
        </transition>
      </router-view>
    </div>

    <!-- 全局Loading组件 -->
    <GlobalLoading
      :visible="globalLoadingState.visible"
      :config="globalLoadingState.config"
      :text="globalLoadingState.text"
      :progress="globalLoadingState.progress"
      @dismiss="globalLoading.hide(true)"
    />

    <!-- 全局遮罩组件 -->
    <GlobalMask />

    <!-- TabBar组件 -->
    <SmartTabBar @visibility-change="onTabBarVisibilityChange" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useLoading } from './composables/useGlobalLoading'
import GlobalLoading from './components/GlobalLoading.vue'
import GlobalMask from './components/GlobalMask.vue'
import SmartTabBar from './components/SmartTabBar/SmartTabBar.vue'
import type { RouteLocationNormalized } from 'vue-router'

// 获取全局Loading状态
const globalLoading = useLoading()
const globalLoadingState = globalLoading.getState()

// 路由过渡状态
const isTransitioning = ref(false)

// 获取当前路由
const route = useRoute()

// TabBar显示状态（由SmartTabBar组件控制）
const hasTabBar = ref(false)

// 处理TabBar可见性变化
function onTabBarVisibilityChange(visible: boolean) {
  hasTabBar.value = visible
}

// 获取过渡动画名称
function getTransitionName(route: RouteLocationNormalized): string {
  // 根据路由类型选择不同的过渡动画
  if (route.path.startsWith('/app/')) {
    return 'slide-left' // 低代码页面使用左滑动画
  } else if (route.path === '/login') {
    return 'fade' // 登录页面使用淡入淡出
  } else {
    return 'slide-right' // 其他页面使用右滑动画
  }
}

// 获取可缓存的组件
function getCacheableComponents(route: RouteLocationNormalized): string[] {
  // 动态页面需要缓存，避免重复加载
  if (route.path.startsWith('/app/')) {
    return ['DynamicPage']
  }
  // 其他页面根据需要添加
  return []
}

// 过渡动画开始前
function onBeforeEnter() {
  isTransitioning.value = true
}

// 过渡动画结束后
function onAfterEnter() {
  isTransitioning.value = false
}

onMounted(() => {
  // 初始化应用
  console.log('🚀 H5应用启动完成')

  // 注意: 设备初始化逻辑已迁移到设备模块
  // 各业务模块会在加载时自动初始化自己的状态
})
</script>

<!-- ✅ 完全复制 device-an 的 App.vue 样式 -->
<style lang="scss">
// App布局样式
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  min-height: 100vh;
  /* 移除 overflow: hidden，允许页面滚动 */
}

.main-content {
  min-height: 100vh;
  /* 移除 overflow: hidden，允许页面滚动 */
  transition: padding-bottom 0.3s ease;

  &.has-tabbar {
    padding-bottom: 50px; /* TabBar高度 */
  }
}

// ✅ 完全复制 device-an 的 Vant 样式覆盖
.van-popup--bottom {
  padding: 0 !important;
  background: #fff !important;
}

.van-toast {
  background-color: rgba($color: #000000, $alpha: 0.7) !important;
}

.van-icon-fail,
.van-icon-success {
  font-size: 2rem;
}

// 低代码组件通用样式
.lowcode-page {
  width: 100%;
  min-height: 100vh;
  background-color: #f7f8fa;
}

.lowcode-component {
  position: relative;
}

.lowcode-editable {
  outline: 2px dashed #1989fa;
  outline-offset: 2px;
}

.lowcode-loading {
  opacity: 0.6;
  pointer-events: none;
}

/* 路由过渡动画 */
/* 左滑动画 - 用于低代码页面 */
.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-left-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.slide-left-leave-to {
  opacity: 0;
  transform: translateX(-100%);
}

/* 右滑动画 - 用于普通页面 */
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-right-enter-from {
  opacity: 0;
  transform: translateX(-100%);
}

.slide-right-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

/* 淡入淡出动画 - 用于登录页面 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* 优化过渡期间的性能 */
.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active,
.fade-enter-active,
.fade-leave-active {
  will-change: transform, opacity;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}
</style>
