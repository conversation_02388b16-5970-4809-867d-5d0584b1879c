// 全局样式文件

// 变量定义
:root {
  --primary-color: #1989fa;
  --success-color: #07c160;
  --warning-color: #ff9500;
  --danger-color: #ee0a24;
  --background-color: #f7f8fa;
  --border-color: #ebedf0;
  --text-color: #323233;
  --text-color-secondary: #646566;
  --text-color-light: #969799;
  --border-radius: 8px;
  --padding: 16px;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// 全局重置
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-color);
}

// 工具类
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: var(--primary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-secondary {
  color: var(--text-color-secondary);
}

.text-light {
  color: var(--text-color-light);
}

.bg-white {
  background-color: white;
}

.bg-primary {
  background-color: var(--primary-color);
}

.border-radius {
  border-radius: var(--border-radius);
}

.shadow {
  box-shadow: var(--shadow);
}

.padding {
  padding: var(--padding);
}

.margin {
  margin: var(--padding);
}

.margin-bottom {
  margin-bottom: var(--padding);
}

.margin-top {
  margin-top: var(--padding);
}

// 低代码组件样式
.lowcode-component {
  position: relative;
  
  &.lowcode-editable {
    outline: 2px dashed var(--primary-color);
    outline-offset: 2px;
    
    &::before {
      content: attr(data-component-type);
      position: absolute;
      top: -24px;
      left: 0;
      background-color: var(--primary-color);
      color: white;
      font-size: 10px;
      padding: 2px 6px;
      border-radius: 2px;
      z-index: 10;
    }
  }
  
  &.lowcode-loading {
    opacity: 0.6;
    pointer-events: none;
  }
  
  &.lowcode-error {
    border: 1px solid var(--danger-color);
    background-color: rgba(238, 10, 36, 0.1);
  }
}

// 页面容器样式
.lowcode-page {
  width: 100%;
  min-height: 100vh;
  background-color: var(--background-color);
}

// 响应式设计
@media (max-width: 768px) {
  :root {
    --padding: 12px;
  }
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}
