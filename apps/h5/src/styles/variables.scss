// ✅ 完全复制device-an的SCSS变量

// ✅ 完全复制device-an的颜色变量
$primary: rgb(59, 130, 246);
$success: #22c55e;
$warning: #f59e0b;
$error: #ef4444;
$info: #7f7fd5;
$wait: #999;

// 背景色
$background: rgb(243, 244, 246);
$box-background: rgb(249, 250, 251);

// 边框和分割线
$border: #eee;

// 圆角
$radius: 0.6rem;

// 阴影
$shadow: 0 0.4rem 2rem rgba(0, 0, 0, 0.05);

// 间距
$padding: 0.7rem;

// Mixin - 白色盒子
@mixin WhiteBox {
  background-color: #fff;
  border-radius: $radius;
  box-shadow: $shadow;
}

// Mixin - 内边距盒子
@mixin PaddingBox {
  box-sizing: border-box;
  padding: $padding;
}

// Mixin - 页面盒子
@mixin PageBox {
  min-height: 100vh;
  background-color: $background;
  box-sizing: border-box;
  padding: $padding;
}

// Mixin - 文本省略
@mixin TextEllipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Mixin - 多行文本省略
@mixin TextEllipsisMulti($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Mixin - Flex居中
@mixin FlexCenter {
  display: flex;
  justify-content: center;
  align-items: center;
}

// Mixin - Flex两端对齐
@mixin FlexBetween {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 响应式断点
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

// 响应式Mixin
@mixin respond-to($breakpoint) {
  @if $breakpoint == sm {
    @media (min-width: $breakpoint-sm) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (min-width: $breakpoint-md) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (min-width: $breakpoint-lg) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (min-width: $breakpoint-xl) {
      @content;
    }
  }
}
