/**
 * 预设操作处理器（重构版）
 * 统一管理UI操作和API操作
 */

import { UIActions, type UIActionConfig } from './UIActions'
import { APIActions, type APIActionConfig } from './APIActions'

export type PresetActionConfig = UIActionConfig | APIActionConfig

export class PresetActions {
  private uiActions: UIActions
  private apiActions: APIActions

  constructor() {
    this.uiActions = new UIActions()
    this.apiActions = new APIActions()
  }

  /**
   * 执行预设操作（统一入口）
   */
  async executePresetAction(
    config: PresetActionConfig,
    data: any,
    component: any,
    eventHandler?: any
  ): Promise<void> {
    console.log(`🎯 执行预设操作: ${config.preset}`, config)

    // 验证配置
    if (!config.preset) {
      console.error('❌ 预设操作配置错误: preset不能为空')
      return
    }

    try {
      // 根据操作类型分发到对应的处理器
      if (UIActions.isUIAction(config.preset)) {
        console.log(`🎨 执行UI操作: ${config.preset}`)
        await this.uiActions.executeUIAction(config as UIActionConfig, data, component, eventHandler)
      } else if (APIActions.isAPIAction(config.preset)) {
        console.log(`🔌 执行API操作: ${config.preset}`)
        await this.apiActions.executeAPIAction(config as APIActionConfig, data, component, eventHandler)
      } else {
        console.warn(`⚠️ 未知预设操作: ${config.preset}`)
        console.log('📋 支持的UI操作:', this.getSupportedUIActions())
        console.log('📋 支持的API操作:', this.getSupportedAPIActions())
      }
    } catch (error) {
      console.error(`❌ 预设操作执行失败: ${config.preset}`, error)
    }
  }

  /**
   * 获取支持的UI操作列表
   */
  getSupportedUIActions(): string[] {
    return [
      'showMessage',
      'showConfirm',
      'toggleComponent',
      'toggleVisibility',
      'copyText',
      'downloadFile',
      'openModal'
    ]
  }

  /**
   * 获取支持的API操作列表
   */
  getSupportedAPIActions(): string[] {
    return APIActions.getSupportedActions()
  }

  /**
   * 获取所有支持的预设操作
   */
  getAllSupportedActions(): { ui: string[]; api: string[] } {
    return {
      ui: this.getSupportedUIActions(),
      api: this.getSupportedAPIActions()
    }
  }

  /**
   * 检查操作是否支持
   */
  isActionSupported(preset: string): boolean {
    return UIActions.isUIAction(preset) || APIActions.isAPIAction(preset)
  }

}
