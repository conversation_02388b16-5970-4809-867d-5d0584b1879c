/**
 * 自定义事件处理器
 * 负责处理自定义代码和预设操作
 */

import type { Router } from 'vue-router'
import { showToast, showDialog } from 'vant'
import apiClient from '../../api/client'
import { PresetActions, type PresetActionConfig } from './PresetActions'

export interface CustomEventConfig {
  preset?: string
  handler?: string
  [key: string]: any
}

export class CustomEventHandler {
  private router: Router
  private presetActions: PresetActions

  constructor(router: Router) {
    this.router = router
    this.presetActions = new PresetActions()
  }

  /**
   * 处理自定义事件
   */
  async handleCustomEvent(
    eventConfig: CustomEventConfig,
    data: any,
    component: any
  ): Promise<void> {
    console.log('🎯 处理自定义事件:', eventConfig)

    if (eventConfig.preset) {
      // 预设操作
      console.log('📋 执行预设操作:', eventConfig.preset)
      await this.handlePresetAction(eventConfig as PresetActionConfig, data, component)
    } else if (eventConfig.handler) {
      // 自定义代码
      console.log('💻 执行自定义代码')
      await this.executeCustomCode(eventConfig.handler, data, component)
    } else {
      console.warn('⚠️ 自定义事件缺少处理逻辑:', eventConfig)
      console.log('📋 事件配置详情:', JSON.stringify(eventConfig, null, 2))
    }
  }

  /**
   * 处理预设操作
   */
  private async handlePresetAction(
    eventConfig: PresetActionConfig, 
    data: any, 
    component: any
  ): Promise<void> {
    await this.presetActions.executePresetAction(eventConfig, data, component, this)
  }

  /**
   * 执行自定义代码
   */
  private async executeCustomCode(
    code: string, 
    data: any, 
    component: any
  ): Promise<any> {
    try {
      // 创建安全的执行环境
      const context = this.createExecutionContext(data, component)

      // 创建函数并执行
      const func = new Function(...Object.keys(context), code)
      const result = await func(...Object.values(context))
      
      console.log('✅ 自定义代码执行成功:', result)
      return result
    } catch (error) {
      console.error('❌ 自定义代码执行失败:', error)
      showToast('代码执行失败: ' + (error as Error).message)
      throw error
    }
  }

  /**
   * 创建代码执行上下文
   */
  private createExecutionContext(data: any, component: any) {
    return {
      // 数据上下文
      data,
      eventData: data, // 向后兼容
      component,
      
      // 路由相关
      router: this.router,
      
      // UI 相关
      showToast,
      showDialog,
      
      // 网络请求
      apiClient,
      
      // 调试工具
      console,
      
      // 工具方法
      utils: {
        showMessage: (message: string) => showToast(message),
        showConfirm: (title: string, message: string) => showDialog({ title, message }),
        navigate: (path: string) => this.router.push(path),
        goBack: () => this.router.back(),
        reload: () => window.location.reload()
      },
      
      // API 相关 - 使用H5应用的API客户端
      api: apiClient
    }
  }

  /**
   * 验证自定义代码安全性（基础检查）
   */
  private validateCustomCode(code: string): boolean {
    // 基础的安全检查
    const dangerousPatterns = [
      /eval\s*\(/,
      /Function\s*\(/,
      /document\.write/,
      /window\.location/,
      /localStorage/,
      /sessionStorage/,
      /document\.cookie/
    ]

    for (const pattern of dangerousPatterns) {
      if (pattern.test(code)) {
        console.warn('⚠️ 检测到潜在的不安全代码:', pattern)
        return false
      }
    }

    return true
  }

  /**
   * 安全执行自定义代码
   */
  async executeCustomCodeSafely(
    code: string, 
    data: any, 
    component: any
  ): Promise<any> {
    // 验证代码安全性
    if (!this.validateCustomCode(code)) {
      throw new Error('代码包含不安全的操作')
    }

    return this.executeCustomCode(code, data, component)
  }
}
