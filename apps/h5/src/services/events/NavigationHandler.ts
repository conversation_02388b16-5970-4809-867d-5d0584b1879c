/**
 * 导航处理器
 * 负责处理各种类型的导航事件
 */

import type { Router } from 'vue-router'
import { ROUTES, DEFAULT_TEXTS } from '@lowcode/aslib/core'
import { PathResolver } from '../utils/PathResolver'

export interface NavigationConfig {
  target: string
  navigateType: 'page' | 'external' | 'back' | 'webview'
  webviewTitle?: string
  webviewOptions?: string[]
}

export class NavigationHandler {
  private router: Router
  private pathResolver: PathResolver

  constructor(router: Router) {
    this.router = router
    this.pathResolver = new PathResolver(router)
  }

  /**
   * 处理导航事件
   */
  async handleNavigation(config: NavigationConfig): Promise<void> {
    const { target, navigateType, webviewTitle, webviewOptions } = config

    if (!target) {
      console.warn('导航事件缺少目标地址')
      return
    }

    if (!navigateType) {
      console.error('导航事件缺少navigateType配置')
      return
    }

    switch (navigateType) {
      case 'external':
        this.handleExternalNavigation(target)
        break

      case 'back':
        this.handleBackNavigation()
        break

      case 'webview':
        await this.handleWebviewNavigation(target, {
          title: webviewTitle,
          options: webviewOptions || []
        })
        break

      case 'page':
      default:
        await this.handlePageNavigation(target)
        break
    }
  }

  /**
   * 处理页面导航
   */
  private async handlePageNavigation(target: string): Promise<void> {
    console.log(`🔗 导航到页面: ${target}`)

    // 使用路径解析器智能解析目标路径
    const finalTarget = this.pathResolver.resolveTargetPath(target)

    try {
      await this.router.push(finalTarget)
    } catch (error) {
      console.error('页面导航失败:', error)
      throw error
    }
  }

  /**
   * 处理外部链接导航
   */
  private handleExternalNavigation(target: string): void {
    console.log(`🔗 打开外部链接: ${target}`)
    window.open(target, '_blank')
  }

  /**
   * 处理返回导航
   */
  private handleBackNavigation(): void {
    console.log(`🔙 返回上一页`)
    this.router.back()
  }

  /**
   * 处理WebView导航
   */
  private async handleWebviewNavigation(
    url: string, 
    config?: { title?: string; options?: string[] }
  ): Promise<void> {
    const title = config?.title || DEFAULT_TEXTS.PAGE_TITLE_WEBVIEW
    const options = config?.options || []

    console.log(`🔗 打开WebView: ${url}`)

    // 跳转到内嵌页面路由
    const webviewRoute = `${ROUTES.WEBVIEW}?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}&options=${encodeURIComponent(options.join(','))}`

    try {
      await this.router.push(webviewRoute)
    } catch (error) {
      console.error('WebView导航失败:', error)
      throw error
    }
  }
}
