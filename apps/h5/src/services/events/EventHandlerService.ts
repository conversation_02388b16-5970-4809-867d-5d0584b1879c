/**
 * 事件处理服务（重构版）
 * 负责统一的事件处理和分发
 */

import type { Router } from 'vue-router'
import { showToast } from 'vant'
import { NavigationHandler, type NavigationConfig } from './NavigationHandler'
import { CustomEventHandler, type CustomEventConfig } from './CustomEventHandler'

export interface EventConfig {
  type: 'navigate' | 'custom'
  elementType?: string
  elementId?: string
  menuItemId?: string // 向后兼容
  [key: string]: any
}

export interface EventData {
  elementType: string
  elementId: string
  elementData?: any
  componentType: string
}

export class EventHandlerService {
  private router: Router
  private navigationHandler: NavigationHandler
  private customEventHandler: CustomEventHandler

  constructor(router: Router) {
    this.router = router
    this.navigationHandler = new NavigationHandler(router)
    this.customEventHandler = new CustomEventHandler(router)
  }

  /**
   * 处理组件事件（主入口）
   */
  async handleComponentEvent(comp: any, eventName: string, data: any): Promise<void> {
    try {
      if (eventName === 'click') {
        // 新的统一事件架构
        await this.handleUnifiedClick(data, comp)
      } else {
        // 兼容旧的事件处理方式
        console.warn(`⚠️ 使用了旧的事件格式: ${eventName}，建议升级到统一的click事件`)
        await this.convertLegacyEventToUnified(eventName, data, comp)
      }
    } catch (error) {
      console.error('❌ 事件处理失败:', error)
      showToast('操作失败，请重试')
    }
  }

  /**
   * 处理统一的click事件
   */
  private async handleUnifiedClick(eventData: EventData, component: any): Promise<void> {
    const { elementType, elementId } = eventData

    // 1. 查找特定的事件配置
    const specificConfig = this.findUnifiedEventConfig(component, elementType, elementId)

    if (specificConfig) {
      await this.executeEventConfig(specificConfig, eventData)
    } else {
      await this.handleDefaultUnifiedAction(eventData)
    }
  }

  /**
   * 查找统一事件配置
   */
  private findUnifiedEventConfig(component: any, elementType: string, elementId: string): EventConfig | null {
    const events = component.events || {}
    console.log('🔍 查找统一事件配置:', { elementType, elementId })

    const clickEvents = events['click']
    if (!clickEvents) {
      console.log('🔍 未找到click事件配置')
      return null
    }

    // 处理数组格式（多个配置）
    if (Array.isArray(clickEvents)) {
      for (const config of clickEvents) {
        if (this.isConfigMatched(config, elementType, elementId)) {
          console.log('🔍 找到匹配的配置:', config)
          return config
        }
      }
    }
    // 处理单个配置格式
    else {
      if (this.isConfigMatched(clickEvents, elementType, elementId)) {
        console.log('🔍 找到单个配置:', clickEvents)
        return clickEvents
      }
    }

    console.warn(`⚠️ 未找到匹配的事件配置: elementType=${elementType}, elementId=${elementId}`)
    return null
  }

  /**
   * 检查配置是否匹配
   */
  private isConfigMatched(config: EventConfig, elementType: string, elementId: string): boolean {
    // 精确匹配逻辑
    let hasMatch = true
    
    // 如果配置中指定了elementType，必须匹配
    if (config.elementType && config.elementType !== elementType) {
      hasMatch = false
    }
    
    // 如果配置中指定了elementId，必须匹配
    if (config.elementId && config.elementId !== elementId) {
      hasMatch = false
    }
    
    // 兼容旧的menuItemId字段
    if (config.menuItemId && config.menuItemId !== elementId) {
      hasMatch = false
    }
    
    // 如果配置中没有指定任何匹配条件，则不匹配
    if (!config.elementType && !config.elementId && !config.menuItemId) {
      hasMatch = false
    }

    return hasMatch
  }

  /**
   * 执行事件配置
   */
  private async executeEventConfig(eventConfig: EventConfig, data: EventData): Promise<void> {
    switch (eventConfig.type) {
      case 'navigate':
        await this.navigationHandler.handleNavigation(eventConfig as NavigationConfig)
        break
      case 'custom':
        await this.customEventHandler.handleCustomEvent(eventConfig as CustomEventConfig, data, null)
        break
      default:
        console.warn(`⚠️ 未知事件类型: ${eventConfig.type}`)
    }
  }

  /**
   * 处理默认的统一事件动作
   */
  private async handleDefaultUnifiedAction(eventData: EventData): Promise<void> {
    const { elementType, elementId, elementData } = eventData

    // 尝试从元素数据中获取默认动作
    if (elementData?.defaultAction) {
      await this.executeEventConfig(elementData.defaultAction, eventData)
      return
    }

    // 没有找到事件配置，提示用户配置事件
    console.warn(`⚠️ 未配置事件处理: elementType=${elementType}, elementId=${elementId}`)
    showToast(`请在设计器中为"${elementData?.label || elementId}"配置点击事件`)
  }

  /**
   * 转换旧事件格式为统一格式
   */
  private async convertLegacyEventToUnified(eventName: string, data: any, comp: any): Promise<void> {
    const eventDataMap: Record<string, EventData> = {
      'menuClick': {
        elementType: 'menu',
        elementId: data.id,
        elementData: data,
        componentType: comp.type
      },
      'buttonClick': {
        elementType: 'button',
        elementId: data.type,
        elementData: data,
        componentType: comp.type
      },
      'actionClick': {
        elementType: 'action',
        elementId: data.type,
        elementData: data,
        componentType: comp.type
      }
    }

    const eventData = eventDataMap[eventName]
    if (eventData) {
      await this.handleUnifiedClick(eventData, comp)
    } else {
      await this.handleLegacyEvent(comp, eventName, data)
    }
  }

  /**
   * 兼容原有事件配置
   */
  private async handleLegacyEvent(comp: any, eventName: string, data: any): Promise<void> {
    const eventConfig = comp.events?.[eventName]

    if (!eventConfig) {
      console.warn(`⚠️ 组件 ${comp.type} 没有配置事件: ${eventName}`)
      return
    }

    await this.executeEventConfig(eventConfig, data)
  }
}
