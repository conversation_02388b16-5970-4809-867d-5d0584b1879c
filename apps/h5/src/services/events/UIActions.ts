/**
 * UI操作处理器
 * 负责处理纯前端UI操作（弹窗、消息、复制等）
 */

import { showToast, showDialog } from 'vant'

export interface UIActionConfig {
  preset: string
  message?: string
  title?: string
  text?: string
  fileUrl?: string
  fileName?: string
  modalTitle?: string
  modalContent?: string
  targetId?: string
  onConfirm?: any
}

export class UIActions {
  /**
   * 执行UI操作
   */
  async executeUIAction(
    config: UIActionConfig,
    data: any,
    component: any,
    eventHandler?: any
  ): Promise<boolean> {
    console.log(`🎨 执行UI操作: ${config.preset}`, config)

    switch (config.preset) {
      case 'showMessage':
        this.showMessage(config.message || '操作成功')
        break
        
      case 'showConfirm':
        await this.showConfirm(config, data, component, eventHandler)
        break
        
      case 'toggleComponent':
      case 'toggleVisibility':
        this.toggleComponentVisibility(config.targetId)
        break

      case 'copyText':
        await this.copyToClipboard(config.text || '')
        break

      case 'downloadFile':
        this.downloadFile(config.fileUrl || '', config.fileName || 'download')
        break

      case 'openModal':
        this.openModal(config.modalTitle || '提示', config.modalContent || '')
        break

      default:
        console.warn(`⚠️ 未知UI操作: ${config.preset}`)
        return false
    }
    
    return true
  }

  /**
   * 显示消息
   */
  private showMessage(message: string): void {
    if (!message) {
      console.warn('⚠️ showMessage: 消息内容为空')
      return
    }
    showToast(message)
  }

  /**
   * 显示确认对话框
   */
  private async showConfirm(
    config: UIActionConfig, 
    data: any, 
    component: any, 
    eventHandler?: any
  ): Promise<void> {
    const title = config.title || '确认'
    const message = config.message || '确定要执行此操作吗？'
    
    if (!message) {
      console.warn('⚠️ showConfirm: 确认消息为空')
      return
    }

    try {
      const confirmed = await showDialog({
        title,
        message
      }).then(() => true).catch(() => false)
      
      if (confirmed && config.onConfirm && eventHandler && typeof eventHandler.handleCustomEvent === 'function') {
        console.log('✅ 用户确认，执行后续操作')
        await eventHandler.handleCustomEvent(config.onConfirm, data, component)
      } else {
        console.log('❌ 用户取消操作')
      }
    } catch (error) {
      console.error('❌ 确认对话框执行失败:', error)
      showToast('操作失败')
    }
  }

  /**
   * 切换组件显示状态
   */
  private toggleComponentVisibility(targetId?: string): void {
    if (!targetId) {
      console.warn('⚠️ toggleComponent: 目标组件ID为空')
      showToast('组件ID未配置')
      return
    }

    const targetElement = document.querySelector(`[data-component-id="${targetId}"]`) as HTMLElement
    if (targetElement) {
      const currentDisplay = targetElement.style.display
      targetElement.style.display = currentDisplay === 'none' ? '' : 'none'
      console.log(`👁️ 切换组件显示状态: ${targetId}`)
      showToast(`组件${currentDisplay === 'none' ? '显示' : '隐藏'}成功`)
    } else {
      console.warn(`⚠️ 未找到目标组件: ${targetId}`)
      showToast('未找到目标组件')
    }
  }

  /**
   * 复制文本到剪贴板
   */
  private async copyToClipboard(text: string): Promise<void> {
    if (!text) {
      console.warn('⚠️ copyText: 复制内容为空')
      showToast('复制内容为空')
      return
    }

    try {
      await navigator.clipboard.writeText(text)
      showToast('复制成功')
      console.log(`📋 复制文本: ${text}`)
    } catch (error) {
      console.error('❌ 复制失败:', error)
      showToast('复制失败')
    }
  }

  /**
   * 下载文件
   */
  private downloadFile(url: string, fileName: string): void {
    if (!url) {
      console.warn('⚠️ downloadFile: 文件URL为空')
      showToast('文件地址未配置')
      return
    }

    try {
      const a = document.createElement('a')
      a.href = url
      a.download = fileName || 'download'
      a.target = '_blank'
      a.click()
      console.log(`📥 下载文件: ${url}`)
      showToast('开始下载')
    } catch (error) {
      console.error('❌ 下载失败:', error)
      showToast('下载失败')
    }
  }

  /**
   * 打开模态框
   */
  private openModal(title: string, content: string): void {
    if (!content) {
      console.warn('⚠️ openModal: 模态框内容为空')
      showToast('模态框内容未配置')
      return
    }

    try {
      showDialog({
        title: title || '提示',
        message: content
      })
      console.log(`🔍 打开模态框: ${title}`)
    } catch (error) {
      console.error('❌ 打开模态框失败:', error)
      showToast('打开模态框失败')
    }
  }

  /**
   * 检查UI操作是否支持
   */
  static isUIAction(preset: string): boolean {
    const uiActions = [
      'showMessage',
      'showConfirm', 
      'toggleComponent',
      'toggleVisibility',
      'copyText',
      'downloadFile',
      'openModal'
    ]
    return uiActions.includes(preset)
  }
}
