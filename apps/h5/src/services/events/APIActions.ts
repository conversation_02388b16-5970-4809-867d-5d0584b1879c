/**
 * API操作处理器
 * 负责处理需要调用后端API的操作（设备刷新、数据同步等）
 */

import { showToast } from 'vant'
import { RenewDeviceDetails } from '../../utils/index'

export interface APIActionConfig {
  preset: string
  message?: string
  deviceId?: string
  params?: Record<string, any>
  onSuccess?: any
  onError?: any
  // PC端配置格式支持
  onSuccessAction?: 'none' | 'navigate' | 'refresh'
  successTarget?: string
  onErrorAction?: 'none' | 'navigate' | 'refresh'
  errorTarget?: string
}

export class APIActions {
  /**
   * 执行API操作
   */
  async executeAPIAction(
    config: APIActionConfig, 
    data: any, 
    component: any,
    eventHandler?: any
  ): Promise<void> {
    console.log(`🔌 执行API操作: ${config.preset}`, config)

    switch (config.preset) {
      case 'refreshDevice':
        await this.refreshDevice(config, data, component, eventHandler)
        break
        
      case 'syncData':
        await this.syncData(config, data, component, eventHandler)
        break
        
      case 'updateStatus':
        await this.updateStatus(config, data, component, eventHandler)
        break

      default:
        console.warn(`⚠️ 未知API操作: ${config.preset}`)
        showToast(`不支持的API操作: ${config.preset}`)
    }
  }

  /**
   * 刷新设备信息
   */
  private async refreshDevice(
    config: APIActionConfig,
    data: any,
    component: any,
    eventHandler?: any
  ): Promise<void> {
    const loadingMessage = config.message || '正在刷新设备信息...'
    showToast(loadingMessage)
    
    try {
      console.log('🔄 开始刷新设备信息...', {
        deviceId: config.deviceId || data?.deviceId,
        params: config.params
      })
      
      // 调用设备刷新API
      await RenewDeviceDetails()
      
      console.log('✅ 设备信息刷新成功')
      showToast('设备信息刷新成功')
      
      // 执行成功回调
      await this.executeSuccessCallback(config, data, component, eventHandler)
      
    } catch (error) {
      console.error('❌ 设备信息刷新失败:', error)
      showToast('刷新设备信息失败')

      // 执行失败回调
      await this.executeErrorCallback(config, data, component, eventHandler, error)
    }
  }

  /**
   * 同步数据
   */
  private async syncData(
    config: APIActionConfig,
    data: any,
    component: any,
    eventHandler?: any
  ): Promise<void> {
    const loadingMessage = config.message || '正在同步数据...'
    showToast(loadingMessage)
    
    try {
      console.log('🔄 开始同步数据...', {
        params: config.params,
        data
      })
      
      // TODO: 实现数据同步逻辑
      // await syncDataAPI(config.params)
      
      console.log('✅ 数据同步成功')
      showToast('数据同步成功')
      
      // 执行成功回调
      if (config.onSuccess && eventHandler && typeof eventHandler.handleCustomEvent === 'function') {
        await eventHandler.handleCustomEvent(config.onSuccess, data, component)
      }
      
    } catch (error) {
      console.error('❌ 数据同步失败:', error)
      showToast('数据同步失败')
      
      // 执行失败回调
      if (config.onError && eventHandler && typeof eventHandler.handleCustomEvent === 'function') {
        await eventHandler.handleCustomEvent(config.onError, {
          ...data,
          error: error instanceof Error ? error.message : '未知错误'
        }, component)
      }
    }
  }

  /**
   * 更新状态
   */
  private async updateStatus(
    config: APIActionConfig,
    data: any,
    component: any,
    eventHandler?: any
  ): Promise<void> {
    const loadingMessage = config.message || '正在更新状态...'
    showToast(loadingMessage)
    
    try {
      console.log('🔄 开始更新状态...', {
        params: config.params,
        data
      })
      
      // TODO: 实现状态更新逻辑
      // await updateStatusAPI(config.params)
      
      console.log('✅ 状态更新成功')
      showToast('状态更新成功')
      
      // 执行成功回调
      if (config.onSuccess && eventHandler && typeof eventHandler.handleCustomEvent === 'function') {
        await eventHandler.handleCustomEvent(config.onSuccess, data, component)
      }

    } catch (error) {
      console.error('❌ 状态更新失败:', error)
      showToast('状态更新失败')

      // 执行失败回调
      if (config.onError && eventHandler && typeof eventHandler.handleCustomEvent === 'function') {
        await eventHandler.handleCustomEvent(config.onError, {
          ...data,
          error: error instanceof Error ? error.message : '未知错误'
        }, component)
      }
    }
  }

  /**
   * 执行成功回调
   */
  private async executeSuccessCallback(
    config: APIActionConfig,
    data: any,
    component: any,
    eventHandler?: any
  ): Promise<void> {
    // 优先处理PC端配置格式
    if (config.onSuccessAction !== undefined) {
      console.log('🎉 执行成功回调 (onSuccessAction):', config.onSuccessAction)

      switch (config.onSuccessAction) {
        case 'navigate':
          if (config.successTarget && eventHandler) {
            const navigationConfig = {
              type: 'navigate',
              navigateType: 'page',
              target: config.successTarget
            }
            console.log('🔗 执行导航:', navigationConfig)
            await eventHandler.handleCustomEvent(navigationConfig, data, component)
          } else {
            console.warn('⚠️ 导航配置不完整，缺少目标地址或事件处理器')
          }
          break

        case 'refresh':
          console.log('🔄 刷新页面')
          window.location.reload()
          break

        case 'none':
        default:
          console.log('ℹ️ 仅显示成功消息，无额外操作')
          break
      }
      return
    }

    // 处理完整的事件配置格式
    if (config.onSuccess && eventHandler && typeof eventHandler.handleCustomEvent === 'function') {
      console.log('🎉 执行成功回调 (onSuccess):', config.onSuccess)
      await eventHandler.handleCustomEvent(config.onSuccess, data, component)
      return
    }

    console.log('ℹ️ 无成功回调配置')
  }

  /**
   * 执行失败回调
   */
  private async executeErrorCallback(
    config: APIActionConfig,
    data: any,
    component: any,
    eventHandler?: any,
    error?: any
  ): Promise<void> {
    const errorData = {
      ...data,
      error: error instanceof Error ? error.message : '未知错误'
    }

    // 处理新格式的回调配置
    if (config.onError && eventHandler && typeof eventHandler.handleCustomEvent === 'function') {
      console.log('❌ 执行失败回调 (onError):', config.onError)
      await eventHandler.handleCustomEvent(config.onError, errorData, component)
      return
    }

    console.log('ℹ️ 无失败回调配置')
  }

  /**
   * 检查API操作是否支持
   */
  static isAPIAction(preset: string): boolean {
    const apiActions = [
      'refreshDevice',
      'syncData',
      'updateStatus'
    ]
    return apiActions.includes(preset)
  }

  /**
   * 获取支持的API操作列表
   */
  static getSupportedActions(): string[] {
    return [
      'refreshDevice',
      'syncData', 
      'updateStatus'
    ]
  }

  /**
   * 验证API操作配置
   */
  static validateConfig(config: APIActionConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!config.preset) {
      errors.push('preset 不能为空')
    }
    
    if (!this.isAPIAction(config.preset)) {
      errors.push(`不支持的API操作: ${config.preset}`)
    }
    
    // 特定操作的验证
    switch (config.preset) {
      case 'refreshDevice':
        // 设备刷新暂时不需要额外验证
        break
        
      case 'syncData':
        if (!config.params) {
          errors.push('syncData 操作需要 params 参数')
        }
        break
        
      case 'updateStatus':
        if (!config.params || !config.params.status) {
          errors.push('updateStatus 操作需要 params.status 参数')
        }
        break
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
}
