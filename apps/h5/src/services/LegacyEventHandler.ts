/**
 * 事件处理服务（兼容版本）
 * 为了向后兼容，保留原有接口，内部使用新的模块化架构
 */

import type { Router } from 'vue-router'
import { EventHandlerService as NewEventHandlerService } from './events/EventHandlerService'

/**
 * 事件处理服务（兼容包装器）
 * 保持原有接口不变，内部委托给新的模块化服务
 */
export class EventHandlerService {
  private newEventHandler: NewEventHandlerService

  constructor(router: Router) {
    this.newEventHandler = new NewEventHandlerService(router)
  }

  /**
   * 处理组件事件（保持原有接口）
   */
  async handleComponentEvent(comp: any, eventName: string, data: any): Promise<void> {
    return this.newEventHandler.handleComponentEvent(comp, eventName, data)
  }
}
