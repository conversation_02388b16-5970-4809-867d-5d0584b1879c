/**
 * 应用数据管理器
 * 统一管理应用配置和页面配置，避免重复请求
 */

import { ref, reactive } from 'vue'
import { request } from '../utils/request'

// 应用基础信息（轻量级）
export interface AppBasicInfo {
  id: string
  name: string
  appType: string
  description?: string
  icon?: string
  published: boolean
}

// 应用完整配置（包含页面列表）
export interface AppFullConfig extends AppBasicInfo {
  defaultHomePage?: string
  tabBar?: any
  pageConfigs?: PageBasicInfo[]
}

// 页面基础信息
export interface PageBasicInfo {
  id: string
  name: string
  path: string
  title: string
  published: boolean
}

// 页面完整配置
export interface PageFullConfig extends PageBasicInfo {
  layout?: any
  style?: any
  components: any[]
  dataSource?: any
  events?: any
}

class AppDataManager {
  // 应用基础信息缓存（长期缓存）
  private appBasicCache = new Map<string, { data: AppBasicInfo; timestamp: number }>()

  // 应用完整配置缓存（中期缓存）
  private appFullCache = new Map<string, { data: AppFullConfig; timestamp: number }>()

  // 页面配置缓存（短期缓存）
  private pageConfigCache = new Map<string, { data: PageFullConfig; timestamp: number }>()

  // 🎯 请求去重：正在进行的请求Promise缓存
  private pendingBasicRequests = new Map<string, Promise<AppBasicInfo | null>>()
  private pendingFullRequests = new Map<string, Promise<AppFullConfig | null>>()
  private pendingPageRequests = new Map<string, Promise<PageFullConfig | null>>()

  // 缓存时间配置
  private readonly CACHE_TTL = {
    APP_BASIC: 30 * 60 * 1000,    // 30分钟 - 应用基础信息很少变化
    APP_FULL: 10 * 60 * 1000,     // 10分钟 - 应用配置偶尔变化
    PAGE_CONFIG: 5 * 60 * 1000,   // 5分钟 - 页面配置可能经常变化
  }

  /**
   * 获取应用基础信息（用于验证AppID）
   * 优先使用缓存，减少重复请求
   */
  async getAppBasicInfo(appId: string, forceRefresh = false): Promise<AppBasicInfo | null> {
    const cacheKey = `app_basic_${appId}`

    // 检查缓存
    if (!forceRefresh) {
      const cached = this.appBasicCache.get(cacheKey)
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL.APP_BASIC) {
        console.log('📦 使用缓存的应用基础信息:', appId)
        return cached.data
      }
    }

    // 🎯 智能复用：如果有完整配置的请求在进行中，复用它
    if (this.pendingFullRequests.has(appId)) {
      console.log('🔄 复用进行中的应用完整配置请求获取基础信息:', appId)
      const fullConfig = await this.pendingFullRequests.get(appId)!
      if (fullConfig) {
        // 从完整配置中提取基础信息并缓存
        const basicInfo: AppBasicInfo = {
          id: fullConfig.id,
          name: fullConfig.name,
          appType: fullConfig.appType,
          description: fullConfig.description,
          icon: fullConfig.icon,
          published: fullConfig.published
        }

        this.appBasicCache.set(cacheKey, {
          data: basicInfo,
          timestamp: Date.now()
        })

        return basicInfo
      }
    }

    // 🎯 请求去重：如果已有相同请求在进行中，直接返回该Promise
    if (this.pendingBasicRequests.has(appId)) {
      console.log('🔄 复用进行中的应用基础信息请求:', appId)
      return this.pendingBasicRequests.get(appId)!
    }

    // 创建请求Promise
    const requestPromise = this.fetchAppBasicInfo(appId)

    // 缓存请求Promise
    this.pendingBasicRequests.set(appId, requestPromise)

    try {
      const result = await requestPromise
      return result
    } catch (error) {
      // 🎯 重要：这里不要简单地 return null，而是重新抛出错误
      // 让 validateAppIdWithErrorHandling 方法根据错误信息进行分类处理
      throw error
    } finally {
      // 请求完成后清除pending状态
      this.pendingBasicRequests.delete(appId)
    }
  }

  /**
   * 实际的应用基础信息请求方法
   */
  private async fetchAppBasicInfo(appId: string): Promise<AppBasicInfo | null> {
    try {
      console.log('🌐 请求应用基础信息:', appId)
      const response = await request.publicGet(`/app/${appId}`)

      // 处理响应数据结构
      const result = response.data
      const appData = result.data || result

      console.log('🔍 应用基础数据结构:', appData)

      const basicInfo: AppBasicInfo = {
        id: appData.id,
        name: appData.name,
        appType: appData.appType || 'device',
        description: appData.description,
        icon: appData.icon,
        published: appData.published
      }

      // 缓存基础信息
      const cacheKey = `app_basic_${appId}`
      this.appBasicCache.set(cacheKey, {
        data: basicInfo,
        timestamp: Date.now()
      })

      console.log('✅ 应用基础信息获取成功:', appId)
      return basicInfo
    } catch (error: any) {
      // 📝 重要：这里不要 return null，而是将错误信息传递给上层处理
      // 获取服务端返回的具体错误信息
      const errorMessage = error.response?.data?.message || error.message
      console.warn('❌ 应用基础信息获取失败:', appId, '-', errorMessage)
      
      // 重新抛出错误，让上层方法根据错误信息进行分类处理
      throw new Error(errorMessage)
    }
  }

  /**
   * 获取应用完整配置（包含页面列表）
   * 仅在需要时请求，避免不必要的数据传输
   */
  async getAppFullConfig(appId: string, forceRefresh = false): Promise<AppFullConfig | null> {
    const cacheKey = `app_full_${appId}`

    // 检查缓存
    if (!forceRefresh) {
      const cached = this.appFullCache.get(cacheKey)
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL.APP_FULL) {
        console.log('📦 使用缓存的应用完整配置:', appId)
        return cached.data
      }
    }

    // 🎯 智能复用：如果有基础信息请求在进行中，等待完成后再发起完整请求
    // 这样可以避免同时发起两个请求到同一个接口
    if (this.pendingBasicRequests.has(appId)) {
      console.log('🔄 等待基础信息请求完成后获取完整配置:', appId)
      await this.pendingBasicRequests.get(appId)!
      // 基础信息请求完成后，检查是否已经有完整配置的缓存
      const cached = this.appFullCache.get(cacheKey)
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL.APP_FULL) {
        console.log('📦 基础信息请求完成后使用缓存的完整配置:', appId)
        return cached.data
      }
    }

    // 🎯 请求去重：如果已有相同请求在进行中，直接返回该Promise
    if (this.pendingFullRequests.has(appId)) {
      console.log('🔄 复用进行中的应用完整配置请求:', appId)
      return this.pendingFullRequests.get(appId)!
    }

    // 创建请求Promise
    const requestPromise = this.fetchAppFullConfig(appId)

    // 缓存请求Promise
    this.pendingFullRequests.set(appId, requestPromise)

    try {
      const result = await requestPromise

      // 🎯 同时缓存基础信息，避免后续的基础信息请求
      if (result) {
        const basicInfo: AppBasicInfo = {
          id: result.id,
          name: result.name,
          appType: result.appType,
          description: result.description,
          icon: result.icon,
          published: result.published
        }

        const basicCacheKey = `app_basic_${appId}`
        this.appBasicCache.set(basicCacheKey, {
          data: basicInfo,
          timestamp: Date.now()
        })
        console.log('📦 同时缓存了应用基础信息:', appId)
      }

      return result
    } finally {
      // 请求完成后清除pending状态
      this.pendingFullRequests.delete(appId)
    }
  }

  /**
   * 实际的应用完整配置请求方法
   */
  private async fetchAppFullConfig(appId: string): Promise<AppFullConfig | null> {
    try {
      console.log('🌐 请求应用完整配置:', appId)

      // 只请求应用信息，因为它已经包含了pageConfigs
      const appResponse = await request.publicGet(`/app/${appId}`)

      // 处理应用数据
      const appResult = appResponse.data
      const appData = appResult.data || appResult

      console.log('🔍 应用完整数据结构:', appData)
      console.log('🔍 应用包含的页面配置:', appData.pageConfigs?.length || 0, '个页面')

      const fullConfig: AppFullConfig = {
        id: appData.id,
        name: appData.name,
        appType: appData.appType || 'device',
        description: appData.description,
        icon: appData.icon,
        published: appData.published,
        defaultHomePage: appData.defaultHomePage,
        tabBar: appData.tabBar,
        pageConfigs: Array.isArray(appData.pageConfigs) ? appData.pageConfigs.map((page: any) => ({
          id: page.id,
          name: page.name,
          path: page.path,
          title: page.title,
          published: page.published
        })) : []
      }

      // 缓存完整配置
      const cacheKey = `app_full_${appId}`
      this.appFullCache.set(cacheKey, {
        data: fullConfig,
        timestamp: Date.now()
      })

      console.log('✅ 应用完整配置获取成功:', appId, `包含${fullConfig.pageConfigs?.length || 0}个页面`)
      return fullConfig
    } catch (error: any) {
      console.warn('❌ 应用完整配置获取失败:', appId, error.message)
      return null
    }
  }

  /**
   * 获取页面配置
   * 独立缓存，避免重复请求
   */
  async getPageConfig(pageId: string, forceRefresh = false): Promise<PageFullConfig | null> {
    const cacheKey = `page_${pageId}`

    // 检查缓存
    if (!forceRefresh) {
      const cached = this.pageConfigCache.get(cacheKey)
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL.PAGE_CONFIG) {
        console.log('📦 使用缓存的页面配置:', pageId)
        return cached.data
      }
    }

    // 🎯 请求去重：如果已有相同请求在进行中，直接返回该Promise
    if (this.pendingPageRequests.has(pageId)) {
      console.log('🔄 复用进行中的页面配置请求:', pageId)
      return this.pendingPageRequests.get(pageId)!
    }

    // 创建请求Promise
    const requestPromise = this.fetchPageConfig(pageId)

    // 缓存请求Promise
    this.pendingPageRequests.set(pageId, requestPromise)

    try {
      const result = await requestPromise
      return result
    } catch (error) {
      // 🔧 修复：抛出异常，让上层处理
      throw error
    } finally {
      // 请求完成后清除pending状态
      this.pendingPageRequests.delete(pageId)
    }
  }

  /**
   * 实际的页面配置请求方法
   */
  private async fetchPageConfig(pageId: string): Promise<PageFullConfig | null> {
    try {
      console.log('🌐 请求页面配置:', pageId)
      const response = await request.publicGet(`/page/${pageId}`)

      // 处理响应数据结构
      const result = response.data
      const pageData = result.data || result

      console.log('🔍 页面配置数据结构:', pageData)

      const pageConfig: PageFullConfig = {
        id: pageData.id,
        name: pageData.name,
        path: pageData.path,
        title: pageData.title,
        published: pageData.published,
        layout: pageData.layout,
        style: pageData.style,
        components: pageData.components || [],
        dataSource: pageData.dataSource,
        events: pageData.events
      }

      // 缓存页面配置
      const cacheKey = `page_${pageId}`
      this.pageConfigCache.set(cacheKey, {
        data: pageConfig,
        timestamp: Date.now()
      })

      console.log('✅ 页面配置获取成功:', pageId)
      return pageConfig
    } catch (error: any) {
      console.warn('❌ 页面配置获取失败:', pageId, error.message)
      // 🔧 修复：抛出异常而不是返回null，保留错误信息
      throw error
    }
  }

  /**
   * 获取页面配置并处理错误跳转
   */
  async getPageConfigWithErrorHandling(pageId: string): Promise<{
    config: PageFullConfig | null;
    errorType?: string;
    errorMessage?: string;
    errorDetails?: any;
  }> {
    try {
      const pageConfig = await this.getPageConfig(pageId)

      if (!pageConfig) {
        return {
          config: null,
          errorType: 'not-found',
          errorMessage: '页面不存在，请检查页面ID是否正确'
        }
      }

      return { config: pageConfig }
    } catch (error: any) {
      console.log('🔍 页面配置获取错误详情:', error)
      console.log('🔍 错误响应结构:', error.response)

      // 🔧 优化：处理服务端返回的详细错误信息
      // NestJS的NotFoundException会将自定义响应放在error.response.data中
      const errorResponse = error.response?.data
      const errorMessage = errorResponse?.message || error.message
      const errorType = errorResponse?.errorType

      console.log('🔍 解析的错误信息:', { errorResponse, errorMessage, errorType })

      // 如果服务端返回了错误类型，直接使用
      if (errorType) {
        console.log('✅ 使用服务端返回的错误类型:', errorType)
        return {
          config: null,
          errorType: errorType,
          errorMessage: errorMessage,
          errorDetails: errorResponse
        }
      }

      // 兜底：根据错误信息判断具体的错误类型
      if (errorMessage.includes('页面存在但未发布')) {
        return {
          config: null,
          errorType: 'page-not-published',
          errorMessage: errorMessage,
          errorDetails: errorResponse
        }
      } else if (errorMessage.includes('应用未发布')) {
        return {
          config: null,
          errorType: 'app-not-published',
          errorMessage: errorMessage,
          errorDetails: errorResponse
        }
      } else {
        return {
          config: null,
          errorType: 'not-found',
          errorMessage: errorMessage,
          errorDetails: errorResponse
        }
      }
    }
  }

  /**
   * 验证AppID是否有效（轻量级检查）
   */
  async validateAppId(appId: string): Promise<boolean> {
    try {
      const basicInfo = await this.getAppBasicInfo(appId)
      return basicInfo !== null && basicInfo.published
    } catch (error) {
      // 任何错误都视为无效
      return false
    }
  }

  /**
   * 验证AppID并处理错误跳转
   */
  async validateAppIdWithErrorHandling(appId: string): Promise<{ isValid: boolean; errorType?: string; errorMessage?: string }> {
    try {
      const basicInfo = await this.getAppBasicInfo(appId)

      if (!basicInfo) {
        return {
          isValid: false,
          errorType: 'not-found',
          errorMessage: '应用不存在，请检查应用ID是否正确'
        }
      }

      if (!basicInfo.published) {
        return {
          isValid: false,
          errorType: 'not-published',
          errorMessage: '应用存在但未发布，请联系管理员发布应用'
        }
      }

      return { isValid: true }
    } catch (error: any) {
      // 🎯 根据服务端返回的具体错误信息进行分类
      const errorMessage = error.message || '未知错误'
      console.log('🔍 错误信息分类:', errorMessage)

      // 根据服务端的错误消息进行精确分类
      if (errorMessage.includes('应用存在但未发布')) {
        return {
          isValid: false,
          errorType: 'not-published',
          errorMessage: errorMessage
        }
      } else if (errorMessage.includes('页面存在但未发布')) {
        return {
          isValid: false,
          errorType: 'page-not-published',
          errorMessage: errorMessage
        }
      } else if (errorMessage.includes('页面所属应用未发布')) {
        return {
          isValid: false,
          errorType: 'app-not-published',
          errorMessage: errorMessage
        }
      } else if (errorMessage.includes('应用不存在') || errorMessage.includes('页面不存在')) {
        return {
          isValid: false,
          errorType: 'not-found',
          errorMessage: errorMessage
        }
      } else {
        // 其他未知错误，默认为不存在
        return {
          isValid: false,
          errorType: 'not-found',
          errorMessage: errorMessage
        }
      }
    }
  }

  /**
   * 清除缓存
   */
  clearCache(type?: 'app' | 'page' | 'all') {
    switch (type) {
      case 'app':
        this.appBasicCache.clear()
        this.appFullCache.clear()
        this.pendingBasicRequests.clear()
        this.pendingFullRequests.clear()
        break
      case 'page':
        this.pageConfigCache.clear()
        this.pendingPageRequests.clear()
        break
      default:
        this.appBasicCache.clear()
        this.appFullCache.clear()
        this.pageConfigCache.clear()
        this.pendingBasicRequests.clear()
        this.pendingFullRequests.clear()
        this.pendingPageRequests.clear()
    }
    console.log('🗑️ 缓存已清除:', type || 'all')
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return {
      cache: {
        appBasic: this.appBasicCache.size,
        appFull: this.appFullCache.size,
        pageConfig: this.pageConfigCache.size,
      },
      pending: {
        appBasic: this.pendingBasicRequests.size,
        appFull: this.pendingFullRequests.size,
        pageConfig: this.pendingPageRequests.size,
      },
      total: this.appBasicCache.size + this.appFullCache.size + this.pageConfigCache.size
    }
  }
}

// 导出单例实例
export const appDataManager = new AppDataManager()
export default appDataManager
