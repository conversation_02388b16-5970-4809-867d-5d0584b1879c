/**
 * Services 统一导出
 * 提供所有服务的统一入口
 */

// 核心服务
export { AppIdResolver, appIdResolver, resolveAppId, validateAppId, getAppDefaultPage } from './AppIdResolver'
export { default as RouteResolver, routeResolver } from './RouteResolver'
export { default as RequestManager, requestManager } from './RequestManager'

// 页面相关服务
export { default as PageConfigService } from './PageConfigService'

// 事件处理服务
export { EventHandlerService } from './LegacyEventHandler'
export { EventHandlerService as ModularEventHandlerService } from './events/EventHandlerService'
export { NavigationHandler } from './events/NavigationHandler'
export { CustomEventHandler } from './events/CustomEventHandler'
export { PresetActions } from './events/PresetActions'
export { UIActions } from './events/UIActions'
export { APIActions } from './events/APIActions'

// 工具类
export { PathResolver } from './utils/PathResolver'

// 类型定义
export type { EventConfig, EventData } from './events/EventHandlerService'
export type { NavigationConfig } from './events/NavigationHandler'
export type { CustomEventConfig } from './events/CustomEventHandler'
export type { PresetActionConfig } from './events/PresetActions'
export type { UIActionConfig } from './events/UIActions'
export type { APIActionConfig } from './events/APIActions'
export type { PathInfo } from './utils/PathResolver'

// 创建服务实例的工厂函数
import type { Router } from 'vue-router'
import { EventHandlerService as LegacyEventHandlerService } from './LegacyEventHandler'
import { EventHandlerService as ModularEventHandlerService } from './events/EventHandlerService'
import { NavigationHandler } from './events/NavigationHandler'
import { CustomEventHandler } from './events/CustomEventHandler'
import { PresetActions } from './events/PresetActions'
import { PathResolver } from './utils/PathResolver'

/**
 * 创建事件处理服务实例（兼容版本）
 */
export function createEventHandlerService(router: Router) {
  return new LegacyEventHandlerService(router)
}

/**
 * 创建模块化事件处理服务实例
 */
export function createModularEventHandlerService(router: Router) {
  return new ModularEventHandlerService(router)
}

/**
 * 创建导航处理器实例
 */
export function createNavigationHandler(router: Router) {
  return new NavigationHandler(router)
}

/**
 * 创建自定义事件处理器实例
 */
export function createCustomEventHandler(router: Router) {
  return new CustomEventHandler(router)
}

/**
 * 创建路径解析器实例
 */
export function createPathResolver(router: Router) {
  return new PathResolver(router)
}

/**
 * 创建预设操作处理器实例
 */
export function createPresetActions() {
  return new PresetActions()
}
