/**
 * 页面配置服务
 * 负责页面配置的加载、处理和样式计算
 */

import { computed, type Ref } from 'vue'
import { usePageConfigStore } from '../stores/pageConfig'
import type { PageConfig } from '@lowcode/aslib/core'
import { appIdResolver, type AppInfo } from './AppIdResolver'

export class PageConfigService {
  private pageConfigStore = usePageConfigStore()
  private configCache = new Map<string, PageConfig>()
  private cacheExpiry = new Map<string, number>()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  // 加载页面配置
  async loadPageConfig(appId: string, pagePath: string, forceReload = false): Promise<PageConfig> {
    if (!appId) {
      throw new Error('缺少应用ID参数')
    }

    try {
      // 1. 解析AppID获取应用信息
      console.log('🔍 解析AppID:', appId, 'pagePath:', pagePath)
      const appInfo = await appIdResolver.resolveAppId(appId, pagePath)
      console.log('✅ AppID解析成功:', appInfo)

      // 2. 构建页面ID
      const pageId = this.buildPageId(appInfo, pagePath)
      console.log('📝 构建页面ID:', pageId)

      // 3. 检查缓存
      if (!forceReload && this.isCacheValid(pageId)) {
        const cachedConfig = this.configCache.get(pageId)!
        console.log('📦 使用缓存的页面配置:', pageId)
        return cachedConfig
      }

      // 4. 从服务器加载配置
      console.log('🔄 从服务器加载页面配置:', pageId)
      const config = await this.pageConfigStore.getPageConfig(pageId)

      if (!config) {
        // 如果没有找到配置，尝试加载默认配置
        console.log('⚠️ 未找到页面配置，尝试加载默认配置')
        return this.loadDefaultPageConfig(appInfo)
      }

      // 5. 增强配置信息
      const enhancedConfig = this.enhanceConfigWithAppInfo(config, appInfo)

      // 6. 缓存配置
      this.cacheConfig(pageId, enhancedConfig)

      // 调试：打印页面配置信息
      console.log('📄 页面布局配置:', enhancedConfig.layout)
      console.log('🎨 页面配置信息:', enhancedConfig)

      // 调试：打印组件样式信息
      enhancedConfig.components?.forEach(comp => {
        if (comp.style) {
          console.log(`🎨 组件 ${comp.type} 样式:`, comp.style)
        }
    })

      return enhancedConfig
    } catch (error) {
      console.error('❌ 页面配置加载失败:', error)
      throw new Error(`页面配置加载失败: ${error.message}`)
    }
  }

  // 检查缓存是否有效
  private isCacheValid(pageId: string): boolean {
    if (!this.configCache.has(pageId)) {
      return false
    }

    const expiry = this.cacheExpiry.get(pageId)
    if (!expiry || Date.now() > expiry) {
      // 缓存过期，清理
      this.configCache.delete(pageId)
      this.cacheExpiry.delete(pageId)
      return false
    }

    return true
  }

  // 缓存配置
  private cacheConfig(pageId: string, config: PageConfig): void {
    this.configCache.set(pageId, config)
    this.cacheExpiry.set(pageId, Date.now() + this.CACHE_DURATION)
  }

  // 清理缓存
  clearCache(pageId?: string): void {
    if (pageId) {
      this.configCache.delete(pageId)
      this.cacheExpiry.delete(pageId)
    } else {
      this.configCache.clear()
      this.cacheExpiry.clear()
    }
  }

  // 计算布局样式
  computeLayoutStyle(pageConfig: Ref<PageConfig | null>) {
    return computed(() => {
      if (!pageConfig.value?.layout) return {}

      const layout = pageConfig.value.layout
      return {
        display: layout.type === 'flex' ? 'flex' : 'block',
        flexDirection: layout.direction || 'column',
        padding: layout.padding ? `${layout.padding}px` : '16px',
        gap: layout.gap ? `${layout.gap}px` : '16px'
      }
    })
  }

  // 计算页面样式
  computePageStyle(pageConfig: Ref<PageConfig | null>) {
    return computed(() => {
      if (!pageConfig.value?.style) return {}

      const pageStyle = pageConfig.value.style
      console.log('🎨 应用页面样式:', pageStyle)

      return pageStyle
    })
  }

  // 合并页面样式和布局样式
  computeCombinedStyle(layoutStyle: any, pageStyle: any) {
    return computed(() => {
      const combined = {
        ...layoutStyle.value,
        ...pageStyle.value
      }

      // 确保基本样式
      if (!combined.minHeight) {
        combined.minHeight = '100vh'
      }
      // 只有在没有任何背景样式时才设置默认背景色
      if (!combined.backgroundColor && !combined.backgroundImage) {
        combined.backgroundColor = '#f5f5f5'
      }

      return combined
    })
  }

  // 获取组件事件处理器
  getComponentEventHandlers(comp: any, eventHandler: (comp: any, eventName: string, data: any) => void) {
    // 暂时保持原有逻辑，确保事件处理正常工作
    const handlers: Record<string, Function> = {}

    if (comp.events) {
      Object.keys(comp.events).forEach(eventName => {
        handlers[eventName] = (data: any) => {
          console.log(`🎯 触发事件: ${eventName}`, data)
          eventHandler(comp, eventName, data)
        }
      })
    }

    return handlers
  }

  // 验证页面配置
  validatePageConfig(config: PageConfig): boolean {
    if (!config) {
      console.error('❌ 页面配置为空')
      return false
    }

    if (!config.components || config.components.length === 0) {
      console.warn('⚠️ 页面没有配置组件')
      return true // 空页面也是有效的
    }

    // 验证组件配置
    for (const comp of config.components) {
      if (!comp.type) {
        console.error('❌ 组件缺少类型定义:', comp)
        return false
      }
      if (!comp.id) {
        console.error('❌ 组件缺少ID:', comp)
        return false
      }
    }

    return true
  }

  // 处理页面配置错误
  handleConfigError(error: any): string {
    console.error('❌ 页面配置加载失败:', error)

    if (error.message?.includes('404')) {
      return '页面不存在'
    } else if (error.message?.includes('网络')) {
      return '网络连接失败，请检查网络设置'
    } else {
      return error.message || '页面加载失败，请重试'
    }
  }

  /**
   * 构建页面ID
   */
  private buildPageId(appInfo: AppInfo, pagePath: string): string {
    // 🎯 智能提取页面名称
    let normalizedPath: string

    if (pagePath === '/') {
      normalizedPath = 'home'
    } else if (pagePath.startsWith('/app/')) {
      // 如果是 /app/appId/pageName 格式，提取最后的页面名称
      const parts = pagePath.split('/')
      normalizedPath = parts[parts.length - 1] || 'home'
    } else {
      // 🎯 智能提取页面名称：取路径的最后一部分
      const parts = pagePath.split('/').filter(part => part.length > 0)
      normalizedPath = parts[parts.length - 1] || 'home'
      console.log('🎯 智能路径解析:', { pagePath, parts, normalizedPath })
    }

    console.log('🔧 构建页面ID:', {
      appId: appInfo.appId,
      appType: appInfo.appType,
      pagePath,
      normalizedPath
    })

    // 使用应用特定的页面ID格式
    const pageId = `${appInfo.appId}_${normalizedPath}`

    console.log('✅ 最终页面ID:', pageId)

    // 🔍 调试：检查当前存储的AppID
    const storedAppId = localStorage.getItem('current-app-id')
    console.log('🔍 当前存储的AppID:', storedAppId, '构建页面ID使用的AppID:', appInfo.appId)

    return pageId
  }

  /**
   * 加载默认页面配置
   */
  private async loadDefaultPageConfig(appInfo: AppInfo): Promise<PageConfig> {
    console.log('🔧 生成默认页面配置:', appInfo.appType)

    // 根据应用类型生成默认配置
    const defaultConfig: PageConfig = {
      id: `default_${appInfo.appType}`,
      name: `${appInfo.application.name}默认页面`,
      layout: {
        type: 'flex',
        direction: 'column',
        style: {
          minHeight: '100vh',
          backgroundColor: '#f5f5f5'
        }
      },
      components: [
        {
          id: 'default-header',
          type: 'Header',
          props: {
            title: appInfo.application.name,
            showBack: false
          },
          style: {
            backgroundColor: appInfo.application.color || '#1890ff',
            color: 'white'
          }
        },
        {
          id: 'default-content',
          type: 'Container',
          props: {
            text: `欢迎使用${appInfo.application.name}！\n\n这是一个默认页面，您可以在设计器中自定义页面内容。`
          },
          style: {
            padding: '2rem',
            textAlign: 'center',
            flex: 1
          }
        }
      ]
    }

    return defaultConfig
  }

  /**
   * 使用应用信息增强配置
   */
  private enhanceConfigWithAppInfo(config: PageConfig, appInfo: AppInfo): PageConfig {
    return {
      ...config,
      // 添加应用信息到配置中
      meta: {
        ...config.meta,
        appId: appInfo.appId,
        appType: appInfo.appType,
        applicationName: appInfo.application.name,
        targetPath: appInfo.targetPath
      }
    }
  }
}

// 默认导出
export default PageConfigService
