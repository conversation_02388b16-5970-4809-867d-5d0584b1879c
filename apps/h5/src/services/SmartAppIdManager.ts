/**
 * 智能AppID管理器
 * 
 * 负责AppID的智能管理，支持从URL参数自动更新本地缓存
 * 解决路由参数丢失问题，提供统一的AppID获取接口
 */

export interface AppIdChangeEvent {
  oldAppId: string
  newAppId: string
  source: 'url' | 'manual' | 'init'
}

export type AppIdChangeListener = (event: AppIdChangeEvent) => void

export class SmartAppIdManager {
  private static instance: SmartAppIdManager
  private readonly STORAGE_KEY = 'current-app-id'
  private readonly DEFAULT_APP_ID = ''  // 🎯 改为空字符串，不自动注入默认值
  private listeners: AppIdChangeListener[] = []

  private constructor() {
    // 私有构造函数，确保单例
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): SmartAppIdManager {
    if (!SmartAppIdManager.instance) {
      SmartAppIdManager.instance = new SmartAppIdManager()
    }
    return SmartAppIdManager.instance
  }

  /**
   * 初始化：页面加载时检查URL中的appid参数
   * 支持 appid 和 appId 两种参数名，兼容哈希路由
   */
  public initializeFromUrl(): { updated: boolean; appId: string; source: 'url' | 'localStorage' | 'default' } {
    // 详细日志：URL解析调试
    console.log('🔍 URL解析调试:', {
      href: window.location.href,
      hash: window.location.hash,
      search: window.location.search,
      pathname: window.location.pathname
    })
    
    // 处理哈希路由的查询参数
    let urlParams: URLSearchParams
    let hashQuery = ''
    
    if (window.location.hash && window.location.hash.includes('?')) {
      // 哈希路由模式：从 hash 中提取查询参数
      hashQuery = window.location.hash.split('?')[1]
      urlParams = new URLSearchParams(hashQuery)
      console.log('🔍 哈希路由模式，提取的查询参数:', hashQuery)
    } else {
      // 常规模式：从 search 中获取查询参数
      urlParams = new URLSearchParams(window.location.search)
      console.log('🔍 常规模式，查询参数:', window.location.search)
    }
    
    const urlAppId = urlParams.get('appid') || urlParams.get('appId')
    console.log('🔍 提取的AppID参数:', {
      appid: urlParams.get('appid'),
      appId: urlParams.get('appId'),
      final: urlAppId,
      allParams: Array.from(urlParams.entries())
    })
    
    // 🎯 如果直接没找到appId，尝试从redirect参数中解析
    let finalAppId = urlAppId
    if (!finalAppId) {
      const redirectParam = urlParams.get('redirect')
      if (redirectParam && redirectParam.includes('appid=')) {
        const redirectUrl = decodeURIComponent(redirectParam)
        console.log('🔍 检查redirect参数中的appId:', redirectUrl)
        
        const appidMatch = redirectUrl.match(/[?&]appid=([^&]*)/i)
        if (appidMatch) {
          finalAppId = appidMatch[1]
          console.log('🔍 从redirect参数中提取到appId:', finalAppId)
        }
      }
    }
    
    if (finalAppId) {
      console.log('🔄 从URL检测到appId，更新本地缓存:', finalAppId)
      const oldAppId = this.getCurrentAppId()
      this.setCurrentAppId(finalAppId, 'url')
      
      // 可选：清理URL参数，保持URL简洁
      this.cleanUrlParams()
      
      return { updated: true, appId: finalAppId, source: 'url' }
    }

    const currentAppId = this.getCurrentAppId()
    const source = localStorage.getItem(this.STORAGE_KEY) ? 'localStorage' : 'default'
    
    return { updated: false, appId: currentAppId, source }
  }

  /**
   * 获取当前appId（优先级：本地缓存 > 空字符串）
   * 🎯 返回空字符串表示没有有效的appId
   */
  public getCurrentAppId(): string {
    const stored = localStorage.getItem(this.STORAGE_KEY)
    return stored || this.DEFAULT_APP_ID  // 现在DEFAULT_APP_ID是空字符串
  }

  /**
   * 检查是否有有效的appId
   */
  public hasValidAppId(): boolean {
    const appId = this.getCurrentAppId()
    return !!appId && appId.trim() !== ''
  }

  /**
   * 设置appId到本地缓存
   */
  public setCurrentAppId(appId: string, source: 'url' | 'manual' | 'init' = 'manual'): void {
    const oldAppId = this.getCurrentAppId()
    
    if (oldAppId !== appId) {
      localStorage.setItem(this.STORAGE_KEY, appId)
      console.log(`✅ AppID已更新: ${oldAppId} → ${appId} (source: ${source})`)
      
      // 触发应用切换事件
      this.notifyAppChange({ oldAppId, newAppId: appId, source })
    }
  }

  /**
   * 检查当前URL是否包含appId参数，并自动更新
   * 返回是否发生了更新
   */
  public checkAndUpdateFromCurrentUrl(): boolean {
    console.log('🔍 主动检查URL中的AppID参数')
    const result = this.initializeFromUrl()
    
    if (result.updated) {
      console.log('✅ URL参数检查：AppID已更新', result.appId)
      // 触发变化事件，让所有监听者知道AppID已更新
      this.notifyAppChange({
        oldAppId: this.DEFAULT_APP_ID,
        newAppId: result.appId,
        source: 'url'
      })
    }
    
    return result.updated
  }

  /**
   * 清理URL参数（可选，保持URL简洁），兼容哈希路由
   */
  public cleanUrlParams(): void {
    if (typeof window !== 'undefined' && window.history) {
      try {
        let hasAppIdParam = false
        let newUrl = window.location.href
        
        if (window.location.hash && window.location.hash.includes('?')) {
          // 哈希路由模式
          const [hashPath, hashQuery] = window.location.hash.split('?')
          const params = new URLSearchParams(hashQuery)
          
          hasAppIdParam = params.has('appid') || params.has('appId')
          
          if (hasAppIdParam) {
            params.delete('appid')
            params.delete('appId')
            
            const newQuery = params.toString()
            const newHash = newQuery ? `${hashPath}?${newQuery}` : hashPath
            newUrl = `${window.location.origin}${window.location.pathname}${window.location.search}${newHash}`
          }
        } else {
          // 常规模式
          const url = new URL(window.location.href)
          hasAppIdParam = url.searchParams.has('appid') || url.searchParams.has('appId')
          
          if (hasAppIdParam) {
            url.searchParams.delete('appid')
            url.searchParams.delete('appId')
            newUrl = url.toString()
          }
        }
        
        if (hasAppIdParam) {
          window.history.replaceState({}, '', newUrl)
          console.log('🧹 已清理URL中的appId参数')
        }
      } catch (error) {
        console.warn('清理URL参数时出错:', error)
      }
    }
  }

  /**
   * 添加AppID变化监听器
   */
  public addChangeListener(listener: AppIdChangeListener): void {
    this.listeners.push(listener)
  }

  /**
   * 移除AppID变化监听器
   */
  public removeChangeListener(listener: AppIdChangeListener): void {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 通知AppID变化
   */
  private notifyAppChange(event: AppIdChangeEvent): void {
    this.listeners.forEach(listener => {
      try {
        listener(event)
      } catch (error) {
        console.error('AppID变化监听器执行出错:', error)
      }
    })
  }

  /**
   * 重置到默认AppID
   */
  public resetToDefault(): void {
    this.setCurrentAppId(this.DEFAULT_APP_ID, 'manual')
  }

  /**
   * 清除缓存的AppID
   */
  public clearCache(): void {
    const oldAppId = this.getCurrentAppId()
    localStorage.removeItem(this.STORAGE_KEY)
    console.log('🗑️ 已清除AppID缓存')
    
    // 触发重置事件
    this.notifyAppChange({ 
      oldAppId, 
      newAppId: this.DEFAULT_APP_ID, 
      source: 'manual' 
    })
  }

  /**
   * 获取调试信息
   */
  public getDebugInfo(): {
    currentAppId: string
    hasStoredValue: boolean
    listenersCount: number
    defaultAppId: string
  } {
    return {
      currentAppId: this.getCurrentAppId(),
      hasStoredValue: !!localStorage.getItem(this.STORAGE_KEY),
      listenersCount: this.listeners.length,
      defaultAppId: this.DEFAULT_APP_ID
    }
  }
}

// 导出单例实例
export const smartAppIdManager = SmartAppIdManager.getInstance()

// 导出便捷函数
export function getCurrentAppId(): string {
  return smartAppIdManager.getCurrentAppId()
}

export function hasValidAppId(): boolean {
  return smartAppIdManager.hasValidAppId()
}

export function setCurrentAppId(appId: string, source?: 'url' | 'manual' | 'init'): void {
  return smartAppIdManager.setCurrentAppId(appId, source)
}

export function initializeAppIdFromUrl(): { updated: boolean; appId: string; source: 'url' | 'localStorage' | 'default' } {
  return smartAppIdManager.initializeFromUrl()
}

export function addAppIdChangeListener(listener: AppIdChangeListener): void {
  return smartAppIdManager.addChangeListener(listener)
}

export function removeAppIdChangeListener(listener: AppIdChangeListener): void {
  return smartAppIdManager.removeChangeListener(listener)
}