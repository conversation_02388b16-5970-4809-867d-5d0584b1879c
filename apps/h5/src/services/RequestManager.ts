import { ref } from 'vue'

export interface RequestConfig {
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  params?: Record<string, any>
  data?: any
  headers?: Record<string, string>
  timeout?: number
  retry?: RetryConfig
  cache?: CacheConfig
}

export interface RetryConfig {
  maxRetries: number
  retryDelay: number
  backoffMultiplier: number
  retryCondition?: (error: any) => boolean
}

export interface CacheConfig {
  key: string
  ttl: number
  storage: 'memory' | 'localStorage' | 'sessionStorage'
}

interface CacheEntry {
  data: any
  expiry: number
  storage: string
}

interface QueuedRequest {
  id: string
  execute: () => Promise<any>
  priority: number
  timestamp: number
  resolve: (value: any) => void
  reject: (error: any) => void
}

class RequestQueue {
  private queue: QueuedRequest[] = []
  private running = 0
  private maxConcurrent: number
  
  constructor(options: { maxConcurrent: number }) {
    this.maxConcurrent = options.maxConcurrent
  }
  
  add<T>(executor: () => Promise<T>, priority = 0): Promise<T> {
    return new Promise((resolve, reject) => {
      const request: QueuedRequest = {
        id: this.generateId(),
        execute: executor,
        priority,
        timestamp: Date.now(),
        resolve,
        reject
      }
      
      this.queue.push(request)
      this.queue.sort((a, b) => {
        if (a.priority !== b.priority) {
          return b.priority - a.priority
        }
        return a.timestamp - b.timestamp
      })
      
      this.processQueue()
    })
  }
  
  private async processQueue() {
    if (this.running >= this.maxConcurrent || this.queue.length === 0) {
      return
    }
    
    const request = this.queue.shift()!
    this.running++
    
    try {
      const result = await request.execute()
      request.resolve(result)
    } catch (error) {
      request.reject(error)
    } finally {
      this.running--
      this.processQueue()
    }
  }
  
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }
}

class RetryManager {
  async execute<T>(
    operation: () => Promise<T>,
    config?: RetryConfig
  ): Promise<T> {
    const retryConfig = {
      maxRetries: 3,
      retryDelay: 1000,
      backoffMultiplier: 2,
      retryCondition: this.defaultRetryCondition,
      ...config
    }
    
    let lastError: Error
    
    for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error as Error
        
        if (
          attempt === retryConfig.maxRetries ||
          !retryConfig.retryCondition(error)
        ) {
          break
        }
        
        const delay = retryConfig.retryDelay * Math.pow(retryConfig.backoffMultiplier, attempt)
        await this.sleep(delay)
      }
    }
    
    throw lastError!
  }
  
  private defaultRetryCondition(error: any): boolean {
    return (
      error.message.includes('网络') ||
      error.message.includes('超时') ||
      error.message.includes('5') ||
      error.name === 'NetworkError' ||
      error.name === 'TimeoutError'
    )
  }
  
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

export class H5RequestManager {
  private cache = new Map<string, CacheEntry>()
  private pendingRequests = new Map<string, Promise<any>>()
  private requestQueue: RequestQueue
  private retryManager: RetryManager
  private isOnline = ref(navigator.onLine)
  private baseURL: string

  constructor(baseURL: string = '') {
    this.requestQueue = new RequestQueue({ maxConcurrent: 6 })
    this.retryManager = new RetryManager()
    this.baseURL = baseURL
    this.initNetworkHandling()
  }
  
  private initNetworkHandling() {
    window.addEventListener('online', () => {
      this.isOnline.value = true
    })
    
    window.addEventListener('offline', () => {
      this.isOnline.value = false
    })
  }
  
  // 统一请求方法
  async request<T>(config: RequestConfig): Promise<T> {
    const requestKey = this.generateRequestKey(config)
    
    // 检查缓存
    if (config.cache) {
      const cached = this.getFromCache<T>(config.cache.key)
      if (cached) {
        return cached
      }
    }
    
    // 检查是否有相同的请求正在进行
    if (this.pendingRequests.has(requestKey)) {
      return this.pendingRequests.get(requestKey)!
    }
    
    // 创建请求Promise
    const requestPromise = this.executeRequest<T>(config)
    this.pendingRequests.set(requestKey, requestPromise)
    
    try {
      const result = await requestPromise
      
      // 缓存结果
      if (config.cache) {
        this.setCache(config.cache.key, result, config.cache)
      }
      
      return result
    } finally {
      this.pendingRequests.delete(requestKey)
    }
  }
  
  // 执行请求
  private async executeRequest<T>(config: RequestConfig): Promise<T> {
    return this.requestQueue.add(async () => {
      if (!this.isOnline.value && !this.isOfflineCapable(config)) {
        throw new Error('网络连接不可用')
      }
      
      return this.retryManager.execute(
        () => this.performRequest<T>(config),
        config.retry
      )
    })
  }
  
  // 执行HTTP请求
  private async performRequest<T>(config: RequestConfig): Promise<T> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => {
      controller.abort()
    }, config.timeout || 10000)
    
    try {
      const response = await fetch(this.buildUrl(config), {
        method: config.method,
        headers: {
          'Content-Type': 'application/json',
          ...config.headers
        },
        body: config.data ? JSON.stringify(config.data) : undefined,
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      return await response.json()
    } catch (error) {
      clearTimeout(timeoutId)
      throw this.handleRequestError(error, config)
    }
  }
  
  // 批量请求
  async batchRequest<T>(configs: RequestConfig[]): Promise<T[]> {
    const batchable = this.canBatch(configs)
    if (batchable) {
      return this.executeBatchRequest<T>(configs)
    }
    
    return Promise.all(configs.map(config => this.request<T>(config)))
  }
  
  // 执行批量请求
  private async executeBatchRequest<T>(configs: RequestConfig[]): Promise<T[]> {
    const batchConfig: RequestConfig = {
      url: '/api/batch',
      method: 'POST',
      data: {
        requests: configs.map(config => ({
          url: config.url,
          method: config.method,
          params: config.params,
          data: config.data
        }))
      }
    }
    
    const response = await this.request<{ results: T[] }>(batchConfig)
    return response.results
  }
  
  // 缓存管理
  private getFromCache<T>(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry || Date.now() > entry.expiry) {
      this.cache.delete(key)
      return null
    }
    return entry.data
  }
  
  private setCache<T>(key: string, data: T, config: CacheConfig) {
    const entry: CacheEntry = {
      data,
      expiry: Date.now() + config.ttl,
      storage: config.storage
    }
    
    this.cache.set(key, entry)
    
    // 持久化缓存
    if (config.storage !== 'memory') {
      const storage = config.storage === 'localStorage' ? localStorage : sessionStorage
      storage.setItem(`cache_${key}`, JSON.stringify(entry))
    }
  }
  
  // 缓存失效
  invalidateCache(pattern: string) {
    const keysToDelete: string[] = []
    
    for (const [key] of this.cache) {
      if (key.includes(pattern)) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => {
      this.cache.delete(key)
      localStorage.removeItem(`cache_${key}`)
      sessionStorage.removeItem(`cache_${key}`)
    })
  }
  
  private generateRequestKey(config: RequestConfig): string {
    return `${config.method}:${config.url}:${JSON.stringify(config.params || {})}`
  }
  
  private buildUrl(config: RequestConfig): string {
    // 如果URL已经是完整URL，直接使用
    let url = config.url
    if (!url.startsWith('http') && this.baseURL) {
      url = this.baseURL.replace(/\/$/, '') + '/' + url.replace(/^\//, '')
    }

    if (config.params && Object.keys(config.params).length > 0) {
      const searchParams = new URLSearchParams(config.params)
      url += `?${searchParams.toString()}`
    }
    return url
  }
  
  private canBatch(configs: RequestConfig[]): boolean {
    return configs.length > 1 && 
           configs.every(config => config.method === 'GET') &&
           configs.length <= 10
  }
  
  private isOfflineCapable(config: RequestConfig): boolean {
    return config.method === 'GET' && !!config.cache
  }
  
  private handleRequestError(error: any, config: RequestConfig): Error {
    if (error.name === 'AbortError') {
      return new Error('请求超时')
    }
    return error
  }
}

import { ENV_CONFIG } from '../config/env'

// 获取API服务端地址
const getAPIBaseURL = (): string => {
  return ENV_CONFIG.API_BASE_URL
}

// 创建单例实例
export const requestManager = new H5RequestManager(getAPIBaseURL())

// 默认导出
export default H5RequestManager
