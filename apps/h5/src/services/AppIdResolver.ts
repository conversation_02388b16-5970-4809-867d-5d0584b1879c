/**
 * AppID解析服务 - 简化版本
 * 
 * 与SmartAppIdManager配合使用，负责将AppID解析为应用类型和页面信息
 * 主要负责API调用和数据转换逻辑
 */

import { 
  APPLICATION_TYPES, 
  getApplicationConfig, 
  getPageByPath,
  buildRoutePath 
} from '@lowcode/aslib/core'
import type { ApplicationType, PageRoute } from '@lowcode/aslib/core'
import { getCurrentAppId } from './SmartAppIdManager'
import { appDataManager } from './AppDataManager'

export interface AppInfo {
  appId: string
  appType: string
  application: ApplicationType
  targetPage?: PageRoute
  targetPath: string
}

export class AppIdResolver {
  private appCache = new Map<string, AppInfo>()
  private appInfoCache = new Map<string, { appType: string; config?: any }>()

  /**
   * 解析当前AppID获取应用信息 - 简化版本
   */
  async resolveCurrentApp(pagePath: string = '/'): Promise<AppInfo> {
    // 使用SmartAppIdManager获取当前appId
    const appId = getCurrentAppId()
    return this.resolveAppId(appId, pagePath)
  }

  /**
   * 解析指定AppID获取应用信息
   */
  async resolveAppId(appId: string, pagePath: string = '/'): Promise<AppInfo> {
    const cacheKey = `${appId}:${pagePath}`

    // 检查完整结果缓存
    if (this.appCache.has(cacheKey)) {
      console.log('🎯 使用缓存的AppID解析结果:', cacheKey)
      return this.appCache.get(cacheKey)!
    }

    try {
      // 1. 从后台API获取AppID对应的应用类型（使用缓存）
      const appInfo = await this.fetchAppInfo(appId)
      
      // 2. 验证应用类型是否存在
      const application = APPLICATION_TYPES[appInfo.appType]
      if (!application) {
        throw new Error(`不支持的应用类型: ${appInfo.appType}`)
      }

      // 3. 解析目标页面
      const targetPage = this.resolveTargetPage(appInfo.appType, pagePath)
      
      // 4. 构建完整的目标路径
      const targetPath = this.buildTargetPath(appInfo.appType, pagePath, targetPage)

      const result: AppInfo = {
        appId,
        appType: appInfo.appType,
        application,
        targetPage,
        targetPath
      }

      // 缓存结果
      this.appCache.set(cacheKey, result)
      
      return result
    } catch (error) {
      console.error('AppID解析失败:', error)
      throw new Error(`无法解析AppID: ${appId}`)
    }
  }

  /**
   * 从后台API获取AppID信息（带缓存）
   */
  private async fetchAppInfo(appId: string): Promise<{ appType: string; config?: any }> {
    // 🎯 检查应用信息缓存
    if (this.appInfoCache.has(appId)) {
      console.log('🎯 使用缓存的应用信息:', appId)
      return this.appInfoCache.get(appId)!
    }

    try {
      // 使用统一的应用数据管理器获取应用信息
      console.log('🌐 请求应用信息:', appId)
      const basicInfo = await appDataManager.getAppBasicInfo(appId)

      if (basicInfo) {
        const appInfo = {
          appType: basicInfo.appType || 'device',
          config: basicInfo
        }

        // 🎯 缓存应用信息
        this.appInfoCache.set(appId, appInfo)
        console.log('✅ 应用信息已缓存:', appId, appInfo.appType)

        return appInfo
      }
    } catch (error: any) {
      console.warn('无法从API获取应用信息，使用默认配置:', error.message)
    }

    // API调用失败时的后备逻辑
    let fallbackAppInfo: { appType: string; config?: any }

    // 根据AppID判断应用类型
    if (appId.includes('device') || appId.includes('default-device')) {
      fallbackAppInfo = { appType: 'device' }
    } else if (appId.includes('mall') || appId.includes('shop')) {
      fallbackAppInfo = { appType: 'mall' }
    } else {
      // 默认返回设备端
      fallbackAppInfo = { appType: 'device' }
    }

    // 🎯 缓存后备配置
    this.appInfoCache.set(appId, fallbackAppInfo)
    console.log('⚠️ 使用后备应用配置并缓存:', appId, fallbackAppInfo.appType)

    return fallbackAppInfo
  }

  /**
   * 解析目标页面
   */
  private resolveTargetPage(appType: string, pagePath: string): PageRoute | undefined {
    // 确保pagePath是字符串
    const pathStr = typeof pagePath === 'string' ? pagePath : String(pagePath)
    // 标准化路径
    const normalizedPath = pathStr.startsWith('/') ? pathStr : `/${pathStr}`
    
    // 查找对应的页面配置
    return getPageByPath(appType, normalizedPath)
  }

  /**
   * 构建目标路径
   */
  private buildTargetPath(appType: string, pagePath: string, targetPage?: PageRoute): string {
    // 如果有具体的页面配置，使用页面配置的路径
    if (targetPage) {
      return targetPage.path
    }
    
    // 否则构建基础路径
    return buildRoutePath(appType, pagePath)
  }

  /**
   * 检查当前AppID是否有效 - 简化版本
   */
  async validateCurrentApp(): Promise<boolean> {
    try {
      await this.resolveCurrentApp()
      return true
    } catch {
      return false
    }
  }

  /**
   * 检查指定AppID是否有效
   */
  async validateAppId(appId: string): Promise<boolean> {
    try {
      await this.resolveAppId(appId)
      return true
    } catch {
      return false
    }
  }

  /**
   * 获取当前应用的默认页面路径
   */
  async getCurrentAppDefaultPage(): Promise<string> {
    const appInfo = await this.resolveCurrentApp()
    return appInfo.application.defaultPage || '/home'
  }

  /**
   * 获取应用的默认页面路径
   */
  async getDefaultPagePath(appId: string): Promise<string> {
    const appInfo = await this.resolveAppId(appId)
    return appInfo.application.defaultPage || '/home'
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.appCache.clear()
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.appCache.size,
      keys: Array.from(this.appCache.keys())
    }
  }
}

// 导出单例实例
export const appIdResolver = new AppIdResolver()

// 导出便捷函数 - 简化版本
export async function resolveCurrentApp(pagePath?: string): Promise<AppInfo> {
  return appIdResolver.resolveCurrentApp(pagePath)
}

export async function resolveAppId(appId: string, pagePath?: string): Promise<AppInfo> {
  return appIdResolver.resolveAppId(appId, pagePath)
}

export async function validateCurrentApp(): Promise<boolean> {
  return appIdResolver.validateCurrentApp()
}

export async function validateAppId(appId: string): Promise<boolean> {
  return appIdResolver.validateAppId(appId)
}

export async function getCurrentAppDefaultPage(): Promise<string> {
  return appIdResolver.getCurrentAppDefaultPage()
}

export async function getAppDefaultPage(appId: string): Promise<string> {
  return appIdResolver.getDefaultPagePath(appId)
}
