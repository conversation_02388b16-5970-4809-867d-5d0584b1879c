/**
 * 数据适配器
 * 负责将API返回的数据转换为H5端期待的格式
 */

import type { AppConfig, TabConfig } from '../stores/app'

export class DataAdapter {
  /**
   * 适配应用配置数据
   */
  static adaptAppConfig(apiData: any): AppConfig {
    return {
      id: apiData.id,
      name: apiData.name,
      description: apiData.description,
      icon: apiData.icon,
      defaultHomePage: apiData.defaultHomePage,
      appType: apiData.appType,
      tabBar: apiData.tabBar ? this.adaptTabBarConfig(apiData.tabBar) : undefined,
      createTime: apiData.createTime,
      updateTime: apiData.updateTime,
      published: apiData.published === 1 || apiData.published === true, // 转换为布尔值
      publishTime: apiData.publishTime,
      permissions: apiData.permissions
    }
  }

  /**
   * 适配TabBar配置
   */
  private static adaptTabBarConfig(apiTabBar: any): AppConfig['tabBar'] {
    if (!apiTabBar || !apiTabBar.tabs) {
      return undefined
    }

    return {
      enabled: apiTabBar.enabled,
      tabs: apiTabBar.tabs.map((tab: any): TabConfig => ({
        id: tab.id,
        name: tab.name,
        label: tab.label,
        icon: tab.icon,
        path: tab.path,
        pageId: tab.pageId,
        active: tab.active
      })),
      style: apiTabBar.style ? {
        backgroundColor: apiTabBar.style.backgroundColor,
        activeColor: apiTabBar.style.activeColor,
        inactiveColor: apiTabBar.style.inactiveColor,
        height: apiTabBar.style.height
      } : undefined
    }
  }

  /**
   * 适配页面配置数据
   */
  static adaptPageConfig(apiData: any): any {
    return {
      id: apiData.id,
      appId: apiData.appId,
      slug: apiData.slug,
      name: apiData.name,
      path: apiData.path,
      title: apiData.title,
      layout: apiData.layout,
      style: apiData.style,
      components: apiData.components || [],
      dataSource: apiData.dataSource,
      events: apiData.events,
      published: apiData.published === 1 || apiData.published === true,
      publishTime: apiData.publishTime,
      createTime: apiData.createTime,
      updateTime: apiData.updateTime
    }
  }

  /**
   * 检查API响应是否成功
   */
  static isSuccessResponse(response: any): boolean {
    return (
      response &&
      (response.code === 1 || response.code === true || response.code === 200) &&
      response.data
    )
  }

  /**
   * 提取API响应数据
   */
  static extractResponseData<T>(response: any): T | null {
    if (this.isSuccessResponse(response)) {
      return response.data
    }
    return null
  }
}

export default DataAdapter
