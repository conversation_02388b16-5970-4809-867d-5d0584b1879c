/**
 * 路径解析器
 * 负责智能路径解析和路由转换
 */

import type { Router } from 'vue-router'
import { getUnifiedRoutesByApplicationType } from '@lowcode/aslib/core'
import { getCurrentAppId as getAppIdFromManager } from '../SmartAppIdManager'

export interface PathInfo {
  appType: string
  pagePath: string
  fullPath: string
}

export class PathResolver {
  private router: Router

  constructor(router: Router) {
    this.router = router
  }

  /**
   * 智能路径解析：根据页面配置决定使用原生路由还是低代码路由
   */
  resolveTargetPath(target: string): string {
    // 解析应用类型和页面路径
    const pathInfo = this.parseAppPath(target)
    
    if (!pathInfo) {
      // 无法解析的路径，直接返回原路径
      console.log(`✅ 无法解析路径，使用原路径: ${target}`)
      return target
    }

    const { appType, pagePath } = pathInfo
    
    // 检查页面是否可配置
    const isConfigurable = this.checkIfPageConfigurable(appType, pagePath)

    if (isConfigurable) {
      // 🎯 可配置页面：转换为简化的低代码路由（不带 appId）
      const lowcodeTarget = `/${pagePath}`
      console.log(`🔄 可配置页面，转换为简化低代码路由: ${target} → ${lowcodeTarget}`)
      return lowcodeTarget
    } else {
      // 非可配置页面：使用原生路由
      console.log(`✅ 非可配置页面，使用原生路由: ${target}`)
      return target
    }
  }

  /**
   * 解析应用路径，提取应用类型和页面路径
   */
  private parseAppPath(target: string): PathInfo | null {
    // 匹配 /appType/pagePath 格式
    const match = target.match(/^\/([^\/]+)\/(.+)$/)
    if (match) {
      return {
        appType: match[1],
        pagePath: match[2],
        fullPath: target
      }
    }
    return null
  }

  /**
   * 获取当前应用ID（优先从SmartAppIdManager获取）
   */
  private getCurrentAppId(): string | null {
    // 🎯 优先从 SmartAppIdManager 获取本地存储的 AppId
    const appId = getAppIdFromManager()
    if (appId && appId !== 'default-device') {
      console.log('✅ 从SmartAppIdManager获取到AppID:', appId)
      return appId
    }

    // 兜底：从路由上下文获取
    const currentRoute = this.router.currentRoute.value

    // 从路由参数获取AppID
    if (currentRoute.params.appId) {
      console.log('✅ 从路由参数获取到AppID:', currentRoute.params.appId)
      return currentRoute.params.appId as string
    }

    // 从路由路径解析AppID（如果当前在 /app/:appId 路径下）
    const pathMatch = currentRoute.path.match(/^\/app\/([^\/]+)/)
    if (pathMatch) {
      console.log('✅ 从路由路径解析到AppID:', pathMatch[1])
      return pathMatch[1]
    }

    console.warn('⚠️ 无法获取AppID，SmartAppIdManager返回:', appId)
    return null
  }

  /**
   * 检查页面是否可配置
   */
  private checkIfPageConfigurable(appType: string, pagePath: string): boolean {
    try {
      // 根据应用类型获取对应的路由配置
      const routes = getUnifiedRoutesByApplicationType(appType as any)

      // 构建完整的应用路径：/appType/PageName
      const fullAppPath = `/${appType}/${pagePath}`

      console.log(`🔍 检查页面配置: ${appType}/${pagePath} → ${fullAppPath}`)
      console.log(`📋 可用路由:`, routes.map(r => `${r.path} (${r.configurable ? '可配置' : '原生'})`))

      // 查找匹配的路由
      const matchedRoute = routes.find(route => {
        return route.path === fullAppPath
      })

      if (matchedRoute) {
        console.log(`✅ 找到页面配置: ${fullAppPath}, configurable: ${matchedRoute.configurable}`)
        return matchedRoute.configurable || false
      } else {
        console.warn(`⚠️ 未找到页面配置: ${fullAppPath}`)
        return false
      }
    } catch (error) {
      console.error(`❌ 检查页面配置失败: ${appType}/${pagePath}`, error)
      return false
    }
  }

  /**
   * 标准化路径
   */
  normalizePath(path: string): string {
    // 确保路径以 / 开头
    if (!path.startsWith('/')) {
      path = `/${path}`
    }
    
    // 移除末尾的 /
    if (path.length > 1 && path.endsWith('/')) {
      path = path.slice(0, -1)
    }
    
    return path
  }

  /**
   * 验证路径格式
   */
  validatePath(path: string): boolean {
    // 路径不能为空
    if (!path || typeof path !== 'string') {
      return false
    }
    
    // 路径必须以 / 开头
    if (!path.startsWith('/')) {
      return false
    }
    
    // 路径不能包含特殊字符（除了 / : + * ?）
    const validPathRegex = /^[a-zA-Z0-9\-_/:+*?]+$/
    return validPathRegex.test(path)
  }
}
