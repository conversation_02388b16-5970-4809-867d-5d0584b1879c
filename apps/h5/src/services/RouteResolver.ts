/**
 * 智能路由解析器
 * 
 * 处理低代码页面和原生页面的智能分发
 * 支持路由守卫和权限验证
 */

import { useRouter } from 'vue-router'
import { request } from '../utils/request'
// 暂时注释掉core包的导入，使用简化版本
// import { getUnifiedRoutesByApplicationType } from '@lowcode/aslib/core'
// import type { UnifiedRoute } from '@lowcode/aslib/core'

// 临时的路由配置
interface SimpleRoute {
  path: string
  name: string
  title: string
  category: string
  requiresAuth: boolean
  configurable: boolean
  enabled: boolean
}

const DEVICE_ROUTES: SimpleRoute[] = [
  {
    path: '/home',
    name: 'Home',
    title: '首页',
    category: 'home',
    requiresAuth: true,
    configurable: true,
    enabled: true
  },
  {
    path: '/login',
    name: 'Login',
    title: '登录',
    category: 'auth',
    requiresAuth: false,
    configurable: false,
    enabled: true
  }
]

// ==================== 类型定义 ====================

export interface AppInfo {
  id: string
  name: string
  appType: string
  description?: string
  icon?: string
}

export interface RouteResolution {
  type: 'lowcode' | 'native' | 'redirect' | 'not-found' | 'error'
  appInfo?: AppInfo
  route?: SimpleRoute
  pageConfig?: any
  redirectTo?: string
  reason?: string
  error?: Error
}

// ==================== 路由解析器类 ====================

export class RouteResolver {
  private appCache = new Map<string, AppInfo>()
  private routeCache = new Map<string, SimpleRoute[]>()
  
  /**
   * 解析应用路由
   */
  async resolveAppRoute(appId: string, pagePath = '/home'): Promise<RouteResolution> {
    try {
      console.log('🔍 [RouteResolver] 解析路由:', { appId, pagePath })
      
      // 1. 验证应用是否存在
      const appInfo = await this.getAppInfo(appId)
      if (!appInfo) {
        console.warn('❌ [RouteResolver] 应用不存在:', appId)
        return { 
          type: 'not-found', 
          reason: 'app-not-found',
          error: new Error(`应用 ${appId} 不存在`)
        }
      }
      
      // 2. 获取路由配置
      const route = this.getRouteConfig(appInfo.appType, pagePath)
      if (!route || !route.enabled) {
        console.warn('❌ [RouteResolver] 路由不存在或未启用:', { appType: appInfo.appType, pagePath })
        return { 
          type: 'not-found', 
          reason: 'page-not-found',
          error: new Error(`页面 ${pagePath} 不存在或未启用`)
        }
      }
      
      // 3. 检查权限
      if (route.requiresAuth && !await this.isAuthenticated(appInfo.appType)) {
        console.log('🔐 [RouteResolver] 需要登录，跳转到登录页')
        const loginPath = `/${appInfo.appType}/login`
        const redirectUrl = `/app/${appId}${pagePath === '/home' ? '' : pagePath}`
        return {
          type: 'redirect',
          redirectTo: `${loginPath}?redirect=${encodeURIComponent(redirectUrl)}`,
          reason: 'auth-required'
        }
      }
      
      // 4. 判断页面类型
      if (route.configurable) {
        // 低代码页面
        console.log('✅ [RouteResolver] 解析为低代码页面')
        const pageConfig = await this.getPageConfig(appId, pagePath)
        return {
          type: 'lowcode',
          appInfo,
          route,
          pageConfig
        }
      } else {
        // 原生页面 - 重定向到modules路由
        console.log('✅ [RouteResolver] 解析为原生页面，重定向')
        return {
          type: 'redirect',
          redirectTo: `/${appInfo.appType}${pagePath}`,
          reason: 'native-page'
        }
      }
    } catch (error) {
      console.error('❌ [RouteResolver] 路由解析失败:', error)
      return { 
        type: 'error', 
        error: error as Error 
      }
    }
  }
  
  /**
   * 获取应用信息
   */
  private async getAppInfo(appId: string): Promise<AppInfo | null> {
    // 检查缓存
    if (this.appCache.has(appId)) {
      return this.appCache.get(appId)!
    }

    try {
      // 从API获取应用信息
      const response = await request.publicGet(`/app/${appId}`)
      const result = response.data

      if (!result.code || !result.data) {
        console.warn(`应用 ${appId} 数据无效`)
        return null
      }

      const appInfo: AppInfo = {
        id: result.data.id,
        name: result.data.name,
        appType: result.data.appType || 'device', // 默认为device类型
        description: result.data.description,
        icon: result.data.icon
      }

      // 缓存应用信息
      this.appCache.set(appId, appInfo)
      console.log(`✅ 应用信息获取成功: ${appInfo.name} (${appInfo.appType})`)
      return appInfo
    } catch (error: any) {
      if (error.response?.status === 404) {
        console.warn(`应用 ${appId} 不存在或无法访问`)
      } else {
        console.error('获取应用信息失败:', error.message)
      }
      return null
    }
  }
  
  /**
   * 获取路由配置
   */
  private getRouteConfig(appType: string, pagePath: string): SimpleRoute | null {
    // 简化版本，直接使用硬编码的路由
    if (appType === 'device') {
      return DEVICE_ROUTES.find(route => route.path === pagePath) || null
    }
    return null
  }
  
  /**
   * 检查登录状态
   */
  private async isAuthenticated(appType: string): Promise<boolean> {
    // 根据应用类型检查不同的登录状态
    switch (appType) {
      case 'device':
        // 检查设备登录状态
        const deviceToken = localStorage.getItem('ZX-DEVICE-TOKEN')
        return !!deviceToken
        
      case 'mall':
        // 检查商城登录状态
        const mallToken = localStorage.getItem('MALL-TOKEN')
        return !!mallToken
        
      default:
        return false
    }
  }
  
  /**
   * 获取页面配置
   */
  private async getPageConfig(appId: string, pagePath: string): Promise<any> {
    try {
      // 构建页面ID
      const pageId = pagePath === '/home' ? `${appId}_home` : `${appId}_${pagePath.replace('/', '')}`
      
      // 从API获取页面配置
      const response = await fetch(`/api/page/${pageId}`)
      if (!response.ok) {
        throw new Error(`获取页面配置失败: ${response.statusText}`)
      }
      
      const result = await response.json()
      if (!result.code) {
        throw new Error(result.msg || '页面配置获取失败')
      }
      
      return result.data
    } catch (error) {
      console.error('获取页面配置失败:', error)
      // 返回默认配置
      return this.getDefaultPageConfig(appId, pagePath)
    }
  }
  
  /**
   * 获取默认页面配置
   */
  private getDefaultPageConfig(appId: string, pagePath: string): any {
    return {
      id: `default_${appId}_${pagePath.replace('/', '')}`,
      name: `${appId}默认页面`,
      layout: {
        type: 'flex',
        direction: 'column',
        style: {
          minHeight: '100vh',
          backgroundColor: '#f5f5f5'
        }
      },
      components: [
        {
          id: 'default-header',
          type: 'Header',
          props: {
            title: '默认页面',
            showBack: false
          },
          style: {
            backgroundColor: '#1890ff',
            color: 'white'
          }
        },
        {
          id: 'default-content',
          type: 'Container',
          props: {
            text: `欢迎使用应用 ${appId}！\n\n这是一个默认页面，您可以在设计器中自定义页面内容。`
          },
          style: {
            padding: '2rem',
            textAlign: 'center',
            flex: 1
          }
        }
      ]
    }
  }
  
  /**
   * 清除缓存
   */
  clearCache(): void {
    this.appCache.clear()
    this.routeCache.clear()
  }
}

// ==================== 导出 ====================

// 创建全局实例
export const routeResolver = new RouteResolver()

// 导出类和实例
export default RouteResolver
