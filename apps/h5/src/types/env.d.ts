/// <reference types="vite/client" />
/// <reference types="@lowcode/core/types/env" />

// H5应用特定的环境变量扩展
interface ImportMetaEnv {
  // H5特定配置
  readonly VITE_H5_TITLE: string
  readonly VITE_H5_DESCRIPTION: string
  readonly VITE_H5_KEYWORDS: string
  
  // 移动端配置
  readonly VITE_VIEWPORT_WIDTH: string
  readonly VITE_VIEWPORT_HEIGHT: string
  readonly VITE_SCALE_MODE: string
  
  // PWA配置
  readonly VITE_ENABLE_PWA: string
  readonly VITE_PWA_NAME: string
  readonly VITE_PWA_SHORT_NAME: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
