/**
 * 业务检测器 - 根据路径和appId检测业务类型
 * 
 * 核心功能：
 * 1. 根据URL路径检测业务类型
 * 2. 根据appId前缀检测业务类型
 * 3. 支持业务规则配置
 * 4. 提供业务信息查询
 */

// 业务类型定义
export type BusinessType = 'device' | 'mall' | 'finance' | 'education' | 'other'

// 业务信息接口
export interface BusinessInfo {
  type: BusinessType
  name: string
  description: string
  pathPrefixes: string[]
  appIdPrefixes: string[]
  staticPages: string[]
  priority: number
}

// 业务检测规则
export interface BusinessRule {
  type: BusinessType
  pathPatterns: RegExp[]
  appIdPatterns: RegExp[]
  exactPaths: string[]
  priority: number
}

export class BusinessDetector {
  private businessInfoMap: Map<BusinessType, BusinessInfo>
  private detectionRules: BusinessRule[]

  constructor() {
    this.businessInfoMap = new Map()
    this.detectionRules = []
    this.initializeBusinesses()
    this.initializeRules()
  }

  /**
   * 检测业务类型
   */
  detect(path: string, appId?: string): BusinessType {
    console.log('🔍 [BusinessDetector] 检测业务类型:', { path, appId })

    // 按优先级排序的规则进行检测
    const sortedRules = [...this.detectionRules].sort((a, b) => b.priority - a.priority)

    for (const rule of sortedRules) {
      // 检查精确路径匹配
      if (rule.exactPaths.includes(path)) {
        console.log('✅ 精确路径匹配:', rule.type, path)
        return rule.type
      }

      // 检查路径模式匹配
      for (const pattern of rule.pathPatterns) {
        if (pattern.test(path)) {
          console.log('✅ 路径模式匹配:', rule.type, pattern, path)
          return rule.type
        }
      }

      // 检查appId模式匹配
      if (appId) {
        for (const pattern of rule.appIdPatterns) {
          if (pattern.test(appId)) {
            console.log('✅ AppId模式匹配:', rule.type, pattern, appId)
            return rule.type
          }
        }
      }
    }

    console.log('⚠️ 未匹配到业务类型，使用默认:', 'device')
    return 'device' // 默认为设备业务
  }

  /**
   * 获取业务信息
   */
  getBusinessInfo(type: BusinessType): BusinessInfo | undefined {
    return this.businessInfoMap.get(type)
  }

  /**
   * 获取所有支持的业务类型
   */
  getSupportedBusinesses(): BusinessType[] {
    return Array.from(this.businessInfoMap.keys())
  }

  /**
   * 检查路径是否为静态页面
   */
  isStaticPage(path: string, businessType: BusinessType): boolean {
    const businessInfo = this.businessInfoMap.get(businessType)
    return businessInfo?.staticPages.includes(path) || false
  }

  /**
   * 获取业务的所有静态页面
   */
  getStaticPages(businessType: BusinessType): string[] {
    const businessInfo = this.businessInfoMap.get(businessType)
    return businessInfo?.staticPages || []
  }

  /**
   * 添加自定义业务
   */
  addBusiness(info: BusinessInfo) {
    this.businessInfoMap.set(info.type, info)
    
    // 添加对应的检测规则
    const rule: BusinessRule = {
      type: info.type,
      pathPatterns: info.pathPrefixes.map(prefix => new RegExp(`^${prefix}`)),
      appIdPatterns: info.appIdPrefixes.map(prefix => new RegExp(`^${prefix}`)),
      exactPaths: info.staticPages,
      priority: info.priority
    }
    
    this.detectionRules.push(rule)
    this.sortRules()
  }

  /**
   * 初始化业务信息
   */
  private initializeBusinesses() {
    // 设备充值业务
    this.businessInfoMap.set('device', {
      type: 'device',
      name: '设备充值',
      description: '设备管理和充值业务',
      pathPrefixes: ['/device'],
      appIdPrefixes: ['device_', 'dev_'],
      staticPages: [
        '/home',           // 首页（当前可配置，但也有静态版本）
        '/login',          // 登录页
        '/PackageList',    // 套餐列表
        '/PackagePayment', // 套餐支付
        '/PackageOrder',   // 套餐订单
        '/BalanceList',    // 余额充值
        '/BalanceDetails', // 余额明细
        '/BalancePayment', // 余额支付
        '/RealName',       // 实名认证
        '/RealNameCards',  // 实名卡片
        '/EditDevice',     // 设备设置
        '/EditPassword',   // 支付密码
        '/DeviceNotice',   // 设备通知
        '/LayoutService',  // 客服
        '/wechat-payment', // 微信支付
        '/qrcode-payment', // 二维码支付
        '/webview'         // 内嵌网页
      ],
      priority: 100
    })

    // 商城业务（未来扩展）
    this.businessInfoMap.set('mall', {
      type: 'mall',
      name: '商城系统',
      description: '电商购物业务',
      pathPrefixes: ['/mall'],
      appIdPrefixes: ['mall_', 'shop_', 'store_'],
      staticPages: [
        '/mall/cart',      // 购物车
        '/mall/checkout',  // 结算页面
        '/mall/orders',    // 订单列表
        '/mall/profile',   // 个人中心
        '/mall/payment'    // 支付页面
      ],
      priority: 90
    })

    // 金融业务（未来扩展）
    this.businessInfoMap.set('finance', {
      type: 'finance',
      name: '金融服务',
      description: '金融理财业务',
      pathPrefixes: ['/finance', '/bank'],
      appIdPrefixes: ['finance_', 'bank_', 'pay_'],
      staticPages: [
        '/finance/transfer', // 转账
        '/finance/loan',     // 贷款
        '/finance/invest'    // 投资
      ],
      priority: 80
    })

    // 教育业务（未来扩展）
    this.businessInfoMap.set('education', {
      type: 'education',
      name: '教育培训',
      description: '在线教育业务',
      pathPrefixes: ['/edu', '/course'],
      appIdPrefixes: ['edu_', 'course_', 'learn_'],
      staticPages: [
        '/edu/exam',       // 考试
        '/edu/homework',   // 作业
        '/edu/live'        // 直播
      ],
      priority: 70
    })

    // 其他业务
    this.businessInfoMap.set('other', {
      type: 'other',
      name: '其他业务',
      description: '未分类的业务',
      pathPrefixes: ['/other'],
      appIdPrefixes: ['other_'],
      staticPages: [],
      priority: 10
    })
  }

  /**
   * 初始化检测规则
   */
  private initializeRules() {
    for (const [type, info] of this.businessInfoMap) {
      const rule: BusinessRule = {
        type,
        pathPatterns: info.pathPrefixes.map(prefix => new RegExp(`^${prefix}`)),
        appIdPatterns: info.appIdPrefixes.map(prefix => new RegExp(`^${prefix}`)),
        exactPaths: info.staticPages,
        priority: info.priority
      }
      this.detectionRules.push(rule)
    }

    // 添加设备业务的特殊规则（兼容现有路径）
    this.detectionRules.push({
      type: 'device',
      pathPatterns: [],
      appIdPatterns: [],
      exactPaths: [
        '/',               // 根路径
        '/home',           // 首页
        '/login'           // 登录页
      ],
      priority: 150 // 最高优先级
    })

    this.sortRules()
  }

  /**
   * 按优先级排序规则
   */
  private sortRules() {
    this.detectionRules.sort((a, b) => b.priority - a.priority)
  }
}
