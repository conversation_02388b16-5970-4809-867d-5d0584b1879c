/**
 * 开放配置管理器 - 控制哪些页面可以被用户配置
 * 
 * 核心功能：
 * 1. 管理业务的开放配置
 * 2. 支持渐进式页面开放
 * 3. 提供配置缓存和更新机制
 * 4. 支持运行时配置修改
 */

import type { BusinessType } from './BusinessDetector'

// 业务开放配置
export interface BusinessOpenConfig {
  business: BusinessType
  enabled: boolean                    // 是否启用低代码功能
  configurablePages: string[]         // 可配置的页面列表
  staticPages: string[]              // 强制使用原生页面的列表
  fallbackMode: 'static' | 'dynamic' // 降级模式
  phase: string                      // 开放阶段标识
  lastUpdated: Date                  // 最后更新时间
}

// 开放阶段配置
export interface PhaseConfig {
  phase: string
  name: string
  description: string
  businesses: Record<BusinessType, {
    configurablePages: string[]
    enabled: boolean
  }>
}

export class OpenConfigManager {
  private configs: Map<BusinessType, BusinessOpenConfig>
  private phases: Map<string, PhaseConfig>
  private currentPhase: string
  private cacheEnabled: boolean
  private debugMode: boolean

  constructor(options: { cacheEnabled?: boolean; debugMode?: boolean } = {}) {
    this.configs = new Map()
    this.phases = new Map()
    this.currentPhase = 'production'
    this.cacheEnabled = options.cacheEnabled ?? true
    this.debugMode = options.debugMode ?? false
    
    this.initializeConfigs()
    this.initializePhases()
  }

  /**
   * 获取业务的开放配置
   */
  async getBusinessConfig(business: BusinessType): Promise<BusinessOpenConfig | null> {
    this.log('获取业务配置:', business)
    
    // 从缓存获取
    const cached = this.configs.get(business)
    if (cached) {
      this.log('使用缓存配置:', cached)
      return cached
    }

    // 如果缓存中没有，尝试从远程加载
    try {
      const remoteConfig = await this.loadRemoteConfig(business)
      if (remoteConfig) {
        this.configs.set(business, remoteConfig)
        return remoteConfig
      }
    } catch (error) {
      console.error('加载远程配置失败:', error)
    }

    this.log('未找到业务配置:', business)
    return null
  }

  /**
   * 检查页面是否可配置
   */
  async isPageConfigurable(business: BusinessType, path: string): Promise<boolean> {
    const config = await this.getBusinessConfig(business)
    if (!config || !config.enabled) {
      return false
    }

    // 检查是否在可配置列表中
    const isConfigurable = config.configurablePages.includes(path)
    
    // 检查是否被强制设为静态页面
    const isForceStatic = config.staticPages.includes(path)
    
    this.log('页面配置检查:', { business, path, isConfigurable, isForceStatic })
    
    return isConfigurable && !isForceStatic
  }

  /**
   * 更新业务配置
   */
  updateBusinessConfig(business: BusinessType, updates: Partial<BusinessOpenConfig>) {
    const existing = this.configs.get(business)
    if (!existing) {
      console.warn('尝试更新不存在的业务配置:', business)
      return
    }

    const updated = {
      ...existing,
      ...updates,
      lastUpdated: new Date()
    }

    this.configs.set(business, updated)
    this.log('业务配置已更新:', business, updated)

    // 触发配置变更事件
    this.emitConfigChange(business, updated)
  }

  /**
   * 切换开放阶段
   */
  switchPhase(phase: string) {
    const phaseConfig = this.phases.get(phase)
    if (!phaseConfig) {
      console.error('未知的开放阶段:', phase)
      return
    }

    this.currentPhase = phase
    this.log('切换到开放阶段:', phase)

    // 应用阶段配置
    for (const [business, config] of Object.entries(phaseConfig.businesses)) {
      this.updateBusinessConfig(business as BusinessType, {
        configurablePages: config.configurablePages,
        enabled: config.enabled,
        phase
      })
    }
  }

  /**
   * 获取当前阶段
   */
  getCurrentPhase(): string {
    return this.currentPhase
  }

  /**
   * 获取所有阶段
   */
  getAllPhases(): PhaseConfig[] {
    return Array.from(this.phases.values())
  }

  /**
   * 清除配置缓存
   */
  clearCache(business?: BusinessType) {
    if (business) {
      this.configs.delete(business)
      this.log('已清除业务配置缓存:', business)
    } else {
      this.configs.clear()
      this.log('已清除所有配置缓存')
    }
  }

  /**
   * 获取配置统计信息
   */
  getStats() {
    const stats = {
      totalBusinesses: this.configs.size,
      currentPhase: this.currentPhase,
      enabledBusinesses: 0,
      totalConfigurablePages: 0,
      businesses: {} as Record<string, any>
    }

    for (const [business, config] of this.configs) {
      if (config.enabled) {
        stats.enabledBusinesses++
      }
      stats.totalConfigurablePages += config.configurablePages.length
      
      stats.businesses[business] = {
        enabled: config.enabled,
        configurablePages: config.configurablePages.length,
        staticPages: config.staticPages.length,
        phase: config.phase
      }
    }

    return stats
  }

  /**
   * 初始化默认配置
   */
  private initializeConfigs() {
    // 设备业务配置 - 当前只开放首页
    this.configs.set('device', {
      business: 'device',
      enabled: true,
      configurablePages: [],  // 暂时没有可配置页面
      staticPages: [
        '/device/login',           // 登录页
        '/device/PackageList',     // 套餐列表
        '/device/PackagePayment',  // 套餐支付
        '/device/PackageOrder',    // 套餐订单
        '/device/BalanceList',     // 余额充值
        '/device/BalanceDetails',  // 余额明细
        '/device/BalancePayment',  // 余额支付
        '/device/RealName',        // 实名认证
        '/device/RealNameCards',   // 实名卡片
        '/device/EditDevice',      // 设备设置
        '/device/EditPassword',    // 支付密码
        '/device/DeviceNotice',    // 设备通知
        '/device/LayoutService',   // 客服
        '/device/wechat-payment',  // 微信支付
        '/device/qrcode-payment'   // 二维码支付
      ],
      fallbackMode: 'static',
      phase: 'production',
      lastUpdated: new Date()
    })



    this.log('默认配置已初始化')
  }

  /**
   * 初始化开放阶段
   */
  private initializePhases() {
    // 生产阶段 - 当前使用的配置
    this.phases.set('production', {
      phase: 'production',
      name: '生产阶段',
      description: '生产环境，只开放经过验证的页面',
      businesses: {
        device: {
          configurablePages: [],  // 暂时没有可配置页面
          enabled: true
        },
        mall: {
          configurablePages: [],
          enabled: false
        },
        finance: {
          configurablePages: [],
          enabled: false
        },
        education: {
          configurablePages: [],
          enabled: false
        },
        other: {
          configurablePages: [],
          enabled: false
        }
      }
    })



    this.log('开放阶段已初始化')
  }

  /**
   * 从远程加载配置（模拟）
   */
  private async loadRemoteConfig(business: BusinessType): Promise<BusinessOpenConfig | null> {
    // 这里可以实现从API加载配置的逻辑
    // 目前返回null，使用本地默认配置
    return null
  }

  /**
   * 触发配置变更事件
   */
  private emitConfigChange(business: BusinessType, config: BusinessOpenConfig) {
    // 可以在这里实现事件通知机制
    // 例如通知其他组件配置已更新
    this.log('配置变更事件:', business, config)
  }

  /**
   * 调试日志
   */
  private log(...args: any[]) {
    if (this.debugMode) {
      console.log('🔧 [OpenConfigManager]', ...args)
    }
  }
}

// 创建全局开放配置管理器实例
export const openConfigManager = new OpenConfigManager({
  debugMode: import.meta.env.DEV,
  cacheEnabled: true
})
