/**
 * H5核心模块统一导出
 *
 * 提供页面级可控开放架构的核心功能：
 * 1. 页面路由器 - 智能分发动态页面和原生页面
 * 2. 业务检测器 - 根据路径和appId检测业务类型
 * 3. 开放配置管理器 - 控制哪些页面可以被用户配置
 * 4. 业务配置管理器 - 管理各业务的配置信息
 */

// 类型定义导出
export type { PageRouteResult } from './PageRouter'
export type { BusinessType, BusinessInfo, BusinessRule } from './BusinessDetector'
export type { BusinessOpenConfig, PhaseConfig } from './OpenConfigManager'
export type { BusinessConfig, ConfigValidationResult } from './BusinessConfigManager'

// 延迟导出核心类，避免循环依赖
export const getPageRouter = async () => {
  const { pageRouter } = await import('./PageRouter')
  return pageRouter
}

export const getBusinessDetector = async () => {
  const { BusinessDetector } = await import('./BusinessDetector')
  return new BusinessDetector()
}

export const getOpenConfigManager = async () => {
  const { openConfigManager } = await import('./OpenConfigManager')
  return openConfigManager
}

export const getBusinessConfigManager = async () => {
  const { businessConfigManager } = await import('./BusinessConfigManager')
  return businessConfigManager
}

// 工具函数
export const CoreUtils = {
  /**
   * 检查页面是否可配置
   */
  async isPageConfigurable(path: string, appId?: string): Promise<boolean> {
    const detector = await getBusinessDetector()
    const openConfigManager = await getOpenConfigManager()
    const business = detector.detect(path, appId)
    return await openConfigManager.isPageConfigurable(business, path)
  },

  /**
   * 获取页面的业务类型
   */
  async getPageBusiness(path: string, appId?: string): Promise<BusinessType> {
    const detector = await getBusinessDetector()
    return detector.detect(path, appId)
  },

  /**
   * 获取业务的配置信息
   */
  async getBusinessConfig(business: BusinessType) {
    const businessConfigManager = await getBusinessConfigManager()
    return businessConfigManager.getConfig(business)
  },

  /**
   * 切换开放阶段
   */
  async switchPhase(phase: string) {
    const openConfigManager = await getOpenConfigManager()
    openConfigManager.switchPhase(phase)
  },

  /**
   * 获取系统统计信息
   */
  async getSystemStats() {
    const pageRouter = await getPageRouter()
    const openConfigManager = await getOpenConfigManager()
    const businessConfigManager = await getBusinessConfigManager()

    return {
      pageRouter: pageRouter.getStats(),
      openConfig: openConfigManager.getStats(),
      businessConfig: businessConfigManager.getAllConfigs()
    }
  }
}

// 初始化函数
export const initializeCore = async () => {
  console.log('🚀 初始化H5核心模块...')

  try {
    // 预加载核心模块
    await getPageRouter()
    await getBusinessDetector()
    await getOpenConfigManager()
    await getBusinessConfigManager()

    // 设置开发环境的调试模式
    if (process.env.NODE_ENV === 'development') {
      const pageRouter = await getPageRouter()
      const openConfigManager = await getOpenConfigManager()
      const businessConfigManager = await getBusinessConfigManager()

      // 在开发环境下可以通过控制台访问核心模块
      ;(window as any).__H5_CORE__ = {
        pageRouter,
        openConfigManager,
        businessConfigManager,
        utils: CoreUtils
      }
      console.log('🔧 开发模式：核心模块已挂载到 window.__H5_CORE__')
    }

    console.log('✅ H5核心模块初始化完成')
  } catch (error) {
    console.error('❌ H5核心模块初始化失败:', error)
    throw error
  }
}

// 默认导出核心工具
export default CoreUtils
