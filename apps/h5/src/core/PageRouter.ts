/**
 * 页面路由器 - 简化版本
 */

import type { RouteLocationNormalized } from 'vue-router'

// 页面路由结果
export interface PageRouteResult {
  type: 'static' | 'dynamic'
  component: string
  business: string
  isConfigurable: boolean
  fallbackAvailable: boolean
}

// 页面路由器配置
export interface PageRouterConfig {
  enableFallback: boolean
  debugMode: boolean
  cacheEnabled: boolean
}

export class PageRouter {
  private config: PageRouterConfig

  constructor(config: Partial<PageRouterConfig> = {}) {
    this.config = {
      enableFallback: true,
      debugMode: false,
      cacheEnabled: true,
      ...config
    }
  }

  /**
   * 解析路由 - 简化版本，直接返回静态页面
   */
  async resolveRoute(_route: RouteLocationNormalized): Promise<PageRouteResult> {
    // 简化逻辑：所有路由都作为静态页面处理
    return {
      type: 'static',
      component: 'StaticPage',
      business: 'device',
      isConfigurable: false,
      fallbackAvailable: false
    }
  }
}

// 创建全局页面路由器实例
export const pageRouter = new PageRouter({
  debugMode: import.meta.env.DEV,
  cacheEnabled: true
})

// 获取页面路由器实例的函数
export async function getPageRouter(): Promise<PageRouter> {
  return pageRouter
}
