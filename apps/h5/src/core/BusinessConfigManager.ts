/**
 * 业务配置管理器 - 统一管理各业务的配置信息
 * 
 * 核心功能：
 * 1. 管理业务模块的配置信息
 * 2. 提供业务配置的CRUD操作
 * 3. 支持配置的动态加载和更新
 * 4. 提供配置验证和默认值处理
 */

import type { BusinessType } from './BusinessDetector'

// 业务配置接口
export interface BusinessConfig {
  type: BusinessType
  name: string
  description: string
  version: string
  enabled: boolean
  
  // 路由配置
  routes: {
    prefix: string
    staticPages: string[]
    dynamicPages: string[]
    defaultPage: string
  }
  
  // API配置
  api: {
    baseURL: string
    timeout: number
    retryCount: number
    authRequired: boolean
  }
  
  // 主题配置
  theme: {
    primaryColor: string
    backgroundColor: string
    textColor: string
    customCSS?: string
  }
  
  // 权限配置
  permissions: {
    required: string[]
    optional: string[]
    adminOnly: string[]
  }
  
  // 功能开关
  features: {
    [key: string]: boolean
  }
  
  // 自定义配置
  custom: Record<string, any>
  
  // 元数据
  metadata: {
    createdAt: Date
    updatedAt: Date
    author: string
    tags: string[]
  }
}

// 配置验证结果
export interface ConfigValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
}

export class BusinessConfigManager {
  private configs: Map<BusinessType, BusinessConfig>
  private defaultConfigs: Map<BusinessType, Partial<BusinessConfig>>
  private validators: Map<string, (value: any) => boolean>

  constructor() {
    this.configs = new Map()
    this.defaultConfigs = new Map()
    this.validators = new Map()
    
    this.initializeDefaultConfigs()
    this.initializeValidators()
    this.loadConfigs()
  }

  /**
   * 获取业务配置
   */
  getConfig(business: BusinessType): BusinessConfig | null {
    return this.configs.get(business) || null
  }

  /**
   * 设置业务配置
   */
  setConfig(business: BusinessType, config: Partial<BusinessConfig>): boolean {
    try {
      const existingConfig = this.configs.get(business)
      const defaultConfig = this.defaultConfigs.get(business)
      
      // 合并配置
      const mergedConfig: BusinessConfig = {
        ...defaultConfig,
        ...existingConfig,
        ...config,
        type: business,
        metadata: {
          ...existingConfig?.metadata,
          ...config.metadata,
          updatedAt: new Date()
        }
      } as BusinessConfig

      // 验证配置
      const validation = this.validateConfig(mergedConfig)
      if (!validation.valid) {
        console.error('配置验证失败:', validation.errors)
        return false
      }

      this.configs.set(business, mergedConfig)
      this.saveConfig(business, mergedConfig)
      
      console.log('✅ 业务配置已更新:', business)
      return true
    } catch (error) {
      console.error('设置业务配置失败:', error)
      return false
    }
  }

  /**
   * 获取业务的路由配置
   */
  getRouteConfig(business: BusinessType) {
    const config = this.getConfig(business)
    return config?.routes || null
  }

  /**
   * 获取业务的API配置
   */
  getApiConfig(business: BusinessType) {
    const config = this.getConfig(business)
    return config?.api || null
  }

  /**
   * 获取业务的主题配置
   */
  getThemeConfig(business: BusinessType) {
    const config = this.getConfig(business)
    return config?.theme || null
  }

  /**
   * 检查业务功能是否启用
   */
  isFeatureEnabled(business: BusinessType, feature: string): boolean {
    const config = this.getConfig(business)
    return config?.features[feature] || false
  }

  /**
   * 启用/禁用业务功能
   */
  toggleFeature(business: BusinessType, feature: string, enabled: boolean): boolean {
    const config = this.getConfig(business)
    if (!config) return false

    config.features[feature] = enabled
    return this.setConfig(business, config)
  }

  /**
   * 获取所有业务配置
   */
  getAllConfigs(): Record<BusinessType, BusinessConfig> {
    const result = {} as Record<BusinessType, BusinessConfig>
    for (const [business, config] of this.configs) {
      result[business] = config
    }
    return result
  }

  /**
   * 重置业务配置为默认值
   */
  resetConfig(business: BusinessType): boolean {
    const defaultConfig = this.defaultConfigs.get(business)
    if (!defaultConfig) return false

    return this.setConfig(business, {
      ...defaultConfig,
      metadata: {
        ...defaultConfig.metadata,
        updatedAt: new Date()
      }
    })
  }

  /**
   * 验证配置
   */
  validateConfig(config: BusinessConfig): ConfigValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // 基础字段验证
    if (!config.type) errors.push('业务类型不能为空')
    if (!config.name) errors.push('业务名称不能为空')
    if (!config.version) errors.push('版本号不能为空')

    // 路由配置验证
    if (!config.routes?.prefix) errors.push('路由前缀不能为空')
    if (!config.routes?.defaultPage) errors.push('默认页面不能为空')

    // API配置验证
    if (!config.api?.baseURL) errors.push('API基础URL不能为空')
    if (config.api?.timeout && config.api.timeout < 1000) {
      warnings.push('API超时时间建议不少于1000ms')
    }

    // 主题配置验证
    if (config.theme?.primaryColor && !this.isValidColor(config.theme.primaryColor)) {
      errors.push('主色调格式不正确')
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * 初始化默认配置
   */
  private initializeDefaultConfigs() {
    // 设备业务默认配置
    this.defaultConfigs.set('device', {
      type: 'device',
      name: '设备充值',
      description: '设备管理和充值业务系统',
      version: '1.0.0',
      enabled: true,
      routes: {
        prefix: '/device',
        staticPages: ['/PackageList', '/PackagePayment', '/BalanceList'],
        dynamicPages: ['/home'],
        defaultPage: '/home'
      },
      api: {
        baseURL: '/api/device',
        timeout: 10000,
        retryCount: 3,
        authRequired: true
      },
      theme: {
        primaryColor: '#1890ff',
        backgroundColor: '#f5f5f5',
        textColor: '#333333'
      },
      permissions: {
        required: ['device.read'],
        optional: ['device.write'],
        adminOnly: ['device.admin']
      },
      features: {
        realTimeUpdate: true,
        offlineMode: false,
        pushNotification: true,
        analytics: true
      },
      custom: {},
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        author: 'system',
        tags: ['device', 'payment', 'mobile']
      }
    })

    // 商城业务默认配置
    this.defaultConfigs.set('mall', {
      type: 'mall',
      name: '商城系统',
      description: '电商购物业务系统',
      version: '1.0.0',
      enabled: false,
      routes: {
        prefix: '/mall',
        staticPages: ['/cart', '/checkout', '/orders'],
        dynamicPages: ['/home', '/products'],
        defaultPage: '/home'
      },
      api: {
        baseURL: '/api/mall',
        timeout: 8000,
        retryCount: 2,
        authRequired: true
      },
      theme: {
        primaryColor: '#ff6b35',
        backgroundColor: '#ffffff',
        textColor: '#333333'
      },
      permissions: {
        required: ['mall.read'],
        optional: ['mall.write', 'mall.order'],
        adminOnly: ['mall.admin']
      },
      features: {
        shoppingCart: true,
        wishlist: true,
        reviews: true,
        recommendations: true
      },
      custom: {},
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        author: 'system',
        tags: ['mall', 'ecommerce', 'shopping']
      }
    })
  }

  /**
   * 初始化验证器
   */
  private initializeValidators() {
    this.validators.set('color', (value: string) => {
      return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value)
    })

    this.validators.set('url', (value: string) => {
      try {
        new URL(value)
        return true
      } catch {
        return false
      }
    })

    this.validators.set('version', (value: string) => {
      return /^\d+\.\d+\.\d+$/.test(value)
    })
  }

  /**
   * 验证颜色格式
   */
  private isValidColor(color: string): boolean {
    const validator = this.validators.get('color')
    return validator ? validator(color) : false
  }

  /**
   * 加载配置
   */
  private loadConfigs() {
    // 从localStorage加载配置
    for (const business of this.defaultConfigs.keys()) {
      const saved = localStorage.getItem(`business_config_${business}`)
      if (saved) {
        try {
          const config = JSON.parse(saved)
          this.configs.set(business, config)
        } catch (error) {
          console.error('加载业务配置失败:', business, error)
        }
      } else {
        // 使用默认配置
        const defaultConfig = this.defaultConfigs.get(business)
        if (defaultConfig) {
          this.configs.set(business, defaultConfig as BusinessConfig)
        }
      }
    }
  }

  /**
   * 保存配置
   */
  private saveConfig(business: BusinessType, config: BusinessConfig) {
    try {
      localStorage.setItem(`business_config_${business}`, JSON.stringify(config))
    } catch (error) {
      console.error('保存业务配置失败:', business, error)
    }
  }
}

// 创建全局业务配置管理器实例
export const businessConfigManager = new BusinessConfigManager()
