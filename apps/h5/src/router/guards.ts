/**
 * 路由守卫 - 集成智能AppID管理器
 */

import type { Router } from 'vue-router'
import { setupGlobalRouterGuards } from '@lowcode/aslib/core'
import { smartAppIdManager } from '../services/SmartAppIdManager'

export function setupRouterGuards(router: Router) {
  // 添加智能AppID管理器初始化 - 在所有路由守卫之前执行
  router.beforeEach((to, from, next) => {
    // 🎯 每次路由变化都检查并更新AppID
    console.log('🎯 路由守卫：检查URL中的AppID参数', {
      to: to.fullPath,
      from: from.fullPath
    })
    const result = smartAppIdManager.initializeFromUrl()
    
    if (result.updated) {
      console.log('🎯 路由守卫：AppID已从URL更新', {
        from: result.source === 'url' ? 'localStorage' : 'default',
        to: result.appId,
        url: to.fullPath
      })
    } else {
      console.log('🎯 路由守卫：当前AppID', result.appId, 'source:', result.source)
    }
    
    next()
  })
  
  // 使用core中的全局路由守卫
  setupGlobalRouterGuards(router)
}
