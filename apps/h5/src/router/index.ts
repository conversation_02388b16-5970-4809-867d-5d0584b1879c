import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { setupRouterGuards } from './guards'
import { RouteGenerator, getAppHomePath, getEnabledApplicationTypes } from '@lowcode/aslib/core'

// 使用 import.meta.glob 预加载所有组件模块
const deviceModules = import.meta.glob('../modules/device/pages/**/*.vue')
const mallModules = import.meta.glob('../modules/mall/pages/**/*.vue')

// 组件模块映射
const MODULE_MAP = {
  device: deviceModules,
  mall: mallModules
} as const

// ==================== 基础公共路由 ====================

const baseRoutes: RouteRecordRaw[] = [
  // 根路径 - 智能重定向
  {
    path: '/',
    redirect: () => {
      // 首先检查本地存储是否有有效的appid
      const storedAppId = localStorage.getItem('current-app-id')
      console.log('🔄 根路径重定向检查，本地AppID:', storedAppId)
      
      if (storedAppId && storedAppId.trim() !== '') {
        // 🎯 动态获取首页路径，不硬编码
        const enabledApps = getEnabledApplicationTypes()
        const defaultAppType = enabledApps.length > 0 ? enabledApps[0].id : 'device'
        const homePath = getAppHomePath(defaultAppType)
        console.log('🔄 根路径重定向到首页 (有AppID):', homePath, '应用类型:', defaultAppType)
        return homePath
      }
      
      // 🎯 没有AppID时，跳转到统一错误处理页面
      console.log('🔄 根路径重定向到AppID缺失页面 (无AppID)')
      return { name: 'AppError', params: { type: 'missing' } }
    }
  },

  // 统一应用错误处理页面
  {
    path: '/app-error/:type?',
    name: 'AppError',
    component: () => import('../views/AppErrorHandler.vue'),
    meta: { title: '应用错误', requiresAuth: false }
  },

  // 兼容旧路由 - 重定向到统一错误页面
  {
    path: '/app-not-published',
    redirect: to => ({
      name: 'AppError',
      params: { type: 'not-published' },
      query: to.query
    })
  },
  {
    path: '/app-id-missing',
    redirect: to => ({
      name: 'AppError',
      params: { type: 'missing' },
      query: to.query
    })
  },

  // 404页面
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue'),
    meta: { title: '页面未找到', requiresAuth: false }
  },

  // WebView页面
  {
    path: '/webview',
    name: 'WebView',
    component: () => import('../views/WebView.vue'),
    meta: { title: '网页', requiresAuth: false }
  },

  // 兜底404
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// ==================== 自动生成业务路由 ====================

// 生成所有启用应用的路由
const enabledApps = getEnabledApplicationTypes()
const allGeneratedRoutes: RouteRecordRaw[] = []

// 开发环境调试信息
if (import.meta.env.DEV) {
  console.log('🚀 启用的应用类型:', enabledApps)
}

// 为每个启用的应用生成路由
enabledApps.forEach(app => {
  if (import.meta.env.DEV) {
    console.log(`🔧 生成应用路由: ${app.id}`)
  }

  const generatedRoutes = RouteGenerator.generateH5Routes(app.id as any)

  if (import.meta.env.DEV) {
    console.log(`📋 生成的路由数量: ${generatedRoutes.length}`, generatedRoutes.map(r => `${String(r.path)} (${String(r.name)})`))
    console.log(`🔍 查找二维码支付路由:`, generatedRoutes.find(r => String(r.path).includes('qrcode')))
  }
  const processedRoutes = generatedRoutes.map(route => {
    if ('redirect' in route && route.redirect) {
      return {
        path: route.path,
        name: route.name,
        redirect: route.redirect,
        meta: route.meta
      }
    }

    const componentPath = (route.meta as any)?.componentPath
    const configurable = (route.meta as any)?.configurable

    let component
    if (configurable) {
      // 低代码页面
      component = () => import('../views/DynamicPage.vue')
    } else if (componentPath) {
      // 原生页面 - 使用 import.meta.glob 安全加载
      const appType = (route.meta as any)?.appType || app.id
      const fullPath = `../modules/${appType}/pages/${componentPath.replace('./pages/', '')}`

      // 根据应用类型选择对应的模块映射
      const modules = MODULE_MAP[appType as keyof typeof MODULE_MAP]
      const loader = modules?.[fullPath]

      if (loader) {
        component = loader
        if (import.meta.env.DEV) {
          console.log(`✅ 找到组件: ${componentPath} → ${fullPath}`)
        }
      } else {
        if (import.meta.env.DEV) {
          console.warn(`❌ 未找到组件: ${fullPath}`)
          console.log(`📋 可用组件:`, Object.keys(modules || {}))
        }
        component = () => Promise.resolve({
          template: `<div style="padding: 20px; color: red;">未找到组件: ${componentPath}</div>`
        })
      }
    } else {
      // 默认组件
      component = () => Promise.resolve({ template: '<div>页面组件未配置</div>' })
    }

    return {
      path: route.path,
      name: route.name,
      component,
      meta: route.meta
    }
  })

  allGeneratedRoutes.push(...processedRoutes)
})

// 为了兼容现有代码，保留deviceRoutes变量名
const deviceRoutes = allGeneratedRoutes

// 手动添加低代码页面路由（支持多种应用前缀）
const dynamicPageRoutes: RouteRecordRaw[] = [
  // 低代码页面 - 通用匹配（包括根路径下的页面）
  {
    path: '/:pagePath+',
    name: 'DynamicPage',
    component: () => import('../views/DynamicPage.vue'),
    meta: { title: '应用页面', requiresAuth: true, isDynamicPage: true }
  }
]

// 注释：criticalRoutes已删除，所有路由现在都通过自动生成

// ==================== 合并所有路由 ====================

const routes: RouteRecordRaw[] = [
  ...baseRoutes,
  ...deviceRoutes,  // 原生应用路由优先，避免被通用路由拦截
  ...dynamicPageRoutes  // 低代码页面路由放最后，作为兜底
]



// ==================== 创建路由实例 ====================

// 调试：打印所有路由
console.log('📋 注册的路由:', routes.map(r => ({ path: r.path, name: r.name })))

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 调试：打印路由器的所有路由
console.log('🔍 路由器中的路由:', router.getRoutes().map(r => ({ path: r.path, name: r.name })))

// 设置路由守卫
setupRouterGuards(router)

// 添加路由调试 - 在守卫之前
router.beforeEach((to, from, next) => {
  console.log('🚦 [路由调试] 路由变化:', {
    from: from.path,
    to: to.path,
    fullPath: to.fullPath,
    name: to.name,
    params: to.params,
    matched: to.matched.map(m => ({ path: m.path, name: m.name }))
  })
  next()
})

export default router
