<template>
  <div class="profile-page">
    <div class="profile-header">
      <div class="avatar">
        <Icon icon="mdi:account-circle" size="80" />
      </div>
      <h2>用户中心</h2>
      <p>管理您的个人信息和设置</p>
    </div>

    <div class="profile-content">
      <div class="menu-section">
        <h3>个人信息</h3>
        <div class="menu-item" @click="handleMenuClick('userInfo')">
          <Icon icon="mdi:account-edit" />
          <span>个人资料</span>
          <Icon icon="mdi:chevron-right" />
        </div>
        <div class="menu-item" @click="handleMenuClick('realName')">
          <Icon icon="mdi:card-account-details" />
          <span>实名认证</span>
          <Icon icon="mdi:chevron-right" />
        </div>
      </div>

      <div class="menu-section">
        <h3>设备管理</h3>
        <div class="menu-item" @click="handleMenuClick('deviceInfo')">
          <Icon icon="mdi:router-wireless" />
          <span>设备信息</span>
          <Icon icon="mdi:chevron-right" />
        </div>
        <div class="menu-item" @click="handleMenuClick('deviceSettings')">
          <Icon icon="mdi:cog" />
          <span>设备设置</span>
          <Icon icon="mdi:chevron-right" />
        </div>
      </div>

      <div class="menu-section">
        <h3>其他</h3>
        <div class="menu-item" @click="handleMenuClick('about')">
          <Icon icon="mdi:information" />
          <span>关于我们</span>
          <Icon icon="mdi:chevron-right" />
        </div>
        <div class="menu-item" @click="handleMenuClick('logout')">
          <Icon icon="mdi:logout" />
          <span>退出登录</span>
          <Icon icon="mdi:chevron-right" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'

const router = useRouter()

const handleMenuClick = (action: string) => {
  switch (action) {
    case 'userInfo':
      showToast('个人资料功能开发中')
      break
    case 'realName':
      showToast('实名认证功能开发中')
      break
    case 'deviceInfo':
      showToast('设备信息功能开发中')
      break
    case 'deviceSettings':
      showToast('设备设置功能开发中')
      break
    case 'about':
      showToast('关于我们功能开发中')
      break
    case 'logout':
      showConfirmDialog({
        title: '确认退出',
        message: '确定要退出登录吗？',
      }).then(() => {
        // 执行退出逻辑
        showToast('退出成功')
        router.push('/device/login')
      }).catch(() => {
        // 用户取消
      })
      break
  }
}
</script>

<style scoped lang="scss">
.profile-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.profile-header {
  text-align: center;
  color: white;
  margin-bottom: 30px;
  
  .avatar {
    margin-bottom: 15px;
  }
  
  h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
  }
  
  p {
    margin: 0;
    opacity: 0.9;
    font-size: 14px;
  }
}

.profile-content {
  .menu-section {
    background: white;
    border-radius: 12px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    
    h3 {
      margin: 0;
      padding: 15px 20px 10px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .menu-item {
      display: flex;
      align-items: center;
      padding: 15px 20px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background-color 0.2s;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover {
        background-color: #f8f9fa;
      }
      
      &:active {
        background-color: #e9ecef;
      }
      
      > :first-child {
        color: #667eea;
        margin-right: 12px;
      }
      
      > span {
        flex: 1;
        font-size: 15px;
        color: #333;
      }
      
      > :last-child {
        color: #ccc;
      }
    }
  }
}
</style>
