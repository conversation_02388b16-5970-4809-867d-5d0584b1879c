<template>
  <div class="not-found">
    <!-- 主要内容容器 -->
    <div class="error-content">
      <!-- 404插图 -->
      <div class="error-illustration">
        <div class="illustration-container">
          <!-- 背景圆圈 -->
          <div class="bg-circle"></div>

          <!-- 主要图形 -->
          <div class="main-circle">
            <div class="error-text">
              <div class="error-code">404</div>
              <div class="error-line"></div>
            </div>
          </div>

          <!-- 装饰元素 -->
          <div class="decoration decoration-1"></div>
          <div class="decoration decoration-2"></div>
          <div class="decoration decoration-3"></div>
        </div>
      </div>

      <!-- 文字内容 -->
      <div class="text-content">
        <h1 class="error-title">{{ getTitle() }}</h1>
        <p class="error-description">{{ getDescription() }}</p>
        <p v-if="isAppNotFound" class="app-hint">
          应用ID: <code>{{ getAppIdFromPath() }}</code>
          <br>
          请确认应用ID是否正确，或联系管理员创建该应用
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <button @click="goHome" class="primary-button">
          <svg viewBox="0 0 24 24" class="button-icon">
            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" fill="currentColor"/>
          </svg>
          返回首页
        </button>

        <div class="secondary-buttons">
          <button @click="refresh" class="secondary-button">
            <svg viewBox="0 0 24 24" class="button-icon">
              <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" fill="currentColor"/>
            </svg>
            刷新
          </button>

          <button @click="goBack" class="secondary-button">
            <svg viewBox="0 0 24 24" class="button-icon">
              <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" fill="currentColor"/>
            </svg>
            返回
          </button>
        </div>
      </div>

      <!-- 底部提示 -->
      <div class="bottom-hint">
        <p class="hint-text">技术支持：安生低代码引擎</p>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="background-decorations">
      <div class="bg-decoration bg-decoration-1"></div>
      <div class="bg-decoration bg-decoration-2"></div>
      <div class="bg-decoration bg-decoration-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getEnabledApplicationTypes, getAppHomePath, getAppLoginPath } from '@lowcode/aslib/core'

const router = useRouter()
const route = useRoute()

// 检查是否是应用不存在的情况
const isAppNotFound = computed(() => {
  return route.path.startsWith('/app/')
})

// 获取应用ID
const getAppIdFromPath = () => {
  const match = route.path.match(/^\/app\/([^\/]+)/)
  return match ? match[1] : ''
}

// 获取标题
const getTitle = () => {
  if (isAppNotFound.value) {
    return '应用不存在'
  }
  return '页面走丢了'
}

// 获取描述
const getDescription = () => {
  if (isAppNotFound.value) {
    return '抱歉，您访问的应用暂时无法找到。可能是应用ID有误或应用尚未创建。'
  }
  return '抱歉，您访问的页面暂时无法找到。可能是页面地址有误或页面已被删除。'
}

// 返回首页
const goHome = () => {
  // 🎯 动态获取首页路径，不硬编码
  const enabledApps = getEnabledApplicationTypes()

  if (enabledApps.length > 0) {
    const defaultApp = enabledApps[0]

    // 检查是否有存储的应用ID
    const storedAppId = localStorage.getItem('current-app-id')
    if (storedAppId && storedAppId.trim() !== '') {
      // 有AppID，跳转到首页
      const homePath = getAppHomePath(defaultApp.id)
      console.log('🏠 返回首页:', homePath)
      router.push(homePath)
    } else {
      // 没有AppID，跳转到登录页
      const loginPath = getAppLoginPath(defaultApp.id)
      console.log('🔑 跳转到登录页:', loginPath)
      router.push(loginPath)
    }
  } else {
    // 兜底：没有启用的应用，跳转到根路径
    console.warn('⚠️ 没有找到启用的应用类型，跳转到根路径')
    router.push('/')
  }
}

// 刷新页面
const refresh = () => {
  window.location.reload()
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.back()
  } else {
    // 如果没有历史记录，返回首页
    goHome()
  }
}
</script>

<style scoped lang="scss">
.not-found {
  min-height: 100vh;
  background: linear-gradient(135deg, #dbeafe 0%, #ffffff 50%, #faf5ff 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

.error-content {
  max-width: 14rem;
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.error-illustration {
  position: relative;
  
  .illustration-container {
    width: 6rem;
    height: 6rem;
    margin: 0 auto;
    position: relative;
  }

  .bg-circle {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, #dbeafe 0%, #e9d5ff 100%);
    border-radius: 50%;
    opacity: 0.5;
  }

  .main-circle {
    position: absolute;
    inset: 0.5rem;
    background: white;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .error-text {
    text-align: center;
    
    .error-code {
      font-size: 1.5rem;
      font-weight: bold;
      color: #d1d5db;
      margin-bottom: 0.25rem;
    }

    .error-line {
      width: 2rem;
      height: 0.125rem;
      background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%);
      border-radius: 9999px;
      margin: 0 auto;
    }
  }

  .decoration {
    position: absolute;
    border-radius: 50%;
    
    &.decoration-1 {
      top: -0.125rem;
      right: -0.125rem;
      width: 1rem;
      height: 1rem;
      background: #fbbf24;
      animation: bounce 2s infinite;
    }

    &.decoration-2 {
      bottom: 0rem;
      left: 0rem;
      width: 0.75rem;
      height: 0.75rem;
      background: #ec4899;
      animation: pulse 2s infinite;
    }

    &.decoration-3 {
      top: 25%;
      left: -0.375rem;
      width: 0.5rem;
      height: 0.5rem;
      background: #10b981;
      animation: ping 2s infinite;
    }
  }
}

.text-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  .error-title {
    font-size: 1.125rem;
    font-weight: bold;
    color: #1f2937;
  }

  .error-description {
    color: #6b7280;
    font-size: 0.75rem;
    line-height: 1.4;
    white-space: pre-line;
  }
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;

  .primary-button {
    width: 100%;
    background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    border: none;
    border-radius: 9999px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
    }

    .button-icon {
      width: 0.75rem;
      height: 0.75rem;
    }
  }

  .secondary-buttons {
    display: flex;
    gap: 0.375rem;

    .secondary-button {
      flex: 1;
      border: 1px solid #e5e7eb;
      background: transparent;
      color: #6b7280;
      border-radius: 9999px;
      padding: 0.375rem 0.5rem;
      font-size: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.125rem;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #f9fafb;
        border-color: #d1d5db;
      }

      .button-icon {
        width: 0.75rem;
        height: 0.75rem;
      }
    }
  }
}

.bottom-hint {
  padding-top: 1rem;

  .hint-text {
    font-size: 0.65rem;
    color: #9ca3af;
  }
}

.background-decorations {
  position: fixed;
  inset: 0;
  pointer-events: none;
  overflow: hidden;

  .bg-decoration {
    position: absolute;
    border-radius: 50%;
    
    &.bg-decoration-1 {
      top: 25%;
      left: 25%;
      width: 0.375rem;
      height: 0.375rem;
      background: #93c5fd;
      animation: float 3s ease-in-out infinite;
    }

    &.bg-decoration-2 {
      top: 75%;
      right: 25%;
      width: 0.5rem;
      height: 0.5rem;
      background: #c4b5fd;
      animation: float-delayed 4s ease-in-out infinite 1s;
    }

    &.bg-decoration-3 {
      bottom: 25%;
      left: 33%;
      width: 0.25rem;
      height: 0.25rem;
      background: #f9a8d4;
      animation: float 3s ease-in-out infinite;
    }
  }
}

/* 动画 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes float-delayed {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

// 应用提示样式
.app-hint {
  margin-top: 16px;
  padding: 12px 16px;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  color: #856404;

  code {
    background: rgba(255, 193, 7, 0.2);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    font-weight: 600;
  }
}
</style>
