<template>
  <div class="dynamic-page">
    <!-- 页面加载中 -->
    <div v-if="loading" class="loading-container">
      <!-- 导航栏骨架 -->
      <div class="skeleton-nav">
        <div class="skeleton-nav-back"></div>
        <div class="skeleton-nav-title"></div>
      </div>
      
      <!-- 内容骨架 -->
      <div class="skeleton-content">
        <div class="skeleton-card" v-for="i in 3" :key="i">
          <div class="skeleton-header">
            <div class="skeleton-circle"></div>
            <div class="skeleton-text-group">
              <div class="skeleton-line skeleton-title"></div>
              <div class="skeleton-line skeleton-subtitle"></div>
            </div>
          </div>
          <div class="skeleton-body">
            <div class="skeleton-line skeleton-text"></div>
            <div class="skeleton-line skeleton-text short"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 路由解析错误 -->
    <ErrorState
      v-else-if="resolveError"
      error-code="ROUTE_ERROR"
      title="路由解析失败"
      :description="resolveError"
      primary-button-text="重新加载"
      :on-primary-action="loadPageConfig"
      :on-refresh="loadPageConfig"
      :on-back="() => $router.back()"
    />

    <!-- 页面加载错误 -->
    <ErrorState
      v-else-if="error"
      error-code="PAGE_ERROR"
      title="页面加载失败"
      :description="error"
      primary-button-text="重新加载"
      :on-primary-action="loadPageConfig"
      :on-refresh="loadPageConfig"
      :on-back="() => $router.back()"
    />

    <!-- 页面内容 -->
    <div v-else-if="pageConfig" class="page-content" :style="combinedPageStyle">
      <!-- 动态渲染组件 -->
      <div class="components-container">
        <div
          v-for="comp in pageConfig.components"
          :key="comp.id"
          class="component-wrapper"
          :data-component-id="comp.id"
          :data-component-type="comp.type"
        >
          <!-- 调试样式应用 -->
          <!-- {{ console.log('🎨 [H5] 组件样式:', comp.id, comp.type, comp.style) }} -->
          <component
            :is="getComponentType(comp.type)"
            v-bind="comp.props"
            v-on="getComponentEventHandlers(comp)"
            :style="comp.style || {}"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useDynamicPage } from '../composables/useDynamicPage'
import ErrorState from '../components/ErrorState.vue'

// 定义组件名称，用于 KeepAlive 缓存
defineOptions({
  name: 'DynamicPage'
})

// 路由解析错误状态（保留用于错误显示）
const resolveError = ref<string | null>(null)

// 使用组合式函数获取页面状态和渲染相关方法
const {
  pageConfig,
  loading,
  error,
  combinedPageStyle,
  getComponentType,
  getComponentEventHandlers,
  loadPageConfig: reloadPageConfig
} = useDynamicPage()

// 页面加载现在由 useDynamicPage 组合式函数处理

// 重新加载页面配置的方法
const loadPageConfig = () => {
  reloadPageConfig()
}
</script>

<style scoped lang="scss">
.dynamic-page {
  min-height: 100vh;
  /* 移除固定背景色，使用页面配置中的背景样式 */
  /* 确保页面可以滚动 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

/* 骨架屏样式 */
.loading-container {
  padding: 0;
  background: #f8f9fa;
  min-height: 100vh;
}

.skeleton-nav {
  height: 46px;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #eee;
  
  .skeleton-nav-back {
    width: 20px;
    height: 20px;
    background: #e9ecef;
    border-radius: 4px;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }
  
  .skeleton-nav-title {
    width: 120px;
    height: 20px;
    background: #e9ecef;
    border-radius: 4px;
    margin-left: auto;
    margin-right: auto;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }
}

.skeleton-content {
  padding: 16px;
}

.skeleton-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  
  .skeleton-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    
    .skeleton-circle {
      width: 40px;
      height: 40px;
      background: #e9ecef;
      border-radius: 50%;
      margin-right: 12px;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }
    
    .skeleton-text-group {
      flex: 1;
      
      .skeleton-title {
        width: 60%;
        height: 16px;
        margin-bottom: 8px;
      }
      
      .skeleton-subtitle {
        width: 40%;
        height: 12px;
      }
    }
  }
  
  .skeleton-body {
    .skeleton-text {
      height: 12px;
      margin-bottom: 8px;
      
      &.short {
        width: 70%;
      }
    }
  }
}

.skeleton-line {
  background: #e9ecef;
  border-radius: 4px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}



/* 页面内容 */
.page-content {
  min-height: 100vh;
  /* 确保内容可以滚动 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

/* 组件容器 */
.components-container {
  .component-wrapper {
    width: 100%;
    min-height: 0px;
  }
}
</style>
