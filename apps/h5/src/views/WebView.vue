<template>
  <div class="webview-page">
    <!-- 导航栏 -->
    <van-nav-bar 
      v-if="showNavBar"
      :title="pageTitle" 
      :left-arrow="showBackButton"
      @click-left="handleBack"
    >
      <template #right v-if="showRefreshButton || allowShare">
        <div class="nav-actions">
          <van-icon 
            v-if="showRefreshButton"
            name="replay" 
            @click="handleRefresh"
            class="action-icon"
          />
          <van-icon 
            v-if="allowShare"
            name="share-o" 
            @click="handleShare"
            class="action-icon"
          />
        </div>
      </template>
    </van-nav-bar>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" size="24px">加载中...</van-loading>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-container">
      <van-empty description="页面加载失败">
        <van-button round type="primary" @click="handleRefresh">重新加载</van-button>
      </van-empty>
      <p class="error-message">{{ error }}</p>
    </div>

    <!-- WebView容器 -->
    <div 
      v-show="!loading && !error"
      class="webview-container"
      :class="{ 'with-navbar': showNavBar }"
    >
      <iframe
        ref="iframeRef"
        :src="url"
        class="webview-iframe"
        @load="handleLoad"
        @error="handleError"
        frameborder="0"
        allowfullscreen
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 路由和状态
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const error = ref<string | null>(null)
const iframeRef = ref<HTMLIFrameElement>()

// 从路由参数获取配置
const url = computed(() => route.query.url as string)
const pageTitle = computed(() => (route.query.title as string) || '网页')
const options = computed(() => {
  const opts = route.query.options as string
  return opts ? opts.split(',') : ['showNavBar', 'showBackButton']
})

// 配置选项
const showNavBar = computed(() => options.value.includes('showNavBar'))
const showBackButton = computed(() => options.value.includes('showBackButton'))
const showRefreshButton = computed(() => options.value.includes('showRefreshButton'))
const allowShare = computed(() => options.value.includes('allowShare'))

// 处理加载完成
function handleLoad() {
  loading.value = false
  error.value = null
}

// 处理加载错误
function handleError() {
  loading.value = false
  error.value = '网页加载失败，请检查网络连接或网址是否正确'
}

// 返回上页
function handleBack() {
  router.back()
}

// 刷新页面
function handleRefresh() {
  if (iframeRef.value) {
    loading.value = true
    error.value = null
    iframeRef.value.src = url.value
  }
}

// 分享页面
function handleShare() {
  if (navigator.share) {
    navigator.share({
      title: pageTitle.value,
      url: url.value
    }).catch(() => {
      // 降级方案：复制到剪贴板
      copyToClipboard()
    })
  } else {
    // 降级方案：复制到剪贴板
    copyToClipboard()
  }
}

// 复制到剪贴板
function copyToClipboard() {
  try {
    navigator.clipboard.writeText(url.value)
  } catch (err) {
    console.error('复制失败:', err)
  }
}

// 生命周期
onMounted(() => {
  if (!url.value) {
    error.value = '缺少网页地址参数'
    loading.value = false
    return
  }
  
  // 验证URL格式
  try {
    new URL(url.value)
  } catch {
    error.value = '网页地址格式不正确'
    loading.value = false
  }
})
</script>

<style scoped lang="scss">
.webview-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.nav-actions {
  display: flex;
  gap: 12px;
  
  .action-icon {
    font-size: 18px;
    color: #333;
    cursor: pointer;
    
    &:hover {
      color: #1989fa;
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

.error-container {
  padding: 32px 16px;
  text-align: center;
  
  .error-message {
    margin-top: 16px;
    color: #ff4444;
    font-size: 14px;
  }
}

.webview-container {
  flex: 1;
  position: relative;
  
  &.with-navbar {
    // 如果有导航栏，需要减去导航栏高度
  }
}

.webview-iframe {
  width: 100%;
  height: 100%;
  border: none;
}
</style>
