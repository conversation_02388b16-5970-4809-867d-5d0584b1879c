<template>
  <div class="app-error-handler">
    <div class="error-content">
      <!-- 插图 -->
      <div class="error-illustration">
        <div class="illustration-container">
          <div class="bg-circle"></div>
          <div class="main-circle">
            <div class="error-text" v-if="!isProcessing">
              <div class="error-icon">{{ errorConfig.emoji }}</div>
              <div class="error-line"></div>
            </div>
            <div class="loading-text" v-else>
              <div class="loading-spinner">⟳</div>
              <div class="error-line"></div>
            </div>
          </div>
          <!-- 装饰元素 -->
          <div class="decoration decoration-1"></div>
          <div class="decoration decoration-2"></div>
          <div class="decoration decoration-3"></div>
        </div>
      </div>

      <!-- 文字内容 -->
      <div class="text-content">
        <h1 class="error-title" v-if="!isProcessing">{{ errorConfig.title }}</h1>
        <h1 class="error-title checking" v-else>{{ processingText }}</h1>
        
        <p class="error-description" v-if="!isProcessing">
          {{ errorConfig.description }}
        </p>
        <p class="error-description" v-else>
          正在处理中，请稍候...
        </p>

        <!-- 当前应用ID显示 -->
        <div class="app-hint" v-if="currentAppId && errorType !== 'missing' && !isProcessing">
          <strong>当前应用ID：</strong>
          <code>{{ currentAppId }}</code>
          <br>
          <small>{{ errorConfig.appIdHint }}</small>
        </div>

        <!-- 手动输入AppID -->
        <div class="app-hint" v-if="errorConfig.showInput && !isProcessing">
          <strong>{{ errorConfig.inputLabel }}：</strong>
          <br>
          <div class="input-group">
            <input
              v-model="newAppId"
              type="text"
              class="app-input"
              :placeholder="errorConfig.inputPlaceholder"
              @keyup.enter="handleSubmitNewAppId"
              :disabled="isProcessing"
            />
          </div>
          <small>例如：ansheng</small>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons" v-if="!isProcessing">
        <!-- 主要操作按钮 -->
        <button 
          v-if="errorConfig.showInput" 
          @click="handleSubmitNewAppId" 
          class="primary-button" 
          :disabled="!newAppId.trim()"
        >
          <svg viewBox="0 0 24 24" class="button-icon">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="currentColor"/>
          </svg>
          确认使用
        </button>

        <!-- 演示应用按钮（仅AppID缺失时显示） -->
        <button 
          v-else-if="errorType === 'missing'" 
          @click="goToDefaultApp" 
          class="primary-button"
        >
          <svg viewBox="0 0 24 24" class="button-icon">
            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" fill="currentColor"/>
          </svg>
          使用演示应用
        </button>

        <!-- 返回首页按钮（其他情况） -->
        <button 
          v-else 
          @click="goHome" 
          class="primary-button"
        >
          <svg viewBox="0 0 24 24" class="button-icon">
            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" fill="currentColor"/>
          </svg>
          返回首页
        </button>

        <!-- 次要操作按钮 -->
        <div class="secondary-buttons">
          <button 
            v-if="errorConfig.showRetry" 
            @click="handleRetry" 
            class="secondary-button"
          >
            <svg viewBox="0 0 24 24" class="button-icon">
              <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" fill="currentColor"/>
            </svg>
            重试
          </button>

          <button @click="refreshPage" class="secondary-button">
            <svg viewBox="0 0 24 24" class="button-icon">
              <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" fill="currentColor"/>
            </svg>
            刷新
          </button>

          <button @click="goBack" class="secondary-button">
            <svg viewBox="0 0 24 24" class="button-icon">
              <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" fill="currentColor"/>
            </svg>
            返回
          </button>
        </div>
      </div>

      <!-- 开发者提示（应用未发布时显示） -->
      <div class="developer-hint" v-if="errorType === 'not-published' && !isProcessing">
        <p class="help-title">开发者提示：</p>
        <ul class="help-list">
          <li>确保应用已在管理后台发布</li>
          <li>检查应用ID是否正确</li>
          <li>确认应用配置完整</li>
        </ul>
      </div>

      <!-- 底部提示 -->
      <div class="bottom-hint">
        <p class="hint-text">技术支持：安生低代码引擎</p>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="background-decorations">
      <div class="bg-decoration bg-decoration-1"></div>
      <div class="bg-decoration bg-decoration-2"></div>
      <div class="bg-decoration bg-decoration-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { setCurrentAppId } from '../services/SmartAppIdManager'
import { getEnabledApplicationTypes, getAppHomePath } from '@lowcode/aslib/core'
import { appDataManager } from '../services/AppDataManager'

const router = useRouter()
const route = useRoute()

// 状态管理
const isProcessing = ref(false)
const newAppId = ref('')
const processingText = ref('正在处理...')

// 错误类型和相关数据
const errorType = computed(() => {
  // 从路由参数或查询参数获取错误类型
  return (route.params.type as string) || (route.query.type as string) || 'missing'
})

const currentAppId = computed(() => {
  return (route.query.appId as string) || localStorage.getItem('current-app-id') || ''
})

const errorMessage = computed(() => route.query.message as string || '')

// 🔧 优化：简化错误配置，移除输入AppID功能
const errorConfig = computed(() => {
  // 如果有具体的错误消息，优先显示
  const customMessage = errorMessage.value

  switch (errorType.value) {
    case 'missing':
      return {
        emoji: '❓',
        title: '应用ID缺失',
        description: customMessage || '当前访问的页面缺少必要的应用标识。请通过正确的链接访问，或手动输入应用ID。',
        showInput: true,
        showRetry: false,
        inputLabel: '输入正确的应用ID',
        inputPlaceholder: '请输入应用ID',
        appIdHint: ''
      }

    case 'not-published':
      return {
        emoji: '⏰',
        title: '应用未发布',
        description: customMessage || '该应用存在但尚未发布，暂时无法访问。请输入其他应用ID或联系开发者。',
        showInput: true,
        showRetry: true,
        inputLabel: '输入其他应用ID',
        inputPlaceholder: '请输入其他应用ID',
        appIdHint: '该应用尚未发布，可以尝试其他应用'
      }

    case 'page-not-published':
      return {
        emoji: '📄',
        title: '页面未发布',
        description: customMessage || '该页面存在但尚未发布，暂时无法访问。请输入其他应用ID。',
        showInput: true,
        showRetry: true,
        inputLabel: '输入其他应用ID',
        inputPlaceholder: '请输入其他应用ID',
        appIdHint: '该页面尚未发布，可以尝试其他应用'
      }

    case 'app-not-published':
      return {
        emoji: '📱',
        title: '应用未发布',
        description: customMessage || '页面所属的应用尚未发布，暂时无法访问。请输入其他应用ID。',
        showInput: true,
        showRetry: true,
        inputLabel: '输入其他应用ID',
        inputPlaceholder: '请输入其他应用ID',
        appIdHint: '所属应用尚未发布，可以尝试其他应用'
      }

    case 'not-found':
    default:
      return {
        emoji: '❌',
        title: '页面不存在',
        description: customMessage || '找不到指定的页面，请检查链接是否正确。',
        showInput: true,
        showRetry: false,
        inputLabel: '输入正确的应用ID',
        inputPlaceholder: '请输入正确的应用ID',
        appIdHint: '该页面不存在，请检查应用ID'
      }
  }
})

// 🎯 动态获取首页路径
function getHomePath(): string {
  const enabledApps = getEnabledApplicationTypes()
  if (enabledApps.length > 0) {
    const defaultApp = enabledApps[0]
    const homePath = getAppHomePath(defaultApp.id)
    console.log('🏠 动态获取首页路径:', homePath, '应用类型:', defaultApp.id)
    return homePath
  }
  console.warn('⚠️ 没有找到启用的应用类型，使用根路径')
  return '/'
}

// 🎯 验证AppID是否有效
async function validateAppId(appId: string): Promise<boolean> {
  console.log('🔍 验证AppID有效性:', appId)
  const validation = await appDataManager.validateAppIdWithErrorHandling(appId)

  if (!validation.isValid) {
    // 如果验证失败，更新当前页面的错误类型
    router.replace({
      name: 'AppError',
      params: { type: validation.errorType },
      query: {
        appId: appId,
        message: validation.errorMessage
      }
    })
    return false
  }

  return true
}

// 🎯 提交新的AppID
const handleSubmitNewAppId = async () => {
  if (!newAppId.value.trim()) {
    message.warning('请输入应用ID')
    return
  }

  isProcessing.value = true
  processingText.value = '正在验证应用...'

  try {
    console.log('🔍 验证新的AppID:', newAppId.value)

    const isValid = await validateAppId(newAppId.value.trim())

    if (isValid) {
      // 验证成功，设置新的AppID
      setCurrentAppId(newAppId.value.trim(), 'manual')
      appDataManager.clearCache() // 清除旧缓存

      message.success(`应用ID验证成功：${newAppId.value}，正在跳转...`)
      processingText.value = '验证成功，正在跳转...'

      setTimeout(() => {
        const homePath = getHomePath()
        console.log('🔄 跳转到动态首页路径:', homePath)
        router.push(homePath)
      }, 1000)
    }
  } catch (error: any) {
    console.error('❌ AppID验证出错:', error)
    message.error('验证失败，请检查网络连接后重试')
  } finally {
    isProcessing.value = false
  }
}

// 🎯 重新尝试当前AppID
const handleRetry = async () => {
  if (!currentAppId.value) return

  isProcessing.value = true
  processingText.value = '正在重新验证...'

  try {
    console.log('🔄 重新验证当前AppID:', currentAppId.value)

    appDataManager.clearCache('app')
    const isValid = await validateAppId(currentAppId.value)

    if (isValid) {
      message.success('应用验证成功，正在跳转...')
      processingText.value = '验证成功，正在跳转...'
      
      setTimeout(() => {
        const homePath = getHomePath()
        router.push(homePath)
      }, 1000)
    }
  } catch (error) {
    console.error('❌ 重新验证失败:', error)
    message.error('验证失败，请稍后重试')
  } finally {
    isProcessing.value = false
  }
}

// 🎯 使用演示应用 - 修复：使用实际存在的应用ID
async function goToDefaultApp() {
  try {
    // 尝试使用实际存在的演示应用
    const demoAppIds = ['device-manager', 'demo-app', 'ansheng']

    for (const appId of demoAppIds) {
      console.log(`🔍 尝试验证演示应用: ${appId}`)
      const isValid = await appDataManager.validateAppId(appId)

      if (isValid) {
        setCurrentAppId(appId, 'manual')
        message.info(`已切换到演示应用：${appId}，正在跳转...`)

        setTimeout(() => {
          // 🔧 修复：使用正确的函数获取首页路径
          const enabledApps = getEnabledApplicationTypes()
          if (enabledApps.length > 0) {
            const defaultApp = enabledApps[0]
            const homePath = getAppHomePath(defaultApp.id)
            console.log('🔄 跳转到动态首页路径:', homePath)
            router.push(homePath)
          } else {
            console.warn('⚠️ 没有找到启用的应用类型，跳转到根路径')
            router.push('/')
          }
        }, 1000)
        return
      }
    }

    // 如果没有找到可用的演示应用，提示用户手动输入
    message.warning('暂无可用的演示应用，请手动输入应用ID')

  } catch (error) {
    console.error('❌ 演示应用切换失败:', error)
    message.error('演示应用切换失败，请手动输入应用ID')
  }
}

// 🎯 返回首页
const goHome = () => {
  const homePath = getHomePath()
  console.log('🏠 返回首页:', homePath)
  router.push(homePath)
}

// 返回上一页
function goBack() {
  if (window.history.length > 1) {
    router.back()
  } else {
    router.push('/')
  }
}

// 🔧 优化：智能刷新功能
function refreshPage() {
  console.log('🔄 智能刷新页面，错误类型:', errorType.value)
  isProcessing.value = false
  newAppId.value = ''

  switch (errorType.value) {
    case 'missing':
      // AppID缺失：重新检查AppID
      checkAndHandleAppId()
      break

    case 'page-not-published':
    case 'app-not-published':
    case 'not-published':
      // 页面或应用未发布：尝试重新访问原页面
      tryReloadOriginalPage()
      break

    default:
      // 其他错误：直接刷新页面
      window.location.reload()
  }
}

// 🔧 新增：尝试重新加载原页面
function tryReloadOriginalPage() {
  isProcessing.value = true
  processingText.value = '正在检查页面状态...'

  // 获取原始页面路径
  const referrer = document.referrer
  const currentUrl = window.location.href

  console.log('🔄 尝试重新加载原页面:', { referrer, currentUrl })

  // 如果有来源页面，尝试返回
  if (referrer && !referrer.includes('/app-error/')) {
    setTimeout(() => {
      console.log('🔄 返回来源页面:', referrer)
      window.location.href = referrer
    }, 1000)
  } else {
    // 否则返回首页
    setTimeout(() => {
      const homePath = getHomePath()
      console.log('🔄 返回首页:', homePath)
      router.push(homePath)
    }, 1000)
  }
}

// 🎯 检查并处理URL中的appid参数（仅限AppID缺失情况）
async function checkAndHandleAppId() {
  if (errorType.value !== 'missing' || isProcessing.value) return
  
  console.log('🔍 检查URL中的appid参数')
  
  // 首先检查本地存储
  const storedAppId = localStorage.getItem('current-app-id')
  console.log('🔍 本地存储的AppID:', storedAppId)
  
  if (storedAppId && storedAppId.trim() !== '') {
    isProcessing.value = true
    processingText.value = '检测到本地应用，正在跳转...'
    console.log('✅ 检测到本地存储的appid，自动跳转到首页', storedAppId)
    message.success(`已找到应用ID：${storedAppId}，正在跳转...`)

    setTimeout(() => {
      const homePath = getHomePath()
      console.log('🔄 跳转到动态首页路径:', homePath)
      router.push(homePath)
    }, 1000)
    return
  }
  
  // 检查URL中的appid参数
  let urlAppId = ''
  
  if (window.location.hash && window.location.hash.includes('?')) {
    const hashQuery = window.location.hash.split('?')[1]
    const urlParams = new URLSearchParams(hashQuery)
    urlAppId = urlParams.get('appid') || urlParams.get('appId') || ''
  } else {
    const urlParams = new URLSearchParams(window.location.search)
    urlAppId = urlParams.get('appid') || urlParams.get('appId') || ''
  }
  
  console.log('🔍 URL参数检查结果:', { urlAppId, currentUrl: window.location.href })
  
  if (urlAppId && urlAppId.trim()) {
    isProcessing.value = true
    processingText.value = '检测到URL参数，正在验证...'
    console.log('🔍 检测到URL中的appid参数，开始验证', urlAppId)

    const isValid = await validateAppId(urlAppId)

    if (isValid) {
      setCurrentAppId(urlAppId, 'url')
      message.success(`应用ID验证成功：${urlAppId}，正在跳转...`)
      processingText.value = '验证成功，正在跳转...'

      setTimeout(() => {
        const homePath = getHomePath()
        console.log('🔄 跳转到动态首页路径:', homePath)
        router.push(homePath)
      }, 1000)
    } else {
      isProcessing.value = false
    }
    return
  }
  
  console.log('ℹ️ 未检测到有效的appid参数')
}

// 监听路由变化
watch(() => route.fullPath, () => {
  if (errorType.value === 'missing') {
    console.log('🔄 路由变化，重新检查appid')
    checkAndHandleAppId()
  }
}, { immediate: true })

// 页面挂载时检查
onMounted(() => {
  console.log('🚨 错误处理页面挂载:', {
    errorType: errorType.value,
    currentAppId: currentAppId.value,
    errorMessage: errorMessage.value
  })

  if (errorType.value === 'missing') {
    checkAndHandleAppId()
  }
})
</script>

<style scoped lang="scss">
.app-error-handler {
  min-height: 100vh;
  background: linear-gradient(135deg, #dbeafe 0%, #ffffff 50%, #faf5ff 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

.error-content {
  max-width: 14rem;
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.error-illustration {
  position: relative;
  
  .illustration-container {
    width: 6rem;
    height: 6rem;
    margin: 0 auto;
    position: relative;
  }

  .bg-circle {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    border-radius: 50%;
    opacity: 0.3;
  }

  .main-circle {
    position: absolute;
    inset: 0.5rem;
    background: white;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .error-text, .loading-text {
    text-align: center;
    
    .error-icon {
      font-size: 1.8rem;
      color: #f59e0b;
      margin-bottom: 0.25rem;
    }

    .loading-spinner {
      font-size: 1.8rem;
      color: #3b82f6;
      margin-bottom: 0.25rem;
      animation: spin 1s linear infinite;
    }

    .error-line {
      width: 2rem;
      height: 0.125rem;
      background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
      border-radius: 9999px;
      margin: 0 auto;
    }
  }

  .decoration {
    position: absolute;
    border-radius: 50%;
    
    &.decoration-1 {
      top: -0.125rem;
      right: -0.125rem;
      width: 1rem;
      height: 1rem;
      background: #fbbf24;
      animation: bounce 2s infinite;
    }

    &.decoration-2 {
      bottom: 0rem;
      left: 0rem;
      width: 0.75rem;
      height: 0.75rem;
      background: #ec4899;
      animation: pulse 2s infinite;
    }

    &.decoration-3 {
      top: 25%;
      left: -0.375rem;
      width: 0.5rem;
      height: 0.5rem;
      background: #10b981;
      animation: ping 2s infinite;
    }
  }
}

.text-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  .error-title {
    font-size: 1.125rem;
    font-weight: bold;
    color: #1f2937;

    &.checking {
      color: #3b82f6;
    }
  }

  .error-description {
    color: #6b7280;
    font-size: 0.75rem;
    line-height: 1.4;
  }

  .app-hint {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background: rgba(251, 191, 36, 0.1);
    border: 1px solid rgba(251, 191, 36, 0.3);
    border-radius: 0.5rem;
    font-size: 0.65rem;
    line-height: 1.5;
    color: #92400e;
    text-align: left;

    code {
      background: rgba(251, 191, 36, 0.2);
      padding: 0.125rem 0.375rem;
      border-radius: 0.25rem;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 0.6rem;
      font-weight: 600;
    }

    small {
      color: #a16207;
      font-size: 0.6rem;
    }

    .input-group {
      margin: 0.5rem 0;
    }

    .app-input {
      width: 100%;
      padding: 0.375rem 0.5rem;
      border: 1px solid rgba(251, 191, 36, 0.5);
      border-radius: 0.25rem;
      background: rgba(255, 255, 255, 0.8);
      font-size: 0.75rem;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;

      &:focus {
        outline: none;
        border-color: #f59e0b;
        box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
      }

      &:disabled {
        background: #f3f4f6;
        cursor: not-allowed;
      }
    }
  }
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;

  .primary-button {
    width: 100%;
    background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
    color: white;
    border: none;
    border-radius: 9999px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .button-icon {
      width: 0.75rem;
      height: 0.75rem;
    }
  }

  .secondary-buttons {
    display: flex;
    gap: 0.375rem;

    .secondary-button {
      flex: 1;
      border: 1px solid #e5e7eb;
      background: transparent;
      color: #6b7280;
      border-radius: 9999px;
      padding: 0.375rem 0.5rem;
      font-size: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.125rem;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #f9fafb;
        border-color: #d1d5db;
      }

      .button-icon {
        width: 0.75rem;
        height: 0.75rem;
      }
    }
  }
}

.developer-hint {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 0.5rem;
  text-align: left;
  
  .help-title {
    font-size: 0.65rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
  }
  
  .help-list {
    font-size: 0.6rem;
    color: #6c757d;
    margin: 0;
    padding-left: 1rem;
    
    li {
      margin-bottom: 0.25rem;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.bottom-hint {
  padding-top: 1rem;

  .hint-text {
    font-size: 0.65rem;
    color: #9ca3af;
  }
}

.background-decorations {
  position: fixed;
  inset: 0;
  pointer-events: none;
  overflow: hidden;

  .bg-decoration {
    position: absolute;
    border-radius: 50%;
    
    &.bg-decoration-1 {
      top: 25%;
      left: 25%;
      width: 0.375rem;
      height: 0.375rem;
      background: #fed7aa;
      animation: float 3s ease-in-out infinite;
    }

    &.bg-decoration-2 {
      top: 75%;
      right: 25%;
      width: 0.5rem;
      height: 0.5rem;
      background: #fde68a;
      animation: float-delayed 4s ease-in-out infinite 1s;
    }

    &.bg-decoration-3 {
      bottom: 25%;
      left: 33%;
      width: 0.25rem;
      height: 0.25rem;
      background: #f9a8d4;
      animation: float 3s ease-in-out infinite;
    }
  }
}

/* 动画 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes float-delayed {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}
</style>