// H5端组件管理器 - 正确的实现

import * as UIComponents from '@lowcode/aslib/ui'
import { registerComponents } from '@lowcode/aslib/core'
import * as H5Components from '../components'

export class H5ComponentManager {
  private static instance: H5ComponentManager
  private initialized = false
  
  static getInstance(): H5ComponentManager {
    if (!this.instance) {
      this.instance = new H5ComponentManager()
    }
    return this.instance
  }
  
  // ✅ H5端的正确初始化：支持双重渲染模式
  async initialize(app: any): Promise<void> {
    if (this.initialized) return
    
    try {
      console.log('📱 初始化H5组件管理器...')
      
      // ✅ 1. 注册业务组件到Vue应用（用于普通页面直接使用）
      Object.entries(UIComponents).forEach(([name, component]) => {
        app.component(name, component)
      })

      // ✅ 2. 注册H5特有组件到Vue应用
      Object.entries(H5Components).forEach(([name, component]) => {
        app.component(name, component)
      })

      // ✅ 3. 注册所有组件到渲染引擎（用于低代码页面）
      registerComponents({
        ...UIComponents,
        ...H5Components
      })
      
      this.initialized = true
      console.log('✅ H5组件管理器初始化完成')
      console.log('📦 已注册业务组件:', Object.keys(UIComponents))
      console.log('📦 已注册H5组件:', Object.keys(H5Components))
      
    } catch (error) {
      console.error('❌ H5组件管理器初始化失败:', error)
      throw error
    }
  }
  
  // ✅ 检查组件是否可用
  isComponentAvailable(type: string): boolean {
    return type in UIComponents || type in H5Components
  }

  // ✅ 获取组件实例（用于直接使用）
  getComponent(type: string): any {
    return UIComponents[type as keyof typeof UIComponents] ||
           H5Components[type as keyof typeof H5Components]
  }

  // ✅ 获取所有可用组件
  getAllComponents(): Record<string, any> {
    return { ...UIComponents, ...H5Components }
  }
  
  // ✅ 获取组件元数据
  getComponentMetadata(type: string): any {
    const component = UIComponents[type as keyof typeof UIComponents] ||
                     H5Components[type as keyof typeof H5Components]
    if (!component) return null

    return component.__componentMetadata ||
           component.__vccOpts?.__componentMetadata ||
           component.default?.__componentMetadata
  }
}

// 导出单例实例
export const h5ComponentManager = H5ComponentManager.getInstance()
