// ✅ 完全复制device-an的选项配置

/**
 * 设备状态选项 (完全复制device-an的逻辑)
 */
export const DeviceStatus = [
  { label: '未知', class: 'error' },
  { label: '待激活', class: 'warning' },
  { label: '已激活', class: 'success' },
  { label: '已停机', class: 'error' },
]

/**
 * 实名认证状态选项 (完全复制device-an的逻辑)
 * 注意：保持与device-an相同的拼写错误RealNnameStatus
 */
export const RealNameStatus = [
  { value: 1, label: '未知', class: 'error' },
  { value: 2, label: '已实名', class: 'success' },
  { value: 3, label: '未实名', class: 'error' },
  { value: 4, label: '无需实名', class: 'success' }
]

// ✅ 完全复制device-an的拼写错误版本
export const RealNnameStatus = RealNameStatus

/**
 * 设备网络选项 (完全复制device-an的逻辑)
 * 注意：数组索引对应运营商编号
 * 0: 未知, 1: 中国电信, 2: 中国联通, 3: 中国移动, 4: 中国广电
 */
export const DeviceNetWork = [
  { value: 0, label: '未知', class: 'wait' },
  { value: 1, label: '中国电信', class: 'DX' },
  { value: 2, label: '中国联通', class: 'LT' },
  { value: 3, label: '中国移动', class: 'YD' },
  { value: 4, label: '中国广电', class: 'GD' }
]



/**
 * 套餐类型选项 (完全复制device-an的逻辑)
 */
export const PackageType = [
  { label: '流量套餐', value: 1 },
  { label: '时长套餐', value: 2 },
  { label: '无限套餐', value: 3 }
]

/**
 * 设备套餐类型选项 (完全复制device-an的逻辑)
 */
export const DevicePackageType = [
  { value: 1, label: '流量套餐' },
  { value: 2, label: '语音套餐' },
  { value: 3, label: '短信套餐' },
  { value: 4, label: '综合套餐' }
]

/**
 * 支付方式选项 (完全复制device-an的逻辑)
 */
export const PaymentMethods = [
  { label: '微信支付', value: 'wechat', icon: 'mdi:wechat' },
  { label: '支付宝', value: 'alipay', icon: 'mdi:alipay' },
  { label: '余额支付', value: 'balance', icon: 'mdi:wallet' }
]

/**
 * 订单状态选项 (完全复制device-an的逻辑)
 */
export const OrderStatus = [
  { label: '待支付', class: 'warning', value: 0 },
  { label: '已支付', class: 'success', value: 1 },
  { label: '已取消', class: 'error', value: 2 },
  { label: '已退款', class: 'error', value: 3 }
]
