/**
 * 统一的 axios 请求工具
 * 替换所有 fetch 请求，使用环境变量配置
 */

import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { message } from 'ant-design-vue'
import { ENV_CONFIG } from '../config/env'

// 创建 axios 实例
const createAxiosInstance = (baseURL: string): AxiosInstance => {
  const instance = axios.create({
    baseURL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 添加认证token等
      const token = localStorage.getItem('auth-token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      
      if (ENV_CONFIG.DEBUG) {
        console.log('🚀 [请求]', config.method?.toUpperCase(), config.url, config.data)
      }
      
      return config
    },
    (error) => {
      console.error('❌ [请求错误]', error)
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      if (ENV_CONFIG.DEBUG) {
        console.log('✅ [响应]', response.config.url, response.status, response.data)
      }
      return response
    },
    (error) => {
      console.error('❌ [响应错误]', error.config?.url, error.response?.status, error.message)
      
      // 统一错误处理
      if (error.response?.status === 401) {
        message.error('登录已过期，请重新登录')
        // 可以在这里处理登录跳转
      } else if (error.response?.status === 403) {
        message.error('没有权限访问')
      } else if (error.response?.status === 404) {
        message.error('请求的资源不存在')
      } else if (error.response?.status >= 500) {
        message.error('服务器错误，请稍后重试')
      } else if (error.code === 'ECONNABORTED') {
        message.error('请求超时，请检查网络连接')
      } else {
        message.error(error.message || '请求失败')
      }
      
      return Promise.reject(error)
    }
  )

  return instance
}

// 创建不同的 axios 实例
export const apiRequest = createAxiosInstance(ENV_CONFIG.API_BASE_URL)
export const publicApiRequest = createAxiosInstance(ENV_CONFIG.API_BASE_URL)

// 便捷的请求方法
export const request = {
  // 普通API请求
  get: (url: string, config?: AxiosRequestConfig) => 
    apiRequest.get(`${ENV_CONFIG.API_PREFIX}${url}`, config),
  
  post: (url: string, data?: any, config?: AxiosRequestConfig) => 
    apiRequest.post(`${ENV_CONFIG.API_PREFIX}${url}`, data, config),
  
  put: (url: string, data?: any, config?: AxiosRequestConfig) => 
    apiRequest.put(`${ENV_CONFIG.API_PREFIX}${url}`, data, config),
  
  delete: (url: string, config?: AxiosRequestConfig) => 
    apiRequest.delete(`${ENV_CONFIG.API_PREFIX}${url}`, config),

  // 公共API请求
  publicGet: (url: string, config?: AxiosRequestConfig) => 
    publicApiRequest.get(`${ENV_CONFIG.PUBLIC_API_PREFIX}${url}`, config),
  
  publicPost: (url: string, data?: any, config?: AxiosRequestConfig) => 
    publicApiRequest.post(`${ENV_CONFIG.PUBLIC_API_PREFIX}${url}`, data, config),
}

// 默认导出
export default request
