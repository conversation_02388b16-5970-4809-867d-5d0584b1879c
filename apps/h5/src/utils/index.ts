// ✅ 完全复制device-an的工具函数

/**
 * 流量单位转换 (完全复制device-an的逻辑)
 * @param flow 流量值(MB)
 * @returns [数值, 单位]
 */
export const toGB = (flow: number): [string, string] => {
  if (!flow || flow === 0) return ['0', 'MB']
  
  if (flow < 1024) {
    return [flow.toFixed(2), 'MB']
  } else {
    const gb = flow / 1024
    return [gb.toFixed(2), 'GB']
  }
}

// 导入统一的剪贴板工具
import { copyToClipboard } from '@lowcode/aslib/ui'

/**
 * 复制文本到剪贴板 (使用统一的剪贴板工具)
 * @param text 要复制的文本
 */
export const CopyTxt = async (text: string) => {
  if (!text) return

  const success = await copyToClipboard(text)
  if (success) {
    console.log('复制成功')
  } else {
    console.error('复制失败')
  }
}

/**
 * 时间格式化 (完全复制device-an的逻辑)
 * @param date 日期
 * @param format 格式化模板，如'YYYY年MM月DD日'
 * @returns 格式化后的时间字符串
 */
export const toTime = (date: Date | string, format?: string): string => {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')

  if (format) {
    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
  }

  return `${year}-${month}-${day} ${hours}:${minutes}`
}

/**
 * 计算时间差 (完全复制device-an的逻辑)
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 相差天数
 */
export const calculateTimeDifference = (startDate: Date, endDate: Date | string): number => {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const diffTime = end.getTime() - start.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays > 0 ? diffDays : 0
}

/**
 * 获取套餐有效期描述 (完全复制device-an的逻辑)
 * @param item 套餐数据
 * @returns 有效期描述
 */
export const GetPackageDays = (item: any) => {
  switch (item.packageValidity) {
    case 1:
      return '月底清零'
    case 2:
      return '26号清零'
    case 3:
      return `${item.validityDays}天有效`
    case 4:
      return '按时计费'
    case 5:
      return '按量计费'
    default:
      return '未知'
  }
}

/**
 * 更新设备详情 (完全复制device-an的逻辑)
 */
export const RenewDeviceDetails = async () => {
  const { useDeviceStore } = await import('../modules/device/stores/device')
  const { showLoadingToast, showSuccessToast, showFailToast } = await import('vant')

  const deviceStore = useDeviceStore()

  // 显示加载提示 (完全复制device-an的UI)
  const toast = showLoadingToast({
    message: ' 更新中...',
    forbidClick: true,
    duration: 0
  })

  try {
    // 调用设备更新方法 (完全复制device-an的逻辑)
    await deviceStore.renewDevice()

    // 关闭加载提示并显示成功消息
    toast.close()
    showSuccessToast('更新成功')

    console.log('✅ 设备信息更新成功')

  } catch (error: any) {
    console.error('❌ 设备信息更新失败:', error)

    // 关闭加载提示并显示错误消息
    toast.close()
    showFailToast(error.message || '更新失败')
  }
}
