/**
 * 简化的组件注册工具
 * 一次性导入所有组件，避免每次手动添加
 */

import type { Component } from 'vue'

// 一次性导入所有UI组件
import * as LowcodeUI from '@lowcode/aslib/ui'

// 🔧 导入H5特有组件
import PageNotPublished from '../components/PageNotPublished.vue'

// 组件注册表
const componentRegistry = new Map<string, Component>()

// 初始化组件注册
export function initComponentRegistry() {
  // 注册所有从 @lowcode/ui 导出的组件
  Object.entries(LowcodeUI).forEach(([name, component]) => {
    // 检查是否是 Vue 组件（排除插件安装函数和其他工具）
    if (component &&
        typeof component === 'object' &&
        name !== 'default' &&
        !name.startsWith('set') &&
        !name.startsWith('use') &&
        name !== 'componentEvents') {
      componentRegistry.set(name, component as Component)
      console.log(`📦 注册组件: ${name}`)
    }
  })

  // 🔧 注册H5特有组件
  componentRegistry.set('PageNotPublished', PageNotPublished)
  console.log('📦 注册H5特有组件: PageNotPublished')

  console.log(`✅ 组件注册完成，共注册 ${componentRegistry.size} 个组件`)
}

// 获取组件
export function getComponent(componentType: string): Component | undefined {
  return componentRegistry.get(componentType)
}

// 获取组件类型（带兼容性处理）
export function getComponentType(type: string): Component | string {
  // 首先尝试直接获取
  let component = getComponent(type)

  // 如果没找到，尝试兼容旧的组件名
  if (!component) {
    const legacyMapping: Record<string, string> = {
      'DeviceInfo': 'HomeBasic',
      'NetworkSwitch': 'HomeNetWork',
      'DeviceDetails': 'HomeDetails',
      'QuickFunctions': 'HomeMore'
    }

    const newType = legacyMapping[type]
    if (newType) {
      component = getComponent(newType)
    }
  }

  if (!component) {
    console.warn(`⚠️ 未找到组件类型: ${type}`)
    return 'div' // 返回默认的 div 元素
  }

  return component
}

// 检查组件是否存在
export function hasComponent(componentType: string): boolean {
  return componentRegistry.has(componentType)
}

// 获取所有组件名称
export function getComponentNames(): string[] {
  return Array.from(componentRegistry.keys())
}
