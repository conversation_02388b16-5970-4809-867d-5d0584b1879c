import { ref, computed } from 'vue'
import type { ModernLoadingConfig } from '../config/modernLoading'
import { getModernTheme } from '../config/modernLoading'

interface ModernLoadingState {
  visible: boolean
  config: ModernLoadingConfig
  progress?: number
  text?: string
  showProgress: boolean
}

class ModernLoadingManager {
  private state = ref<ModernLoadingState>({
    visible: false,
    config: getModernTheme(),
    progress: undefined,
    text: undefined,
    showProgress: false
  })

  private loadingCount = 0
  private progressTimer: number | null = null

  /**
   * 显示现代化Loading
   */
  show(options?: Partial<ModernLoadingConfig & { 
    text?: string
    progress?: number
    showProgress?: boolean
  }>) {
    this.loadingCount++
    
    const config = {
      ...this.state.value.config,
      ...options
    }

    this.state.value = {
      visible: true,
      config,
      progress: options?.progress,
      text: options?.text,
      showProgress: options?.showProgress || false
    }

    // 自动进度
    if (config.type === 'fluid' && options?.showProgress && options?.progress === undefined) {
      this.startAutoProgress()
    }

    return this
  }

  /**
   * 隐藏Loading
   */
  hide(force = false) {
    if (force) {
      this.loadingCount = 0
    } else {
      this.loadingCount = Math.max(0, this.loadingCount - 1)
    }

    if (this.loadingCount === 0) {
      this.state.value.visible = false
      this.stopAutoProgress()
      
      setTimeout(() => {
        if (!this.state.value.visible) {
          this.state.value.progress = undefined
          this.state.value.text = undefined
        }
      }, 300)
    }

    return this
  }

  /**
   * 更新进度
   */
  progress(value: number) {
    if (this.state.value.visible) {
      this.state.value.progress = Math.max(0, Math.min(100, value))
      this.state.value.showProgress = true
    }
    return this
  }

  /**
   * 更新文本
   */
  text(value: string) {
    if (this.state.value.visible) {
      this.state.value.text = value
    }
    return this
  }

  /**
   * 链式调用 - 设置主题
   */
  theme(theme: Partial<ModernLoadingConfig>) {
    this.state.value.config = {
      ...this.state.value.config,
      ...theme
    }
    return this
  }

  /**
   * 获取状态
   */
  getState() {
    return this.state
  }

  /**
   * 极简显示 - 只显示加载动画
   */
  minimal(color = '#3B82F6') {
    return this.show({
      type: 'minimal',
      accentColor: color,
      backdrop: false,
      glassmorphism: false,
      size: 'sm'
    })
  }

  /**
   * 玻璃态显示 - 现代毛玻璃效果
   */
  glass(text?: string) {
    return this.show({
      type: 'glass',
      text,
      glassmorphism: true,
      backdrop: true,
      backdropBlur: 24,
      glow: true
    })
  }

  /**
   * 霓虹显示 - 科技感十足
   */
  neon(text?: string) {
    return this.show({
      type: 'neon',
      text,
      theme: 'dark',
      glow: true,
      particles: true,
      accentColor: '#00F5FF'
    })
  }

  /**
   * 流体显示 - 带进度的流体动画
   */
  fluid(text?: string, showProgress = true) {
    return this.show({
      type: 'fluid',
      text,
      showProgress,
      easing: 'elastic',
      glassmorphism: true
    })
  }

  /**
   * 呼吸显示 - 舒缓的呼吸动画
   */
  breathing(text?: string) {
    return this.show({
      type: 'breathing',
      text,
      glow: true,
      easing: 'ease'
    })
  }

  /**
   * 异步加载模拟
   */
  async simulate(duration = 3000, config?: Partial<ModernLoadingConfig>) {
    return new Promise<void>((resolve) => {
      this.show({
        ...config,
        showProgress: true
      })
      
      let progress = 0
      const increment = duration / 100 / 10
      
      const timer = setInterval(() => {
        progress += Math.random() * 5 + 2
        
        if (progress >= 100) {
          progress = 100
          this.progress(progress)
          
          setTimeout(() => {
            this.hide()
            resolve()
          }, 500)
          
          clearInterval(timer)
        } else {
          this.progress(progress)
        }
      }, increment)
    })
  }

  /**
   * 支付场景专用
   */
  payment() {
    return this.show({
      type: 'breathing',
      accentColor: '#059669',
      text: '安全支付处理中...',
      glow: true,
      showProgress: true,
      cancelable: false,
      timeout: 30000
    })
  }

  /**
   * 认证场景专用
   */
  auth() {
    return this.show({
      type: 'glass',
      accentColor: '#3B82F6',
      text: '身份验证中...',
      size: 'lg',
      timeout: 10000
    })
  }

  /**
   * AI处理场景专用
   */
  ai() {
    return this.show({
      type: 'neon',
      accentColor: '#00D4FF',
      text: 'AI处理中...',
      size: 'xl',
      particles: true,
      theme: 'dark'
    })
  }

  /**
   * 自动进度
   */
  private startAutoProgress() {
    this.stopAutoProgress()
    
    let progress = 0
    const increment = () => {
      if (progress < 90 && this.state.value.visible) {
        progress += Math.random() * 8 + 2
        this.progress(progress)
        this.progressTimer = window.setTimeout(increment, 150 + Math.random() * 200)
      }
    }
    
    increment()
  }

  /**
   * 停止自动进度
   */
  private stopAutoProgress() {
    if (this.progressTimer) {
      clearTimeout(this.progressTimer)
      this.progressTimer = null
    }
  }
}

// 创建全局实例
export const modernLoading = new ModernLoadingManager()

// Vue 插件
export default {
  install(app: any) {
    app.config.globalProperties.$modernLoading = modernLoading
    app.provide('modernLoading', modernLoading)
  }
}

// Composable
export function useModernLoading() {
  return modernLoading
}

// 快捷方法
export const loading = {
  show: (options?: any) => modernLoading.show(options),
  hide: (force?: boolean) => modernLoading.hide(force),
  minimal: (color?: string) => modernLoading.minimal(color),
  glass: (text?: string) => modernLoading.glass(text),
  neon: (text?: string) => modernLoading.neon(text),
  fluid: (text?: string, showProgress?: boolean) => modernLoading.fluid(text, showProgress),
  breathing: (text?: string) => modernLoading.breathing(text),
  payment: () => modernLoading.payment(),
  auth: () => modernLoading.auth(),
  ai: () => modernLoading.ai()
}