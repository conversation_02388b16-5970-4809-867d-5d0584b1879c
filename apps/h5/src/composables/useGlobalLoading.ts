import { ref, nextTick } from 'vue'
import type { LoadingConfig } from '../config/loading'
import { currentLoadingConfig } from '../config/loading'

interface LoadingState {
  visible: boolean
  config: LoadingConfig
  progress?: number
  text?: string
}

class LoadingManager {
  private state = ref<LoadingState>({
    visible: false,
    config: { ...currentLoadingConfig },
    progress: undefined
  })

  private loadingCount = 0
  private progressTimer: number | null = null

  /**
   * 显示Loading
   * @param options 加载配置选项
   */
  show(options?: Partial<LoadingConfig & { text?: string; progress?: number }>) {
    this.loadingCount++
    
    // 合并配置
    const config = {
      ...this.state.value.config,
      ...options
    }

    this.state.value = {
      visible: true,
      config,
      progress: options?.progress,
      text: options?.text || config.text
    }

    // 如果设置了自动进度
    if (config.showProgress && options?.progress === undefined) {
      this.startAutoProgress()
    }
  }

  /**
   * 隐藏Loading
   * @param force 强制隐藏所有Loading
   */
  hide(force = false) {
    if (force) {
      this.loadingCount = 0
    } else {
      this.loadingCount = Math.max(0, this.loadingCount - 1)
    }

    if (this.loadingCount === 0) {
      this.state.value.visible = false
      this.stopAutoProgress()
      
      // 延迟重置状态，等待动画完成
      setTimeout(() => {
        if (!this.state.value.visible) {
          this.state.value.progress = undefined
        }
      }, 300)
    }
  }

  /**
   * 更新进度
   * @param progress 进度值 0-100
   */
  updateProgress(progress: number) {
    if (this.state.value.visible) {
      this.state.value.progress = Math.max(0, Math.min(100, progress))
    }
  }

  /**
   * 更新文本
   * @param text 新的文本内容
   */
  updateText(text: string) {
    if (this.state.value.visible) {
      this.state.value.config.text = text
    }
  }

  /**
   * 切换主题
   * @param theme 主题配置
   */
  setTheme(theme: Partial<LoadingConfig>) {
    this.state.value.config = {
      ...this.state.value.config,
      ...theme
    }
  }

  /**
   * 获取当前状态
   */
  getState() {
    return this.state
  }

  /**
   * 自动进度动画
   */
  private startAutoProgress() {
    this.stopAutoProgress()
    
    let progress = 0
    const increment = () => {
      if (progress < 90 && this.state.value.visible) {
        progress += Math.random() * 10
        this.updateProgress(progress)
        this.progressTimer = window.setTimeout(increment, 200 + Math.random() * 300)
      }
    }
    
    increment()
  }

  /**
   * 停止自动进度
   */
  private stopAutoProgress() {
    if (this.progressTimer) {
      clearTimeout(this.progressTimer)
      this.progressTimer = null
    }
  }

  /**
   * 模拟加载过程
   * @param duration 持续时间(ms)
   * @param options 配置选项
   */
  async simulate(duration = 3000, options?: Partial<LoadingConfig & { text?: string }>) {
    return new Promise<void>((resolve) => {
      this.show({ ...options, showProgress: true })
      
      let progress = 0
      const step = 100 / (duration / 50)
      
      const timer = setInterval(() => {
        progress += step + Math.random() * 5
        
        if (progress >= 100) {
          progress = 100
          this.updateProgress(progress)
          
          setTimeout(() => {
            this.hide()
            resolve()
          }, 300)
          
          clearInterval(timer)
        } else {
          this.updateProgress(progress)
        }
      }, 50)
    })
  }
}

// 创建全局Loading实例
export const loading = new LoadingManager()

// Vue 3 插件
export default {
  install(app: any) {
    app.config.globalProperties.$loading = loading
    app.provide('loading', loading)
  }
}

// Hooks
export function useLoading() {
  return loading
}