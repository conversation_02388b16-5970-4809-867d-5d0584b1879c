/**
 * 动态页面组合式函数
 * 整合页面配置和事件处理逻辑
 */

import { ref, onMounted, onActivated, onDeactivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { PageConfig } from '@lowcode/aslib/core'
import { PageConfigService } from '../services/PageConfigService'
import { EventHandlerService } from '../services'
import { getComponentType } from '../utils/componentRegistry'
import { getCurrentAppId } from '../services/SmartAppIdManager'
import { useGlobalData } from '@lowcode/aslib/hooks'
import { dataManager, getCurrentEnvironment } from '@lowcode/aslib/ui/managers/DataManager'

export function useDynamicPage() {
  const route = useRoute()
  const router = useRouter()
  
  // 服务实例
  const pageConfigService = new PageConfigService()
  const eventHandlerService = new EventHandlerService(router)
  
  // 全局数据管理
  const { 
    setDeviceDetails, 
    setDeviceCards, 
    setRealNameCards, 
    setDeviceLoading,
    deviceData 
  } = useGlobalData()
  
  // 响应式数据
  const pageConfig = ref<PageConfig | null>(null)
  const loading = ref(true)
  const error = ref<string | null>(null)

  // 🎯 跟踪上次加载的参数，避免重复加载 - 简化版本
  const lastLoadedParams = ref<{ appId: string; pagePath: string } | null>(null)

  // 计算属性
  const layoutStyle = pageConfigService.computeLayoutStyle(pageConfig)
  const pageStyle = pageConfigService.computePageStyle(pageConfig)
  const combinedPageStyle = pageConfigService.computeCombinedStyle(layoutStyle, pageStyle)

  // 🌐 全局数据初始化
  async function initializeGlobalData() {
    console.log(`🌐 [${getCurrentEnvironment()}] 开始初始化全局数据...`)
    
    try {
      setDeviceLoading(true)
      
      // 并行加载所有必要的数据
      const [deviceDetails, deviceCards, realNameCards] = await Promise.allSettled([
        dataManager.getDeviceInfo(),
        dataManager.getDeviceCards(),
        // 假设存在 getRealNameCards 方法，如果没有可以移除这行
        Promise.resolve([]) // 临时使用空数组，实际应该是: dataManager.getRealNameCards()
      ])
      
      // 处理设备详情
      if (deviceDetails.status === 'fulfilled') {
        setDeviceDetails(deviceDetails.value)
        console.log('✅ 全局设备详情已设置:', deviceDetails.value)
      } else {
        console.error('❌ 设备详情加载失败:', deviceDetails.reason)
      }
      
      // 处理设备卡片
      if (deviceCards.status === 'fulfilled') {
        setDeviceCards(deviceCards.value)
        console.log('✅ 全局设备卡片已设置:', deviceCards.value)
      } else {
        console.error('❌ 设备卡片加载失败:', deviceCards.reason)
      }
      
      // 处理实名卡片
      if (realNameCards.status === 'fulfilled') {
        setRealNameCards(realNameCards.value)
        console.log('✅ 全局实名卡片已设置:', realNameCards.value)
      } else {
        console.error('❌ 实名卡片加载失败:', realNameCards.reason)
      }
      
      console.log('🌐 全局数据初始化完成')
    } catch (error) {
      console.error('❌ 全局数据初始化失败:', error)
    } finally {
      setDeviceLoading(false)
    }
  }

  // 加载页面配置 - 简化版本，使用智能AppID管理器，增强错误处理
  async function loadPageConfig(forceReload = false) {
    // 使用智能AppID管理器获取当前appId，而不是从路由参数
    const appId = getCurrentAppId()
    console.log('🔍 DynamicPage获取到的appId:', appId)
    
    // 从路由获取页面路径
    const pagePathParam = route.params.pagePath
    const pagePath = Array.isArray(pagePathParam)
      ? `/${pagePathParam.join('/')}`
      : (pagePathParam as string || '/')

    // 🎯 检查AppID有效性 - 但某些页面可能不需要AppID
    const isPublicPage = pagePath === '/login' || pagePath === '/register' || pagePath === '/404'
    
    console.log('🔍 AppID有效性检查:', {
      appId,
      pagePath,
      isPublicPage,
      isEmpty: !appId || appId.trim() === ''
    })
    
    if (!isPublicPage && (!appId || appId.trim() === '')) {
      console.warn('⚠️ 未找到有效的AppID且不是公开页面，跳转到错误页面')
      // 跳转到专门的AppID缺失页面
      router.push({ name: 'AppError', params: { type: 'missing' } })
      return
    }

    // 🎯 检查是否需要重新加载
    const currentParams = { appId, pagePath }
    if (!forceReload && lastLoadedParams.value &&
        lastLoadedParams.value.appId === appId &&
        lastLoadedParams.value.pagePath === pagePath) {
      console.log('🎯 参数未变化，跳过重新加载')
      return
    }

    try {
      // 🎯 优化：如果已有配置且不是强制重载，先显示当前内容，避免闪烁
      const hasExistingConfig = !!pageConfig.value
      if (!hasExistingConfig || forceReload) {
        loading.value = true
      }
      error.value = null

      // 🎯 如果有现有配置，减少加载时间避免闪烁
      const minLoadingTime = hasExistingConfig ? 100 : 200
      const startTime = Date.now()

      const config = await pageConfigService.loadPageConfig(appId, pagePath, forceReload)

      // 验证配置
      if (!pageConfigService.validatePageConfig(config)) {
        throw new Error('页面配置验证失败')
      }

      // 🎯 确保最小加载时间，但只在需要loading时才延迟
      const elapsedTime = Date.now() - startTime
      if ((hasExistingConfig || forceReload) && elapsedTime < minLoadingTime) {
        await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime))
      }

      pageConfig.value = config
      // 🎯 更新跟踪参数
      lastLoadedParams.value = currentParams
      console.log('✅ 页面配置加载成功:', config)
    } catch (err) {
      error.value = pageConfigService.handleConfigError(err)
    } finally {
      loading.value = false
    }
  }

  // 处理组件事件
  function handleComponentEvent(comp: any, eventName: string, data: any) {
    return eventHandlerService.handleComponentEvent(comp, eventName, data)
  }

  // 获取组件事件处理器
  function getComponentEventHandlers(comp: any) {
    return pageConfigService.getComponentEventHandlers(comp, handleComponentEvent)
  }

  // 强制重新加载页面配置
  function reloadPageConfig() {
    console.log('🔄 强制重新加载页面配置')
    loadPageConfig(true)
  }

  // 初始化
  onMounted(() => {
    console.log('🔄 DynamicPage mounted - 首次加载')
    // 🌐 同时初始化全局数据和页面配置
    Promise.all([
      initializeGlobalData(),
      loadPageConfig()
    ]).catch(error => {
      console.error('❌ 页面初始化失败:', error)
    })
  })

  // KeepAlive 激活时（从其他页面返回）
  onActivated(() => {
    console.log('🔄 DynamicPage activated - 从缓存恢复')
    // 🎯 简化逻辑：直接调用loadPageConfig，它会自动检查参数变化
    // 🌐 不重复加载全局数据，因为数据是全局共享的
    loadPageConfig()
  })

  // KeepAlive 停用时（跳转到其他页面）
  onDeactivated(() => {
    console.log('💤 DynamicPage deactivated - 进入缓存')
  })

  return {
    // 数据
    pageConfig,
    loading,
    error,
    
    // 计算属性
    layoutStyle,
    pageStyle,
    combinedPageStyle,
    
    // 方法
    getComponentType,
    getComponentEventHandlers,
    handleComponentEvent,
    loadPageConfig: reloadPageConfig,
    
    // 路由信息
    route,
    router
  }
}
