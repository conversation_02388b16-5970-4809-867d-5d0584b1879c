import { ref, onMounted, onUnmounted } from 'vue'

interface NetworkInfo {
  isOnline: boolean
  effectiveType?: string
  downlink?: number
  rtt?: number
  saveData?: boolean
}

export function useNetworkStatus() {
  const isOnline = ref(navigator.onLine)
  const networkInfo = ref<NetworkInfo>({
    isOnline: navigator.onLine
  })
  
  // 更新网络信息
  const updateNetworkInfo = () => {
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection
    
    networkInfo.value = {
      isOnline: navigator.onLine,
      effectiveType: connection?.effectiveType,
      downlink: connection?.downlink,
      rtt: connection?.rtt,
      saveData: connection?.saveData
    }
  }
  
  // 网络状态变化处理
  const handleOnline = () => {
    isOnline.value = true
    updateNetworkInfo()
    console.log('Network: Online')
  }
  
  const handleOffline = () => {
    isOnline.value = false
    updateNetworkInfo()
    console.log('Network: Offline')
  }
  
  const handleConnectionChange = () => {
    updateNetworkInfo()
    console.log('Network: Connection changed', networkInfo.value)
  }
  
  onMounted(() => {
    // 监听网络状态变化
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
    
    // 监听连接信息变化
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection
    
    if (connection) {
      connection.addEventListener('change', handleConnectionChange)
    }
    
    // 初始化网络信息
    updateNetworkInfo()
  })
  
  onUnmounted(() => {
    window.removeEventListener('online', handleOnline)
    window.removeEventListener('offline', handleOffline)
    
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection
    
    if (connection) {
      connection.removeEventListener('change', handleConnectionChange)
    }
  })
  
  return {
    isOnline,
    networkInfo
  }
}

// 网络质量评估
export function useNetworkQuality() {
  const { networkInfo } = useNetworkStatus()
  
  const getNetworkQuality = () => {
    if (!networkInfo.value.isOnline) {
      return 'offline'
    }
    
    const effectiveType = networkInfo.value.effectiveType
    const rtt = networkInfo.value.rtt || 0
    const downlink = networkInfo.value.downlink || 0
    
    if (effectiveType === '4g' && rtt < 100 && downlink > 10) {
      return 'excellent'
    } else if (effectiveType === '4g' || (rtt < 200 && downlink > 5)) {
      return 'good'
    } else if (effectiveType === '3g' || (rtt < 500 && downlink > 1)) {
      return 'fair'
    } else {
      return 'poor'
    }
  }
  
  const getNetworkAdvice = () => {
    const quality = getNetworkQuality()
    
    switch (quality) {
      case 'offline':
        return '网络连接不可用，请检查网络设置'
      case 'poor':
        return '网络连接较差，建议切换到更好的网络环境'
      case 'fair':
        return '网络连接一般，部分功能可能受限'
      case 'good':
        return '网络连接良好'
      case 'excellent':
        return '网络连接优秀'
      default:
        return '网络状态未知'
    }
  }
  
  return {
    networkInfo,
    getNetworkQuality,
    getNetworkAdvice
  }
}
