import { ref, onUnmounted, watch, computed } from 'vue'
import { requestManager, type RequestConfig } from '../services/RequestManager'

interface UseComponentDataOptions {
  componentId: string
  autoRefresh?: boolean
  refreshInterval?: number
  shareKey?: string
  dependencies?: any[]
  immediate?: boolean
}

export function useComponentData<T>(
  requestConfig: RequestConfig | (() => RequestConfig),
  options: UseComponentDataOptions
) {
  const data = ref<T | null>(null)
  const loading = ref(false)
  const error = ref<Error | null>(null)
  
  let refreshTimer: number | null = null
  
  // 获取请求配置
  const getConfig = () => {
    return typeof requestConfig === 'function' ? requestConfig() : requestConfig
  }
  
  // 加载数据
  const loadData = async (force = false) => {
    if (loading.value && !force) return
    
    loading.value = true
    error.value = null
    
    try {
      const config = getConfig()
      const result = await requestManager.request<T>(config)
      data.value = result
      
      // 如果有共享键，更新共享数据
      if (options.shareKey) {
        dataSharingService.setSharedData(options.shareKey, result, options.componentId)
      }
    } catch (err) {
      error.value = err as Error
      console.error(`Component ${options.componentId} data loading failed:`, err)
    } finally {
      loading.value = false
    }
  }
  
  // 刷新数据
  const refresh = () => loadData(true)
  
  // 设置自动刷新
  const setupAutoRefresh = () => {
    if (options.autoRefresh && options.refreshInterval) {
      refreshTimer = setInterval(refresh, options.refreshInterval)
    }
  }
  
  // 清理定时器
  const cleanup = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
    
    // 清理共享数据
    if (options.shareKey) {
      dataSharingService.cleanupComponent(options.componentId)
    }
  }
  
  // 监听依赖变化
  if (options.dependencies && options.dependencies.length > 0) {
    watch(
      () => options.dependencies,
      () => refresh(),
      { deep: true }
    )
  }
  
  // 组件卸载时清理
  onUnmounted(cleanup)
  
  // 初始加载
  if (options.immediate !== false) {
    loadData()
    setupAutoRefresh()
  }
  
  return {
    data: computed(() => data.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    refresh,
    loadData,
    cleanup
  }
}

// 数据共享服务
interface SharedData {
  key: string
  data: any
  timestamp: number
  subscribers: Set<string>
}

class DataSharingService {
  private sharedData = new Map<string, SharedData>()
  private subscribers = new Map<string, Set<(data: any) => void>>()
  
  // 设置共享数据
  setSharedData(key: string, data: any, componentId?: string) {
    const existing = this.sharedData.get(key)
    const shared: SharedData = {
      key,
      data,
      timestamp: Date.now(),
      subscribers: existing?.subscribers || new Set()
    }
    
    if (componentId) {
      shared.subscribers.add(componentId)
    }
    
    this.sharedData.set(key, shared)
    this.notifySubscribers(key, data)
  }
  
  // 获取共享数据
  getSharedData(key: string): any {
    const shared = this.sharedData.get(key)
    return shared?.data
  }
  
  // 订阅数据变化
  subscribe(key: string, callback: (data: any) => void, componentId?: string): () => void {
    if (!this.subscribers.has(key)) {
      this.subscribers.set(key, new Set())
    }
    
    this.subscribers.get(key)!.add(callback)
    
    if (componentId) {
      const shared = this.sharedData.get(key)
      if (shared) {
        shared.subscribers.add(componentId)
      }
    }
    
    // 返回取消订阅函数
    return () => {
      this.subscribers.get(key)?.delete(callback)
      if (componentId) {
        const shared = this.sharedData.get(key)
        if (shared) {
          shared.subscribers.delete(componentId)
        }
      }
    }
  }
  
  // 通知订阅者
  private notifySubscribers(key: string, data: any) {
    const callbacks = this.subscribers.get(key)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('Error in data sharing callback:', error)
        }
      })
    }
  }
  
  // 清理组件相关数据
  cleanupComponent(componentId: string) {
    for (const [key, shared] of this.sharedData) {
      shared.subscribers.delete(componentId)
      
      if (shared.subscribers.size === 0) {
        this.sharedData.delete(key)
        this.subscribers.delete(key)
      }
    }
  }
}

// 创建单例实例
export const dataSharingService = new DataSharingService()

// 使用共享数据的Composable
export function useSharedData<T>(key: string, componentId?: string) {
  const data = ref<T | null>(dataSharingService.getSharedData(key))
  
  // 订阅数据变化
  const unsubscribe = dataSharingService.subscribe(
    key,
    (newData: T) => {
      data.value = newData
    },
    componentId
  )
  
  // 设置数据
  const setData = (newData: T) => {
    dataSharingService.setSharedData(key, newData, componentId)
  }
  
  // 组件卸载时取消订阅
  onUnmounted(() => {
    unsubscribe()
  })
  
  return {
    data: computed(() => data.value),
    setData
  }
}

// 防抖请求Hook
export function useDebouncedRequest<T>(
  requestConfig: RequestConfig | (() => RequestConfig),
  delay = 300
) {
  const data = ref<T | null>(null)
  const loading = ref(false)
  const error = ref<Error | null>(null)
  
  let timeoutId: number | null = null
  
  const debouncedRequest = async () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(async () => {
      loading.value = true
      error.value = null
      
      try {
        const config = typeof requestConfig === 'function' ? requestConfig() : requestConfig
        const result = await requestManager.request<T>(config)
        data.value = result
      } catch (err) {
        error.value = err as Error
      } finally {
        loading.value = false
      }
    }, delay)
  }
  
  const cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
  }
  
  onUnmounted(cancel)
  
  return {
    data: computed(() => data.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    request: debouncedRequest,
    cancel
  }
}
