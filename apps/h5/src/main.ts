import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import router from './router'
import App from './App.vue'

// 导入请求管理服务
import { requestManager } from './services/RequestManager'

// 导入核心模块
import { initializeCore } from './core'

// 导入模块系统
import { initializeModuleSystem } from './modules'

// 导入新的API架构
import { quickInit } from '@lowcode/aslib/core'

// 导入Vant组件
import { Button, Cell, CellGroup, Tag, List, Loading, Toast, Popup, PasswordInput, NumberKeyboard } from 'vant'
import 'vant/lib/index.css'

// 导入低代码组件库
import LowcodeUI, { setAPIClient, setDataManagerConfig } from '@lowcode/aslib/ui'

// ✅ 导入H5组件管理器
import { h5ComponentManager } from './utils/componentManager'

// 导入组件自动注册
import { initComponentRegistry } from './utils/componentRegistry'

// 导入样式
import './styles/index.scss'

const app = createApp(App)

// 配置Pinia
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

// 注册Vant组件
app.use(Button)
app.use(Cell)
app.use(CellGroup)
app.use(Tag)
app.use(List)
app.use(Loading)
app.use(Toast)
app.use(Popup)
app.use(PasswordInput)
app.use(NumberKeyboard)

// 注册低代码UI组件库
app.use(LowcodeUI)

// ✅ 异步初始化H5组件管理器
async function initializeH5Components() {
  try {
    console.log('📱 开始初始化H5组件...')

    // 初始化组件自动注册
    initComponentRegistry()

    // ✅ 初始化组件管理器（支持双重渲染模式）
    await h5ComponentManager.initialize(app)

    console.log('✅ H5组件初始化成功')
  } catch (error) {
    console.error('❌ H5组件初始化失败:', error)
  }
}

// ✅ 初始化H5组件并挂载应用
async function startApp() {
  try {
    // 初始化组件管理器
    await initializeH5Components()
  } catch (error) {
    console.error('❌ 组件初始化失败:', error)
  } finally {
    // 无论初始化是否成功都要挂载应用
    app.mount('#app')
  }
}

app.use(pinia)

// ✅ 在注册路由之前先初始化模块系统
async function initializeModulesBeforeRouting() {
  try {
    console.log('🚀 开始初始化模块系统...')

    // 初始化核心模块
    await initializeCore()

    // 初始化模块系统（这会注册所有模块的路由）
    await initializeModuleSystem(app, router)

    console.log('✅ 模块系统初始化完成')
  } catch (error) {
    console.error('❌ 模块系统初始化失败:', error)
  }
}

// 初始化模块系统然后注册路由
await initializeModulesBeforeRouting()

console.log('🔧 注册路由器到Vue应用')
console.log('🔧 当前URL:', window.location.href)
console.log('🔧 Hash:', window.location.hash)

app.use(router)

// 路由器注册后立即检查当前路由
router.isReady().then(() => {
  console.log('🔧 路由器就绪，当前路由:', router.currentRoute.value.path)
  console.log('🔧 路由器就绪，完整路由信息:', router.currentRoute.value)
})

// 全局错误处理
app.config.errorHandler = (error, _instance, info) => {
  console.error('Global error handler:', error)

  // 上报错误
  try {
    const errorData = {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      componentInfo: info,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: Date.now()
    }

    // 使用 axios 上报错误（不阻塞应用）
    import('./utils/request').then(({ request }) => {
      request.post('/errors/report', errorData).catch(reportError => {
        console.error('Failed to report error:', reportError)
      })
    })
  } catch (reportError) {
    console.error('Error in error reporting:', reportError)
  }
}

// 设置H5为运行时环境
setDataManagerConfig({
  environment: 'runtime'
})

// 初始化新的API架构
const apiGateway = quickInit('HYBRID', 'development')
console.log('🚀 API网关初始化完成')

// 导入旧的API客户端
import apiClient from './api/client'

// 配置UI组件的API客户端（使用旧的API客户端）
setAPIClient(apiClient)

// 提供全局服务
app.provide('requestManager', requestManager)
app.provide('apiGateway', apiGateway)

// ✅ 启动应用
startApp()
