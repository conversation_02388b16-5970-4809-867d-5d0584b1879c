/**
 * 模块事件总线
 * 
 * 提供模块间通信的事件总线实现：
 * 1. 事件发射和监听
 * 2. 事件过滤和路由
 * 3. 事件历史记录
 * 4. 性能监控
 */

import type { ModuleEvent, ModuleEventBus } from './types'

type EventHandler = (event: ModuleEvent) => void

export class ModuleEventBusImpl implements ModuleEventBus {
  private handlers = new Map<string, Set<EventHandler>>()
  private onceHandlers = new Map<string, Set<EventHandler>>()
  private eventHistory: ModuleEvent[] = []
  private maxHistorySize = 1000
  private debugMode = false

  constructor(options: { maxHistorySize?: number; debugMode?: boolean } = {}) {
    this.maxHistorySize = options.maxHistorySize || 1000
    this.debugMode = options.debugMode || false
  }

  /**
   * 发射事件
   */
  emit(event: ModuleEvent): void {
    // 添加时间戳
    if (!event.timestamp) {
      event.timestamp = Date.now()
    }

    // 记录事件历史
    this.addToHistory(event)

    // 调试日志
    if (this.debugMode) {
      console.log('🚌 [ModuleEventBus] 发射事件:', event)
    }

    // 触发普通监听器
    const handlers = this.handlers.get(event.type)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(event)
        } catch (error) {
          console.error('事件处理器执行失败:', error, event)
        }
      })
    }

    // 触发一次性监听器
    const onceHandlers = this.onceHandlers.get(event.type)
    if (onceHandlers) {
      onceHandlers.forEach(handler => {
        try {
          handler(event)
        } catch (error) {
          console.error('一次性事件处理器执行失败:', error, event)
        }
      })
      // 清除一次性监听器
      this.onceHandlers.delete(event.type)
    }

    // 触发通配符监听器
    this.emitWildcard(event)
  }

  /**
   * 监听事件
   */
  on(type: string, handler: EventHandler): void {
    if (!this.handlers.has(type)) {
      this.handlers.set(type, new Set())
    }
    this.handlers.get(type)!.add(handler)

    if (this.debugMode) {
      console.log('🎧 [ModuleEventBus] 添加监听器:', type)
    }
  }

  /**
   * 移除事件监听器
   */
  off(type: string, handler?: EventHandler): void {
    if (!handler) {
      // 移除所有监听器
      this.handlers.delete(type)
      this.onceHandlers.delete(type)
    } else {
      // 移除特定监听器
      const handlers = this.handlers.get(type)
      if (handlers) {
        handlers.delete(handler)
        if (handlers.size === 0) {
          this.handlers.delete(type)
        }
      }

      const onceHandlers = this.onceHandlers.get(type)
      if (onceHandlers) {
        onceHandlers.delete(handler)
        if (onceHandlers.size === 0) {
          this.onceHandlers.delete(type)
        }
      }
    }

    if (this.debugMode) {
      console.log('🔇 [ModuleEventBus] 移除监听器:', type)
    }
  }

  /**
   * 一次性监听事件
   */
  once(type: string, handler: EventHandler): void {
    if (!this.onceHandlers.has(type)) {
      this.onceHandlers.set(type, new Set())
    }
    this.onceHandlers.get(type)!.add(handler)

    if (this.debugMode) {
      console.log('🎧 [ModuleEventBus] 添加一次性监听器:', type)
    }
  }

  /**
   * 处理通配符事件
   */
  private emitWildcard(event: ModuleEvent): void {
    // 支持 * 通配符监听所有事件
    const wildcardHandlers = this.handlers.get('*')
    if (wildcardHandlers) {
      wildcardHandlers.forEach(handler => {
        try {
          handler(event)
        } catch (error) {
          console.error('通配符事件处理器执行失败:', error, event)
        }
      })
    }

    // 支持模式匹配，如 module:* 匹配所有模块事件
    for (const [pattern, handlers] of this.handlers) {
      if (pattern.includes('*') && pattern !== '*') {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'))
        if (regex.test(event.type)) {
          handlers.forEach(handler => {
            try {
              handler(event)
            } catch (error) {
              console.error('模式匹配事件处理器执行失败:', error, event)
            }
          })
        }
      }
    }
  }

  /**
   * 添加到事件历史
   */
  private addToHistory(event: ModuleEvent): void {
    this.eventHistory.push(event)
    
    // 限制历史记录大小
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift()
    }
  }

  /**
   * 获取事件历史
   */
  getEventHistory(filter?: {
    type?: string
    source?: string
    target?: string
    since?: number
    limit?: number
  }): ModuleEvent[] {
    let history = [...this.eventHistory]

    if (filter) {
      if (filter.type) {
        history = history.filter(event => event.type === filter.type)
      }
      if (filter.source) {
        history = history.filter(event => event.source === filter.source)
      }
      if (filter.target) {
        history = history.filter(event => event.target === filter.target)
      }
      if (filter.since) {
        history = history.filter(event => event.timestamp >= filter.since!)
      }
      if (filter.limit) {
        history = history.slice(-filter.limit)
      }
    }

    return history
  }

  /**
   * 清除事件历史
   */
  clearHistory(): void {
    this.eventHistory = []
    if (this.debugMode) {
      console.log('🗑️ [ModuleEventBus] 清除事件历史')
    }
  }

  /**
   * 获取统计信息
   */
  getStats() {
    const eventTypes = new Set(this.eventHistory.map(e => e.type))
    const sources = new Set(this.eventHistory.map(e => e.source))
    
    return {
      totalEvents: this.eventHistory.length,
      uniqueEventTypes: eventTypes.size,
      uniqueSources: sources.size,
      activeHandlers: this.handlers.size,
      onceHandlers: this.onceHandlers.size,
      recentEvents: this.eventHistory.slice(-10)
    }
  }

  /**
   * 创建模块专用的事件发射器
   */
  createModuleEmitter(moduleId: string) {
    return {
      emit: (type: string, data?: any, target?: string) => {
        this.emit({
          type,
          source: moduleId,
          target,
          data,
          timestamp: Date.now()
        })
      },
      
      broadcast: (type: string, data?: any) => {
        this.emit({
          type: `${type}:broadcast`,
          source: moduleId,
          data,
          timestamp: Date.now()
        })
      },
      
      message: (target: string, data: any) => {
        this.emit({
          type: 'module:message',
          source: moduleId,
          target,
          data,
          timestamp: Date.now()
        })
      }
    }
  }

  /**
   * 销毁事件总线
   */
  destroy(): void {
    this.handlers.clear()
    this.onceHandlers.clear()
    this.eventHistory = []
    
    if (this.debugMode) {
      console.log('💥 [ModuleEventBus] 事件总线已销毁')
    }
  }
}

// 创建全局事件总线实例
export const moduleEventBus = new ModuleEventBusImpl({
  debugMode: process.env.NODE_ENV === 'development',
  maxHistorySize: 1000
})
