/**
 * 模块管理器
 * 
 * 统一管理H5应用的所有业务模块：
 * 1. 模块注册和发现
 * 2. 模块生命周期管理
 * 3. 模块间通信协调
 * 4. 模块配置管理
 */

import type { App } from 'vue'
import type { Router } from 'vue-router'
import type {
  Module,
  ModuleDefinition,
  ModuleStats,
  BusinessType
} from './types'
import { ModuleLoaderImpl } from './ModuleLoader'
import { moduleEventBus } from './ModuleEventBus'

// 内置模块
import deviceModuleDef from './device'
import mallModuleDef from './mall'

export class ModuleManager {
  private moduleLoader: ModuleLoaderImpl
  private moduleDefinitions = new Map<string, ModuleDefinition>()
  private autoLoadEnabled = true

  constructor(app: App, router: Router) {
    this.moduleLoader = new ModuleLoaderImpl(app, router)
    this.registerBuiltinModules()
  }

  /**
   * 初始化模块管理器
   */
  async initialize(): Promise<void> {
    console.log('🚀 [ModuleManager] 初始化模块管理器...')

    // 设置事件监听
    this.setupEventListeners()

    // 自动加载启用的模块
    if (this.autoLoadEnabled) {
      await this.autoLoadModules()
    }

    console.log('✅ [ModuleManager] 模块管理器初始化完成')
  }

  /**
   * 注册模块定义
   */
  registerModuleDefinition(definition: ModuleDefinition): void {
    const moduleId = definition.config.id
    
    if (this.moduleDefinitions.has(moduleId)) {
      console.warn(`⚠️ [ModuleManager] 模块定义已存在，将被覆盖: ${moduleId}`)
    }

    this.moduleDefinitions.set(moduleId, definition)
    console.log(`📝 [ModuleManager] 模块定义已注册: ${moduleId}`)
  }

  /**
   * 注册模块实例
   */
  async registerModule(module: Module): Promise<void> {
    await this.moduleLoader.register(module)
  }

  /**
   * 加载模块
   */
  async loadModule(moduleId: string): Promise<Module> {
    // 如果模块未注册，尝试从定义创建
    if (!this.moduleLoader.getModule(moduleId)) {
      const definition = this.moduleDefinitions.get(moduleId)
      if (definition) {
        const module = await this.createModuleFromDefinition(definition)
        await this.moduleLoader.register(module)
      } else {
        throw new Error(`模块定义未找到: ${moduleId}`)
      }
    }

    return await this.moduleLoader.load(moduleId)
  }

  /**
   * 卸载模块
   */
  async unloadModule(moduleId: string): Promise<void> {
    await this.moduleLoader.unload(moduleId)
  }

  /**
   * 启用模块
   */
  async enableModule(moduleId: string): Promise<void> {
    const definition = this.moduleDefinitions.get(moduleId)
    if (definition) {
      definition.config.enabled = true
      await this.loadModule(moduleId)
      console.log(`✅ [ModuleManager] 模块已启用: ${moduleId}`)
    }
  }

  /**
   * 禁用模块
   */
  async disableModule(moduleId: string): Promise<void> {
    const definition = this.moduleDefinitions.get(moduleId)
    if (definition) {
      definition.config.enabled = false
      if (this.moduleLoader.isLoaded(moduleId)) {
        await this.unloadModule(moduleId)
      }
      console.log(`❌ [ModuleManager] 模块已禁用: ${moduleId}`)
    }
  }

  /**
   * 获取模块
   */
  getModule(moduleId: string): Module | null {
    return this.moduleLoader.getModule(moduleId)
  }

  /**
   * 获取所有模块
   */
  getAllModules(): Module[] {
    return this.moduleLoader.getAllModules()
  }

  /**
   * 根据业务类型获取模块
   */
  getModulesByBusiness(business: BusinessType): Module[] {
    return this.moduleLoader.getModulesByBusiness(business)
  }

  /**
   * 获取已加载的模块
   */
  getLoadedModules(): Module[] {
    return this.moduleLoader.getLoadedModules()
  }

  /**
   * 检查模块是否已加载
   */
  isModuleLoaded(moduleId: string): boolean {
    return this.moduleLoader.isLoaded(moduleId)
  }

  /**
   * 检查模块是否启用
   */
  isModuleEnabled(moduleId: string): boolean {
    const definition = this.moduleDefinitions.get(moduleId)
    return definition ? definition.config.enabled : false
  }

  /**
   * 获取模块统计信息
   */
  getStats(): ModuleStats {
    return this.moduleLoader.getStats()
  }

  /**
   * 获取模块定义列表
   */
  getModuleDefinitions(): ModuleDefinition[] {
    return Array.from(this.moduleDefinitions.values())
  }

  /**
   * 重新加载模块
   */
  async reloadModule(moduleId: string): Promise<Module> {
    if (this.moduleLoader.isLoaded(moduleId)) {
      await this.moduleLoader.unload(moduleId)
    }
    return await this.moduleLoader.load(moduleId, { force: true })
  }

  /**
   * 批量加载模块
   */
  async loadModules(moduleIds: string[]): Promise<Module[]> {
    const results: Module[] = []
    
    for (const moduleId of moduleIds) {
      try {
        const module = await this.loadModule(moduleId)
        results.push(module)
      } catch (error) {
        console.error(`❌ [ModuleManager] 模块加载失败: ${moduleId}`, error)
      }
    }
    
    return results
  }

  /**
   * 批量卸载模块
   */
  async unloadModules(moduleIds: string[]): Promise<void> {
    for (const moduleId of moduleIds) {
      try {
        await this.unloadModule(moduleId)
      } catch (error) {
        console.error(`❌ [ModuleManager] 模块卸载失败: ${moduleId}`, error)
      }
    }
  }

  /**
   * 注册内置模块
   */
  private registerBuiltinModules(): void {
    console.log('📦 [ModuleManager] 注册内置模块...')
    
    // 注册设备模块
    this.registerModuleDefinition(deviceModuleDef)
    
    // 注册商城模块
    this.registerModuleDefinition(mallModuleDef)
    
    console.log('✅ [ModuleManager] 内置模块注册完成')
  }

  /**
   * 自动加载启用的模块
   */
  private async autoLoadModules(): Promise<void> {
    console.log('🔄 [ModuleManager] 自动加载启用的模块...')
    
    const enabledDefinitions = Array.from(this.moduleDefinitions.values())
      .filter(def => def.config.enabled)
      .sort((a, b) => b.config.priority - a.config.priority) // 按优先级排序
    
    for (const definition of enabledDefinitions) {
      try {
        await this.loadModule(definition.config.id)
      } catch (error) {
        console.error(`❌ [ModuleManager] 自动加载模块失败: ${definition.config.id}`, error)
      }
    }
    
    console.log('✅ [ModuleManager] 自动加载完成')
  }

  /**
   * 从定义创建模块实例
   */
  private async createModuleFromDefinition(definition: ModuleDefinition): Promise<Module> {
    const context = {
      app: this.moduleLoader['context'].app,
      router: this.moduleLoader['context'].router,
      eventBus: moduleEventBus,
      moduleLoader: this.moduleLoader
    }
    
    return await definition.factory(context)
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 监听模块加载事件
    moduleEventBus.on('module:loaded', (event) => {
      console.log(`📦 [ModuleManager] 模块加载完成: ${event.target}`)
    })

    // 监听模块卸载事件
    moduleEventBus.on('module:unloaded', (event) => {
      console.log(`📤 [ModuleManager] 模块卸载完成: ${event.target}`)
    })

    // 监听模块错误事件
    moduleEventBus.on('module:load-error', (event) => {
      console.error(`❌ [ModuleManager] 模块加载错误: ${event.target}`, event.data)
    })
  }

  /**
   * 销毁模块管理器
   */
  async destroy(): Promise<void> {
    console.log('💥 [ModuleManager] 销毁模块管理器...')
    
    // 卸载所有已加载的模块
    const loadedModules = this.getLoadedModules()
    for (const module of loadedModules) {
      try {
        await this.unloadModule(module.config.id)
      } catch (error) {
        console.error(`❌ [ModuleManager] 模块卸载失败: ${module.config.id}`, error)
      }
    }
    
    // 清理模块定义
    this.moduleDefinitions.clear()
    
    console.log('✅ [ModuleManager] 模块管理器已销毁')
  }
}

// 创建全局模块管理器实例（延迟初始化）
let moduleManagerInstance: ModuleManager | null = null

export const createModuleManager = (app: App, router: Router): ModuleManager => {
  if (moduleManagerInstance) {
    console.warn('⚠️ [ModuleManager] 模块管理器已存在，将被替换')
  }
  
  moduleManagerInstance = new ModuleManager(app, router)
  return moduleManagerInstance
}

export const getModuleManager = (): ModuleManager => {
  if (!moduleManagerInstance) {
    throw new Error('模块管理器未初始化，请先调用 createModuleManager')
  }
  return moduleManagerInstance
}
