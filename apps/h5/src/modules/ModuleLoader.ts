/**
 * 模块加载器
 * 
 * 负责模块的注册、加载、卸载和生命周期管理：
 * 1. 模块注册和发现
 * 2. 依赖解析和加载
 * 3. 生命周期管理
 * 4. 错误处理和恢复
 */

import type { App } from 'vue'
import type { Router } from 'vue-router'
import type {
  Module,
  ModuleLoader as IModuleLoader,
  ModuleContext,
  ModuleRegistration,
  ModuleLoadOptions,
  ModuleStats,
  ModuleStatus,
  ModuleError
} from './types'
import { MODULE_EVENTS } from './types'
import type { BusinessType } from '../core'
import { moduleEventBus } from './ModuleEventBus'

export class ModuleLoaderImpl implements IModuleLoader {
  private modules = new Map<string, ModuleRegistration>()
  private context: ModuleContext
  private loadingPromises = new Map<string, Promise<Module>>()

  constructor(app: App, router: Router) {
    this.context = {
      app,
      router,
      eventBus: moduleEventBus,
      moduleLoader: this
    }
  }

  /**
   * 注册模块
   */
  async register(module: Module): Promise<void> {
    const moduleId = module.config.id

    if (this.modules.has(moduleId)) {
      throw new ModuleError(
        `模块 ${moduleId} 已经注册`,
        moduleId,
        'MODULE_ALREADY_REGISTERED'
      )
    }

    // 验证模块配置
    this.validateModule(module)

    // 注册模块
    this.modules.set(moduleId, {
      module,
      loadedAt: undefined,
      loadTime: undefined,
      error: undefined
    })

    console.log(`📦 [ModuleLoader] 模块已注册: ${moduleId}`)

    // 发射注册事件
    moduleEventBus.emit({
      type: 'module:registered',
      source: 'module-loader',
      target: moduleId,
      data: { config: module.config },
      timestamp: Date.now()
    })
  }

  /**
   * 注销模块
   */
  async unregister(moduleId: string): Promise<void> {
    const registration = this.modules.get(moduleId)
    if (!registration) {
      throw new ModuleError(
        `模块 ${moduleId} 未注册`,
        moduleId,
        'MODULE_NOT_REGISTERED'
      )
    }

    // 如果模块已加载，先卸载
    if (registration.module.status === 'loaded') {
      await this.unload(moduleId)
    }

    // 注销模块
    this.modules.delete(moduleId)

    console.log(`🗑️ [ModuleLoader] 模块已注销: ${moduleId}`)

    // 发射注销事件
    moduleEventBus.emit({
      type: 'module:unregistered',
      source: 'module-loader',
      target: moduleId,
      timestamp: Date.now()
    })
  }

  /**
   * 加载模块
   */
  async load(moduleId: string, options: ModuleLoadOptions = {}): Promise<Module> {
    const registration = this.modules.get(moduleId)
    if (!registration) {
      throw new ModuleError(
        `模块 ${moduleId} 未注册`,
        moduleId,
        'MODULE_NOT_REGISTERED'
      )
    }

    const module = registration.module

    // 如果已经在加载中，返回加载Promise
    if (this.loadingPromises.has(moduleId)) {
      return this.loadingPromises.get(moduleId)!
    }

    // 如果已经加载且不强制重新加载，直接返回
    if (module.status === 'loaded' && !options.force) {
      return module
    }

    // 创建加载Promise
    const loadPromise = this.doLoad(module, options)
    this.loadingPromises.set(moduleId, loadPromise)

    try {
      const result = await loadPromise
      this.loadingPromises.delete(moduleId)
      return result
    } catch (error) {
      this.loadingPromises.delete(moduleId)
      throw error
    }
  }

  /**
   * 执行模块加载
   */
  private async doLoad(module: Module, options: ModuleLoadOptions): Promise<Module> {
    const moduleId = module.config.id
    const startTime = Date.now()

    try {
      // 更新状态
      module.status = 'loading'

      // 发射加载开始事件
      moduleEventBus.emit({
        type: MODULE_EVENTS.BEFORE_LOAD,
        source: 'module-loader',
        target: moduleId,
        timestamp: Date.now()
      })

      // 检查依赖
      if (options.loadDependencies !== false) {
        await this.loadDependencies(module)
      }

      // 执行生命周期钩子
      if (module.beforeLoad) {
        await module.beforeLoad()
      }

      // 安装模块
      await module.install(this.context)

      // 注册路由
      this.registerModuleRoutes(module)

      // 执行生命周期钩子
      if (module.onLoad) {
        await module.onLoad(this.context)
      }

      // 更新状态
      module.status = 'loaded'
      const registration = this.modules.get(moduleId)!
      registration.loadedAt = new Date()
      registration.loadTime = Date.now() - startTime
      registration.error = undefined

      // 执行生命周期钩子
      if (module.afterLoad) {
        await module.afterLoad()
      }

      console.log(`✅ [ModuleLoader] 模块加载成功: ${moduleId} (${registration.loadTime}ms)`)

      // 发射加载完成事件
      moduleEventBus.emit({
        type: MODULE_EVENTS.LOADED,
        source: 'module-loader',
        target: moduleId,
        data: { loadTime: registration.loadTime },
        timestamp: Date.now()
      })

      return module

    } catch (error) {
      // 更新错误状态
      module.status = 'error'
      const registration = this.modules.get(moduleId)!
      registration.error = error as Error

      console.error(`❌ [ModuleLoader] 模块加载失败: ${moduleId}`, error)

      // 发射加载错误事件
      moduleEventBus.emit({
        type: MODULE_EVENTS.LOAD_ERROR,
        source: 'module-loader',
        target: moduleId,
        data: { error: error instanceof Error ? error.message : String(error) },
        timestamp: Date.now()
      })

      throw new ModuleError(
        `模块 ${moduleId} 加载失败: ${error instanceof Error ? error.message : String(error)}`,
        moduleId,
        'MODULE_LOAD_FAILED',
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * 卸载模块
   */
  async unload(moduleId: string): Promise<void> {
    const registration = this.modules.get(moduleId)
    if (!registration) {
      throw new ModuleError(
        `模块 ${moduleId} 未注册`,
        moduleId,
        'MODULE_NOT_REGISTERED'
      )
    }

    const module = registration.module

    if (module.status !== 'loaded') {
      return
    }

    try {
      // 更新状态
      module.status = 'unloading'

      // 发射卸载开始事件
      moduleEventBus.emit({
        type: MODULE_EVENTS.BEFORE_UNLOAD,
        source: 'module-loader',
        target: moduleId,
        timestamp: Date.now()
      })

      // 执行生命周期钩子
      if (module.beforeUnload) {
        await module.beforeUnload()
      }

      // 卸载模块
      if (module.uninstall) {
        await module.uninstall()
      }

      // 移除路由
      this.unregisterModuleRoutes(module)

      // 执行生命周期钩子
      if (module.onUnload) {
        await module.onUnload()
      }

      // 更新状态
      module.status = 'unloaded'
      registration.loadedAt = undefined
      registration.loadTime = undefined
      registration.error = undefined

      // 执行生命周期钩子
      if (module.afterUnload) {
        await module.afterUnload()
      }

      console.log(`🔄 [ModuleLoader] 模块已卸载: ${moduleId}`)

      // 发射卸载完成事件
      moduleEventBus.emit({
        type: MODULE_EVENTS.UNLOADED,
        source: 'module-loader',
        target: moduleId,
        timestamp: Date.now()
      })

    } catch (error) {
      console.error(`❌ [ModuleLoader] 模块卸载失败: ${moduleId}`, error)
      throw new ModuleError(
        `模块 ${moduleId} 卸载失败: ${error instanceof Error ? error.message : String(error)}`,
        moduleId,
        'MODULE_UNLOAD_FAILED',
        error instanceof Error ? error : undefined
      )
    }
  }

  /**
   * 获取模块
   */
  getModule(moduleId: string): Module | null {
    const registration = this.modules.get(moduleId)
    return registration ? registration.module : null
  }

  /**
   * 获取所有模块
   */
  getAllModules(): Module[] {
    return Array.from(this.modules.values()).map(reg => reg.module)
  }

  /**
   * 根据业务类型获取模块
   */
  getModulesByBusiness(business: BusinessType): Module[] {
    return this.getAllModules().filter(module => module.config.business === business)
  }

  /**
   * 获取已加载的模块
   */
  getLoadedModules(): Module[] {
    return this.getAllModules().filter(module => module.status === 'loaded')
  }

  /**
   * 检查模块是否已加载
   */
  isLoaded(moduleId: string): boolean {
    const module = this.getModule(moduleId)
    return module ? module.status === 'loaded' : false
  }

  /**
   * 获取模块状态
   */
  getModuleStatus(moduleId: string): ModuleStatus {
    const module = this.getModule(moduleId)
    return module ? module.status : 'unloaded'
  }

  /**
   * 解析依赖
   */
  resolveDependencies(moduleId: string): string[] {
    const module = this.getModule(moduleId)
    if (!module) return []

    const dependencies = module.config.dependencies || []
    const resolved: string[] = []
    const visited = new Set<string>()

    const resolve = (id: string) => {
      if (visited.has(id)) return
      visited.add(id)

      const dep = this.getModule(id)
      if (dep) {
        const depDeps = dep.config.dependencies || []
        depDeps.forEach(resolve)
        resolved.push(id)
      }
    }

    dependencies.forEach(resolve)
    return resolved
  }

  /**
   * 检查依赖
   */
  checkDependencies(moduleId: string): boolean {
    const dependencies = this.resolveDependencies(moduleId)
    return dependencies.every(depId => this.isLoaded(depId))
  }

  /**
   * 加载依赖
   */
  private async loadDependencies(module: Module): Promise<void> {
    const dependencies = module.config.dependencies || []
    
    for (const depId of dependencies) {
      if (!this.isLoaded(depId)) {
        await this.load(depId)
      }
    }
  }

  /**
   * 注册模块路由
   */
  private registerModuleRoutes(module: Module): void {
    const routes = module.getRoutes ? module.getRoutes() : (module.config.routes || [])
    
    routes.forEach(route => {
      // 添加模块前缀
      if (module.config.routePrefix) {
        route.path = `${module.config.routePrefix}${route.path}`
      }
      
      // 添加模块元信息
      route.meta = {
        ...route.meta,
        moduleId: module.config.id,
        business: module.config.business
      }
      
      this.context.router.addRoute(route)
    })
  }

  /**
   * 移除模块路由
   */
  private unregisterModuleRoutes(module: Module): void {
    const routes = module.getRoutes ? module.getRoutes() : (module.config.routes || [])
    
    routes.forEach(route => {
      if (route.name) {
        this.context.router.removeRoute(route.name)
      }
    })
  }

  /**
   * 验证模块
   */
  private validateModule(module: Module): void {
    const config = module.config

    if (!config.id) {
      throw new ModuleError('模块ID不能为空', '', 'INVALID_MODULE_CONFIG')
    }

    if (!config.name) {
      throw new ModuleError('模块名称不能为空', config.id, 'INVALID_MODULE_CONFIG')
    }

    if (!config.version) {
      throw new ModuleError('模块版本不能为空', config.id, 'INVALID_MODULE_CONFIG')
    }

    if (!config.business) {
      throw new ModuleError('模块业务类型不能为空', config.id, 'INVALID_MODULE_CONFIG')
    }

    if (typeof module.install !== 'function') {
      throw new ModuleError('模块必须实现install方法', config.id, 'INVALID_MODULE_CONFIG')
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): ModuleStats {
    const modules = this.getAllModules()
    const byBusiness = {} as Record<BusinessType, number>
    const loadTimes = {} as Record<string, number>

    let loaded = 0
    let loading = 0
    let error = 0

    modules.forEach(module => {
      // 统计状态
      switch (module.status) {
        case 'loaded':
          loaded++
          break
        case 'loading':
          loading++
          break
        case 'error':
          error++
          break
      }

      // 统计业务类型
      const business = module.config.business
      byBusiness[business] = (byBusiness[business] || 0) + 1

      // 统计加载时间
      const registration = this.modules.get(module.config.id)
      if (registration?.loadTime) {
        loadTimes[module.config.id] = registration.loadTime
      }
    })

    return {
      total: modules.length,
      loaded,
      loading,
      error,
      byBusiness,
      loadTimes
    }
  }
}
