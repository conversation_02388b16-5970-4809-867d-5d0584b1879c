/**
 * H5模块系统类型定义
 * 
 * 定义模块系统的核心接口和类型，包括：
 * 1. 模块接口定义
 * 2. 模块生命周期
 * 3. 模块配置
 * 4. 模块通信
 */

import type { App } from 'vue'
import type { Router, RouteRecordRaw } from 'vue-router'
import type { BusinessType } from '../core'

// 模块状态
export type ModuleStatus = 'unloaded' | 'loading' | 'loaded' | 'error' | 'unloading'

// 模块生命周期钩子
export interface ModuleLifecycle {
  beforeLoad?: () => Promise<void> | void
  onLoad?: (context: ModuleContext) => Promise<void> | void
  afterLoad?: () => Promise<void> | void
  beforeUnload?: () => Promise<void> | void
  onUnload?: () => Promise<void> | void
  afterUnload?: () => Promise<void> | void
}

// 模块上下文
export interface ModuleContext {
  app: App
  router: Router
  eventBus: ModuleEventBus
  moduleLoader: ModuleLoader
}

// 模块配置
export interface ModuleConfig {
  id: string
  name: string
  version: string
  description?: string
  author?: string
  
  // 业务配置
  business: BusinessType
  enabled: boolean
  priority: number
  
  // 依赖配置
  dependencies?: string[]
  optionalDependencies?: string[]
  
  // 路由配置
  routes?: RouteRecordRaw[]
  routePrefix?: string
  
  // 页面配置
  pages?: ModulePageConfig[]
  defaultPage?: string
  
  // API配置
  apis?: ModuleApiConfig[]
  
  // 权限配置
  permissions?: string[]
  
  // 自定义配置
  custom?: Record<string, any>
}

// 模块页面配置
export interface ModulePageConfig {
  path: string
  name: string
  component: string | (() => Promise<any>)
  configurable: boolean
  meta?: Record<string, any>
}

// 模块API配置
export interface ModuleApiConfig {
  name: string
  baseURL: string
  timeout?: number
  headers?: Record<string, string>
}

// 模块接口
export interface Module extends ModuleLifecycle {
  config: ModuleConfig
  status: ModuleStatus
  
  // 核心方法
  install(context: ModuleContext): Promise<void> | void
  uninstall(): Promise<void> | void
  
  // 可选方法
  getRoutes?(): RouteRecordRaw[]
  getPages?(): ModulePageConfig[]
  getApis?(): ModuleApiConfig[]
  getStores?(): any[]
  getComponents?(): any[]
}

// 模块事件
export interface ModuleEvent {
  type: string
  source: string
  target?: string
  data?: any
  timestamp: number
}

// 模块事件总线
export interface ModuleEventBus {
  emit(event: ModuleEvent): void
  on(type: string, handler: (event: ModuleEvent) => void): void
  off(type: string, handler?: (event: ModuleEvent) => void): void
  once(type: string, handler: (event: ModuleEvent) => void): void
}

// 模块加载器
export interface ModuleLoader {
  // 模块管理
  register(module: Module): Promise<void>
  unregister(moduleId: string): Promise<void>
  load(moduleId: string): Promise<Module>
  unload(moduleId: string): Promise<void>
  
  // 模块查询
  getModule(moduleId: string): Module | null
  getAllModules(): Module[]
  getModulesByBusiness(business: BusinessType): Module[]
  getLoadedModules(): Module[]
  
  // 模块状态
  isLoaded(moduleId: string): boolean
  getModuleStatus(moduleId: string): ModuleStatus
  
  // 依赖管理
  resolveDependencies(moduleId: string): string[]
  checkDependencies(moduleId: string): boolean
}

// 模块注册信息
export interface ModuleRegistration {
  module: Module
  loadedAt?: Date
  loadTime?: number
  error?: Error
}

// 模块加载选项
export interface ModuleLoadOptions {
  force?: boolean
  loadDependencies?: boolean
  timeout?: number
}

// 模块统计信息
export interface ModuleStats {
  total: number
  loaded: number
  loading: number
  error: number
  byBusiness: Record<BusinessType, number>
  loadTimes: Record<string, number>
}

// 模块工厂函数
export type ModuleFactory = (context: ModuleContext) => Module | Promise<Module>

// 模块定义
export interface ModuleDefinition {
  config: ModuleConfig
  factory: ModuleFactory
}

// 模块导出
export interface ModuleExports {
  default: ModuleDefinition
  config?: ModuleConfig
  pages?: ModulePageConfig[]
  routes?: RouteRecordRaw[]
  stores?: any[]
  components?: any[]
  apis?: ModuleApiConfig[]
}

// 模块错误类型
export class ModuleError extends Error {
  constructor(
    message: string,
    public moduleId: string,
    public code: string,
    public cause?: Error
  ) {
    super(message)
    this.name = 'ModuleError'
  }
}

// 模块事件类型常量
export const MODULE_EVENTS = {
  // 生命周期事件
  BEFORE_LOAD: 'module:before-load',
  LOADED: 'module:loaded',
  LOAD_ERROR: 'module:load-error',
  BEFORE_UNLOAD: 'module:before-unload',
  UNLOADED: 'module:unloaded',
  
  // 状态变更事件
  STATUS_CHANGED: 'module:status-changed',
  
  // 业务事件
  PAGE_CHANGED: 'module:page-changed',
  API_CALLED: 'module:api-called',
  ERROR_OCCURRED: 'module:error-occurred',
  
  // 通信事件
  MESSAGE: 'module:message',
  BROADCAST: 'module:broadcast'
} as const

// 模块优先级常量
export const MODULE_PRIORITY = {
  SYSTEM: 1000,
  CORE: 900,
  BUSINESS: 500,
  PLUGIN: 100,
  CUSTOM: 50
} as const
