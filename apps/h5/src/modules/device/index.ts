/**
 * 设备业务模块
 * 
 * 设备充值业务的完整模块实现：
 * 1. 模块配置和生命周期
 * 2. 页面和路由定义
 * 3. 状态管理集成
 * 4. API服务集成
 */

import type { RouteRecordRaw } from 'vue-router'
import type {
  Module,
  ModuleConfig,
  ModuleContext,
  ModulePageConfig,
  ModuleApiConfig
} from '../types'
import { MODULE_PRIORITY } from '../types'
// 路由配置现在由统一路由系统管理，不再需要单独的routes文件
const deviceRoutes: any[] = []
const configurablePages: string[] = []
const staticPages: string[] = []

// 设备模块配置
const deviceModuleConfig: ModuleConfig = {
  id: 'device',
  name: '设备充值',
  version: '1.0.0',
  description: '设备管理和充值业务模块',
  author: 'lowcode-team',
  
  // 业务配置
  business: 'device',
  enabled: true,
  priority: MODULE_PRIORITY.BUSINESS,
  
  // 依赖配置
  dependencies: [],
  optionalDependencies: [],
  
  // 路由配置
  routePrefix: '',
  
  // 页面配置 (从路由配置推导)
  pages: configurablePages.map(path => ({
    path,
    name: path.replace('/', ''),
    component: path.replace('/', ''),
    configurable: true,
    meta: { title: '可配置页面' }
  })),
  
  defaultPage: '/home',
  
  // API配置
  apis: [
    {
      name: 'device-java',
      baseURL: '/api/device',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    },
    {
      name: 'device-python',
      baseURL: '/backend',
      timeout: 8000
    }
  ],
  
  // 权限配置
  permissions: [
    'device.read',
    'device.write',
    'device.payment'
  ],
  
  // 自定义配置
  custom: {
    theme: {
      primaryColor: '#1890ff',
      backgroundColor: '#f5f5f5'
    },
    features: {
      realTimeUpdate: true,
      offlineMode: false,
      pushNotification: true
    }
  }
}

// 设备模块实现
export class DeviceModule implements Module {
  config = deviceModuleConfig
  status: 'unloaded' | 'loading' | 'loaded' | 'error' | 'unloading' = 'unloaded'
  
  private context?: ModuleContext
  private stores: any[] = []
  private components: any[] = []

  /**
   * 模块安装
   */
  async install(context: ModuleContext): Promise<void> {
    this.context = context

    console.log('📦 [DeviceModule] 开始安装设备模块...')

    // 注册路由
    await this.registerRoutes()

    // 注册状态管理
    await this.registerStores()

    // 注册组件
    await this.registerComponents()

    // 初始化API服务
    await this.initializeApis()

    // 设置事件监听
    this.setupEventListeners()

    console.log('✅ [DeviceModule] 设备模块安装完成')
  }

  /**
   * 模块卸载
   */
  async uninstall(): Promise<void> {
    console.log('🔄 [DeviceModule] 开始卸载设备模块...')

    // 移除路由
    this.unregisterRoutes()

    // 清理事件监听
    this.cleanupEventListeners()

    // 清理状态管理
    this.cleanupStores()

    // 清理组件
    this.cleanupComponents()

    console.log('✅ [DeviceModule] 设备模块卸载完成')
  }

  /**
   * 获取路由配置
   */
  getRoutes(): RouteRecordRaw[] {
    // 直接返回从路由文件导入的路由配置
    return deviceRoutes
  }

  /**
   * 获取页面配置
   */
  getPages(): ModulePageConfig[] {
    return this.config.pages || []
  }

  /**
   * 获取API配置
   */
  getApis(): ModuleApiConfig[] {
    return this.config.apis || []
  }

  /**
   * 获取状态管理
   */
  getStores(): any[] {
    return this.stores
  }

  /**
   * 获取组件
   */
  getComponents(): any[] {
    return this.components
  }

  /**
   * 注册路由
   */
  private async registerRoutes(): Promise<void> {
    if (!this.context) return

    const routes = this.getRoutes()
    routes.forEach(route => {
      this.context!.router.addRoute(route)
      console.log(`🛣️ [DeviceModule] 注册路由: ${route.path}`)
    })

    console.log(`✅ [DeviceModule] 已注册 ${routes.length} 个路由`)
  }

  /**
   * 移除路由
   */
  private unregisterRoutes(): void {
    if (!this.context) return

    const routes = this.getRoutes()
    routes.forEach(route => {
      if (route.name) {
        this.context!.router.removeRoute(route.name)
        console.log(`🗑️ [DeviceModule] 移除路由: ${route.path}`)
      }
    })

    console.log(`✅ [DeviceModule] 已移除 ${routes.length} 个路由`)
  }

  /**
   * 注册状态管理
   */
  private async registerStores(): Promise<void> {
    // 这里可以注册设备相关的Pinia stores
    // 例如：deviceStore, wechatStore, userStore等
    console.log('📊 [DeviceModule] 注册状态管理...')
  }

  /**
   * 注册组件
   */
  private async registerComponents(): Promise<void> {
    // 这里可以注册设备相关的Vue组件
    console.log('🧩 [DeviceModule] 注册组件...')
  }

  /**
   * 初始化API服务
   */
  private async initializeApis(): Promise<void> {
    // 这里可以初始化设备相关的API服务
    console.log('🔌 [DeviceModule] 初始化API服务...')
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    if (!this.context) return

    // 监听设备相关事件
    this.context.eventBus.on('device:refresh', (event) => {
      console.log('🔄 [DeviceModule] 收到设备刷新事件:', event)
    })

    this.context.eventBus.on('device:payment', (event) => {
      console.log('💳 [DeviceModule] 收到支付事件:', event)
    })
  }

  /**
   * 清理事件监听
   */
  private cleanupEventListeners(): void {
    if (!this.context) return

    this.context.eventBus.off('device:refresh')
    this.context.eventBus.off('device:payment')
  }

  /**
   * 清理状态管理
   */
  private cleanupStores(): void {
    this.stores = []
  }

  /**
   * 清理组件
   */
  private cleanupComponents(): void {
    this.components = []
  }
}

// 导出模块实例
export const deviceModule = new DeviceModule()

// 默认导出
export default {
  config: deviceModuleConfig,
  factory: async (context: ModuleContext) => {
    console.log('🏭 [DeviceModule] 工厂函数被调用，创建设备模块实例')
    return deviceModule
  }
}
