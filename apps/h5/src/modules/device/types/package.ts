// ✅ 完全复制device-an的package类型定义

export interface CreatePackageOrderData {
  packageId: number
  takeeffectType: number
  cycleId: number | null
}

export interface PackageOrderData {
  id: number
  name: number
  packageTotal: number
  packageIntroduce: string
  packagePrice: number
  popular: number
  packageType: number
  packageValidity: number
  validityDays: number
  cycles: CycleData[]
}

export interface CycleData {
  id: number
  cycleId: number
  cycleCount: number
}

export interface PackagePreviewData {
  price: number
  list: PackageEffective[]
}

export interface PackageEffective {
  takeeffectTime: string
  expirationTime: string
}

// ✅ 套餐订单历史记录数据类型
export interface PackageOrderHistoryData {
  id: number
  orderName: string
  systemOrdernumber: string
  upstreamOrdernumber: string
  paymentOrdernumber: string
  packageCost: number
  packagePrice: number
  paidInAmount: number
  orderProfit: number
  discountAmount: number
  discountType: number
  takeeffectType: number
  orderState: number
  orderCount: number
  orderPayment: number
  orderIp: string
  rechargeAddress: string
  creationTime: Date
  rechargeTime: Date
  takeeffectTime: Date
  expirationTime: Date
  deviceNo: string
  iccidNumber: string
  userAccount: string
  errorLog: string
}

// ✅ 余额明细数据类型
export interface BalanceDetailsData {
  id: number
  systemOrdernumber: string // 关联单号
  balanceBefore: number // 变动前
  balanceAfter: number // 变动后
  balanceAmount: number // 变动金额
  unit: string // 变动符号
  balanceAlterationTime: Date // 变动时间
  balanceRemark: string // 变动备注
}

// ✅ 月度余额统计数据类型
export interface MonthBalanceData {
  monthRecharge: number // 本月充值
  monthConsumption: number // 本月消费
}
