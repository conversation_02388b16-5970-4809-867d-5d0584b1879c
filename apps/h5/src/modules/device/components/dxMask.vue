<!-- ✅ 完全复制device-an的dxMask.vue -->
<script setup lang="ts">
import { useMaskStore } from '@device/stores/mask'
const useMask = useMaskStore()

import { useRouter } from 'vue-router'
const router = useRouter()
const close = (msg: string) => {
  if (msg === 'NOT_LOGIN') {
    router.push('/device/login')
  }
  useMask.show = false
}
</script>

<template>
  <Transition name="fade">
    <div v-if="useMask.show" class="dx-mask" @click.self="close(useMask.Error.detail)" />
  </Transition>

  <Transition name="pop">
    <div v-if="useMask.show" class="dx-mask-content">
      <component :is="useMask.MaskComp" />
    </div>
  </Transition>
</template>

<style lang="scss" scoped>
.dx-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.dx-mask-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  width: 90%;
  max-width: 400px;
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.pop-enter-active,
.pop-leave-active {
  transition: all 0.3s ease;
}

.pop-enter-from,
.pop-leave-to {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
}
</style>
