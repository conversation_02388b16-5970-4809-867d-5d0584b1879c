<!-- ✅ 完全复制device-an的PhonePayment.vue -->
<script setup lang="ts">
import { ref } from 'vue'
import { useDeviceStore } from '@device/stores/device'
const useDevice = useDeviceStore()
const emits = defineEmits(['PhonePaymentSubmit'])

import dxInput from './dxInput.vue'
import dxSending from './dxSending.vue'

import apiClient from '@/api/client'
const SedningApi = async (): Promise<boolean> => {
  try {
    const response = await apiClient.sendSmsCode({
      type: 'payment'
    })
    return Boolean(response.code)
  } catch (error) {
    console.error('发送验证码失败:', error)
    return false
  }
}

const code = ref('')
</script>

<template>
  <div class="PhonePayment">
    <dxInput
      v-model="useDevice.details.phone"
      left-icon="phone"
      :readonly="true"
      placeholder="请输入手机号"
    />

    <dxSending style="margin-top: 0.8rem" v-model="code" :disabled="false" :api="SedningApi" />

    <div class="PhonePayment-box">
      <button class="PhonePayment-btn" :disabled="!code" @click="emits('PhonePaymentSubmit', code)">
        立即支付
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '../../../styles/variables.scss';

.PhonePayment {
  box-sizing: border-box;
  padding: $padding;
  padding-top: calc($padding * 2);
  min-height: 65vh;

  &-btn {
    display: block;
    outline: none;
    border: none;
    background-color: $primary;
    font-size: 0.7rem;
    color: #fff;
    height: 2rem;
    margin-top: 1rem;
    border-radius: 1rem;
    width: 100%;

    &:disabled {
      background-color: rgba(229, 231, 235);
      color: #999;
    }
  }
}
</style>
