<!-- ✅ 简化的dxInput组件 -->
<script setup lang="ts">
import { computed } from 'vue'
import SvgIcon from '@/components/SvgIcon.vue'

interface Props {
  modelValue?: string
  leftIcon?: string
  readonly?: boolean
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  readonly: false,
  placeholder: ''
})

const emits = defineEmits(['update:modelValue'])

const value = computed({
  get: () => props.modelValue,
  set: (val) => emits('update:modelValue', val)
})
</script>

<template>
  <div class="dxInput">
    <div class="dxInput-left" v-if="leftIcon">
      <SvgIcon :name="leftIcon" class="dxInput-left-icon" />
    </div>
    
    <input
      v-model="value"
      :readonly="readonly"
      :placeholder="placeholder"
      class="dxInput-input"
    />
  </div>
</template>

<style lang="scss" scoped>
@import '../../../styles/variables.scss';

.dxInput {
  display: flex;
  align-items: center;
  background-color: $background;
  border-radius: $radius;
  padding: 0 $padding;
  height: 2.5rem;

  &-left {
    margin-right: 0.5rem;

    &-icon {
      font-size: 1rem;
      color: #666;
    }
  }

  &-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 0.7rem;

    &::placeholder {
      color: #999;
    }
  }
}
</style>
