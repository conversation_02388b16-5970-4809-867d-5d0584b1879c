<!-- ✅ 完全复制device-an的PasswordPayment.vue -->
<script setup lang="ts">
const emits = defineEmits(['PsswordPaymentSubmit'])
import { ref } from 'vue'
const show = ref(false)
const value = ref('')
</script>

<template>
  <div class="PasswordPayment">
    <div class="PasswordPayment-tit">旧密码(默认密码:123456)</div>
    <van-password-input :value="value" :focused="show" @focus="show = true" :length="6" />

    <!-- 数字键盘 -->
    <van-number-keyboard v-model="value" :show="show" @blur="show = false" />

    <div class="PasswordPayment-box">
      <button
        class="PasswordPayment-btn"
        :disabled="value.length !== 6"
        @click="emits('PsswordPaymentSubmit', value)"
      >
        立即支付
      </button>
    </div>
  </div>
</template>



<style lang="scss" scoped>
@import '../../../styles/variables.scss';

.PasswordPayment {
  box-sizing: border-box;
  padding: $padding;
  padding-top: calc($padding * 2);
  min-height: 60vh;
  background-color: $background;

  &-tit {
    font-size: 0.7rem;
    color: #666;
    margin-bottom: 0.3rem;
    box-sizing: border-box;
    padding: 0 0.9rem;
  }

  &-box {
    box-sizing: border-box;
    padding: 0 0.9rem;
  }

  &-btn {
    display: block;
    outline: none;
    border: none;
    background-color: $primary;
    font-size: 0.7rem;
    color: #fff;
    height: 2rem;
    margin-top: 1rem;
    border-radius: 1rem;
    width: 100%;

    &:disabled {
      background-color: rgba(229, 231, 235);
      color: #999;
    }
  }
}
</style>
