<!-- ✅ 完全复制device-an的dxPayment.vue -->
<script setup lang="ts">
import { type PropType } from 'vue'
import { ref, watch } from 'vue'

// ✅ 导入支付hooks
import { usePayment } from '@device/composables/usePayment'
import { loading } from '@/composables/useModernLoading'


// ✅ 临时使用any类型避免类型错误
type PaymentData = any

const props = defineProps({
  PaymentList: {
    type: Array as PropType<PaymentData[]>,
    required: true
  },
  Money: {
    type: Number,
    required: true
  },
  GetOrderNum: {
    type: Function as PropType<() => Promise<{ orderType: number; orderNo: string }>>,
    required: true
  }
})

const PaymentMethodsSelect = ref<boolean>(false)
import PaymentBox from './PaymentBox.vue'
import SvgIcon from '@/components/SvgIcon.vue'

// 选择的支付
const PaymentMethod = ref<PaymentData>({} as PaymentData)
// 选择支付的数组下标
const PaymentMethodIndex = ref<number>(0)

// ✅ 修复：在PaymentList有数据且组件mounted后初始化
watch(() => props.PaymentList, (newList) => {
  if (newList && newList.length > 0) {
    PaymentMethod.value = newList[0]
    PaymentMethodIndex.value = 0
  }
}, { immediate: true })

//  切换支付
const SelectPayment = (index: number) => {
  PaymentMethod.value = props.PaymentList[index]
  PaymentMethodIndex.value = index
  PaymentMethodsSelect.value = false
}

// ✅ 使用现代化Loading和支付hooks

const { handlePayment } = usePayment()
const emits = defineEmits(['BalancePayment'])

// ✅ 完全复制device-an的Submit函数实现
const Submit = async () => {
  // 显示现代化支付Loading
  loading.payment()

  const { orderNo, orderType } = await props.GetOrderNum()

  // 余额支付
  if (PaymentMethod.value.type === 'balance') {
    loading.hide()
    emits('BalancePayment', orderNo)
    return
  }

  // 验证参数
  if (
    !orderNo ||
    typeof PaymentMethodIndex.value !== 'number' ||
    typeof PaymentMethod.value.type !== 'string' ||
    !orderType
  ) {
    loading.hide()
    import('vant').then(({ showFailToast }) => {
      showFailToast('订单信息有误，请重试')
    })
    return
  }

  // ✅ 调用真正的支付处理函数
  try {
    const result = await handlePayment({
      Payment: PaymentMethod.value,
      state: {
        orderNo,
        orderType
      }
    })
    
    loading.hide()
    
    if (result.status === 1) {
      // 显示成功Loading
      loading.breathing('支付成功 ✓')
      
      setTimeout(() => {
        loading.hide()
      }, 2000)
    } else {
      import('vant').then(({ showFailToast }) => {
        showFailToast(result.msg || '支付失败')
      })
    }
  } catch (error) {
    console.error('支付失败:', error)
    loading.hide()
    import('vant').then(({ showFailToast }) => {
      showFailToast('支付失败，请稍后重试')
    })
  }
}
</script>

<template>
  <div class="dxPayment">
    <div class="dxPayment-head">
      <div class="dxPayment-head-label">应付金额</div>

      <div class="dxPayment-head-moeny"><span>￥</span>{{ props.Money.toFixed(2) }}</div>

      <div class="dxPayment-head-time">
        <SvgIcon name="time" class="dxPayment-head-time-icon" />订单将在30分钟后自动关闭
      </div>
    </div>

    <div class="dxPayment-details">
      <div class="dxPayment-details-label">订单详情</div>
      <slot></slot>
    </div>

    <div class="dxPayment-payment">
      <div class="dxPayment-payment-label">支付方式</div>
      <PaymentBox
        v-if="PaymentMethod && PaymentMethod.name"
        :Payment="PaymentMethod"
        :Active="true"
        Right="View"
        @click="PaymentMethodsSelect = true"
      />

      <div class="dxPayment-payment-secure">
        <div class="dxPayment-payment-secure-svg">
          <SvgIcon name="secure" />
        </div>

        <div class="dxPayment-payment-secure-txt">
          <div class="dxPayment-payment-secure-txt-label">支付安全保障</div>

          <div class="dxPayment-payment-secure-txt-tip">银行级加密保护您的安全</div>
        </div>
      </div>

      <div class="dxPayment-payment-btn" @click="Submit">立即支付</div>
    </div>

    <van-popup v-model:show="PaymentMethodsSelect" round position="bottom">
      <div class="PaymentSelectBox">
        <div class="PaymentSelectBox-head">
          <div class="PaymentSelectBox-head-label">选择支付方式</div>

          <SvgIcon
            class="PaymentSelectBox-head-icon"
            name="close"
            @click="PaymentMethodsSelect = false"
          />
        </div>

        <PaymentBox
          v-for="(item, i) in props.PaymentList"
          :key="i"
          :Payment="item"
          :Active="PaymentMethodIndex === i"
          :Right="PaymentMethodIndex === i ? 'Select' : 'not'"
          @click="SelectPayment(i)"
        />
      </div>
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
@import '../../../styles/variables.scss';

.dxPayment {
  @include PageBox;

  &-head {
    border-radius: $radius $radius 0 0;
    background-color: #fff;
    box-sizing: border-box;
    padding: calc($padding * 2.5) $padding;
    text-align: center;
    box-shadow: $shadow;

    &-label {
      font-size: 0.7rem;
      color: #666;
    }

    &-moeny {
      font-size: 2.4rem;
      font-weight: bold;
      position: relative;
      left: -0.1rem;
      margin-top: 0.5rem;

      span {
        font-size: 0.8rem;
      }
    }

    &-time {
      font-size: 0.6rem;
      color: #999;
      margin-top: 0.3rem;
      text-align: center;

      &-icon {
        position: relative;
        top: -0.1rem;
        margin-right: 0.1rem;
      }
    }
  }

  &-details {
    box-sizing: border-box;
    padding: $padding;
    margin: calc($padding / 2) 0;
    background-color: #fff;
    box-shadow: $shadow;

    &-label {
      font-size: 0.6rem;
      color: #666;
    }
  }

  &-payment {
    box-sizing: border-box;
    padding: $padding;
    background-color: #fff;
    box-shadow: $shadow;
    border-radius: 0 0 $radius $radius;

    &-label {
      font-size: 0.6rem;
      color: #666;
    }

    &-secure {
      background-color: $background;
      border-radius: $radius;
      box-sizing: border-box;
      padding: $padding;
      display: flex;
      justify-content: start;
      align-items: center;
      margin-top: calc($padding / 1.2);

      &-svg {
        width: 2rem;
        height: 2rem;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        color: $success;
        font-size: 1.2rem;
      }

      &-txt {
        box-sizing: border-box;
        padding: 0 0.5rem;

        &-label {
          font-size: 0.7rem;
        }

        &-tip {
          color: #666;
          font-size: 0.6rem;
          margin-top: 0.2rem;
        }
      }
    }

    &-btn {
      margin-top: calc($padding * 2);
      text-align: center;
      color: #fff;
      background-color: $primary;
      height: 2rem;
      border-radius: 1rem;
      line-height: 2rem;
      font-size: 0.7rem;
      font-weight: bold;
    }
  }
}

.PaymentSelectBox {
  box-sizing: border-box;
  padding: $padding;
  padding-top: 1rem;
  min-height: 50vh;
  max-height: 70vh;
  overflow-y: scroll;
  overflow-x: hidden;

  &-head {
    display: flex;
    justify-content: space-between;
    font-size: 0.7rem;
    color: #666;

    &-icon {
      font-size: 1rem;
    }
  }
}
</style>
