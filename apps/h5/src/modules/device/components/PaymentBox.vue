<!-- ✅ 完全复制device-an的PaymentBox.vue -->
<script setup lang="ts">
import { type PaymentData } from '@device/types/payment'
import { type PropType } from 'vue'
import SvgIcon from '@/components/SvgIcon.vue'

const props = defineProps({
  Payment: {
    type: Object as PropType<PaymentData>,
    required: true
  },
  Active: {
    type: Boolean,
    default: false
  },
  Right: {
    type: String as PropType<'View' | 'Select' | 'not'>,
    default: 'not'
  }
})

// 支付方式图标映射
const getPaymentIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    'balance': 'balance',
    'wechat': 'wechat',
    'alipay': 'alipay',
    'unionpay': 'unionpay'
  }
  return iconMap[type] || 'payment'
}
</script>

<template>
  <div class="PaymentBox" :class="{ 'PaymentBox-active': Active }">
    <div class="PaymentBox-left">
      <div class="PaymentBox-left-icon">
        <SvgIcon :name="getPaymentIcon(Payment.type)" />
      </div>

      <div class="PaymentBox-left-name">{{ Payment.name }}</div>
    </div>

    <div class="PaymentBox-right">
      <SvgIcon v-if="Right === 'View'" name="right" class="PaymentBox-right-view" />
      <SvgIcon v-else-if="Right === 'Select'" name="select" class="PaymentBox-right-select" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '../../../styles/variables.scss';

.PaymentBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: calc($padding / 1.2);
  margin-top: calc($padding / 2);
  background-color: $background;
  border-radius: $radius;
  transition: 0.15s linear;
  border: 0.01rem solid transparent;

  &-active {
    border-color: $primary;
    background-color: rgba($primary, 0.05);
  }

  &-left {
    display: flex;
    justify-content: start;
    align-items: center;

    &-icon {
      width: 1.5rem;
      height: 1.5rem;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 0.5rem;
      font-size: 1.2rem;
      color: $primary;
    }

    &-name {
      font-size: 0.7rem;
      color: #333;
    }
  }

  &-right {
    &-view {
      color: #999;
      font-size: 0.8rem;
    }

    &-select {
      color: $primary;
      font-size: 1rem;
    }
  }
}
</style>
