/**
 * 设备模块公共组件导出
 *
 * 只导出可复用的公共组件
 * 业务组件已迁移到各自页面的components文件夹中
 */

// 通用输入组件
export { default as DxInput } from './dxInput.vue'
export { default as DxSending } from './dxSending.vue'

// 通用支付组件
export { default as PasswordPayment } from './PasswordPayment.vue'
export { default as PaymentBox } from './PaymentBox.vue'
export { default as PhonePayment } from './PhonePayment.vue'
export { default as DxPayment } from './dxPayment.vue'

// 通用UI组件
export { default as DxMask } from './dxMask.vue'
