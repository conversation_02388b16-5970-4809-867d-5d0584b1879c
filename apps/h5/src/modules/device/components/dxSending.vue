<!-- ✅ 简化的dxSending组件 -->
<script setup lang="ts">
import { ref, computed } from 'vue'
import { showSuccessToast, showFailToast } from 'vant'

interface Props {
  modelValue?: string
  disabled?: boolean
  api?: () => Promise<boolean>
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  disabled: false
})

const emits = defineEmits(['update:modelValue'])

const value = computed({
  get: () => props.modelValue,
  set: (val) => emits('update:modelValue', val)
})

const countdown = ref(0)
const sending = ref(false)

const sendCode = async () => {
  if (countdown.value > 0 || sending.value) return
  
  try {
    sending.value = true
    
    if (props.api) {
      const success = await props.api()
      if (success) {
        showSuccessToast('验证码已发送')
        startCountdown()
      } else {
        showFailToast('发送失败')
      }
    } else {
      // 模拟发送成功
      showSuccessToast('验证码已发送')
      startCountdown()
    }
  } catch (error) {
    showFailToast('发送失败')
  } finally {
    sending.value = false
  }
}

const startCountdown = () => {
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}
</script>

<template>
  <div class="dxSending">
    <div class="dxSending-input">
      <input
        v-model="value"
        type="number"
        placeholder="请输入验证码"
        maxlength="6"
        class="dxSending-input-field"
      />
      
      <button
        class="dxSending-input-btn"
        :disabled="countdown > 0 || sending || disabled"
        @click="sendCode"
      >
        {{ countdown > 0 ? `${countdown}s` : sending ? '发送中...' : '获取验证码' }}
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '../../../styles/variables.scss';

.dxSending {
  &-input {
    display: flex;
    gap: 0.5rem;
    background-color: $background;
    border-radius: $radius;
    padding: 0 $padding;
    height: 2.5rem;
    align-items: center;

    &-field {
      flex: 1;
      border: none;
      outline: none;
      background: transparent;
      font-size: 0.7rem;

      &::placeholder {
        color: #999;
      }
    }

    &-btn {
      background-color: $primary;
      color: #fff;
      border: none;
      border-radius: 0.3rem;
      padding: 0.3rem 0.6rem;
      font-size: 0.6rem;
      cursor: pointer;

      &:disabled {
        background-color: #ddd;
        cursor: not-allowed;
      }
    }
  }
}
</style>
