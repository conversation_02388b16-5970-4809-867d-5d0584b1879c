import { ref } from 'vue'
import { defineStore } from 'pinia'
import axios from 'axios'
import { md5 } from 'js-md5'

// ==================== 类型定义（完全复制自Device-An） ====================

// 微信信息
interface UseWeChatInfo {
  img: string //用户头像
  name: string //用户名
  openid: string //openid
}

// 系统信息
interface UseManageConfig {
  img: string //用户头像
  name: string //用户名
  icp: string //备案号
}

// 充值配置
export interface UseRechargeConfig {
  frontName: string //配置名称
  customerServiceConnection: string //客服链接
  balancePayVerification: number //余额验证方式
  appId: string //appid
  phoneLoginVerification: number // 手机号登录
}

// ==================== API配置（完全复制自Device-An） ====================

// 环境配置
const getBaseURL = () => {
  const mode = import.meta.env.MODE || 'development'
  switch (mode) {
    case 'development':
      return {
        java: '/Fan',  // 代理到Device-An的Java API
        python: '/Lin' // 代理到Device-An的Python API
      }
    case 'production':
      return {
        // java: window.location.protocol + '//' + window.location.hostname + '/java/',
        // python: window.location.protocol + '//' + window.location.hostname + '/py/'
          java: "https://ml.liangaiyun.com" + '/java/',
        python: "https://ml.liangaiyun.com" + '/py/'
      }
    default:
      return {
        java: 'https://cmp.tqzhkj.com/',
        python: 'https://cmp.tqzhkj.com/'
      }
  }
}

// Java API实例
const javaAPI = axios.create({
  baseURL: getBaseURL().java,
  timeout: 50000
})

// Python API实例
const pythonAPI = axios.create({
  baseURL: getBaseURL().python,
  timeout: 50000
})

// ✅ 完全复制 device-an 的请求拦截器配置
// 生成加密密钥
const generateKey = () => {
  const timestamp = Math.floor(Date.now() / 1000)
  const secret = import.meta.env.VITE_AUTH_SECRET || 'django-insecure-_=0rpi4cfhzdus5ih*4^8p%j)zdg%y2i^_d6_tbe(z$tfk!yp%'

  // 开发环境警告
  if (import.meta.env.DEV && !import.meta.env.VITE_AUTH_SECRET) {
    console.warn('⚠️ 使用默认认证密钥，生产环境请设置 VITE_AUTH_SECRET 环境变量')
  }

  return {
    ZHIXUN_YAN: md5(timestamp + secret),
    ZHIXUN_DATE: timestamp
  }
}

javaAPI.interceptors.request.use((req) => {
  const { ZHIXUN_YAN, ZHIXUN_DATE } = generateKey()

  // ✅ 修复：正确获取JWT token
  let deviceToken = 'NOT_LOGIN'
  try {
    const storedData = localStorage.getItem('ZX-DEVICE-TOKEN')
    if (storedData) {
      const parsed = JSON.parse(storedData)
      deviceToken = parsed.key || storedData
    }
  } catch (error) {
    const storedToken = localStorage.getItem('ZX-DEVICE-TOKEN')
    if (storedToken && storedToken !== 'NOT_LOGIN') {
      deviceToken = storedToken
    }
  }

  req.headers['deviceToken'] = deviceToken
  req.headers['Conten-Date'] = ZHIXUN_DATE
  req.headers['Conten-Zx'] = ZHIXUN_YAN

  return req
})

pythonAPI.interceptors.request.use((req) => {
  const { ZHIXUN_YAN, ZHIXUN_DATE } = generateKey()

  // ✅ 修复：正确获取JWT token
  let deviceToken = 'NOT_LOGIN'
  try {
    const storedData = localStorage.getItem('ZX-DEVICE-TOKEN')
    if (storedData) {
      const parsed = JSON.parse(storedData)
      deviceToken = parsed.key || storedData
    }
  } catch (error) {
    const storedToken = localStorage.getItem('ZX-DEVICE-TOKEN')
    if (storedToken && storedToken !== 'NOT_LOGIN') {
      deviceToken = storedToken
    }
  }

  req.headers['Authorization'] = deviceToken
  req.headers['Conten-Date'] = ZHIXUN_DATE
  req.headers['Conten-Zx'] = ZHIXUN_YAN

  return req
})

// ✅ 完全复制 device-an 的响应拦截器错误处理
javaAPI.interceptors.response.use((res) => {
  if (!res.data.code) {
    console.error('❌ WeChat Java API请求失败:', res.data.msg)
    import('vant').then(({ showFailToast }) => {
      showFailToast(res.data.msg || '网络错误，请稍后重试')
    })
  }
  return res.data
}, (error) => {
  console.error('❌ WeChat Java 网络请求异常:', error)
  import('vant').then(({ showFailToast }) => {
    showFailToast('网络错误，请稍后重试')
  })
  return Promise.resolve({
    code: false,
    data: null,
    msg: '网络错误，请稍后重试'
  })
})

pythonAPI.interceptors.response.use((res) => {
  if (!res.data.code) {
    console.error('❌ WeChat Python API请求失败:', res.data.msg)
    import('vant').then(({ showFailToast }) => {
      showFailToast(res.data.msg || '网络错误，请稍后重试')
    })
  }
  return res.data
}, (error) => {
  console.error('❌ WeChat Python 网络请求异常:', error)
  import('vant').then(({ showFailToast }) => {
    showFailToast('网络错误，请稍后重试')
  })
  return Promise.resolve({
    code: false,
    data: null,
    msg: '网络错误，请稍后重试'
  })
})

// ==================== API方法（完全复制自Device-An） ====================

// 系统配置API
const GetManageConfig = async () => {
  try {
    const response = await pythonAPI.get('/backend/backstage/getsitesettings/', {
      params: { domain_name: window.location.hostname }
    })
    return response.data
  } catch (error) {
    console.error('❌ 获取系统配置失败:', error)
    throw error
  }
}

// 获取充值端配置API
const GetRechargeConfig = async () => {
  try {
    console.log('📡 调用获取充值配置API')
    
    const response = await javaAPI.get('/front/setup/getFrontSetup', {
      params: { type: 2 }
    })
    
    console.log('📡 充值配置API响应:', response.data)
    return response.data
  } catch (error) {
    console.error('❌ 获取充值配置失败:', error)
    throw error
  }
}

// ==================== WeChat Store（完全复制自Device-An） ====================

export const useWeChatStore = defineStore(
  'WeChat',
  () => {
    const loading = ref<boolean>(true)

    const ManageConfig = ref<UseManageConfig>({} as UseManageConfig)

    const FetchManageConfig = async () => {
      if (ManageConfig.value.name) return
      try {
        const { code, data } = await GetManageConfig()
        if (!code || !data[0]) return
        
        ManageConfig.value = {
          img: data[0]?.site_ico,
          name: data[0]?.site_name,
          icp: data[0]?.site_icp
        }

        // 设置页面图标
        const link: HTMLLinkElement =
          document.querySelector("link[rel*='icon']") || document.createElement('link')
        link.href = data[0].site_ico
        
        console.log('✅ 系统配置获取成功:', ManageConfig.value)
      } catch (error) {
        console.error('❌ 系统配置获取失败:', error)
      }
    }

    const RechargeConfig = ref<UseRechargeConfig>({} as UseRechargeConfig)

    // ✅ 完全复制 device-an 的 FetchRechargeConfig 方法
    const FetchRechargeConfig = async () => {
      if (RechargeConfig.value.frontName) return
      try {
        const { code, data } = await GetRechargeConfig()
        if (!code) return
        
        RechargeConfig.value = data
        console.log('✅ 充值配置获取成功:', RechargeConfig.value)
      } catch (error) {
        console.error('❌ 充值配置获取失败:', error)
      }
    }

    const isWeChat = /MicroMessenger/i.test(navigator.userAgent)
    const WeChat = ref<UseWeChatInfo>({} as UseWeChatInfo)

    return {
      FetchManageConfig,
      ManageConfig,
      FetchRechargeConfig,
      RechargeConfig,
      isWeChat,
      WeChat,
      loading
    }
  },
  {
    persist: [
      {
        key: 'lowcode-wechat',
        paths: ['WeChat']
      }
    ]
  }
)
