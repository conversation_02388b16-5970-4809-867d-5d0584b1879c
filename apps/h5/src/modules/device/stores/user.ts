import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
// 权限服务暂未实现
// import { permissionService } from '@/services/PermissionService'
import { requestManager } from '@/services/RequestManager'

interface User {
  id: string
  username: string
  nickname?: string
  avatar?: string
  email?: string
  phone?: string
  roles: string[]
  permissions: string[]
  lastLoginTime?: string
}

interface LoginCredentials {
  username: string
  password: string
  deviceId?: string
}

interface LoginResponse {
  user: User
  token: string
  refreshToken: string
  expiresIn: number
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref<User | null>(null)
  const token = ref<string>('')
  const refreshToken = ref<string>('')
  const isLoggedIn = computed(() => !!currentUser.value && !!token.value)
  const isLoading = ref(false)
  
  // 从本地存储恢复状态
  const restoreFromStorage = () => {
    const storedUser = localStorage.getItem('user')
    const storedToken = localStorage.getItem('auth_token')
    const storedRefreshToken = localStorage.getItem('refresh_token')
    
    if (storedUser && storedToken) {
      try {
        currentUser.value = JSON.parse(storedUser)
        token.value = storedToken
        refreshToken.value = storedRefreshToken || ''
        
        // 更新权限服务的用户信息
        // permissionService.setCurrentUser(currentUser.value)
      } catch (error) {
        console.error('Failed to restore user from storage:', error)
        clearStorage()
      }
    }
  }
  
  // 保存到本地存储
  const saveToStorage = () => {
    if (currentUser.value && token.value) {
      localStorage.setItem('user', JSON.stringify(currentUser.value))
      localStorage.setItem('auth_token', token.value)
      if (refreshToken.value) {
        localStorage.setItem('refresh_token', refreshToken.value)
      }
    }
  }
  
  // 清除本地存储
  const clearStorage = () => {
    localStorage.removeItem('user')
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
  }
  
  // 登录
  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    isLoading.value = true
    
    try {
      const response = await requestManager.request<LoginResponse>({
        url: '/api/auth/login',
        method: 'POST',
        data: credentials
      })
      
      // 更新状态
      currentUser.value = response.user
      token.value = response.token
      refreshToken.value = response.refreshToken
      
      // 保存到本地存储
      saveToStorage()
      
      // 更新权限服务
      // permissionService.setCurrentUser(response.user)
      
      return true
    } catch (error) {
      console.error('Login failed:', error)
      return false
    } finally {
      isLoading.value = false
    }
  }
  
  // 登出
  const logout = async (): Promise<void> => {
    isLoading.value = true
    
    try {
      // 调用登出API
      await requestManager.request({
        url: '/api/auth/logout',
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token.value}`
        }
      })
    } catch (error) {
      console.error('Logout API failed:', error)
    } finally {
      // 清除本地状态
      currentUser.value = null
      token.value = ''
      refreshToken.value = ''
      
      // 清除本地存储
      clearStorage()
      
      // 清除权限服务状态
      // permissionService.setCurrentUser(null)
      // permissionService.clearCache()
      
      // 清除请求管理器缓存
      requestManager.invalidateCache('')
      
      isLoading.value = false
    }
  }
  
  // 刷新token
  const refreshAuthToken = async (): Promise<boolean> => {
    if (!refreshToken.value) {
      return false
    }
    
    try {
      const response = await requestManager.request<LoginResponse>({
        url: '/api/auth/refresh',
        method: 'POST',
        data: {
          refreshToken: refreshToken.value
        }
      })
      
      // 更新token
      token.value = response.token
      if (response.refreshToken) {
        refreshToken.value = response.refreshToken
      }
      
      // 保存到本地存储
      saveToStorage()
      
      return true
    } catch (error) {
      console.error('Token refresh failed:', error)
      // token刷新失败，清除登录状态
      await logout()
      return false
    }
  }
  
  // 获取用户信息
  const fetchUserInfo = async (): Promise<boolean> => {
    if (!token.value) {
      return false
    }
    
    try {
      const user = await requestManager.request<User>({
        url: '/api/user/profile',
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token.value}`
        },
        cache: {
          key: `user_profile_${currentUser.value?.id}`,
          ttl: 5 * 60 * 1000, // 5分钟缓存
          storage: 'memory'
        }
      })
      
      currentUser.value = user
      saveToStorage()
      // permissionService.setCurrentUser(user)
      
      return true
    } catch (error) {
      console.error('Fetch user info failed:', error)
      return false
    }
  }
  
  // 更新用户信息
  const updateUserInfo = async (updates: Partial<User>): Promise<boolean> => {
    if (!currentUser.value || !token.value) {
      return false
    }
    
    try {
      const updatedUser = await requestManager.request<User>({
        url: '/api/user/profile',
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token.value}`
        },
        data: updates
      })
      
      currentUser.value = updatedUser
      saveToStorage()
      // permissionService.setCurrentUser(updatedUser)
      
      // 清除用户信息缓存
      requestManager.invalidateCache(`user_profile_${currentUser.value.id}`)
      
      return true
    } catch (error) {
      console.error('Update user info failed:', error)
      return false
    }
  }
  
  // 检查权限
  const hasPermission = (permission: string): boolean => {
    if (!currentUser.value) {
      return false
    }
    
    return currentUser.value.permissions.includes(permission)
  }
  
  // 检查角色
  const hasRole = (role: string): boolean => {
    if (!currentUser.value) {
      return false
    }
    
    return currentUser.value.roles.includes(role)
  }
  
  // 初始化时恢复状态
  restoreFromStorage()
  
  return {
    // 状态
    currentUser,
    token,
    refreshToken,
    isLoggedIn,
    isLoading,
    
    // 方法
    login,
    logout,
    refreshAuthToken,
    fetchUserInfo,
    updateUserInfo,
    hasPermission,
    hasRole,
    restoreFromStorage
  }
}, {
  persist: {
    key: 'user-store',
    storage: localStorage,
    paths: ['currentUser', 'token', 'refreshToken']
  }
})
