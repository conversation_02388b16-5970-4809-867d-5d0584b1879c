import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import axios from 'axios'
import { md5 } from 'js-md5'
import { apiClient } from '@/api/client'
import { useGlobalData } from '@lowcode/aslib/hooks'

// ==================== API配置（来自Device-An） ====================

import { ENV_CONFIG } from '../../../config/env'

// 环境配置
const getBaseURL = () => {
  const mode = import.meta.env.MODE || 'development'
  switch (mode) {
    case 'development':
      return {
        java: ENV_CONFIG.DEVICE_AN_API_URL || '/Fan',  // 从环境变量获取或使用代理
        python: '/Lin' // 代理到Device-An的Python API
      }
    case 'production':
      return {
        java: ENV_CONFIG.DEVICE_AN_API_URL || "https://ml.liangaiyun.com/java/",
        python: "https://ml.liangaiyun.com/py/"
      }
    default:
      return {
        java: ENV_CONFIG.DEVICE_AN_API_URL || 'https://cmp.tqzhkj.com/',
        python: 'https://cmp.tqzhkj.com/'
      }
  }
}

// Java API实例（来自Device-An配置）
const javaAPI = axios.create({
  baseURL: getBaseURL().java,
  timeout: 50000
})

// Python API实例
const pythonAPI = axios.create({
  baseURL: getBaseURL().python,
  timeout: 50000
})

// ✅ 完全复制 device-an 的请求拦截器配置
javaAPI.interceptors.request.use((req) => {
  const { ZHIXUN_YAN, ZHIXUN_DATE } = generateKey()

  // ✅ 修复：正确获取JWT token
  let deviceToken = 'NOT_LOGIN'
  try {
    const storedData = localStorage.getItem('ZX-DEVICE-TOKEN')
    if (storedData) {
      // 如果存储的是对象格式，解析出key字段
      const parsed = JSON.parse(storedData)
      deviceToken = parsed.key || storedData
    }
  } catch (error) {
    // 如果解析失败，说明存储的就是纯字符串token
    const storedToken = localStorage.getItem('ZX-DEVICE-TOKEN')
    if (storedToken && storedToken !== 'NOT_LOGIN') {
      deviceToken = storedToken
    }
  }

  req.headers['deviceToken'] = deviceToken
  req.headers['Conten-Date'] = ZHIXUN_DATE
  req.headers['Conten-Zx'] = ZHIXUN_YAN

  console.log('📡 Java API请求头:', {
    deviceToken: deviceToken.substring(0, 50) + '...',
    'Conten-Date': ZHIXUN_DATE,
    'Conten-Zx': ZHIXUN_YAN
  })

  return req
})

pythonAPI.interceptors.request.use((req) => {
  const { ZHIXUN_YAN, ZHIXUN_DATE } = generateKey()

  // ✅ 修复：正确获取JWT token
  let deviceToken = 'NOT_LOGIN'
  try {
    const storedData = localStorage.getItem('ZX-DEVICE-TOKEN')
    if (storedData) {
      // 如果存储的是对象格式，解析出key字段
      const parsed = JSON.parse(storedData)
      deviceToken = parsed.key || storedData
    }
  } catch (error) {
    // 如果解析失败，说明存储的就是纯字符串token
    const storedToken = localStorage.getItem('ZX-DEVICE-TOKEN')
    if (storedToken && storedToken !== 'NOT_LOGIN') {
      deviceToken = storedToken
    }
  }

  req.headers['Authorization'] = deviceToken
  req.headers['Conten-Date'] = ZHIXUN_DATE
  req.headers['Conten-Zx'] = ZHIXUN_YAN

  console.log('📡 Python API请求头:', {
    Authorization: deviceToken.substring(0, 50) + '...',
    'Conten-Date': ZHIXUN_DATE,
    'Conten-Zx': ZHIXUN_YAN
  })

  return req
})

// ✅ 完全复制device-an的响应拦截器：使用OpenErrorBox
javaAPI.interceptors.response.use((res) => {
  // 只对包含code字段且code为false的响应显示错误
  if (res.data && typeof res.data === 'object' && res.data.hasOwnProperty('code') && !res.data.code) {
    console.error('❌ API请求失败:', res.data.msg)
    // ✅ 使用device-an的OpenErrorBox显示错误弹窗
    import('../../../stores/mask').then(({ useMaskStore }) => {
      const useMask = useMaskStore()
      useMask.OpenErrorBox(res.data.msg || '网络错误，请稍后重试')
    })
  }

  // 直接返回原始数据，让业务层处理
  return res.data
}, (error) => {
  console.error('❌ 网络请求异常:', error)
  // ✅ 使用device-an的OpenErrorBox显示错误弹窗
  import('../../../stores/mask').then(({ useMaskStore }) => {
    const useMask = useMaskStore()
    useMask.OpenErrorBox('网络错误，请稍后重试')
  })
  // ✅ 网络错误时返回标准格式，避免业务层报错
  return Promise.resolve({
    code: false,
    data: null,
    msg: '网络错误，请稍后重试'
  })
})

pythonAPI.interceptors.response.use((res) => {
  // 如果 code 为 false，说明请求失败，但仍然返回数据供业务层处理
  if (!res.data.code) {
    console.error('❌ Python API请求失败:', res.data.msg)
    // 使用 Vant 的 showFailToast 显示错误信息
    import('vant').then(({ showFailToast }) => {
      showFailToast(res.data.msg || '网络错误，请稍后重试')
    })
  }
  // ✅ 始终返回 res.data，让业务层处理
  return res.data
}, (error) => {
  console.error('❌ Python 网络请求异常:', error)
  import('vant').then(({ showFailToast }) => {
    showFailToast('网络错误，请稍后重试')
  })
  // ✅ 网络错误时返回标准格式，避免业务层报错
  return Promise.resolve({
    code: false,
    data: null,
    msg: '网络错误，请稍后重试'
  })
})

// 生成加密密钥（来自Device-An）
const generateKey = () => {
  const timestamp = Math.floor(Date.now() / 1000)
  const secret = import.meta.env.VITE_AUTH_SECRET || 'django-insecure-_=0rpi4cfhzdus5ih*4^8p%j)zdg%y2i^_d6_tbe(z$tfk!yp%'

  // 开发环境警告
  if (import.meta.env.DEV && !import.meta.env.VITE_AUTH_SECRET) {
    console.warn('⚠️ 使用默认认证密钥，生产环境请设置 VITE_AUTH_SECRET 环境变量')
  }

  return {
    ZHIXUN_YAN: md5(timestamp + secret),
    ZHIXUN_DATE: timestamp
  }
}

// FormData转换（来自Device-An）
const toFormData = (data: Record<string, any>): FormData => {
  const formData = new FormData()
  for (const key in data) {
    if (data[key] !== null && data[key] !== '' && data[key] !== undefined) {
      formData.append(key, data[key])
    }
  }
  return formData
}

// 登录数据接口
export interface LoginData {
  deviceNo: string // 设备号
  groupId?: number // 设备规则ID
}

// API响应格式
interface DeviceAnResponse<T> {
  code: number
  data: T
  msg: string
}

// ==================== API方法（完整迁移自Device-An） ====================

// 设备登录API
const loginDevice = async (data: LoginData): Promise<DeviceAnResponse<string>> => {
  try {
    console.log('📡 调用设备登录API:', data)

    // ✅ 修复：响应拦截器已经处理了格式转换，直接返回处理后的数据
    const response = await javaAPI.post('/front/deviceIndex/deviceLogin', toFormData({
      deviceNo: data.deviceNo,
      groupId: data.groupId || 2
    }))

    console.log('📡 登录API响应:', response)

    // 响应拦截器已经将JWT token包装成标准格式，直接返回
    return response as unknown as DeviceAnResponse<string>
  } catch (error) {
    console.error('❌ 登录API调用失败:', error)
    throw error
  }
}

// 获取设备详情API
const getDeviceDetails = async (): Promise<DeviceAnResponse<DeviceDetailsData>> => {
  try {
    console.log('📡 调用获取设备详情API')

    // ✅ 完全复制 device-an 的API调用方式
    const response = await javaAPI.get('/frontDevice/device/getDeviceInfo')

    console.log('📡 设备详情API响应:', response.data)
    return response.data
  } catch (error) {
    console.error('❌ 设备详情API调用失败:', error)
    throw error
  }
}

// 获取设备卡片API
const getDeviceCards = async (): Promise<DeviceAnResponse<DeviceCardData[]>> => {
  try {
    console.log('📡 调用获取设备卡片API')

    // ✅ 完全复制 device-an 的API调用方式
    const response = await javaAPI.get('/frontDevice/device/getDeviceCardList')

    console.log('📡 设备卡片API响应:', response.data)
    return response.data
  } catch (error) {
    console.error('❌ 设备卡片API调用失败:', error)
    throw error
  }
}

// 获取实名卡片API
const getDeviceRealNameCards = async (): Promise<DeviceAnResponse<DeviceRealNameCardData[]>> => {
  try {
    console.log('📡 调用获取实名卡片API')

    // ✅ 完全复制 device-an 的API调用方式
    const response = await javaAPI.get('/frontDevice/name/getCardList')

    console.log('📡 实名卡片API响应:', response.data)
    return response.data
  } catch (error) {
    console.error('❌ 实名卡片API调用失败:', error)
    throw error
  }
}

// ✅ 获取实名认证地址API (完全复制device-an的API)
const getDeviceCardRealNameAddress = async (params: { number: number, deviceId: number }) => {
  try {
    console.log('📡 调用实名认证地址API:', params)

    const requestParams = {
      ...params,
      frontType: 2,
      url: window.location.href.split('#')[0]
    }

    const response = await javaAPI.get('/device/name/getNameLike', { params: requestParams })

    console.log('📡 实名认证地址API响应:', response.data)

    // ✅ 修复：直接返回response.data，因为API直接返回了数据结构
    // API响应格式：{card: {...}, code: 1, msg: '...', url: null}
    return response.data
  } catch (error) {
    console.error('❌ 实名认证地址API调用失败:', error)
    throw error
  }
}

// ✅ 获取设备规则详情 (完全复制device-an的API)
const getDeviceRuleDetails = async (groupId: number): Promise<DeviceAnResponse<DeviceRuleData>> => {
  try {
    console.log('📡 调用设备规则API:', { groupId })

    const response = await javaAPI.get('/front/deviceIndex/getGroupData', {
      params: { groupId }
    })

    console.log('📡 设备规则API响应:', response)
    return response as unknown as DeviceAnResponse<DeviceRuleData>
  } catch (error) {
    console.error('❌ 设备规则API调用失败:', error)
    throw error
  }
}

// 设备基础信息接口 (复用device-an的类型定义)
export interface DeviceDetailsData {
  id: number
  becomedueDatetime: Date
  packageName: string
  deviceNo: string
  imeiNo: number
  vTotalFlow: number
  vUseFlow: number
  vResidueFlow: number
  vDayFlow: number
  currentCardId: number
  devicePackageGroupId: number
  deviceConfigId: number
  userId: number
  balance: number
  currentNetwork: number
  powerOnStatus: number
  presence: number
  status: number
  nameStatus: number
  restartTime: string
  reseTime: string
  recentHeartbeat: string
  updateTime: string
  activationDatetime: string
  currentBatteryLevel: number | -1
  currentSignal: number | -1
  cardSlot1Id?: number
  cardSlot2Id?: number
  cardSlot3Id?: number
  cardSlot4Id?: number
  prestoreId: number
  phone: string
  wifiName: string
  wifiPwd: string
  hideStatus: number
  wifiLike: number | -1
  wifi5gName: string
  wifi5gPwd: string
  wifi5gLike: number | -1
  hideStatus5g: number
}

export interface DeviceCardData {
  id: number
  iccid: string
  msisdn: string
  network: number
  operator: number
  cardType: number
  supports5g: number
}

export interface DeviceRealNameCardData {
  number: number
  cardName: number
  iccid: string
  msisdn: string
  network: number
  operator: number
}

// ✅ 完全复制device-an的设备规则数据结构
export interface DeviceRuleData {
  id: number
  name: string //规则名称
  groupImg: string //规则主图
  deviceType: number // 设备类型 1单网  2双网  3三网  4四网
  networkSwitching: number //网络切换 1不支持 2支持 3虚假 4内置主卡切换
  masterCard: number //主卡网络 1中国电信 2中国联通 3中国移动 4中国广电
  powerDisplay: number //电量显示 1不支持 2支持
  signaling: number //信号显示 1不支持 2支持
  wifiName: number //名称修改 1不支持 2支持
  wifiPwd: number //密码修改 1不支持 2支持
  wifiStatus: number //隐藏状态 1不支持 2支持
  restoreFactory: number //重置设备 1不支持 2支持
  restart: number //重启设备 1不支持 2支持
  clearCache: number //清除缓存 1不支持 2支持
  trafficSharing: number //流量共享 1不共享 2共享
  cardSlot1: number //卡槽网络1
  cardSlot2: number //卡槽网络2
  cardSlot3: number //卡槽网络3
  cardSlot4: number //卡槽网络4
  inAdvanceStop: number //提前停机
  msg: number //激活描述
  updateTime: number //更新时间
  cardType: number // 卡片类型  1实体卡 2云卡
  supports5g: number //支持5g 1支持 2不支持
}

export const useDeviceStore = defineStore(
  'device',
  () => {
    // 使用全局数据钩子
    const { setDeviceDetails, setDeviceCards, setRealNameCards } = useGlobalData()
    // 初始化时从localStorage读取token
    const getTokenFromStorage = () => {
      try {
        const storedData = localStorage.getItem('ZX-DEVICE-TOKEN')
        if (storedData && storedData !== 'NOT_LOGIN') {
          // 如果存储的是对象格式，解析出key字段
          const parsed = JSON.parse(storedData)
          return parsed.key || storedData
        }
      } catch (error) {
        // 如果解析失败，说明存储的就是纯字符串token
        const storedToken = localStorage.getItem('ZX-DEVICE-TOKEN')
        if (storedToken && storedToken !== 'NOT_LOGIN') {
          return storedToken
        }
      }
      return ''
    }

    const key = ref<string>(getTokenFromStorage())
    const loading = ref<boolean>(false)
    const details = ref<DeviceDetailsData>({} as DeviceDetailsData)
    const cards = ref<DeviceCardData[]>([])
    const realNameCards = ref<DeviceRealNameCardData[]>([])
    const rule = ref<DeviceRuleData>({} as DeviceRuleData)

    // 计算属性
    const isRealName = computed(() => {
      const cardsRealName = realNameCards.value.find(
        (item) => item.cardName != 2 && item.cardName != 4
      )
      return cardsRealName || (details.value.nameStatus !== 2 && details.value.nameStatus !== 4)
    })

    const flowProgress = computed(() => {
      if (!details.value.vTotalFlow) return 0
      const result = (details.value.vResidueFlow / details.value.vTotalFlow) * 100
      return isNaN(result) ? 0 : result
    })

    // ✅ 初始化方法 - 按device-an的顺序调用API
    const initialize = async () => {
      if (!key.value) {
        console.warn('⚠️ 没有设备key，无法初始化')
        return
      }

      try {
        loading.value = true
        console.log('🔄 开始初始化设备信息...')

        // ✅ 完全按照device-an的调用顺序
        await fetchDeviceDetails()
        await fetchDeviceCards()
        await fetchDeviceRuleDetails()
        await fetchRealNameCards()

        console.log('✅ 设备信息初始化完成')
      } catch (error) {
        console.error('❌ 设备信息初始化失败:', error)
        throw error
      } finally {
        loading.value = false
      }
    }

    // ✅ 获取设备详情（修复响应格式处理）
    const fetchDeviceDetails = async (force = false) => {
      if (!key.value) {
        console.warn('⚠️ 没有设备key，无法获取设备详情')
        return
      }

      if (!force && details.value.id) {
        console.log('✅ 设备详情已存在，跳过获取')
        return
      }

      try {
        console.log('🔄 获取设备详情...')

        // 调用Device-An的设备详情API
        const response = await getDeviceDetails()

        // ✅ 修复：检查响应格式，支持直接返回数据对象和标准格式
        if (response && (response as any).code) {
          // 标准格式：{code: true, data: {...}}
          const resp = response as DeviceAnResponse<DeviceDetailsData>
          details.value = resp.data
          console.log('✅ 设备详情获取成功:', resp.data)

          // 存储到全局数据
          setDeviceDetails(resp.data)
        } else if (response && (response as any).id) {
          // 直接返回设备详情对象
          details.value = response as unknown as DeviceDetailsData
          console.log('✅ 设备详情获取成功:', response)

          // 存储到全局数据
          setDeviceDetails(response as unknown as DeviceDetailsData)
        } else {
          console.error('❌ 设备详情获取失败:', (response as any)?.msg || '未知错误')
          throw new Error((response as any)?.msg || '获取设备详情失败')
        }
      } catch (error) {
        console.error('❌ 设备详情获取失败:', error)
        throw error
      }
    }

    // ✅ 获取设备卡片（修复响应格式处理）
    const fetchDeviceCards = async () => {
      if (!key.value) {
        console.warn('⚠️ 没有设备key，无法获取设备卡片')
        return
      }

      if (cards.value.length) {
        console.log('✅ 设备卡片已存在，跳过获取')
        return
      }

      try {
        console.log('🔄 获取设备卡片...')

        // 调用Device-An的设备卡片API
        const response = await getDeviceCards()

        // ✅ 修复：检查响应格式，支持直接返回数组和标准格式
        if (response && (response as any).code) {
          // 标准格式：{code: true, data: [...]}
          const resp = response as DeviceAnResponse<DeviceCardData[]>
          cards.value = resp.data
          console.log('✅ 设备卡片获取成功:', resp.data)

          // 存储到全局数据
          setDeviceCards(resp.data)
        } else if (Array.isArray(response)) {
          // 直接返回卡片数组
          cards.value = response as DeviceCardData[]
          console.log('✅ 设备卡片获取成功:', response)

          // 存储到全局数据
          setDeviceCards(response as DeviceCardData[])
        } else {
          console.error('❌ 设备卡片获取失败:', (response as any)?.msg || '未知错误')
          // 卡片获取失败不抛出错误，使用空数组
          cards.value = []

          // 存储空数组到全局数据
          setDeviceCards([])
        }
      } catch (error) {
        console.error('❌ 设备卡片获取失败:', error)
        cards.value = []
      }
    }

    // ✅ 获取实名卡片（修复响应格式处理）
    const fetchRealNameCards = async () => {
      if (!key.value) {
        console.warn('⚠️ 没有设备key，无法获取实名卡片')
        return
      }

      if (realNameCards.value.length) {
        console.log('✅ 实名卡片已存在，跳过获取')
        return
      }

      try {
        console.log('🔄 获取实名卡片...')

        // 调用Device-An的实名卡片API
        const response = await getDeviceRealNameCards()

        // ✅ 修复：检查响应格式，支持直接返回数组和标准格式
        if (response && (response as any).code) {
          // 标准格式：{code: true, data: [...]}
          const resp = response as DeviceAnResponse<DeviceRealNameCardData[]>
          realNameCards.value = resp.data
          console.log('✅ 实名卡片获取成功:', resp.data)

          // 存储到全局数据
          setRealNameCards(resp.data)
        } else if (Array.isArray(response)) {
          // 直接返回实名卡片数组
          realNameCards.value = response as DeviceRealNameCardData[]
          console.log('✅ 实名卡片获取成功:', response)

          // 存储到全局数据
          setRealNameCards(response as DeviceRealNameCardData[])
        } else {
          console.error('❌ 实名卡片获取失败:', (response as any)?.msg || '未知错误')
          // 实名卡片获取失败不抛出错误，使用空数组
          realNameCards.value = []

          // 存储空数组到全局数据
          setRealNameCards([])
        }
      } catch (error) {
        console.error('❌ 实名卡片获取失败:', error)
        realNameCards.value = []

        // 存储空数组到全局数据
        setRealNameCards([])
      }
    }

    // ✅ 获取设备规则（完全复制device-an的逻辑）
    const fetchDeviceRuleDetails = async () => {
      if (!key.value) {
        console.warn('⚠️ 没有设备key，无法获取设备规则')
        return
      }

      if (!details.value.devicePackageGroupId) {
        console.warn('⚠️ 没有设备包组ID，无法获取设备规则')
        return
      }

      if (rule.value.id) {
        console.log('✅ 设备规则已存在，跳过获取')
        return
      }

      try {
        console.log('🔄 获取设备规则...')

        // 调用Device-An的设备规则API
        const response = await getDeviceRuleDetails(details.value.devicePackageGroupId)

        // ✅ 修复：检查响应格式，支持直接返回对象和标准格式
        if (response && (response as any).code) {
          // 标准格式：{code: true, data: {...}}
          const resp = response as DeviceAnResponse<DeviceRuleData>
          rule.value = resp.data
          console.log('✅ 设备规则获取成功:', resp.data)
        } else if (response && (response as any).id) {
          // 直接返回设备规则对象
          rule.value = response as unknown as DeviceRuleData
          console.log('✅ 设备规则获取成功:', response)
        } else {
          console.error('❌ 设备规则获取失败:', (response as any)?.msg || '未知错误')
          // 设备规则获取失败不抛出错误，使用空对象
          rule.value = {} as DeviceRuleData
        }
      } catch (error) {
        console.error('❌ 设备规则获取失败:', error)
        rule.value = {} as DeviceRuleData
      }
    }

    const renewDevice = async () => {
      console.log('🔄 开始更新设备信息...')
      loading.value = true

      try {
        // 第一步：调用更新设备API (完全复制device-an的逻辑)
        const updateResult = await apiClient.updateDevice()

        if (!updateResult.code) {
          console.error('❌ 更新设备失败:', updateResult.msg)
          throw new Error(updateResult.msg || '更新设备失败')
        }

        console.log('✅ 设备更新成功，开始获取最新设备信息')

        // 第二步：获取最新的设备详情 (完全复制device-an的逻辑)
        const detailsResult = await apiClient.getDeviceInfo()

        if (!detailsResult.code) {
          console.error('❌ 获取设备详情失败:', detailsResult.msg)
          throw new Error(detailsResult.msg || '获取设备详情失败')
        }

        // 更新本地数据
        details.value = detailsResult.data
        console.log('✅ 设备信息更新完成')

        return { success: true, message: '更新成功' }

      } catch (error: any) {
        console.error('❌ 更新设备信息失败:', error)
        throw error
      } finally {
        loading.value = false
      }
    }

    // ✅ 设备登录方法（修复JWT token处理）
    const login = async (loginData: LoginData): Promise<{ success: boolean; message: string }> => {
      try {
        console.log('🔐 开始设备登录:', loginData)

        // 调用Device-An的登录API
        const response = await loginDevice({
          deviceNo: loginData.deviceNo,
          groupId: loginData.groupId || 2 // 默认groupId为2
        })

        console.log('📡 登录API响应:', response)

        // ✅ 修复：直接检查响应是否为JWT token字符串
        if (typeof response === 'string' && (response as string).length > 0) {
          // 直接收到JWT token，说明登录成功
          key.value = response as string
          console.log('✅ 登录成功，token已保存:', (response as string).substring(0, 50) + '...')

          return { success: true, message: '登录成功' }
        } else if (response && (response as any).code) {
          // 标准格式响应
          const resp = response as DeviceAnResponse<string>
          key.value = resp.data
          console.log('✅ 登录成功，token已保存:', resp.data.substring(0, 50) + '...')

          return { success: true, message: '登录成功' }
        } else {
          // 登录失败
          const resp = response as DeviceAnResponse<string>
          console.log('❌ 登录失败:', resp?.msg || '未知错误')
          return { success: false, message: resp?.msg || '登录失败' }
        }
      } catch (error) {
        console.error('❌ 登录异常:', error)
        // 网络错误已经由响应拦截器处理
        return { success: false, message: '网络错误，请稍后重试' }
      }
    }

    // ✅ 登出方法
    const logout = () => {
      console.log('🚪 开始登出，清除所有状态')

      // 立即清除响应式状态
      key.value = ''
      details.value = {} as DeviceDetailsData
      cards.value = []
      realNameCards.value = []

      // 清理localStorage中的token
      localStorage.removeItem('ZX-DEVICE-TOKEN')

      // 清理API客户端的认证信息
      try {
        apiClient.clearAuth()
      } catch (error) {
        console.error('清理API客户端认证信息失败:', error)
      }

      console.log('🚪 用户已登出，所有状态已清除')
    }

    // ✅ 检查登录状态
    const checkLoginStatus = computed(() => !!key.value)

    return {
      // 状态
      key,
      loading,
      details,
      cards,
      realNameCards,
      rule,

      // 计算属性
      isRealName,
      flowProgress,
      checkLoginStatus,

      // 方法
      initialize,
      fetchDeviceDetails,
      fetchDeviceCards,
      fetchRealNameCards,
      fetchDeviceRuleDetails,
      renewDevice,
      login,
      logout,
      // API方法
      getDeviceCardRealNameAddress
    }
  },
  {
    persist: [
      {
        key: 'ZX-DEVICE-TOKEN', // ✅ 与device-an保持完全一致
        paths: ['key']
      }
    ]
  }
)
