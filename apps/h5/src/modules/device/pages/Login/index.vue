<template>
  <div class="device-login-page">
    <!-- 使用设备登录组件 -->
    <DeviceLogin ref="deviceLoginRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import DeviceLogin from './components/DeviceLogin.vue'
import { appDataManager } from '../../../../services/AppDataManager'
import { useDeviceStore } from '@device/stores/device'

const router = useRouter()
const route = useRoute()
const deviceStore = useDeviceStore()

const deviceLoginRef = ref<InstanceType<typeof DeviceLogin>>()

// ==================== 生命周期 ====================
onMounted(async () => {
  // 🎯 检查AppID有效性
  const appId = localStorage.getItem('current-app-id')

  if (!appId || appId.trim() === '') {
    console.warn('⚠️ 登录页面：没有有效的AppID，跳转到AppID缺失页面')
    router.replace('/app-id-missing')
    return
  }

  // 🎯 验证AppID是否存在
  const validation = await appDataManager.validateAppIdWithErrorHandling(appId)

  if (!validation.isValid) {
    console.warn('❌ AppID验证失败:', appId, validation.errorMessage)
    // 清除无效的AppID
    localStorage.removeItem('current-app-id')

    // 跳转到对应的错误页面
    router.replace({
      path: '/app-error',
      query: {
        type: validation.errorType,
        appId: appId,
        message: validation.errorMessage
      }
    })
    return
  }

  console.log('✅ AppID验证成功:', appId)

  console.log('🔄 AppID有效，继续登录页面初始化:', appId)

  // 检查是否已登录
  if (deviceStore.checkLoginStatus) {
    console.log('✅ 用户已登录，跳转到首页')

    // 跳转到重定向页面或首页
    const redirectUrl = route.query.redirect as string
    if (redirectUrl) {
      // 如果有重定向地址，检查是否是低代码应用
      const currentAppId = appId || localStorage.getItem('currentAppId')
      if (currentAppId && redirectUrl.includes('/app/')) {
        // 低代码应用路径，直接跳转
        router.replace(redirectUrl)
      } else {
        // 普通页面路径，跳转到对应页面
        router.replace(redirectUrl)
      }
    } else {
      // 没有重定向地址，跳转到首页
      if (appId) {
        // 如果有appId，跳转到低代码应用首页
        router.replace(`/app/${appId}`)
      } else {
        // 否则跳转到套餐列表页面
        router.replace('/device/PackageList')
      }
    }
    return
  }

  // 设置低代码应用锁定状态
  if (appId) {
    console.log('🔒 重新设置低代码应用锁定状态')
    localStorage.setItem('currentAppId', appId)
  }
})
</script>

<style scoped lang="scss">
.device-login-page {
  min-height: 100vh;
  background: #f7f8fa;
}
</style>
