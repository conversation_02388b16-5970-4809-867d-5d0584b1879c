<!-- ✅ 完全复制自 device-an UserLogin.vue + DeviceNum.vue -->
<template>
  <div class="UserLogin">
    <div class="UserLogin-box">
      <!-- ✅ 完全复制 UserLogin-box-svg 结构 -->
      <div class="UserLogin-box-svg">
        <div class="UserLogin-box-svg-icon">
          <SvgIcon name="wifi" />
        </div>
        <div class="UserLogin-box-svg-label">登录</div>
      </div>

      <!-- ✅ 完全复制 UserLogin-box-nav 结构 -->
      <div class="UserLogin-box-nav">
        <div
          class="UserLogin-box-nav-box"
          :class="{ 'UserLogin-box-nav-box-active': loginType === 'device' }"
          @click="loginType = 'device'"
        >
          设备卡登录
        </div>
        <!-- 手机号登录暂时隐藏，与Device-An保持一致 -->
      </div>

      <!-- ✅ 完全复制 DeviceNum.vue 的结构和样式 -->
      <div v-if="loginType === 'device'" class="DeviceNum">
        <div class="DeviceNum-ipt">
          <SvgIcon name="card" class="DeviceNum-ipt-icon" />
          <input
            type="text"
            class="DeviceNum-ipt-ipt"
            v-model="deviceNo"
            placeholder="请输入设备卡号"
            :disabled="loading"
          />
        </div>
        <div class="DeviceNum-tip">
          <SvgIcon name="tip" class="DeviceNum-tip-icon" />设备卡号通常因在设备背面或者包装盒上
        </div>

        <div
          class="DeviceNum-btn"
          @click="handleDeviceLogin"
          :class="{ disabled: loading || !deviceNo.trim() }"
        >
          {{ loading ? '登录中...' : '登录' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Toast } from 'vant'
import { useDeviceStore } from '@device/stores/device'
import { useWeChatStore } from '@device/stores/wechat'
// ✅ 使用公共的 SvgIcon 组件
import SvgIcon from '@/components/SvgIcon.vue'


// ==================== 响应式数据 ====================

const router = useRouter()
const route = useRoute()
const deviceStore = useDeviceStore()
const wechatStore = useWeChatStore()

const loginType = ref<'device' | 'phone'>('device')
const deviceNo = ref<string>('')
const loading = ref<boolean>(false)

// ==================== 生命周期 ====================

onMounted(async () => {
  // ✅ 完全复制 device-an 的登录页面初始化逻辑
  console.log('🔄 登录页面初始化...')

  // 获取系统配置和充值配置
  try {
    await wechatStore.FetchManageConfig()
    await wechatStore.FetchRechargeConfig()
    console.log('✅ 登录页面配置获取成功')
  } catch (error) {
    console.error('❌ 登录页面配置获取失败:', error)
  }

  // 清除加载状态
  deviceStore.loading = false

  // 开发环境下可以预填设备号
  if (import.meta.env.DEV) {
    // deviceNo.value = '863780070053924' // 测试设备号
  }
})

// ==================== 登录逻辑 ====================

// ✅ 完全复制 device-an 的简单登录逻辑
const handleDeviceLogin = async () => {
  // 防止重复点击
  if (loading.value) return

  if (!deviceNo.value.trim()) {
    Toast.fail('请输入设备卡号')
    return
  }

  loading.value = true

  try {
    console.log('🔐 开始设备登录:', deviceNo.value)

    // ✅ 完全复制 device-an 的登录参数
    const result = await deviceStore.login({
      deviceNo: deviceNo.value.trim(),
      groupId: 2 // ✅ 与Device-An完全一致，硬编码groupId为2
    })

    // ✅ 完全复制 device-an 的逻辑：成功就跳转，失败由拦截器处理
    if (result.success) {
      // 登录成功后跳转处理
      const redirectUrl = route.query.redirect as string
      const appId = route.query.appId as string || localStorage.getItem('current-app-id')

      if (redirectUrl) {
        // 如果有重定向地址，检查是否是低代码应用
        if (appId && appId !== 'home') {
          // 重新设置低代码应用锁定状态
          localStorage.setItem('isLowcodeApp', 'true')
          console.log('🔒 重新设置低代码应用锁定状态')
        }
        await router.push(redirectUrl)
      } else if (appId && appId !== 'home') {
        // 如果是低代码应用，重新设置锁定状态并跳转
        localStorage.setItem('isLowcodeApp', 'true')
        await router.push(`/app/${appId}`)
      } else if (appId === 'home') {
        // home应用跳转到静态H5页面
        await router.push('/app/home')
      } else {
        // 没有明确的appId，尝试使用存储的appId
        const storedAppId = localStorage.getItem('current-app-id')
        if (storedAppId) {
          localStorage.setItem('isLowcodeApp', 'true')
          await router.push(`/app/${storedAppId}`)
        } else {
          // 没有存储的appId，跳转到原生页面
          await router.push('/device/PackageList')
        }
      }
    }
    // 错误信息已经由响应拦截器自动显示，这里不需要额外处理
  } catch (error) {
    console.error('❌ 登录失败:', error)
    // 网络错误已经由响应拦截器处理，这里不需要额外显示Toast
  } finally {
    loading.value = false
  }
}


// ==================== 暴露给父组件 ====================

defineExpose({
  deviceNo,
  loading,
  handleDeviceLogin
})
</script>

<!-- ✅ 完全复制自 device-an 的样式 -->
<style lang="scss" scoped>
// ✅ 完全复制 UserLogin.vue 的样式
.UserLogin {
  box-sizing: border-box;
  position: relative;
  padding-top: 4rem;

  &-box {
    @include PaddingBox;

    &-svg {
      text-align: center;

      &-icon {
        height: 3rem;
        width: 3rem;
        margin: 0 auto;
        background: linear-gradient(135deg, rgba(86, 153, 247), rgba(46, 108, 237));
        color: #fff;
        border-radius: 0.8rem;
        font-size: 1.7rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      &-label {
        font-size: 1rem;
        font-weight: bold;
        margin-top: 0.5rem;
      }
    }

    &-nav {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: calc($padding * 1.5);

      &-box {
        margin: 0 0.6rem;
        font-size: 0.7rem;
        height: 1.4rem;
        color: #666;
        font-weight: bold;
        transition: color 0.15s linear;
      }

      &-box-active {
        color: $primary;
        box-sizing: border-box;
        border-bottom: 0.1rem solid $primary;
      }
    }
  }
}

// ✅ 完全复制 DeviceNum.vue 的样式
.DeviceNum {
  margin-top: calc($padding * 1);
  @include PaddingBox;

  &-ipt {
    display: flex;
    justify-content: start;
    align-items: center;
    background-color: $background;
    height: 2.6rem;
    border-radius: $radius;
    box-sizing: border-box;
    padding: 0 0.8rem;

    &-icon {
      margin-right: 0.4rem;
      color: #999;
      flex-shrink: 0;
      display: flex;
      align-items: center;
    }

    &-ipt {
      background-color: transparent !important;
      border: none;
      font-size: 0.8rem;
      flex: 1;
      outline: none;

      &:disabled {
        opacity: 0.6;
      }
    }
  }

  &-tip {
    font-size: 0.65rem;
    margin-top: 0.4rem;
    color: #666;
    display: flex;
    align-items: center;

    &-icon {
      font-size: 0.8rem;
      margin-right: 0.2rem;
      flex-shrink: 0;
    }
  }

  &-btn {
    margin-top: 1.5rem;
    background-color: $primary;
    text-align: center;
    line-height: 2.4rem;
    border-radius: $radius;
    color: #fff;
    font-size: 0.85rem;
    cursor: pointer;
    transition: opacity 0.3s ease;

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}
</style>
