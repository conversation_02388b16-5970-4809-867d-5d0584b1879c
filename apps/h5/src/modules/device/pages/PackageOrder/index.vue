<template>
  <div class="PackageOrder">
    <div class="PackageOrder-label" v-if="list.length">套餐订单</div>

    <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <PackageOrderBox v-for="item in list" :key="item.id" :data="item" :Gray="true" />
    </van-list>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useDeviceStore } from '@device/stores/device'
import type { PackageOrderHistoryData } from '@device/types/package'
import PackageOrderBox from '../PackageList/components/PackageOrderBox.vue'
import { createApiClient } from '@/utils/api'

const useDevice = useDeviceStore()
const apiClient = createApiClient()

const list = ref<PackageOrderHistoryData[]>([])
const loading = ref<boolean>(false)
const finished = ref<boolean>(false)

const GetParams = ref({
  page: 0,
  pageSize: 10
})

const onLoad = async () => {
  GetParams.value.page++
  loading.value = true
  
  try {
    const response = await apiClient.request({
      url: 'frontDevice/devicePackageOrder/getPackOrderList',
      method: 'get',
      params: Object.assign({}, { orderState: 2 }, GetParams.value)
    })

    if (response.code) {
      list.value = [...list.value, ...response.data.rows]
      finished.value = list.value.length >= response.data.total
    }
  } catch (error) {
    console.error('获取套餐订单失败:', error)
  } finally {
    loading.value = false
    useDevice.loading = false
  }
}

onMounted(() => {
  useDevice.loading = true
})
</script>

<style lang="scss" scoped>
.PackageOrder {
  @include PaddingBox;

  &-label {
    font-size: 0.6rem;
    color: #666;
  }
}
</style>