<template>
  <div class="wechat-payment">
    <div class="payment-container">
      <div class="payment-header">
        <h2>微信支付</h2>
        <p v-if="isProcessing">正在处理支付，请稍候...</p>
        <p v-else-if="error">{{ error }}</p>
        <p v-else>请在微信中完成支付</p>
      </div>
      
      <div class="payment-loading" v-if="isProcessing">
        <van-loading type="spinner" size="24px" color="#07c160">
          处理中...
        </van-loading>
      </div>
      
      <div class="payment-error" v-if="error">
        <van-button type="primary" @click="goBack">返回</van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useWeChatPayment } from '@device/composables/usePayment'
import { showFailToast, showSuccessToast } from 'vant'

const { processPayment, isProcessing } = useWeChatPayment()
const error = ref<string>('')

const goBack = () => {
  window.history.go(-2)
}

onMounted(async () => {
  try {
    const result = await processPayment()
    
    if (result.status === 1) {
      showSuccessToast(result.msg || '支付成功')
    } else {
      error.value = result.msg || '支付失败'
      showFailToast(error.value)
    }
  } catch (err: any) {
    error.value = err.message || '支付处理失败'
    showFailToast(error.value)
  }
})
</script>

<style lang="scss" scoped>
.wechat-payment {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.payment-container {
  background: white;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  max-width: 400px;
  width: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.payment-header {
  h2 {
    color: #07c160;
    margin-bottom: 16px;
    font-size: 24px;
  }
  
  p {
    color: #666;
    font-size: 16px;
    margin-bottom: 20px;
  }
}

.payment-loading {
  margin: 20px 0;
}

.payment-error {
  margin-top: 20px;
}
</style>