<template>
  <div class="BalanceDetails">
    <div class="BalanceDetails-MonthData">
      <div class="BalanceDetails-MonthData-box">
        <div class="BalanceDetails-MonthData-box-label">本月消费</div>

        <div class="BalanceDetails-MonthData-box-money" style="color: #ef4444">
          <span>￥</span>{{ MonthData.monthConsumption.toFixed(2) }}
        </div>
      </div>

      <div class="BalanceDetails-MonthData-box">
        <div class="BalanceDetails-MonthData-box-label">本月充值</div>

        <div class="BalanceDetails-MonthData-box-money" style="color: rgb(59, 130, 246)">
          <span>￥</span>{{ MonthData.monthRecharge.toFixed(2) }}
        </div>
      </div>
    </div>

    <div class="BalanceDetails-label">交易记录</div>

    <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <BalanceDetailsBox v-for="item in list" :key="item.id" :data="item" :Gray="true" />
    </van-list>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useDeviceStore } from '@device/stores/device'
import type { BalanceDetailsData, MonthBalanceData } from '@device/types/package'
import BalanceDetailsBox from './BalanceDetails/components/BalanceDetailsBox.vue'
import apiClient from '@/api/client'

const useDevice = useDeviceStore()

const MonthData = ref<MonthBalanceData>({
  monthRecharge: 0,
  monthConsumption: 0
})

const list = ref<BalanceDetailsData[]>([])

const loading = ref<boolean>(false)
const finished = ref<boolean>(false)

const GetParams = ref({
  page: 0,
  pageSize: 10
})

const onLoad = async () => {
  GetParams.value.page++
  loading.value = true

  try {
    const response = await apiClient.getBalanceDetails(GetParams.value)
    console.log('🔍 余额明细分页API响应:', response)

    if (!response.code) {
      // ✅ 显示获取余额明细失败的错误信息
      import('vant').then(({ showFailToast }) => {
        showFailToast(response.msg || '获取余额明细失败')
      })
      loading.value = false
      return
    }

    // 处理分页数据，支持多种数据结构
    const responseData = response.data as any
    let newItems: BalanceDetailsData[] = []
    let total = 0

    if (responseData?.rows && Array.isArray(responseData.rows)) {
      newItems = responseData.rows
      total = responseData.total || responseData.rows.length
    } else if (Array.isArray(responseData)) {
      newItems = responseData
      total = responseData.length
    } else {
      console.warn('⚠️ 余额明细分页数据格式异常:', responseData)
      newItems = []
      total = 0
    }

    list.value = [...list.value, ...newItems]
    loading.value = false
    finished.value = list.value.length >= total
    useDevice.loading = false

    console.log('✅ 处理后的余额明细列表:', list.value)
  } catch (error) {
    console.error('获取余额明细失败:', error)
    import('vant').then(({ showFailToast }) => {
      showFailToast('网络错误，请稍后重试')
    })
    loading.value = false
  }
}

// ✅ 完全复制device-an的初始化逻辑
const initialize = async () => {
  useDevice.loading = true

  try {
    // 先获取月度统计数据
    const monthResponse = await apiClient.getMonthBalanceData()
    console.log('🔍 月度余额数据API响应:', monthResponse)

    if (!monthResponse.code) {
      import('vant').then(({ showFailToast }) => {
        showFailToast(monthResponse.msg || '获取月度统计失败')
      })
      useDevice.loading = false
      return
    }

    // 处理月度数据
    const monthData = monthResponse.data as any
    MonthData.value = {
      monthRecharge: monthData?.monthRecharge || 0,
      monthConsumption: monthData?.monthConsumption || 0
    }

    console.log('✅ 处理后的月度数据:', MonthData.value)

    // 然后加载明细列表
    await onLoad()
  } catch (error) {
    console.error('初始化余额明细页面失败:', error)
    import('vant').then(({ showFailToast }) => {
      showFailToast('初始化失败，请稍后重试')
    })
    useDevice.loading = false
  }
}

onMounted(() => {
  initialize()
})
</script>

<style lang="scss" scoped>
.BalanceDetails {
  @include PaddingBox;

  &-MonthData {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: calc($padding * 1);

    &-box {
      background-color: $background;
      border-radius: $radius;
      @include PaddingBox;

      &-label {
        font-size: 0.6rem;
        color: #666;
      }

      &-money {
        font-size: 1.2rem;
        font-weight: bold;
        margin-top: 0.3rem;

        span {
          font-size: 0.6rem;
        }
      }
    }
  }

  &-label {
    margin-top: calc($padding * 1.5);
    font-size: 0.6rem;
    color: #666;
  }
}
</style>
