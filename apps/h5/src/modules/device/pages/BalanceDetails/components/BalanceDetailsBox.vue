<template>
  <div
    class="BalanceDetailsBox"
    :class="{
      ['BalanceDetailsBox-' + (props.data.unit === '-' ? 'minus' : 'add')]: true,
      'BalanceDetailsBox-gray': props.Gray
    }"
  >
    <div class="BalanceDetailsBox-icon">
      <SvgIcon :name="props.data.unit === '-' ? 'minus' : 'add'" />
    </div>

    <div class="BalanceDetailsBox-details">
      <div class="BalanceDetailsBox-details-top">
        <div class="BalanceDetailsBox-details-top-label">
          {{ props.data?.balanceRemark }}
        </div>

        <div class="BalanceDetailsBox-details-top-money">
          {{ props.data.unit + props.data.balanceAmount }}
        </div>
      </div>

      <div class="BalanceDetailsBox-details-bottom">
        <div class="BalanceDetailsBox-details-bottom-time">
          <SvgIcon name="time" class="BalanceDetailsBox-details-bottom-time-icon" />
          {{ toTime(props.data?.balanceAlterationTime) }}
        </div>

        <div class="BalanceDetailsBox-details-bottom-that">
          当前余额: {{ props.data.balanceAfter }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { type PropType } from 'vue'
import type { BalanceDetailsData } from '@device/types/package'
import SvgIcon from '@/components/SvgIcon.vue'
import { toTime } from '@/utils'

const props = defineProps({
  data: {
    type: Object as PropType<BalanceDetailsData>,
    required: true
  },
  Gray: {
    type: Boolean,
    default: false,
    required: false
  }
})
</script>

<style lang="scss" scoped>
.BalanceDetailsBox-gray {
  background-color: $background;
}

.BalanceDetailsBox {
  display: flex;
  justify-content: start;
  align-items: center;
  margin-top: calc($padding / 1.2);
  box-shadow: $shadow;
  @include PaddingBox;
  border-radius: $radius;

  &-icon {
    width: 1.8rem;
    height: 1.8rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    font-size: 0.7rem;
  }

  &-details {
    width: calc(100% - 1.8rem);
    box-sizing: border-box;
    padding-left: 0.5rem;

    &-top,
    &-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: nowrap;
    }

    &-top {
      &-label {
        font-size: 0.7rem;
        overflow: hidden;
        text-overflow: ellipsis;
        width: calc(100% - 3rem);
      }

      &-money {
        font-size: 0.8rem;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 3rem;
        text-align: right;
      }
    }

    &-bottom {
      margin-top: 0.2rem;

      &-time {
        font-size: 0.6rem;
        color: $wait;

        &-icon {
          position: relative;
          top: -0.05rem;
          margin-right: 0.1rem;
        }
      }

      &-that {
        font-size: 0.6rem;
        color: $wait;
      }
    }
  }
}

.BalanceDetailsBox-minus {
  .BalanceDetailsBox-icon {
    background-color: rgba($color: $error, $alpha: 0.1);
    color: $error;
  }

  .BalanceDetailsBox-details-top-money {
    color: $error;
  }
}

.BalanceDetailsBox-add {
  .BalanceDetailsBox-icon {
    background-color: rgba($color: $primary, $alpha: 0.1);
    color: $primary;
  }

  .BalanceDetailsBox-details-top-money {
    color: $primary;
  }
}
</style>