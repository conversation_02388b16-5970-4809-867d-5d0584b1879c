<!-- ✅ 完全复制device-an的RealNameCards.vue -->
<template>
  <div class="RealNameCards">
    <div class="RealNameCards-box">
      <div class="RealNameCards-box-label">实名卡片</div>

      <div
        class="RealNameCards-box-item"
        v-for="item in useDevice.realNameCards"
        :key="item.iccid"
        :class="
          item.cardName === 2 || item.cardName === 4
            ? 'RealNameCards-box-item-success'
            : 'RealNameCards-box-item-warning'
        "
      >
        <div class="RealNameCards-box-item-box">
          <div class="RealNameCards-box-item-box-left">
            <div class="RealNameCards-box-item-box-left-svg">
              <Icon icon="mdi:phone" />
            </div>

            <div class="RealNameCards-box-item-box-left-txt">
              <div class="RealNameCards-box-item-box-left-txt-label">
                {{ '卡' + item.number + '(' + DeviceNetWork[item.network]?.label + ')' }}
              </div>

              <div class="RealNameCards-box-item-box-left-txt-num">
                {{ item.iccid }}
              </div>
            </div>
          </div>

          <div class="RealNameCards-box-item-box-realNameStatus">
            <Icon
              :icon="item.cardName === 2 || item.cardName === 4 ? 'mdi:check-circle' : 'mdi:alert-circle'"
              class="RealNameCards-box-item-box-realNameStatus-svg"
            />
            <div class="RealNameCards-box-item-box-realNameStatus-txt">
              {{ RealNnameStatus[item.cardName - 1]?.label || '未知' }}
            </div>
          </div>
        </div>

        <div class="RealNameCards-box-item-btn" v-if="item.cardName !== 2 && item.cardName !== 4">
          <Icon icon="mdi:share" class="RealNameCards-box-item-btn-svg" />
          <div
            class="RealNameCards-box-item-btn-txt"
            @click="toRealName(item.number, item.network)"
          >
            去实名认证
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDeviceStore } from '@device/stores/device'
import { useRoute, useRouter } from 'vue-router'
import { DeviceNetWork, RealNnameStatus } from '@/utils/options'
import { Icon } from '@iconify/vue'

const useDevice = useDeviceStore()
const route = useRoute()
const router = useRouter()

// ✅ 完全复制device-an的实名认证跳转逻辑
const toRealName = (number: number, network: number) => {
  router.push({
    path: '/RealName',
    query: {
      number: number,
      network: network
    }
  })
}

// ✅ 完全复制device-an的初始化逻辑 - 在script setup顶层直接执行
if (route.query.deviceNo) {
  useDevice.loading = true

  // 先登录设备
  useDevice.login({
    deviceNo: route.query.deviceNo as string
  }).then(async (result) => {
    if (result.success) {
      // 登录成功后获取实名卡片
      try {
        await useDevice.fetchRealNameCards()
      } catch (error) {
        console.error('获取实名卡片失败:', error)
      } finally {
        useDevice.loading = false
      }
    } else {
      useDevice.loading = false
    }
  }).catch((error) => {
    console.error('设备登录失败:', error)
    useDevice.loading = false
  })
}
</script>

<style lang="scss" scoped>
@import '../../../../styles/variables.scss';

/* ✅ 完全复制device-an的RealNameCards样式 */
.RealNameCards {
  @include PageBox;

  &-box {
    @include WhiteBox;
    @include PaddingBox;

    &-label {
      font-size: 0.7rem;
      color: #666;
      margin-bottom: 0.4rem;
    }

    &-item {
      @include PaddingBox;
      margin-top: calc($padding / 1.2);
      border-radius: $radius;

      &-box {
        display: flex;
        justify-content: space-between;

        &-left {
          display: flex;
          justify-content: start;
          align-items: center;

          &-svg {
            width: 1.8rem;
            height: 1.8rem;
            margin-right: 0.4rem;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
          }

          &-txt {
            &-label {
              font-size: 0.8rem;
            }

            &-num {
              font-size: 0.65rem;
              color: #666;
              margin-top: 0.2rem;
            }
          }
        }

        &-realNameStatus {
          display: flex;
          justify-content: start;
          align-items: center;
          font-size: 0.7rem;

          &-svg {
            margin-right: 0.3rem;
          }
        }
      }

      &-btn {
        width: 100%;
        height: 2rem;
        margin-top: $padding;
        background-color: $primary;
        text-align: center;
        border-radius: 0.4rem;
        color: #fff;
        font-size: 0.7rem;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &-svg {
          margin-right: 0.3rem;
        }
      }
    }
  }
}

.RealNameCards-box-item-warning {
  background-color: rgb(255, 251, 237);

  .RealNameCards-box-item-box-left-svg {
    background-color: rgb(255, 244, 204);
    color: rgb(233, 162, 59);
  }

  .RealNameCards-box-item-box-realNameStatus {
    color: rgb(233, 162, 59);
  }
}

.RealNameCards-box-item-success {
  background-color: rgba($color: $success, $alpha: 0.1);

  .RealNameCards-box-item-box-left-svg {
    background-color: rgba($color: $success, $alpha: 0.1);
    color: $success;
  }

  .RealNameCards-box-item-box-realNameStatus {
    color: $success;
  }
}
</style>
