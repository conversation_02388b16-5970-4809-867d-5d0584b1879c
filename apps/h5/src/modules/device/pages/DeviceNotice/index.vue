<template>
  <div class="DeviceNotice">
    <div class="DeviceNotice-label">
      {{ useMask.Notice.title }}
    </div>
    <div class="DeviceNotice-box">
      {{ useMask.Notice.content }}
      <div class="DeviceNotice-box-time">
        {{ toTime(useMask.Notice.time, 'YYYY年MM月DD日 HH:mm:ss') }}
      </div>
    </div>

    <div class="DeviceNotice-bottom">
      <div
        class="DeviceNotice-bottom-left"
        @click="
          () => {
            if (useMask.NoticeTime === useMask.Notice.time) {
              useMask.NoticeTime = ''
            } else {
              useMask.NoticeTime = useMask.Notice.time
            }
          }
        "
      >
        <div
          class="DeviceNotice-bottom-left-radio"
          :class="{
            'DeviceNotice-bottom-left-radio-active': useMask.NoticeTime === useMask.Notice.time
          }"
        />
        不再弹出
      </div>

      <div class="DeviceNotice-bottom-btn" @click="useMask.show = false">我已知晓</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useMaskStore } from '@device/stores/mask'
import { toTime } from '@/utils'
const useMask = useMaskStore()
</script>

<style lang="scss" scoped>
.DeviceNotice {
  width: 100%;
  @include WhiteBox;
  @include PaddingBox;

  &-label {
    text-align: center;
  }

  &-box {
    @include PaddingBox;
    background-color: $background;
    font-size: 0.8rem;
    margin: $padding 0;
    max-height: 60vh;
    overflow-y: scroll;
    white-space: normal;
    word-wrap: break-word;

    &-time {
      text-align: right;
      margin-top: 1rem;
      font-size: 0.7rem;
      color: #666;
    }
  }

  &-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-left {
      display: flex;
      justify-content: start;
      align-items: center;
      font-size: 0.7rem;

      &-radio {
        width: 0.7rem;
        height: 0.7rem;
        border-radius: 50%;
        box-sizing: border-box;
        border: 0.01rem solid #999;
        margin-right: 0.3rem;
        transition: linear 0.15s;
        position: relative;
        top: 0.05rem;
      }

      &-radio-active {
        border: 0.2rem solid $primary;
      }
    }

    &-btn {
      height: 1.4rem;
      background-color: $primary;
      color: #fff;
      font-size: 0.7rem;
      line-height: 1.4rem;
      padding: 0 $padding;
      border-radius: 1rem;
    }
  }
}
</style>