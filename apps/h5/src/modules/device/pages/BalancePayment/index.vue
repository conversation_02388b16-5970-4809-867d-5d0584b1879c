<template>
  <div class="BalancePayment">
    <dxPayment
      v-if="PaymentList.length"
      :PaymentList="PaymentList"
      :Money="orderData.prestorePrice"
      :GetOrderNum="GetOrderNum"
      @BalancePayment="BalancePayment"
    >
      <div class="BalancePayment-details">
        <div class="BalancePayment-details-box" v-for="(item, i) in ViewDetails" :key="i">
          <div class="BalancePayment-details-box-label">
            {{ item.label }}
          </div>

          <div class="BalancePayment-details-box-txt">
            {{ item.txt() }}
          </div>
        </div>
      </div>
    </dxPayment>

    <van-popup v-model:show="show" round position="bottom">
      <component
        :is="PaymentComp"
        @PasswordPaymentSubmit="PasswordPaymentSubmit"
        @PhonePaymentSubmit="PhonePaymentSubmit"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import dxPayment from '@device/components/dxPayment.vue'
import { useRoute } from 'vue-router'
const route = useRoute()

import { useDeviceStore } from '@device/stores/device'
import { useWeChatStore } from '@device/stores/wechat'
import { useRouter } from 'vue-router'
const useDevice = useDeviceStore()
const useWeChat = useWeChatStore()
const router = useRouter()

import { showLoadingToast } from 'vant'
import apiClient from '@/api/client'
import { ref, onMounted, shallowRef, type Component } from 'vue'

const PaymentList = ref<any[]>([])

// ✅ 完全复制device-an的订单数据结构
interface BalanceOrderData {
  id: number
  orderName: string
  systemOrdernumber: string
  prestorePrice: number
  prestoreGive: number
  creationTime: Date
}

// ✅ 完全复制device-an的详情视图数据结构
interface ViewDetailsData {
  label: string
  txt: () => string
  visible?: () => boolean
}

const orderData = ref<BalanceOrderData>({} as BalanceOrderData)
const ViewDetails = ref<ViewDetailsData[]>([])
import { toTime } from '@/utils'

// ✅ 完全复制device-an的初始化逻辑
const initialize = async () => {
  useDevice.loading = true
  
  try {
    // 创建余额订单
    const orderResponse = await apiClient.createBalanceOrder(Number(route.query.id as unknown as number))
    if (!orderResponse.code) {
      import('vant').then(({ showFailToast }) => {
        showFailToast(orderResponse.msg || '创建余额订单失败')
      })
      useDevice.loading = false
      return
    }
    
    orderData.value = orderResponse.data
    
    // ✅ 完全复制device-an的视图详情配置
    ViewDetails.value = [
      {
        label: '订单名称',
        txt: () => orderData.value.orderName
      },
      {
        label: '充值金额',
        txt: () => '￥' + orderData.value.prestorePrice.toFixed(2)
      },
      {
        label: '订单编号',
        txt: () => orderData.value.systemOrdernumber
      },
      {
        label: '赠送金额',
        txt: () => '￥' + orderData.value.prestoreGive.toFixed(2),
        visible: () => !!orderData.value.prestoreGive
      },
      {
        label: '下单时间',
        txt: () => toTime(orderData.value.creationTime) || ''
      }
    ]

    console.log(useDevice.details,'说呢很难过1')
    
    // 获取支付方式
    // const paymentResponse = await apiClient.getPaymentMethods({ type: 9, userId: useDevice.details.userId })
    const paymentResponse = await apiClient.getPaymentMethods({ type: 9, userId: orderData.value.userId })
    if (!paymentResponse.code) {
      import('vant').then(({ showFailToast }) => {
        showFailToast(paymentResponse.msg || '获取支付方式失败')
      })
      useDevice.loading = false
      return
    }
    
    PaymentList.value = paymentResponse.data
    useDevice.loading = false
  } catch (error) {
    console.error('余额支付页面初始化失败:', error)
    import('vant').then(({ showFailToast }) => {
      showFailToast('初始化失败，请稍后重试')
    })
    useDevice.loading = false
  }
}

// ✅ 完全复制device-an的订单号获取逻辑
const GetOrderNum = async () => {
  return {
    orderNo: orderData.value.systemOrdernumber,
    orderType: 9
  }
}

// ✅ 完全复制device-an的余额支付处理
const BalancePayment = async (orderNo: string) => {
  const toast = showLoadingToast({
    message: '请稍等...',
    forbidClick: true,
    duration: 0
  })
  
  // ✅ 根据充值配置决定支付验证方式 - 默认使用支付密码
  switch (useWeChat.RechargeConfig?.balancePayVerification || 2) {
    case 1:
      // 无需验证直接支付
      try {
        const response = await apiClient.balancePaymentOrder({ orderNo })
        toast.close()
        if (!response.code) {
          import('vant').then(({ showFailToast }) => {
            showFailToast(response.msg || '支付失败')
          })
          return
        }
        import('vant').then(({ showSuccessToast }) => {
          showSuccessToast(response.msg || '支付成功')
        })
        router.push('/home')
      } catch (error) {
        toast.close()
        import('vant').then(({ showFailToast }) => {
          showFailToast('支付失败，请稍后重试')
        })
      }
      break
    case 2:
      // 支付密码支付
      toast.close()
      ThatOrderNo.value = orderNo
      PaymentComp.value = PasswordPayment
      show.value = true
      break
    case 3:
      // 短信验证码支付
      toast.close()
      ThatOrderNo.value = orderNo
      PaymentComp.value = PhonePayment
      show.value = true
      break
    default:
      // 默认使用支付密码
      toast.close()
      ThatOrderNo.value = orderNo
      PaymentComp.value = PasswordPayment
      show.value = true
  }
}

// ✅ 支付组件导入和设置
import PasswordPayment from '@device/components/PasswordPayment.vue'
import PhonePayment from '@device/components/PhonePayment.vue'
const PaymentComp = shallowRef<Component>(PasswordPayment)
const show = ref(false)
const ThatOrderNo = ref<string>('')

// ✅ 完全复制device-an的密码支付处理
const PasswordPaymentSubmit = async (pwd: string) => {
  try {
    const response = await apiClient.balancePaymentOrder({
      orderNo: ThatOrderNo.value,
      payPwd: pwd
    })
    
    if (!response.code) {
      import('vant').then(({ showFailToast }) => {
        showFailToast(response.msg || '支付失败')
      })
      return
    }
    
    import('vant').then(({ showSuccessToast }) => {
      showSuccessToast(response.msg || '支付成功')
    })
    show.value = false
    router.push('/home')
  } catch (error) {
    import('vant').then(({ showFailToast }) => {
      showFailToast('支付失败，请稍后重试')
    })
  }
}

// ✅ 完全复制device-an的短信验证码支付处理
const PhonePaymentSubmit = async (code: string) => {
  try {
    const response = await apiClient.balancePaymentOrder({
      orderNo: ThatOrderNo.value,
      code
    })
    
    if (!response.code) {
      import('vant').then(({ showFailToast }) => {
        showFailToast(response.msg || '支付失败')
      })
      return
    }
    
    import('vant').then(({ showSuccessToast }) => {
      showSuccessToast(response.msg || '支付成功')
    })
    show.value = false
    router.push('/home')
  } catch (error) {
    import('vant').then(({ showFailToast }) => {
      showFailToast('支付失败，请稍后重试')
    })
  }
}

onMounted(() => {
  initialize()
})
</script>

<style lang="scss" scoped>
.BalancePayment-details {
  margin-top: calc($padding / 2);
  &-box {
    padding: calc($padding / 2) 0;
    display: flex;
    justify-content: space-between;
    font-size: 0.7rem;

    &-label {
      color: #666;
      width: calc(0.7rem * 5);
    }

    &-txt {
      width: calc(100% - 0.7rem * 6);
      text-align: right;
      white-space: normal;
      word-wrap: break-word;
    }
  }
}
</style>
