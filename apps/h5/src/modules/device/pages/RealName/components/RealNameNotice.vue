<template>
  <div class="real-name-notice">
    <div class="notice-icon">
      <Icon icon="mdi:alert" />
    </div>
    <div class="notice-content">
      <div class="notice-message">{{ message }}</div>
      <div class="notice-button" @click="handleClick">
        {{ buttonText }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'

const props = withDefaults(defineProps<{
  message?: string
  buttonText?: string
}>(), {
  message: '检测到您的设备卡尚未完成实名认证，请尽快完成认证以确保正常使用',
  buttonText: '去实名'
})

const emit = defineEmits<{
  click: []
}>()

const handleClick = () => {
  emit('click')
}
</script>

<style scoped lang="scss">
.real-name-notice {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #ff9500, #ff6b00);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  color: white;
}

.notice-icon {
  font-size: 24px;
  margin-right: 12px;
  flex-shrink: 0;
}

.notice-content {
  flex: 1;
}

.notice-message {
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.notice-button {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 6px 16px;
  font-size: 12px;
  display: inline-block;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }
}
</style>
