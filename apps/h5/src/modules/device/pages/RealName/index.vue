<!-- ✅ 完全复制device-an的RealName.vue -->
<template>
  <div class="RealName">
    <div class="RealName-box">
      <div class="RealName-box-label">查询设备卡实名状态</div>

      <div class="RealName-box-device">
        <Icon icon="mdi:sim-card" class="RealName-box-device-svg" />

        <div class="RealName-box-device-txt">
          <div class="RealName-box-device-txt-label">设备号</div>

          <div class="RealName-box-device-txt-txt">
            {{ useDevice.details.deviceNo }}
          </div>
        </div>
      </div>
      <div class="RealName-box-tip">设备号通常印在设备背面或包装盒上</div>

      <div class="RealName-box-label">查询结果</div>

      <div class="RealName-box-warning">
        <div class="RealName-box-warning-label">
          <Icon class="RealName-box-warning-label-svg" icon="mdi:alert-circle" />
          <div class="RealName-box-warning-label-txt">未完成实名认证</div>
        </div>

        <div class="RealName-box-warning-box">
          <div class="RealName-box-warning-box-label">设备卡号</div>

          <div class="RealName-box-warning-txt">
            {{ CardData.iccid }}
          </div>
        </div>

        <div class="RealName-box-warning-box">
          <div class="RealName-box-warning-box-label">运营商</div>

          <div class="RealName-box-warning-txt">
            {{ DeviceNetWork[Number(route.query.network as string)]?.label }}
          </div>
        </div>
      </div>

      <div class="RealName-box-label">实名认证信息</div>
      <div class="RealName-box-title">ICCID</div>
      <div class="RealName-box-ipt">
        <div class="RealName-box-ipt-txt">
          {{ CardData.iccid }}
        </div>

        <div class="RealName-box-ipt-svg" @click="CopyTxt(CardData.iccid)">
          <Icon icon="mdi:content-copy" />
        </div>
      </div>

      <div class="RealName-box-title">MSISDN</div>
      <div class="RealName-box-ipt">
        <div class="RealName-box-ipt-txt">
          {{ CardData.msisdn }}
        </div>

        <div class="RealName-box-ipt-svg" @click="CopyTxt(CardData.msisdn)">
          <Icon icon="mdi:content-copy" />
        </div>
      </div>

      <div class="RealName-box-tipBox">
        <div class="RealName-box-tipBox-svg">
          <Icon icon="mdi:lightbulb" />
        </div>

        <div class="RealName-box-tipBox-txt">
          根据工信部规定，所有移动通信设备必须进行实名认证才能正常使用。
        </div>
      </div>

      <div class="RealName-box-btn" v-if="link" @click="toUel">
        <Icon icon="mdi:share" class="RealName-box-btn-svg" />
        <div class="RealName-box-btn-txt">开始实名认证</div>
      </div>

      <div class="RealName-box-scan" v-else-if="urlLink">
        <div class="RealName-box-scan-label">请长按识别二维码</div>

        <img :src="urlLink" class="RealName-box-scan-img" />
      </div>

      <!-- ✅ 微信小程序组件（联通小程序） -->
      <div v-if="showWxComponent" class="RealName-box-wx">
        <!-- 在H5环境中，微信小程序组件可能不可用，显示备用按钮 -->
        <div class="RealName-box-btn" @click="handleWxLaunch">
          <Icon icon="mdi:wechat" class="RealName-box-btn-svg" />
          <div class="RealName-box-btn-txt">开始实名认证</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { useDeviceStore } from '@device/stores/device'
import { DeviceNetWork } from '@/utils/options'
import { Icon } from '@iconify/vue'
import { apiClient } from '@/api/client'
import { CopyTxt } from '@/utils'

const route = useRoute()
const useDevice = useDeviceStore()

// ✅ 完全复制device-an的卡片数据结构
const CardData = ref<any>({})
const link = ref('')
const urlLink = ref('')
const showWxComponent = ref(false)
const wxConfig = ref<any>({})

// ✅ 完全复制device-an的打开实名认证链接
const toUel = () => {
  window.location.href = link.value
}

// ✅ 处理微信小程序启动
const handleWxLaunch = () => {
  // 在H5环境中，可以尝试跳转到微信小程序页面或显示提示
  console.log('启动微信小程序:', wxConfig.value)
  alert('请在微信中打开此页面以使用小程序功能')
}

// ✅ 完全复制device-an的联通微信配置
const LTWeChatConfig = (data: any) => {
  wxConfig.value = data
  showWxComponent.value = true
  // 在device-an中这里会配置微信SDK，H5环境中暂时跳过
}



// ✅ 获取设备卡实名认证地址 (完全复制device-an的API)
const getDeviceCardRealNameAddress = async (params: { deviceId: number; number: number }) => {
  try {
    const response = await apiClient.getDeviceCardRealNameAddress(params)
    return response
  } catch (error) {
    console.error('获取设备卡实名认证地址失败:', error)
    throw error
  }
}
// ✅ 完全复制device-an的初始化逻辑 - 在script setup顶层直接执行
useDevice.loading = true

getDeviceCardRealNameAddress({
  deviceId: useDevice.details.id,
  number: Number(route.query.number as string)
}).then((response) => {
  console.log('📡 API响应结构:', response)

  // ✅ 修复：根据实际API响应结构设置CardData
  if (response.data?.card) {
    CardData.value = response.data.card
  }

  // ✅ 修复：根据实际API响应结构访问code
  const responseCode = response.code

  switch (responseCode) {
    case 1:
      // 系统内置
      console.log('🔍 系统内置认证，网络类型:', route.query.network)
      switch (route.query.network as string) {
        case '1':
          // 电信
          link.value = 'https://cmp-miniapps.ctwing.cn/#/pages/ordinaryuserRealName/index'
          console.log('✅ 设置电信认证链接:', link.value)
          break
        case '2':
          // 联通
          urlLink.value = '/images/appletLT.jpg'
          console.log('✅ 设置联通二维码:', urlLink.value)
          break
        case '3':
          // 移动
          urlLink.value = '/images/appletYD.png'
          console.log('✅ 设置移动二维码:', urlLink.value)
          break
        case '4':
          // 广电
          link.value = 'https://m.10099.com.cn/h5wap/busiClient/activateIOTCard/authenticationIccId.html'
          console.log('✅ 设置广电认证链接:', link.value)
          break
        default:
          console.log('❌ 未知网络类型:', route.query.network)
      }
      break
    case 2:
      // 跳转链接
      if (response.data?.url) {
        window.location.href = response.data.url
      }
      break
    case 3:
      // 联通小程序
      if (response.data) {
        LTWeChatConfig(response.data)
      }
      break
  }

  useDevice.loading = false
}).catch((error) => {
  console.error('获取实名认证地址失败:', error)
  useDevice.loading = false
})
</script>

<style lang="scss" scoped>
@import '../../../../styles/variables.scss';

/* ✅ 完全复制device-an的RealName样式 */
.RealName {
  @include PageBox;
  @include PaddingBox;

  &-box {
    @include PaddingBox;
    @include WhiteBox;
    padding-top: 0.2rem;

    &-label {
      font-size: 0.8rem;
      margin-bottom: 0.4rem;
      font-weight: bold;
      margin-top: 0.6rem;
    }

    &-title {
      font-size: 0.6rem;
      color: #666;
      margin-bottom: 0.3rem;
      margin-top: 0.6rem;
    }

    &-device {
      display: flex;
      justify-content: start;
      align-items: center;
      background-color: $background;
      box-sizing: border-box;
      padding: calc($padding / 1.2);
      border-radius: $radius;

      &-svg {
        margin-right: 0.6rem;
        color: $primary;
      }

      &-txt {
        &-label {
          font-size: 0.75rem;
        }

        &-txt {
          font-size: 0.7rem;
          color: #666;
          margin-top: 0.2rem;
          letter-spacing: 0.05rem;
        }
      }
    }

    &-tip {
      font-size: 0.6rem;
      color: #666;
      margin-bottom: 0.4rem;
      margin-top: 0.2rem;
    }

    &-warning {
      @include PaddingBox;
      background-color: rgb(255, 251, 235);
      border-radius: $radius;

      &-label {
        display: flex;
        justify-content: start;
        align-items: center;
        font-size: 0.7rem;
        font-weight: bold;
        color: rgb(233, 162, 59);

        &-svg {
          margin-right: 0.3rem;
        }
      }

      &-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        font-size: 0.6rem;
        margin-top: calc($padding / 1.5);

        &-label {
          color: #666;
        }
      }
    }

    &-ipt {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: $background;
      box-sizing: border-box;
      padding: calc($padding / 1.2);
      border-radius: $radius;

      &-txt {
        font-size: 0.75rem;
      }

      &-svg {
        width: 1.4rem;
        height: 1.4rem;
        background-color: rgb(230, 230, 230);
        border-radius: 0.3rem;
        font-size: 0.7rem;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #999;
      }
    }

    &-tipBox {
      background-color: rgba($color: $primary, $alpha: 0.1);
      border-radius: $radius;
      @include PaddingBox;
      margin-top: $padding;
      display: flex;
      justify-content: start;
      // align-items: center;

      &-svg {
        margin-right: 0.4rem;
        color: $primary;
        font-size: 1.3rem;
      }

      &-txt {
        font-size: 0.65rem;
      }
    }

    &-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 44px;
      background-color: $primary;
      color: #fff;
      margin-top: 20px;
      font-size: 0.7rem;
      border-radius: 10px;

      &-svg {
        margin-right: 0.4rem;
        position: relative;
        top: 0.05rem;
      }
    }

    &-scan {
      margin-top: calc($padding * 2);

      &-label {
        font-size: 0.7rem;
        // color: $primary;
        text-align: center;
      }

      &-img {
        display: block;
        width: 100px;
        height: 100px;
        margin: 0 auto;
        margin-top: calc($padding * 1.5);
      }
    }

    &-wx {
      margin-top: 20px;
    }
  }
}
</style>
