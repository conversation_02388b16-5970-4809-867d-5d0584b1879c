<template>
  <div class="BalanceList">
    <div class="BalanceList-box">
      <div class="BalanceList-box-head">
        <div class="BalanceList-box-head-label">
          <SvgIcon name="moeny" class="BalanceList-box-head-label-icon" />当前账户余额
        </div>

        <div class="BalanceList-box-head-moeny">
          <span>￥</span>{{ useDevice.details.balance?.toFixed(2) || '0.00' }}
        </div>

        <div class="BalanceList-box-head-tip">
          <SvgIcon
            name="tip"
            class="BalanceList-box-head-tip-icon"
          />充值后可用于购买套餐或支付流量费用
        </div>
      </div>

      <div class="BalanceList-box-tip">
        <div class="BalanceList-box-tip-box">
          <SvgIcon name="gift" class="BalanceList-box-tip-box-icon" />部分充值金额可以赠送额外金额
        </div>
      </div>

      <div class="BalanceList-box-list">
        <div class="BalanceList-box-list-label">选择充值金额</div>

        <div class="BalanceList-box-list-box">
          <div
            class="BalanceList-box-list-box-item"
            v-for="item in list"
            :key="item.id"
            @click="id = item.id"
            :class="{ 'BalanceList-box-list-box-item-active': id === item.id }"
          >
            <div class="BalanceList-box-list-box-item-price">
              <span>￥</span>{{ item.prestorePrice }}
            </div>

            <div class="BalanceList-box-list-box-item-give" v-if="item.prestoreGive">
              +赠{{ item.prestoreGive }}元
            </div>
          </div>
        </div>
      </div>

      <button class="BalanceList-box-btn" :disabled="!id" @click="Payment">
        立即充值<span v-if="id">￥{{ selectedValue?.prestorePrice || 0 }}</span>
        <span v-if="selectedValue?.prestoreGive">(赠送￥{{ selectedValue?.prestoreGive }})</span>
      </button>

      <div class="BalanceList-box-details" v-if="detailsList.length">
        <div class="BalanceList-box-details-head">
          <div class="BalanceList-box-details-head-label">近期交易</div>

          <div class="BalanceList-box-details-head-view" @click="router.push('/BalanceDetails')">
            查看全部<SvgIcon name="right" class="BalanceList-box-details-head-view-icon" />
          </div>
        </div>

        <BalanceDetailsBox v-for="item in detailsList" :key="item.id" :data="item" />

        <div class="BalanceList-box-details-vieAll" @click="router.push('/BalanceDetails')">
          查看更多交易记录<SvgIcon name="to" class="BalanceList-box-details-vieAll-icon" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useDeviceStore } from '@device/stores/device'
const useDevice = useDeviceStore()

import { useRouter } from 'vue-router'
const router = useRouter()

import SvgIcon from '@/components/SvgIcon.vue'
import BalanceDetailsBox from '../BalanceDetails/components/BalanceDetailsBox.vue'
import apiClient from '@/api/client'
import type { BalanceDetailsData } from '@device/types/package'

// ✅ 完全复制device-an的余额数据类型
interface BalanceData {
  id: number
  prestorePrice: number
  prestoreGive: number
  orderName: string
}

const id = ref<number>()

// ✅ 完全复制device-an的选中值计算 - 添加安全检查
const selectedValue = computed((): BalanceData | undefined => {
  if (!Array.isArray(list.value) || !id.value) {
    return undefined
  }
  return list.value.find((item) => item.id === id.value)
})

const list = ref<BalanceData[]>([])
const detailsList = ref<BalanceDetailsData[]>([])

// ✅ 完全复制device-an的初始化逻辑
const initialize = async () => {
  useDevice.loading = true

  try {
    // 获取余额列表
    const balanceResponse = await apiClient.getBalanceList()
    console.log('🔍 余额列表API响应:', balanceResponse)

    if (!balanceResponse.code) {
      import('vant').then(({ showFailToast }) => {
        showFailToast(balanceResponse.msg || '获取余额列表失败')
      })
      useDevice.loading = false
      return
    }

    // ✅ 修复：确保返回的是数组格式，支持多种数据结构
    const responseData = balanceResponse.data as any
    if (Array.isArray(responseData)) {
      list.value = responseData
    } else if (responseData?.list && Array.isArray(responseData.list)) {
      list.value = responseData.list
    } else if (responseData?.data && Array.isArray(responseData.data)) {
      list.value = responseData.data
    } else {
      console.warn('⚠️ 余额列表数据格式异常:', responseData)
      list.value = []
    }

    console.log('✅ 处理后的余额列表:', list.value)

    // 获取近期交易记录
    const detailsResponse = await apiClient.getBalanceDetails({ page: 1, pageSize: 2 })
    console.log('🔍 余额明细API响应:', detailsResponse)

    if (!detailsResponse.code) {
      import('vant').then(({ showFailToast }) => {
        showFailToast(detailsResponse.msg || '获取交易记录失败')
      })
      useDevice.loading = false
      return
    }

    // 处理交易记录数据
    const detailsData = detailsResponse.data as any
    if (detailsData?.rows && Array.isArray(detailsData.rows)) {
      detailsList.value = detailsData.rows
    } else if (Array.isArray(detailsData)) {
      detailsList.value = detailsData
    } else {
      console.warn('⚠️ 余额明细数据格式异常:', detailsData)
      detailsList.value = []
    }

    console.log('✅ 处理后的交易记录:', detailsList.value)
    useDevice.loading = false
  } catch (error) {
    console.error('余额列表页面初始化失败:', error)
    import('vant').then(({ showFailToast }) => {
      showFailToast('初始化失败，请稍后重试')
    })
    useDevice.loading = false
  }
}

// ✅ 完全复制device-an的支付跳转逻辑
const Payment = () => {
  if (!id.value) {
    import('vant').then(({ showFailToast }) => {
      showFailToast('请选择充值金额')
    })
    return
  }
  
  router.push({
    path: '/device/BalancePayment',
    query: {
      id: id.value
    }
  })
}

onMounted(() => {
  initialize()
})
</script>

<style lang="scss" scoped>
.BalanceList {
  @include PageBox;

  &-box {
    @include WhiteBox;
    @include PaddingBox;

    &-head {
      @include WhiteBox;
      @include PaddingBox;
      background: linear-gradient(135deg, rgba(58, 128, 245), rgba(40, 102, 237)) !important;
      color: #fff;

      &-label {
        font-size: 0.65rem;

        &-icon {
          font-size: 1rem;
          margin-right: 0.3rem;
          position: relative;
          top: -0.1rem;
        }
      }

      &-moeny {
        font-size: 1.8rem;
        font-weight: bold;
        margin: 0.4rem 0;

        span {
          font-size: 0.9rem;
        }
      }

      &-tip {
        font-size: 0.6rem;

        &-icon {
          position: relative;
          top: -0.1rem;
          margin-right: 0.2rem;
        }
      }
    }

    &-tip {
      font-size: 0.6rem;
      display: flex;
      justify-content: center;
      align-items: center;

      &-box {
        background-color: rgba($color: $primary, $alpha: 0.1);
        margin-top: 0.5rem;
        height: 1.4rem;
        line-height: 1.4rem;
        color: $primary;
        border-radius: 0.7rem;
        padding: 0 0.8rem;

        &-icon {
          margin-right: 0.2rem;
          position: relative;
          top: -0.1rem;
        }
      }
    }

    &-list {
      margin-top: calc($padding * 1.5);

      &-label {
        font-size: 0.6rem;
        color: #666;
      }

      &-box {
        margin-top: $padding;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: calc($padding / 1.2);

        &-item {
          box-sizing: border-box;
          border-radius: $radius;
          border: 0.01rem solid hsla(214.3, 31.8%, 91.4%);
          text-align: center;
          padding: 0.6rem 0;
          overflow: hidden;
          transition: 0.15s linear;
          cursor: pointer;

          &-price {
            font-size: 0.8rem;

            span {
              font-size: 0.6rem;
            }
          }

          &-give {
            font-size: 0.55rem;
            margin-top: 0.3rem;
            color: $primary;
          }
        }

        &-item-active {
          border-color: rgba($color: $primary, $alpha: 0.4);
          position: relative;

          .BalanceList-box-list-box-item-price {
            color: $primary;
          }

          &::after {
            content: '';
            display: block;
            width: 50%;
            height: 50%;
            background-color: $primary;
            filter: blur(2rem);
            position: absolute;
            top: -2rem;
            left: calc(40% - 1.1rem);
          }

          &::before {
            content: '';
            display: block;
            height: 0.1rem;
            width: 100%;
            position: absolute;
            bottom: 0;
            left: 0;
            background-color: $primary;
          }
        }
      }
    }

    &-btn {
      display: block;
      outline: none;
      border: none;
      margin-top: calc($padding * 1.5);
      background-color: $primary;
      color: #fff;
      width: 100%;
      height: 2rem;
      border-radius: 1rem;
      font-size: 0.7rem;
      box-shadow: $shadow;
      transition: linear 0.15s;
      cursor: pointer;

      &:disabled {
        background-color: rgba($color: $wait, $alpha: 0.2);
        color: #666;
        cursor: not-allowed;
      }
    }

    &-details {
      margin-top: calc($padding * 2);

      &-head {
        display: flex;
        justify-content: space-between;
        align-items: center;

        &-label {
          font-size: 0.6rem;
          color: #666;
        }

        &-view {
          color: $primary;
          font-size: 0.5rem;
          cursor: pointer;

          &-icon {
            position: relative;
            top: -0.1rem;
          }
        }
      }

      &-vieAll {
        background-color: rgba($color: $primary, $alpha: 0.1);
        height: 1.6rem;
        line-height: 1.6rem;
        color: $primary;
        text-align: center;
        font-size: 0.65rem;
        margin-top: $padding;
        border-radius: 0.3rem;
        cursor: pointer;

        &-icon {
          position: relative;
          top: -0.1rem;
          margin-left: 0.4rem;
        }
      }
    }
  }
}
</style>
