<!-- ✅ 完全复制device-an的PackageList.vue -->
<template>
  <div class="PackageList">
    <div class="PackageList-label">
      <div class="PackageList-label-package">
        <div class="PackageList-label-package-label">当前套餐</div>

        <div class="PackageList-label-package-name">
          {{ useDevice.details.packageName || '未有生效中套餐' }}
        </div>
      </div>

      <div class="PackageList-label-time">
        <SvgIcon name="days" class="PackageList-label-time-icon" />
        {{
          useDevice.details.becomedueDatetime
            ? '有效期至' + toTime(useDevice.details.becomedueDatetime)
            : '未有生效中套餐'
        }}
      </div>
    </div>

    <div class="PackageList-list">
      <div class="PackageList-list-label">选择套餐</div>

      <div class="PackageList-list-box">
        <div
          class="PackageList-list-box-item"
          v-for="item in list"
          :key="item.id"
          :class="{ 'PackageList-list-box-item-active': id === item.id }"
          @click="id = item.id"
        >
          <div class="PackageList-list-box-item-top">
            <div class="PackageList-list-box-item-top-left">
              <div class="PackageList-list-box-item-top-left-radio" />

              <div class="PackageList-list-box-item-top-left-name">
                {{ item.name }}
              </div>
            </div>

            <div class="PackageList-list-box-item-top-tag" v-if="item.popular === 1">热门</div>
          </div>

          <div class="PackageList-list-box-item-bottom">
            <div class="PackageList-list-box-item-bottom-left">
              <div class="PackageList-list-box-item-bottom-left-box">
                <div class="PackageList-list-box-item-bottom-left-box-txt">
                  {{ toGB(item.packageTotal).join('') }}
                </div>

                <div class="PackageList-list-box-item-bottom-left-box-label">流量</div>
              </div>

              <div class="PackageList-list-box-item-bottom-left-box" v-if="item.packageType === 1">
                <div class="PackageList-list-box-item-bottom-left-box-txt">
                  {{ GetPackageDays(item) }}
                </div>

                <div class="PackageList-list-box-item-bottom-left-box-label">有效期</div>
              </div>

              <div class="PackageList-list-box-item-bottom-left-box">
                <div class="PackageList-list-box-item-bottom-left-box-txt">
                  {{ DevicePackageType[item.packageType - 1].label }}
                </div>

                <div class="PackageList-list-box-item-bottom-left-box-label">套餐类型</div>
              </div>
            </div>

            <div class="PackageList-list-box-item-bottom-price">
              <span>￥</span>{{ item.packagePrice }}
            </div>
          </div>
        </div>
      </div>

      <div class="PackageList-list-tip">
        <div class="PackageList-list-tip-label">套餐特权</div>

        <div class="PackageList-list-tip-box">
          <div class="PackageList-list-tip-box-item">
            <SvgIcon name="true" class="PackageList-list-tip-box-item-icon" />
            全国流量不限速
          </div>

          <div class="PackageList-list-tip-box-item">
            <SvgIcon name="true" class="PackageList-list-tip-box-item-icon" />
            网络智能切换
          </div>

          <div class="PackageList-list-tip-box-item">
            <SvgIcon name="true" class="PackageList-list-tip-box-item-icon" />
            客服支持
          </div>

          <div class="PackageList-list-tip-box-item">
            <SvgIcon name="true" class="PackageList-list-tip-box-item-icon" />
            续费无忧
          </div>
        </div>
      </div>

      <button
        class="PackageList-list-btn"
        :disabled="!id"
        @click="
          router.push({
            path: '/device/PackagePayment',
            query: { id }
          })
        "
      >
        立即购买
      </button>

      <div class="PackageList-list-toBalance" @click="router.push('/device/BalanceList')">
        账户余额充值<SvgIcon name="to" class="PackageList-list-toBalance-icon" />
      </div>
    </div>

    <div class="PackageList-order" v-if="OrderList.length">
      <div class="PackageList-order-head">
        <div class="PackageList-order-head-label">近期订单</div>

        <div class="PackageList-order-head-more" @click="router.push('/device/PackageOrder')">
          更多 <SvgIcon name="right" class="PackageList-order-head-more-icon" />
        </div>
      </div>

      <!-- ✅ 完全复制device-an的PackageOrderBox组件使用 -->
      <PackageOrderBox :data="OrderList[0]" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDeviceStore } from '@device/stores/device'
import { useRouter } from 'vue-router'
import { ref, onMounted } from 'vue'
import apiClient from '@/api/client'
import { toGB, toTime } from '@/utils'
import { DevicePackageType } from '@/utils/options'
import SvgIcon from '@/components/SvgIcon.vue'
import PackageOrderBox from './components/PackageOrderBox.vue'

// ✅ 临时在组件内定义GetPackageDays函数
const GetPackageDays = (item: any) => {
  switch (item.packageValidity) {
    case 1:
      return '月底清零'
    case 2:
      return '26号清零'
    case 3:
      return `${item.validityDays}天有效`
    case 4:
      return '按时计费'
    case 5:
      return '按量计费'
    default:
      return '未知'
  }
}

const useDevice = useDeviceStore()
const router = useRouter()

// ✅ 完全复制device-an的数据结构
const id = ref<number>()

// ✅ 完全复制device-an的数据类型定义
interface CycleData {
  id: number
  cycleId: number
  cycleCount: number
}

// ✅ 完全复制device-an的PackageData类型定义
interface PackageData {
  id: number
  name: number  // ✅ device-an中确实是number类型
  packageTotal: number
  packageIntroduce: string
  packagePrice: number
  popular: number  // 1是热门 2否
  packageType: number
  packageValidity: number  // 1.月底清零 2.26号清零 3.按天自定义 4.按时自定义 5.按量计费
  validityDays: number
  cycles: CycleData[]
}

// ✅ 订单数据类型定义
interface PackageOrderData {
  id: number
  orderName: string
  systemOrdernumber: string
  upstreamOrdernumber: string
  paymentOrdernumber: string
  packageCost: number
  packagePrice: number
  paidInAmount: number
  orderProfit: number
  discountAmount: number
  discountType: number
  takeeffectType: number
  orderState: number
  orderCount: number
  orderPayment: number
  orderIp: string
  rechargeAddress: string
  creationTime: Date
  rechargeTime: Date
  takeeffectTime: Date
  expirationTime: Date
  deviceNo: string
  iccidNumber: string
  userAccount: string
  errorLog: string
}

const list = ref<PackageData[]>([])
const OrderList = ref<PackageOrderData[]>([])

// ✅ 完全复制device-an的API调用逻辑 - 无参数调用
const initialize = async () => {
  useDevice.loading = true

  try {
    // 获取套餐列表
    const packageResponse = await apiClient.getPackageList()
    console.log('🔍 套餐列表API响应:', packageResponse)

    if (!packageResponse.code) {
      import('vant').then(({ showFailToast }) => {
        showFailToast(packageResponse.msg || '获取套餐列表失败')
      })
      useDevice.loading = false
      return
    }

    // 处理套餐数据
    const packageData = packageResponse.data as any
    if (Array.isArray(packageData)) {
      list.value = packageData
    } else if (packageData?.list && Array.isArray(packageData.list)) {
      list.value = packageData.list
    } else {
      console.warn('⚠️ 套餐列表数据格式异常:', packageData)
      list.value = []
    }

    console.log('✅ 处理后的套餐列表:', list.value)

    // ✅ 获取近期订单 - 修复API调用
    const orderResponse = await apiClient.getPackageOrderList({ page: 1, pageSize: 1, orderState: 2 })
    console.log('🔍 套餐订单API响应:', orderResponse)

    if (!orderResponse.code) {
      console.warn('⚠️ 获取套餐订单失败:', orderResponse.msg)
      OrderList.value = []
      useDevice.loading = false
      return
    }

    // 处理订单数据
    const orderData = orderResponse.data as any
    if (orderData?.rows && Array.isArray(orderData.rows)) {
      OrderList.value = orderData.rows
    } else if (Array.isArray(orderData)) {
      OrderList.value = orderData
    } else {
      console.warn('⚠️ 套餐订单数据格式异常:', orderData)
      OrderList.value = []
    }

    console.log('✅ 处理后的订单列表:', OrderList.value)
    useDevice.loading = false
  } catch (error) {
    console.error('套餐列表页面初始化失败:', error)
    import('vant').then(({ showFailToast }) => {
      showFailToast('初始化失败，请稍后重试')
    })
    useDevice.loading = false
  }
}

// 页面挂载时初始化
onMounted(() => {
  initialize()
})
</script>

<style lang="scss" scoped>
@import '../../../../styles/variables.scss';

$label-height: 2rem;

.PackageList {
  @include PaddingBox;
  height: 100vh;
  overflow-x: hidden;
  overflow-y: scroll;

  &-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: $label-height;

    &-package {
      &-label {
        font-size: 0.6rem;
        color: #666;
      }

      &-name {
        font-size: 0.8rem;
        margin-top: 0.3rem;
      }
    }

    &-time {
      font-size: 0.65rem;
      color: #666;

      &-icon {
        position: relative;
        top: -0.1rem;
      }
    }
  }

  &-list {
    box-sizing: border-box;
    padding-top: calc($padding * 1.5);

    &-label {
      font-size: 0.6rem;
      color: #666;
      height: 0.6rem;
    }

    &-box {
      max-height: calc(
        100vh - ($padding * (2 + 1.5 + 1.5 + 1 + 1 + 1.5 + 0.5 + 0.5 + 2)) - $label-height -
          (0.6rem * 6) - calc(1.2rem * 2) - 2rem - 2rem
      );
      overflow-y: scroll;
      overflow-x: hidden;

      &-item {
        @include PaddingBox;
        background-color: $background;
        border-radius: $radius;
        margin-top: calc($padding / 1.2);
        transition: 0.15s linear;
        box-sizing: border-box;
        border: 0.01rem solid transparent;

        &-top {
          display: flex;
          justify-content: space-between;
          align-items: center;

          &-left {
            display: flex;
            justify-content: start;
            align-items: center;

            &-radio {
              width: 0.8rem;
              height: 0.8rem;
              box-sizing: border-box;
              border: 0.01rem solid #999;
              margin-right: 0.4rem;
              border-radius: 50%;
              transition: 0.15s linear;
            }

            &-name {
              font-size: 0.8rem;
            }
          }

          &-tag {
            font-size: 0.5rem;
            height: 1rem;
            line-height: 1rem;
            padding: 0 0.4rem;
            border-radius: 0.5rem;
            background-color: $primary;
            color: #fff;
          }
        }

        &-bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 0.4rem;

          &-left {
            display: flex;
            justify-content: start;
            align-items: center;

            &-box {
              text-align: center;
              margin-right: 0.5rem;

              &-txt {
                font-size: 0.65rem;
                font-weight: bold;
              }

              &-label {
                font-size: 0.5rem;
                color: #666;
                margin-top: 0.2rem;
              }
            }
          }

          &-price {
            font-size: 0.8rem;
            font-weight: bold;

            span {
              font-size: 0.5rem;
            }
          }
        }
      }

      &-item-active {
        border-color: $primary;
        background-color: rgba($color: $primary, $alpha: 0.1);

        .PackageList-list-box-item-top-left-radio {
          border: 0.25rem solid $primary;
        }

      }
    }

    &-tip {
      margin-top: calc($padding * 1.5);

      &-label {
        font-size: 0.6rem;
        color: #666;
        height: 0.6rem;
      }

      &-box {
        margin-top: calc($padding * 1);
        background-color: $background;
        border-radius: $radius;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        padding: calc($padding / 2) $padding;

        &-item {
          font-size: 0.6rem;
          height: 1.2rem;
          line-height: 1.2rem;

          &-icon {
            color: $primary;
            box-sizing: border-box;
            position: relative;
            top: -0.07rem;
            margin-right: 0.2rem;
            font-size: 0.65rem;
          }
        }
      }
    }

    &-btn {
      margin-top: calc($padding * 1.5);
      display: block;
      border: none;
      outline: none;
      background-color: $primary;
      color: #fff;
      width: 100%;
      height: 2rem;
      border-radius: 1rem;
      font-size: 0.7rem;
      transition: 0.15s linear;
    }

    &-btn:disabled {
      background-color: rgba(229, 231, 235);
      color: #666;
    }

    &-toBalance {
      margin-top: calc($padding / 2);
      font-size: 0.6rem;
      text-align: center;
      color: $primary;
      height: 0.6rem;

      &-icon {
        margin-left: 0.2rem;
        position: relative;
        top: -0.1rem;
      }
    }
  }

  &-order {
    margin-top: calc($padding * 1.5);

    &-head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.6rem;
      height: 0.6rem;

      &-more {
        color: $primary;

        &-icon {
          position: relative;
          top: -0.08rem;
          left: -0.1rem;
          font-size: 0.5rem;
        }
      }
    }
  }
}
</style>
