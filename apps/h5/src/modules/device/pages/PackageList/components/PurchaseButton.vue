<template>
  <div class="purchase-button">
    <van-button
      type="primary"
      size="large"
      block
      :loading="loading"
      :disabled="disabled"
      @click="handlePurchase"
    >
      {{ buttonText }}
    </van-button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Button as VanButt<PERSON> } from 'vant'

const props = withDefaults(defineProps<{
  data?: any
  config?: {
    text?: string
    loadingText?: string
    disabledText?: string
  }
  loading?: boolean
  disabled?: boolean
}>(), {
  data: () => ({}),
  config: () => ({
    text: '立即购买',
    loadingText: '处理中...',
    disabledText: '暂不可用'
  }),
  loading: false,
  disabled: false
})

const emit = defineEmits<{
  purchase: [data: any]
  click: [data: any]
}>()

const buttonText = computed(() => {
  if (props.loading) return props.config.loadingText
  if (props.disabled) return props.config.disabledText
  return props.config.text
})

function handlePurchase() {
  if (props.loading || props.disabled) return
  
  emit('purchase', props.data)
  emit('click', props.data)
}
</script>

<style scoped lang="scss">
.purchase-button {
  padding: 16px;
  background: white;
  border-top: 1px solid #e8e8e8;
  
  :deep(.van-button) {
    height: 48px;
    border-radius: 24px;
    font-size: 16px;
    font-weight: 600;
  }
}
</style>
