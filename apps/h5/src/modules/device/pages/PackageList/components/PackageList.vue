<template>
  <div class="package-list">
    <div
      v-for="pkg in packages"
      :key="pkg.id"
      class="package-item"
      :class="{ 'package-selected': selectedId === pkg.id }"
      @click="handleSelect(pkg)"
    >
      <div class="package-header">
        <div class="package-name">{{ pkg.name }}</div>
        <div v-if="pkg.popular" class="package-popular">{{ config.popularText || '热门' }}</div>
      </div>
      
      <div class="package-info">
        <div class="package-flow">
          <span class="label">{{ config.flowLabel || '流量' }}:</span>
          <span class="value">{{ formatFlow(pkg.packageTotal) }}</span>
        </div>
        
        <div class="package-price">
          <span class="price">¥{{ pkg.packagePrice }}</span>
          <span class="unit">/月</span>
        </div>
      </div>
      
      <div v-if="config.showValidity && pkg.validityDays" class="package-validity">
        {{ config.validityLabel || '有效期' }}: {{ pkg.validityDays }}天
      </div>
      
      <div v-if="config.showDescription && pkg.packageIntroduce" class="package-description">
        {{ pkg.packageIntroduce }}
      </div>
    </div>
    
    <div v-if="packages.length === 0" class="empty-state">
      <div class="empty-text">暂无套餐</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const props = withDefaults(defineProps<{
  data?: any[]
  config?: {
    popularText?: string
    flowLabel?: string
    validityLabel?: string
    typeLabel?: string
    showPopularTag?: boolean
    showValidity?: boolean
    showDescription?: boolean
    allowMultiSelect?: boolean
  }
}>(), {
  data: () => [],
  config: () => ({
    popularText: '热门',
    flowLabel: '流量',
    validityLabel: '有效期',
    typeLabel: '套餐类型',
    showPopularTag: true,
    showValidity: true,
    showDescription: false,
    allowMultiSelect: false
  })
})

const emit = defineEmits<{
  packageSelect: [packageId: number]
  select: [packageId: number]
}>()

const selectedId = ref<number | null>(null)

const packages = computed(() => {
  return Array.isArray(props.data) ? props.data : []
})

// 格式化流量显示
function formatFlow(mb: number): string {
  if (mb >= 1024) {
    return `${(mb / 1024).toFixed(1)}GB`
  }
  return `${mb}MB`
}

// 处理选择
function handleSelect(pkg: any) {
  selectedId.value = pkg.id
  emit('packageSelect', pkg.id)
  emit('select', pkg.id)
}
</script>

<style scoped lang="scss">
.package-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.package-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #1989fa;
    box-shadow: 0 2px 8px rgba(25, 137, 250, 0.15);
  }
  
  &.package-selected {
    border-color: #1989fa;
    background-color: #f0f8ff;
  }
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.package-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.package-popular {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
}

.package-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.package-flow {
  .label {
    color: #666;
    font-size: 14px;
  }
  
  .value {
    color: #333;
    font-weight: 500;
    margin-left: 4px;
  }
}

.package-price {
  .price {
    color: #ff6b6b;
    font-size: 18px;
    font-weight: 600;
  }
  
  .unit {
    color: #999;
    font-size: 12px;
  }
}

.package-validity {
  color: #666;
  font-size: 12px;
  margin-bottom: 8px;
}

.package-description {
  color: #999;
  font-size: 12px;
  line-height: 1.4;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-text {
  font-size: 14px;
}
</style>
