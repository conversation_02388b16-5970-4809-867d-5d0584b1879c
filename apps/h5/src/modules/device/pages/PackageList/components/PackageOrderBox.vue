<!-- ✅ 完全复制device-an的PackageOrderBox组件 -->
<script setup lang="ts">
import { type PropType } from 'vue'
import { toTime } from '@/utils'
import type { PackageOrderHistoryData } from '@device/types/package'

const props = defineProps({
  data: {
    type: Object as PropType<PackageOrderHistoryData>,
    required: true
  },
  Gray: {
    type: Boolean,
    default: false
  }
})
</script>

<template>
  <div class="PackageOrderItem">
    <div class="PackageOrderItem-head">
      <div class="PackageOrderItem-head-name">
        {{ props.data.orderName }}
      </div>

      <div class="PackageOrderItem-head-price">
        <span>￥</span>{{ props.data.packagePrice }}
      </div>
    </div>

    <div class="PackageOrderItem-bottom">
      <div class="PackageOrderItem-bottom-num">
        {{ props.data.systemOrdernumber }}
      </div>

      <div class="PackageOrderItem-bottom-time">
        {{ toTime(props.data.rechargeTime, 'YYYY-MM-DD') }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '../../../../../styles/variables.scss';

.PackageOrderItem {
  background-color: $background;
  margin-top: calc($padding / 2);
  border-radius: $radius;
  @include PaddingBox;

  &-head,
  &-bottom {
    display: flex;
    justify-content: space-between;
  }

  &-head {
    font-size: 0.7rem;
    height: 1.4rem;

    &-price {
      font-size: 0.8rem;

      span {
        font-size: 0.5rem;
      }
    }
  }

  &-bottom {
    font-size: 0.6rem;
    color: #666;
    height: 0.6rem;
  }
}
</style>
