<template>
  <div class="package-privileges">
    <div class="privileges-header">
      <div class="header-title">套餐特权</div>
    </div>
    
    <div class="privileges-list">
      <div
        v-for="privilege in privileges"
        :key="privilege.id"
        class="privilege-item"
      >
        <div class="privilege-icon">
          <Icon :icon="privilege.icon" />
        </div>
        <div class="privilege-content">
          <div class="privilege-title">{{ privilege.title }}</div>
          <div class="privilege-description">{{ privilege.description }}</div>
        </div>
        <div v-if="privilege.enabled" class="privilege-status">
          <Icon icon="mdi:check-circle" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { computed } from 'vue'

const props = withDefaults(defineProps<{
  data?: any[]
  config?: {
    showStatus?: boolean
  }
}>(), {
  data: () => [],
  config: () => ({
    showStatus: true
  })
})

const privileges = computed(() => {
  return Array.isArray(props.data) ? props.data : [
    {
      id: 1,
      title: '全国流量',
      description: '全国范围内使用，无漫游费',
      icon: 'mdi:earth',
      enabled: true
    },
    {
      id: 2,
      title: '高速网络',
      description: '4G/5G高速网络，畅享极速体验',
      icon: 'mdi:speedometer',
      enabled: true
    },
    {
      id: 3,
      title: '免费热点',
      description: '支持WiFi热点分享功能',
      icon: 'mdi:wifi',
      enabled: true
    },
    {
      id: 4,
      title: '24小时客服',
      description: '全天候客服支持，随时解决问题',
      icon: 'mdi:headset',
      enabled: false
    }
  ]
})
</script>

<style scoped lang="scss">
.package-privileges {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.privileges-header {
  margin-bottom: 16px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.privileges-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.privilege-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.privilege-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e6f7ff;
  border-radius: 50%;
  margin-right: 12px;
  font-size: 16px;
  color: #1989fa;
}

.privilege-content {
  flex: 1;
}

.privilege-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.privilege-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.privilege-status {
  font-size: 16px;
  color: #52c41a;
}
</style>
