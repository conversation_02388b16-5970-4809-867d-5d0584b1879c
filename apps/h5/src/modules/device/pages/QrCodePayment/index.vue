<template>
  <div class="QrCodePayment">
    <!-- 导航栏 -->
    <van-nav-bar
      title="扫码支付"
      left-arrow
      @click-left="goBack"
      class="QrCodePayment-nav"
    />

    <div class="QrCodePayment-container">
      <!-- 支付头部信息 -->
      <div class="QrCodePayment-header">
        <div class="QrCodePayment-header-icon">
          <SvgIcon name="qrcode" />
        </div>
        <div class="QrCodePayment-header-title">扫码支付</div>
        <div class="QrCodePayment-header-desc">请使用支付宝扫描下方二维码完成支付</div>
      </div>

      <!-- 二维码显示区域 -->
      <div class="QrCodePayment-qrcode">
        <!-- Canvas元素始终存在，用于生成二维码 -->
        <div class="QrCodePayment-qrcode-box">
          <canvas
            ref="qrcodeCanvas"
            class="QrCodePayment-qrcode-canvas"
            v-show="qrcodeGenerated && !isLoading && !error"
          ></canvas>
          <div v-if="!qrcodeGenerated && !isLoading && !error" class="QrCodePayment-qrcode-placeholder">
            <van-loading type="spinner" size="24px" />
            <p>生成二维码中...</p>
          </div>
        </div>
        <div class="QrCodePayment-qrcode-tip" v-show="qrcodeGenerated && !isLoading && !error">
          <SvgIcon name="tip" class="QrCodePayment-qrcode-tip-icon" />
          请长按保存图片到相册，然后在支付宝中扫描
        </div>
      </div>

      <!-- 加载状态 -->
      <div class="QrCodePayment-loading" v-if="isLoading">
        <van-loading type="spinner" size="32px" />
        <p>生成二维码中...</p>
      </div>

      <!-- 错误状态 -->
      <div class="QrCodePayment-error" v-if="error">
        <SvgIcon name="error" class="QrCodePayment-error-icon" />
        <p class="QrCodePayment-error-text">{{ error }}</p>
        <van-button type="primary" @click="retryGenerate" class="QrCodePayment-error-btn">
          重新生成
        </van-button>
      </div>

      <!-- 操作按钮 -->
      <div class="QrCodePayment-actions" v-if="!isLoading">
        <van-button
          type="default"
          @click="goBack"
          class="QrCodePayment-actions-btn"
        >
          返回
        </van-button>
        <van-button
          type="primary"
          @click="checkPaymentStatus"
          class="QrCodePayment-actions-btn"
        >
          检查支付状态
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { showSuccessToast, showLoadingToast } from 'vant'
import QRCode from 'qrcode'
import SvgIcon from '@/components/SvgIcon.vue'

// ==================== 响应式数据 ====================

const qrcodeUrl = ref<string>('')
const isLoading = ref(true)
const error = ref<string>('')
const qrcodeGenerated = ref(false)
const qrcodeCanvas = ref<HTMLCanvasElement>()

// ==================== 工具函数 ====================

const getQueryParams = () => {
  // 获取hash部分的查询参数
  const hash = window.location.hash
  const queryIndex = hash.indexOf('?')

  if (queryIndex === -1) {
    return {}
  }

  const queryString = hash.substring(queryIndex + 1)
  const urlParams = new URLSearchParams(queryString)
  const params: Record<string, string> = {}
  urlParams.forEach((value, key) => {
    params[key] = value
  })
  return params
}

// ==================== QR码生成 ====================

const generateQRCode = async (url: string) => {
  try {
    console.log('🔄 开始生成二维码:', url)

    // 验证URL格式
    if (!url || typeof url !== 'string') {
      throw new Error('无效的URL参数')
    }

    // 检查Canvas元素
    if (!qrcodeCanvas.value) {
      throw new Error('Canvas元素未找到')
    }

    console.log('✅ Canvas元素已找到，开始生成二维码')

    // 生成二维码到canvas
    await QRCode.toCanvas(qrcodeCanvas.value, url, {
      width: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      errorCorrectionLevel: 'M'
    })

    qrcodeGenerated.value = true
    isLoading.value = false
    console.log('✅ 二维码生成成功')

  } catch (err: any) {
    console.error('❌ 二维码生成失败:', err)
    error.value = `二维码生成失败: ${err.message}`
    isLoading.value = false
    qrcodeGenerated.value = false
  }
}

// ==================== 事件处理 ====================

const goBack = () => {
  window.history.go(-2)
}

const checkPaymentStatus = () => {
  const toast = showLoadingToast({
    message: '检查支付状态...',
    forbidClick: true,
    duration: 3000
  })

  // 模拟检查支付状态
  setTimeout(() => {
    toast.close()
    showSuccessToast('请在支付应用中完成支付')
  }, 3000)
}

const retryGenerate = async () => {
  console.log('🔄 重新生成二维码...')
  error.value = ''
  isLoading.value = true
  qrcodeGenerated.value = false

  // 等待DOM更新
  await nextTick()

  if (qrcodeUrl.value) {
    try {
      await generateQRCode(qrcodeUrl.value)
    } catch (err: any) {
      console.error('❌ 重试生成二维码失败:', err)
      error.value = '重试失败，请检查网络连接'
      isLoading.value = false
    }
  } else {
    error.value = '缺少支付参数'
    isLoading.value = false
  }
}

// ==================== 生命周期 ====================

onMounted(async () => {
  try {
    console.log('🔄 QR码支付页面初始化...')
    const params = getQueryParams()

    if (params.url) {
      // 直接URL二维码
      qrcodeUrl.value = decodeURIComponent(params.url)
      console.log('📝 获取到URL参数:', qrcodeUrl.value)
    } else if (params.complex) {
      // 复杂数据二维码
      const complexData = JSON.parse(decodeURIComponent(params.complex))

      if (complexData.type === '1') {
        // 扫码类型
        qrcodeUrl.value = complexData.qrcode || complexData.url
        console.log('📝 获取到复杂数据URL:', qrcodeUrl.value)
      } else {
        // 跳转类型
        console.log('🔄 跳转类型，直接跳转到:', complexData.url)
        window.location.href = complexData.url
        return
      }
    } else {
      error.value = '缺少支付参数'
      isLoading.value = false
      return
    }

    // 确保有有效的URL
    if (!qrcodeUrl.value) {
      error.value = '无效的支付URL'
      isLoading.value = false
      return
    }

    // 等待DOM更新后生成二维码
    await nextTick()

    try {
      await generateQRCode(qrcodeUrl.value)
    } catch (err: any) {
      console.error('❌ 生成二维码失败:', err)
      error.value = '二维码生成失败，请刷新页面重试'
      isLoading.value = false
    }

  } catch (err: any) {
    console.error('❌ 参数解析失败:', err)
    error.value = '参数解析失败: ' + err.message
    isLoading.value = false
  }
})
</script>

<style lang="scss" scoped>
/* ✅ 完全复制项目设计系统的样式规范 */
.QrCodePayment {
  min-height: 100vh;
  background-color: #f5f5f5;

  &-nav {
    background: white;
    border-bottom: 1px solid #ebedf0;

    :deep(.van-nav-bar__title) {
      color: #323233;
      font-weight: 600;
    }
  }

  &-container {
    padding: 20px 16px;
  }

  &-header {
    background: white;
    border-radius: 12px;
    padding: 24px 20px;
    text-align: center;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    &-icon {
      width: 48px;
      height: 48px;
      margin: 0 auto 16px;
      background: linear-gradient(135deg, #1989fa 0%, #1890ff 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      :deep(.svg-icon) {
        width: 24px;
        height: 24px;
        color: white;
      }
    }

    &-title {
      font-size: 20px;
      font-weight: 600;
      color: #323233;
      margin-bottom: 8px;
    }

    &-desc {
      font-size: 14px;
      color: #969799;
      line-height: 1.5;
    }
  }

  &-qrcode {
    background: white;
    border-radius: 12px;
    padding: 32px 20px;
    text-align: center;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    &-box {
      position: relative;
      display: inline-block;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 12px;
      border: 2px dashed #e1e3e6;
      margin-bottom: 20px;
    }

    &-canvas {
      display: block;
      border-radius: 8px;
      background: white;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &-placeholder {
      width: 200px;
      height: 200px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: white;
      border-radius: 8px;

      p {
        margin-top: 12px;
        font-size: 14px;
        color: #969799;
      }
    }

    &-tip {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      font-size: 12px;
      color: #969799;
      line-height: 1.5;
      padding: 0 16px;

      &-icon {
        width: 14px;
        height: 14px;
        margin-right: 6px;
        margin-top: 1px;
        color: #ffa940;
        flex-shrink: 0;
      }
    }
  }

  &-loading {
    background: white;
    border-radius: 12px;
    padding: 60px 20px;
    text-align: center;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    p {
      margin-top: 16px;
      font-size: 14px;
      color: #969799;
    }
  }

  &-error {
    background: white;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    &-icon {
      width: 48px;
      height: 48px;
      color: #ee0a24;
      margin-bottom: 16px;
    }

    &-text {
      font-size: 14px;
      color: #ee0a24;
      margin-bottom: 24px;
      line-height: 1.5;
    }

    &-btn {
      min-width: 120px;
    }
  }

  &-actions {
    display: flex;
    gap: 12px;
    padding: 0 4px;

    &-btn {
      flex: 1;
      height: 44px;
      border-radius: 22px;
      font-size: 16px;
      font-weight: 500;

      &.van-button--default {
        background: white;
        border: 1px solid #ebedf0;
        color: #646566;

        &:active {
          background: #f2f3f5;
        }
      }

      &.van-button--primary {
        background: linear-gradient(135deg, #1989fa 0%, #1890ff 100%);
        border: none;

        &:active {
          background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        }
      }
    }
  }
}

/* ✅ 响应式设计 */
@media (max-width: 375px) {
  .QrCodePayment {
    &-container {
      padding: 16px 12px;
    }

    &-header {
      padding: 20px 16px;
    }

    &-qrcode {
      padding: 24px 16px;

      &-box {
        padding: 12px;
      }

      &-canvas,
      &-placeholder {
        width: 180px;
        height: 180px;
      }
    }
  }
}
</style>