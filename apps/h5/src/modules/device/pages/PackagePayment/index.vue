<!-- ✅ 完全复制device-an的PackagePayment.vue -->
<template>
  <div class="PackagePayment">
    <dxPayment
      v-if="PaymentList && PaymentList.length"
      :PaymentList="PaymentList"
      :GetOrderNum="GetOrderNum"
      :Money="PackagePreview.price ? PackagePreview.price : OrderData.packagePrice"
      @BalancePayment="BalancePayment"
    >
      <div class="PackagePayment-box">
        <div class="PackagePayment-box-item">
          <div class="PackagePayment-box-item-label">套餐名称</div>

          <div class="PackagePayment-box-item-txt">
            {{ OrderData.name }}
          </div>
        </div>

        <div class="PackagePayment-box-item">
          <div class="PackagePayment-box-item-label">套餐流量</div>

          <div class="PackagePayment-box-item-txt">
            {{ toGB(OrderData.packageTotal).join('') }}
          </div>
        </div>

        <div class="PackagePayment-box-type">
          <div class="PackagePayment-box-type-label">生效类型</div>

          <div class="PackagePayment-box-type-box">
            <div
              class="PackagePayment-box-type-box-item"
              @click="GetPackagePreview({
                packageId: Number(route.query.id),
                takeeffectType: 1,
                cycleId: null
              })"
              :class="{ 'PackagePayment-box-type-box-item-active': form.takeeffectType === 1 }"
            >
              立即生效
            </div>

            <div
              class="PackagePayment-box-type-box-item"
              @click="GetPackagePreview({
                packageId: Number(route.query.id),
                takeeffectType: 2,
                cycleId: OrderData.cycles && OrderData.cycles[0]?.id ? OrderData.cycles[0].id : null
              })"
              :class="{ 'PackagePayment-box-type-box-item-active': form.takeeffectType === 2 }"
            >
              次月生效
            </div>
          </div>
        </div>
        <div class="PackagePayment-box-cycle" v-if="OrderData.cycles && form.takeeffectType === 2">
          <div class="PackagePayment-box-cycle-label">购买个数</div>

          <div class="PackagePayment-box-cycle-box">
            <div
              class="PackagePayment-box-cycle-box-item"
              v-for="item in OrderData.cycles"
              :key="item.id"
              :class="{ 'PackagePayment-box-cycle-box-item-active': form.cycleId === item.id }"
              @click="GetPackagePreview({
                packageId: Number(route.query.id),
                takeeffectType: 2,
                cycleId: item.id
              })"
            >
              {{ item.cycleCount + '个' }}
            </div>
          </div>
        </div>

        <div class="PackagePayment-box-time" v-if="PackagePreview.list">
          <div class="PackagePayment-box-time-label">有效时间</div>

          <div class="PackagePayment-box-time-box">
            <SvgIcon name="days" class="PackagePayment-box-time-box-icon" />
            <div class="PackagePayment-box-time-box-txt">
              <div class="PackagePayment-box-time-box-txt-label">
                {{ form.takeeffectType === 1 ? '立即生效' : '次月生效' }}
              </div>

              <div class="PackagePayment-box-time-box-txt-time">
                {{
                  toTime(PackagePreview.list[0].takeeffectTime, 'YYYY年MM月DD日') +
                  '至' +
                  toTime(
                    PackagePreview.list[PackagePreview.list.length - 1].expirationTime,
                    'YYYY年MM月DD日'
                  )
                }}
              </div>
            </div>
          </div>
        </div>

        <div class="PackagePayment-box-item">
          <div class="PackagePayment-box-item-label">套餐价格</div>

          <div class="PackagePayment-box-item-txt">
            {{ '￥' + OrderData.packagePrice.toFixed(2) }}
          </div>
        </div>

        <div class="PackagePayment-box-item">
          <div class="PackagePayment-box-item-label">套餐介绍</div>

          <div class="PackagePayment-box-item-txt">
            {{ OrderData.packageIntroduce }}
          </div>
        </div>
      </div>
    </dxPayment>

    <van-popup v-model:show="show" round position="bottom">
      <component
        :is="PaymentComp"
        @PasswordPaymentSubmit="PasswordPaymentSubmit"
        @PhonePaymentSubmit="PhonePaymentSubmit"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import dxPayment from '@device/components/dxPayment.vue'
import { useRoute } from 'vue-router'
const route = useRoute()

import { showLoadingToast } from 'vant'
import { useDeviceStore } from '@device/stores/device'
import { useWeChatStore } from '@device/stores/wechat'
import { useRouter } from 'vue-router'
const useDevice = useDeviceStore()
const useWeChat = useWeChatStore()
const router = useRouter()

console.log(route.query.id)
console.log(route.query,"kkkkk")

import { ref, type Component, shallowRef } from 'vue'

const PaymentList = ref<any[]>([])
const OrderData = ref<any>({} as any)

import apiClient from '@/api/client'
import { toGB, toTime } from '@/utils'

const form = ref<any>({
  packageId: Number(route.query.id),
  takeeffectType: 0,
  cycleId: null
})

const PackagePreview = ref<any>({} as any)
const GetPackagePreview = (params: any) => {
  // 未作改变
  if (params.takeeffectType === form.value.takeeffectType && params.cycleId === form.value.cycleId)
    return

  useDevice.loading = true
  apiClient.getPackageOrderDetails({...params}).then(({ code, data, msg }) => {
    if (!code) {
      // ✅ 修复：显示套餐预览获取失败的错误信息
      import('vant').then(({ showFailToast }) => {
        showFailToast(msg || '获取套餐信息失败')
      })
      return (useDevice.loading = false)
    }
    PackagePreview.value = data
    form.value = {...params}
    useDevice.loading = false
  })
}

useDevice.loading = true

// ✅ 完全复制device-an的初始化逻辑
// 初始化RechargeConfig
useWeChat.FetchRechargeConfig()

apiClient.getPackageOrderRule(Number(route.query.id)).then(({ code, data, msg }) => {
  if (!code) {
    // ✅ 修复：显示套餐规则获取失败的错误信息
    import('vant').then(({ showFailToast }) => {
      showFailToast(msg || '获取套餐规则失败')
    })
    useDevice.loading = false
    return
  }
  OrderData.value = data

  // 无预存模板
  if (!useDevice.details.prestoreId) {
    apiClient.getPackagePaymentMethods(Number(route.query.id)).then(({ code, data, msg }) => {
      if (!code) {
        // ✅ 修复：显示支付方式获取失败的错误信息
        import('vant').then(({ showFailToast }) => {
          showFailToast(msg || '获取支付方式失败')
        })
        useDevice.loading = false
        return
      }
      PaymentList.value = data.list || []
      useDevice.loading = false
      if (!PaymentList.value || !PaymentList.value.length) {
        import('vant').then(({ showFailToast }) => {
          showFailToast('未配置支付')
        })
      }
    })
  } else {
    apiClient.getPerstoreRule(useDevice.details.prestoreId).then((PrestoreRule) => {
      if (!PrestoreRule.code) {
        // ✅ 修复：显示预存规则获取失败的错误信息
        import('vant').then(({ showFailToast }) => {
          showFailToast(PrestoreRule.msg || '获取预存规则失败')
        })
        useDevice.loading = false
        return
      }
      apiClient.getPackagePaymentMethods(Number(route.query.id)).then(
        ({ code, data, msg }) => {
          if (!code) {
            // ✅ 修复：显示支付方式获取失败的错误信息
            import('vant').then(({ showFailToast }) => {
              showFailToast(msg || '获取支付方式失败')
            })
            useDevice.loading = false
            return
          }

          switch (PrestoreRule.data.prestoreRule) {
            // 强制预存
            case 1:
              if (data.ye) {
                PaymentList.value.push({
                  type: 'balance',
                  payId: 239,
                  key: 'balance',
                  name: '余额支付'
                })
              }

              console.log(data.ye, PaymentList.value)

              break
            // 非强制预存
            case 2:
              PaymentList.value = data.list
              if (data.ye) {
                PaymentList.value.push({
                  type: 'balance',
                  payId: 239,
                  key: 'balance',
                  name: '余额支付'
                })
              }
              break
          }
          useDevice.loading = false
        }
      )
    })
  }
})

// ✅ 完全复制device-an的余额支付处理
const BalancePayment = async (orderNo: string) => {
  const toast = showLoadingToast({
    message: '请稍等...',
    forbidClick: true,
    duration: 0
  })
  
  // ✅ 根据充值配置决定支付验证方式
  switch (useWeChat.RechargeConfig?.balancePayVerification || 2) {
    case 1:
      // 无需验证直接支付
      try {
        const response = await apiClient.balancePaymentPackageOrder({ orderNo })
        toast.close()
        if (!response.code) {
          import('vant').then(({ showFailToast }) => {
            showFailToast(response.msg || '支付失败')
          })
          return
        }
        import('vant').then(({ showSuccessToast }) => {
          showSuccessToast(response.msg || '支付成功')
        })
        router.push('/home')
      } catch (error) {
        toast.close()
        import('vant').then(({ showFailToast }) => {
          showFailToast('支付失败，请稍后重试')
        })
      }
      break
    case 2:
      // 支付密码支付
      toast.close()
      ThatOrderNo.value = orderNo
      PaymentComp.value = PasswordPayment
      show.value = true
      break
    case 3:
      // 短信验证码支付
      toast.close()
      ThatOrderNo.value = orderNo
      PaymentComp.value = PhonePayment
      show.value = true
      break
    default:
      // 默认使用支付密码
      toast.close()
      ThatOrderNo.value = orderNo
      PaymentComp.value = PasswordPayment
      show.value = true
  }
}

// ✅ 支付组件和相关处理
import PasswordPayment from '@device/components/PasswordPayment.vue'
import PhonePayment from '@device/components/PhonePayment.vue'
const PaymentComp = shallowRef<Component>(PhonePayment)
const show = ref(false)
const ThatOrderNo = ref<string>('')

// ✅ 完全复制device-an的密码支付处理
const PasswordPaymentSubmit = async (pwd: string) => {
  try {
    const response = await apiClient.balancePaymentPackageOrder({
      orderNo: ThatOrderNo.value,
      payPwd: pwd
    })
    
    if (!response.code) {
      import('vant').then(({ showFailToast }) => {
        showFailToast(response.msg || '支付失败')
      })
      return
    }
    
    import('vant').then(({ showSuccessToast }) => {
      showSuccessToast(response.msg || '支付成功')
    })
    show.value = false
    router.push('/home')
  } catch (error) {
    import('vant').then(({ showFailToast }) => {
      showFailToast('支付失败，请稍后重试')
    })
  }
}

// ✅ 完全复制device-an的短信验证码支付处理
const PhonePaymentSubmit = async (code: string) => {
  try {
    const response = await apiClient.balancePaymentPackageOrder({
      orderNo: ThatOrderNo.value,
      code
    })
    
    if (!response.code) {
      import('vant').then(({ showFailToast }) => {
        showFailToast(response.msg || '支付失败')
      })
      return
    }
    
    import('vant').then(({ showSuccessToast }) => {
      showSuccessToast(response.msg || '支付成功')
    })
    show.value = false
    router.push('/home')
  } catch (error) {
    import('vant').then(({ showFailToast }) => {
      showFailToast('支付失败，请稍后重试')
    })
  }
}

const GetOrderNum = async () => {
  if (!form.value.takeeffectType) {
    import('vant').then(({ showFailToast }) => {
      showFailToast('请选择生效类型')
    })
    return {
      orderNo: '',
      orderType: 0
    }
  }
  return await apiClient.createPackageOrder({...form.value}).then(({ code, data, msg }) => {
    console.log('创建套餐订单响应:', { code, data, msg })
    if (!code) {
      // ✅ 修复：显示订单创建失败的错误信息
      import('vant').then(({ showFailToast }) => {
        showFailToast(msg || '订单创建失败')
      })
      return {
        orderNo: '',
        orderType: 0
      }
    }
    return {
      orderNo: data.order.systemOrdernumber,
      orderType: 10
    }
  })
}

import SvgIcon from '@/components/SvgIcon.vue'

</script>

<style lang="scss" scoped>
@import '../../../../styles/variables.scss';

.PackagePayment-box {
  margin-top: calc($padding / 2);
  &-item {
    padding: calc($padding / 2) 0;
    display: flex;
    justify-content: space-between;
    font-size: 0.7rem;

    &-label {
      color: #666;
      width: calc(0.7rem * 5);
    }

    &-txt {
      width: calc(100% - 0.7rem * 6);
      text-align: right;
      white-space: normal;
      word-wrap: break-word;
    }
  }

  &-type {
    padding: calc($padding / 2) 0;

    &-label {
      font-size: 0.7rem;
      color: #666;
      width: calc(0.7rem * 5);
    }

    &-box {
      margin-top: calc($padding / 2);
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: $padding;

      &-item {
        background-color: $background;
        border-radius: 0.3rem;
        text-align: center;
        line-height: 1.8rem;
        height: 1.8rem;
        font-size: 0.7rem;
        box-sizing: border-box;
        border: 0.01rem solid $background;
        position: relative;
        transition: 0.15s linear;
      }

      &-item-active {
        border-color: $primary;

        &::after {
          display: block;
          content: '';
          width: 1rem;
          height: 0.07rem;
          background-color: $primary;
          position: absolute;
          bottom: 0.01rem;
          left: 50%;
          transform: translateX(-50%);
        }
      }

      &-item-disabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: none;
      }
    }
  }

  &-cycle {
    padding: calc($padding / 2) 0;

    &-label {
      font-size: 0.7rem;
      color: #666;
      width: calc(0.7rem * 5);
    }

    &-box {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: $padding;
      margin-top: calc($padding / 2);

      &-item {
        background-color: $background;
        border-radius: 0.3rem;
        text-align: center;
        line-height: 1.8rem;
        height: 1.8rem;
        font-size: 0.7rem;
        box-sizing: border-box;
        border: 0.01rem solid $background;
        position: relative;
        transition: 0.15s linear;
      }

      &-item-active {
        border-color: $primary;

        &::after {
          display: block;
          content: '';
          width: 1rem;
          height: 0.07rem;
          background-color: $primary;
          position: absolute;
          bottom: 0.01rem;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }

  &-time {
    padding: calc($padding / 2) 0;

    &-label {
      font-size: 0.7rem;
      color: #666;
      width: calc(0.7rem * 5);
    }

    &-box {
      margin-top: calc($padding / 2);
      background-color: $background;
      border-radius: 0.3rem;
      display: flex;
      justify-content: start;
      align-items: center;
      font-size: 0.7rem;
      box-sizing: border-box;
      padding: $padding;

      &-icon {
        position: relative;
        color: $primary;
        margin-right: 0.4rem;
        font-size: 1rem;
      }

      &-txt {
        font-size: 0.7rem;

        &-label {
          color: #000;
          font-size: 0.7rem;
        }

        &-time {
          font-size: 0.7rem;
          color: #666;
        }
      }
    }
  }
}
</style>
