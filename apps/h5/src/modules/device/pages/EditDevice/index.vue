<template>
  <div class="EditDevice">
    <div class="EditDevice-box">
      <div
        class="EditDevice-box-nav"
        v-if="
          useDevice.rule.supports5g === 1 &&
          (useDevice.rule.wifiName === 2 ||
            useDevice.rule.wifiPwd === 2 ||
            useDevice.rule.wifiStatus === 2)
        "
      >
        <div
          class="EditDevice-box-nav-item"
          :class="{ 'EditDevice-box-nav-item-active': type === 1 }"
          @click="TypeChange(1)"
        >
          4G信息
        </div>

        <div
          class="EditDevice-box-nav-item"
          :class="{ 'EditDevice-box-nav-item-active': type === 2 }"
          @click="TypeChange(2)"
        >
          5G信息
        </div>
      </div>

      <div class="EditDevice-box-info">
        <template v-if="useDevice.rule.wifiName === 2">
          <div class="EditDevice-box-info-label">WiFi名称</div>
          <dxInput placeholder="请输入WiFi名称" leftIcon="wifi" v-model="WifiInfo.wifiName" />
          <div class="EditDevice-box-info-tip">WiFi名称长度为1-32个字符，支持中英文和特殊字符</div>
        </template>

        <template v-if="useDevice.rule.wifiPwd === 2">
          <div class="EditDevice-box-info-label">WiFi密码</div>
          <dxInput
            placeholder="请输入WiFi密码"
            leftIcon="secure"
            :rightIcon="PasswordView ? 'viewNot' : 'view'"
            :type="PasswordView ? 'text' : 'password'"
            :RightClick="
              () => {
                PasswordView = !PasswordView
              }
            "
            v-model="WifiInfo.wifiPwd"
          />
          <div class="EditDevice-box-info-tip">
            密码长度为8-18个字符，建议使用字母、数字和符号的组合
          </div>
        </template>

        <div
          class="EditDevice-box-info-btn"
          @click="EditInfo"
          v-if="useDevice.rule.wifiName === 2 || useDevice.rule.wifiPwd === 2"
        >
          立即保存
        </div>

        <template v-if="useDevice.rule.wifiStatus === 2">
          <div class="EditDevice-box-info-hide">
            <div class="EditDevice-box-info-hide-label">隐藏WiFi名称</div>

            <div
              class="EditDevice-box-info-hide-switch"
              :class="{ 'EditDevice-box-info-hide-switch-active': WifiInfo.hide === 2 }"
              @click="EditHide(WifiInfo.hide === 1 ? 2 : 1)"
            />
          </div>
        </template>

        <div
          class="EditDevice-box-info-warning"
          v-if="useDevice.rule.wifiName === 2 || useDevice.rule.wifiPwd === 2"
        >
          <div class="EditDevice-box-info-warning-label">
            <SvgIcon name="warning" class="EditDevice-box-info-warning-label-icon" />
            修改WiFi设置注意事项
          </div>

          <p>1.修改WiFi名称或密码后，所有已连接设备将断开连接</p>
          <p>2.您需要使用新的WiFi名称和密码重新连接</p>
          <p>3.请确保记住新设置的WiFi密码</p>
        </div>
      </div>

      <div
        class="EditDevice-box-operate"
        v-if="
          useDevice.rule.restart === 2 ||
          useDevice.rule.restoreFactory === 2
        "
      >
        <!-- 关机功能暂不支持 -->
        <!--
        <div
          class="EditDevice-box-operate-box"
          @click="OperateStatus(1, '关机')"
          v-if="false"
        >
          <div class="EditDevice-box-operate-box-icon">
            <SvgIcon name="exit" />
          </div>

          <div class="EditDevice-box-operate-box-label">关机</div>
        </div>
        -->

        <div
          class="EditDevice-box-operate-box"
          @click="OperateStatus(2, '重启')"
          v-if="useDevice.rule.restart === 2"
        >
          <div class="EditDevice-box-operate-box-icon">
            <SvgIcon name="restart" />
          </div>

          <div class="EditDevice-box-operate-box-label">重启</div>
        </div>

        <div
          class="EditDevice-box-operate-box"
          @click="OperateStatus(3, '恢复出厂设置')"
          v-if="useDevice.rule.restoreFactory === 2"
        >
          <div class="EditDevice-box-operate-box-icon">
            <SvgIcon name="renew" />
          </div>

          <div class="EditDevice-box-operate-box-label">重置设备</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDeviceStore } from '@device/stores/device'
const useDevice = useDeviceStore()
import { ref, onMounted } from 'vue'
import dxInput from '@device/components/dxInput.vue'
import apiClient from '@/api/client'
import SvgIcon from '@/components/SvgIcon.vue'

onMounted(() => {
  initialize()
})

const type = ref(1)
const PasswordView = ref(false)
const WifiInfo = ref({
  wifiName: '',
  wifiPwd: '',
  hide: 1
})

// ✅ 完全复制device-an的初始化逻辑
const initialize = async () => {
  useDevice.loading = true
  
  try {
    // 获取设备详情和权限
    const deviceResponse = await apiClient.getDeviceInfo()
    if (!deviceResponse.code) {
      import('vant').then(({ showFailToast }) => {
        showFailToast(deviceResponse.msg || '获取设备信息失败')
      })
      useDevice.loading = false
      return
    }
    
    // 初始化WiFi信息
    WifiInfo.value = {
      wifiName: useDevice.details.wifiName || '',
      wifiPwd: useDevice.details.wifiPwd || '',
      hide: useDevice.details.hideStatus || 1
    }
    
    useDevice.loading = false
  } catch (error) {
    console.error('设备编辑页面初始化失败:', error)
    import('vant').then(({ showFailToast }) => {
      showFailToast('初始化失败，请稍后重试')
    })
    useDevice.loading = false
  }
}

// ✅ 完全复制device-an的频段切换逻辑
const TypeChange = (val: number) => {
  useDevice.loading = true
  if (val === 1) {
    WifiInfo.value = {
      wifiName: useDevice.details.wifiName || '',
      wifiPwd: useDevice.details.wifiPwd || '',
      hide: useDevice.details.hideStatus || 1
    }
  } else {
    WifiInfo.value = {
      wifiName: useDevice.details.wifi5gName || '',
      wifiPwd: useDevice.details.wifi5gPwd || '',
      hide: useDevice.details.hideStatus5g || 1
    }
  }
  type.value = val
  setTimeout(() => {
    useDevice.loading = false
  }, 200)
}

// ✅ 完全复制device-an的WiFi信息编辑逻辑
const EditInfo = async () => {
  useDevice.loading = true
  
  try {
    const response = await apiClient.editDeviceWiFiInfo({
      wifiName: WifiInfo.value.wifiName,
      wifiPwd: WifiInfo.value.wifiPwd,
      type: type.value
    })
    
    if (!response.code) {
      import('vant').then(({ showFailToast }) => {
        showFailToast(response.msg || 'WiFi设置失败')
      })
      useDevice.loading = false
      return
    }
    
    useDevice.loading = false
    import('vant').then(({ showSuccessToast }) => {
      showSuccessToast(response.msg || 'WiFi设置成功')
    })
  } catch (error) {
    console.error('WiFi信息编辑失败:', error)
    import('vant').then(({ showFailToast }) => {
      showFailToast('网络错误，请稍后重试')
    })
    useDevice.loading = false
  }
}

// ✅ 完全复制device-an的WiFi隐藏设置逻辑
const EditHide = async (val: number) => {
  useDevice.loading = true
  
  try {
    const response = await apiClient.editDeviceWiFiHide({
      type: type.value,
      hide: val
    })
    
    if (!response.code) {
      import('vant').then(({ showFailToast }) => {
        showFailToast(response.msg || '设置失败')
      })
      useDevice.loading = false
      return
    }
    
    useDevice.loading = false
    import('vant').then(({ showSuccessToast }) => {
      showSuccessToast(response.msg || '设置成功')
    })
    WifiInfo.value.hide = val
  } catch (error) {
    console.error('WiFi隐藏设置失败:', error)
    import('vant').then(({ showFailToast }) => {
      showFailToast('网络错误，请稍后重试')
    })
    useDevice.loading = false
  }
}

// ✅ 完全复制device-an的设备操作逻辑
const OperateStatus = (operationType: number, tip: string) => {
  import('vant').then(({ showConfirmDialog, showLoadingToast, showSuccessToast }) => {
    showConfirmDialog({
      title: '确认' + tip,
      message: '您确定要执行' + tip + '吗？'
    })
      .then(async () => {
        const toast = showLoadingToast({
          message: tip + '中...',
          forbidClick: true,
          duration: 0
        })
        
        try {
          const response = await apiClient.deviceStatusOperate(operationType)
          if (!response.code) {
            toast.close()
            import('vant').then(({ showFailToast }) => {
              showFailToast(response.msg || '操作失败')
            })
            return
          }
          
          toast.close()
          showSuccessToast(response.msg || '操作成功')
        } catch (error) {
          console.error('设备操作失败:', error)
          toast.close()
          import('vant').then(({ showFailToast }) => {
            showFailToast('网络错误，请稍后重试')
          })
        }
      })
      .catch(() => {
        // 用户取消操作
      })
  })
}
</script>

<style lang="scss" scoped>
.EditDevice {
  @include PageBox;

  &-box {
    @include WhiteBox;

    &-nav {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: $padding;
      @include PaddingBox;
      border-bottom: 0.01rem solid $border;

      &-item {
        height: 1.8rem;
        background-color: $background;
        text-align: center;
        line-height: 1.8rem;
        position: relative;
        font-size: 0.8rem;
        border-radius: 0.3rem;
        box-sizing: border-box;
        border: 0.01rem solid $background;
      }

      &-item-active {
        border-color: rgba($color: $primary, $alpha: 0.6);

        &::after {
          display: block;
          content: '';
          width: 1rem;
          height: 0.1rem;
          position: absolute;
          bottom: 0.01rem;
          left: 50%;
          transform: translateX(-50%);
          background-color: $primary;
        }
      }
    }

    &-info {
      @include PaddingBox;

      &-label {
        font-size: 0.7rem;
        margin-bottom: 0.5rem;
      }

      &-tip {
        margin-bottom: 0.5rem;
        font-size: 0.6rem;
        color: #666;
        margin-top: 0.3rem;
      }

      &-hide {
        margin-top: calc($padding * 1.5);
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: $background;
        padding: calc($padding / 1.2) $padding;
        border-radius: 0.3rem;

        &-label {
          font-size: 0.8rem;
        }

        &-switch {
          width: 2rem;
          background-color: rgb(209, 213, 219);
          height: 1rem;
          border-radius: 0.5rem;
          position: relative;
          &::after {
            content: '';
            display: block;
            width: 1rem;
            height: 1rem;
            background-color: #fff;
            border-radius: 50%;
            position: absolute;
            top: 0;
            left: 0;
            box-sizing: border-box;
            border: 0.25rem solid rgb(230, 231, 235);
          }
        }

        &-switch-active {
          background-color: $success;

          &::after {
            left: 1rem;
          }
        }
      }

      &-btn {
        margin-top: calc($padding * 1.5);
        text-align: center;
        background-color: $primary;
        height: 2rem;
        border-radius: 1rem;
        color: #fff;
        font-size: 0.75rem;
        line-height: 2rem;
      }

      &-warning {
        margin-top: calc($padding * 1.5);
        box-sizing: border-box;
        padding: $padding;
        background-color: rgb(255, 251, 235);
        color: rgb(180, 83, 10);

        &-label {
          display: flex;
          justify-content: start;
          align-items: center;
          font-weight: bold;
          font-size: 0.8rem;
          margin-bottom: 0.2rem;

          &-icon {
            position: relative;
            margin-right: 0.3rem;
          }
        }

        p {
          font-size: 0.7rem;
          margin-top: 0.2rem;
        }
      }
    }

    &-operate {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      margin-top: calc($padding * 1.5);
      @include PaddingBox;
      border-top: 0.01rem solid $border;

      &-box {
        text-align: center;

        &-icon {
          text-align: center;
          font-size: 0.8rem;
          height: 1.8rem;
          width: 1.8rem;
          margin: 0 auto;
          background-color: $primary;
          color: #fff;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 50%;
        }

        &-label {
          font-size: 0.6rem;
          margin-top: 0.5rem;
        }
      }
    }
  }
}
</style>
