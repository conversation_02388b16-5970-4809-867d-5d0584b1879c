<template>
  <div class="SetPassword">
    <div class="SetPassword_box">
      <!-- 密码输入框 -->
      <div class="SetPassword_box_tit">旧密码(默认密码:123456)</div>
      <van-password-input
        :value="value.oidPayPwd"
        :focused="show.oldShow"
        @focus="show.oldShow = true"
        :length="6"
      />

      <!-- 数字键盘 -->
      <van-number-keyboard
        v-model="value.oidPayPwd"
        :show="show.oldShow"
        @blur="show.oldShow = false"
      />
    </div>

    <div class="SetPassword_box">
      <!-- 密码输入框 -->
      <div class="SetPassword_box_tit">新密码</div>
      <van-password-input
        :value="value.newPayPwd"
        :focused="show.newShow"
        @focus="show.newShow = true"
        :length="6"
      />
      <!-- 数字键盘 -->
      <van-number-keyboard
        v-model="value.newPayPwd"
        :show="show.newShow"
        @blur="show.newShow = false"
      />
    </div>

    <div class="SetPassword-btn">
      <button @click="Submit" class="SetPassword-btn-box">立即修改</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDeviceStore } from '@device/stores/device'
const useDevice = useDeviceStore()
import { ref, onMounted } from 'vue'
import apiClient from '@/api/client'
import { useRouter } from 'vue-router'
const router = useRouter()

onMounted(() => {
  useDevice.loading = false
})

const show = ref({
  oldShow: false,
  newShow: false
})

// ✅ 完全复制device-an的支付密码数据类型
interface SetPasswordData {
  oidPayPwd: string
  newPayPwd: string
}

const value = ref<SetPasswordData>({
  oidPayPwd: '',
  newPayPwd: ''
})

// ✅ 完全复制device-an的提交逻辑
const Submit = async () => {
  if (value.value.oidPayPwd.length !== 6) {
    import('vant').then(({ showFailToast }) => {
      showFailToast('请输入6位旧密码')
    })
    return
  }

  if (value.value.newPayPwd.length !== 6) {
    import('vant').then(({ showFailToast }) => {
      showFailToast('请输入6位新密码')
    })
    return
  }

  try {
    const response = await apiClient.editPassword(value.value)
    if (!response.code) {
      import('vant').then(({ showFailToast }) => {
        showFailToast(response.msg || '修改支付密码失败')
      })
      return
    }
    
    import('vant').then(({ showSuccessToast }) => {
      showSuccessToast(response.msg || '支付密码修改成功')
    })
    router.push('/Layout')
  } catch (error) {
    console.error('修改支付密码失败:', error)
    import('vant').then(({ showFailToast }) => {
      showFailToast('网络错误，请稍后重试')
    })
  }
}
</script>

<style lang="scss" scoped>
.SetPassword {
  background-color: $background;
  box-sizing: border-box;
  padding: $padding;
  font-size: 0.8rem !important;
  height: 100vh;

  &_box {
    &_tit {
      font-size: 0.8rem !important;
      margin-top: calc($padding * 1.2);
      margin-bottom: calc($padding / 1.5);
      box-sizing: border-box;
      padding-left: 0.9rem;
    }
  }

  &-btn {
    margin-top: calc($padding * 2);
    box-sizing: border-box;
    padding: 0 0.9rem;

    &-box {
      display: block;
      border: none;
      outline: none;
      background-color: $primary;
      color: #fff;
      width: 100%;
      height: 2rem;
      border-radius: 1rem;
    }
  }
}
</style>
