import { ref } from 'vue'
import apiClient from '@/api/client'
import { closeToast } from 'vant'

// 支付参数接口
export interface PaymentParameter {
  Payment: {
    type: string
    payId: number
    key: string
    name?: string
    appid?: string
    payWay?: string
  }
  state: {
    orderNo: string
    orderType: number
  }
}

// 支付结果接口
export interface PaymentResult {
  status: number
  msg?: string
  data?: any
}

// 微信JS Bridge支付参数
interface WeixinPayParams {
  appId: string
  timeStamp: string
  nonceStr: string
  package: string
  signType: string
  paySign: string
}

/**
 * 微信支付hooks - 集成Current目录的支付逻辑
 */
export function useWeChatPayment() {
  const isProcessing = ref(false)

  // 获取URL参数
  const getQueryParams = () => {
    const urlParams = new URLSearchParams(window.location.search)
    const params: Record<string, string> = {}
    urlParams.forEach((value, key) => {
      params[key] = value
    })
    return params
  }

  // 检测是否在微信浏览器中
  const isWeixinBrowser = () => {
    return /micromessenger/i.test(navigator.userAgent)
  }

  // 微信支付Bridge调用
  const onBridgeReady = async (payValue: WeixinPayParams): Promise<PaymentResult> => {
    return new Promise((resolve, reject) => {
      if (typeof window.WeixinJSBridge === 'undefined') {
        reject({ status: 0, msg: '微信支付环境异常' })
        return
      }

      window.WeixinJSBridge.invoke(
        'getBrandWCPayRequest',
        payValue,
        function (res: any) {
          switch (res.err_msg) {
            case 'get_brand_wcpay_request:ok':
              resolve({
                status: 1,
                msg: '支付成功',
                data: res
              })
              break
            case 'get_brand_wcpay_request:cancel':
              reject({
                status: 0,
                msg: '取消支付'
              })
              break
            case 'get_brand_wcpay_request:fail':
              reject({
                status: 0,
                msg: '支付失败'
              })
              break
            default:
              reject({
                status: 0,
                msg: res.err_msg || '支付异常'
              })
              break
          }
        }
      )
    })
  }

  // 获取微信code换取openid
  const getWeChatOpenId = async (code: string, appId: string): Promise<string | null> => {
    try {
      const response = await apiClient.getWeChatOpenId({ code, appId })
      
      if (!response.code) {
        throw new Error(response.msg || '获取OpenID失败')
      }
      
      return response.data.openid
    } catch (error) {
      console.error('获取OpenID失败:', error)
      return null
    }
  }

  // 微信授权获取code
  const getWeChatCode = (appid: string, state: string) => {
    const currentUrl = window.location.origin + window.location.pathname
    const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?redirect_uri=${currentUrl}&response_type=code&scope=snsapi_base&appid=${appid}&state=${state}`
    window.location.href = authUrl
  }

  // 获取支付参数
  const getPaymentParameter = async (params: {
    notifyUrl: string
    orderType: number
    payType: string
    orderNo: string
    type: number
    payWay?: string
    payId: number
    openId?: string
    h5InfoType?: string
  }) => {
    try {
      const response = await apiClient.getPaymentParameter(params)
      if (!response.code) {
        throw new Error(response.msg || '获取支付参数失败')
      }
      return response
    } catch (error) {
      console.error('获取支付参数失败:', error)
      throw error
    }
  }

  // 处理微信内支付
  const handleWeixinPay = async (
    weChatPaymentParams: any,
    orderInfo: { orderNo: string; orderType: number }
  ): Promise<PaymentResult> => {
    try {
      isProcessing.value = true
      const urlParams = getQueryParams()
      
      let openId: string | null = null

      // 如果有code，交换openid
      if (urlParams.code) {
        openId = await getWeChatOpenId(urlParams.code, weChatPaymentParams.appid)
        if (!openId) {
          throw new Error('获取用户授权失败')
        }
      } else {
        // 没有code，跳转授权
        getWeChatCode(weChatPaymentParams.appid, JSON.stringify(orderInfo))
        return { status: 0, msg: '正在跳转微信授权...' }
      }

      // 获取支付参数
      const paymentData = await getPaymentParameter({
        notifyUrl: window.location.protocol + '//' + window.location.hostname,
        orderType: orderInfo.orderType,
        payType: weChatPaymentParams.type,
        openId: openId!,
        orderNo: orderInfo.orderNo,
        type: 1,
        payWay: weChatPaymentParams.payWay,
        payId: weChatPaymentParams.payId,
        h5InfoType: 'Wap'
      })

      // 调用微信支付
      const result = await onBridgeReady(paymentData.data)
      
      // 支付成功，跳转回调地址
      if (result.status === 1) {
        setTimeout(() => {
          window.location.href = weChatPaymentParams.callBackUrl || '/home'
        }, 1000)
      }

      return result
    } catch (error: any) {
      console.error('微信支付失败:', error)
      return {
        status: 0,
        msg: error.message || '支付失败'
      }
    } finally {
      isProcessing.value = false
    }
  }

  // 处理非微信环境支付
  const handleH5Pay = async (
    weChatPaymentParams: any,
    orderInfo: { orderNo: string; orderType: number }
  ): Promise<PaymentResult> => {
    try {
      isProcessing.value = true

      const paymentData = await getPaymentParameter({
        notifyUrl: window.location.protocol + '//' + window.location.hostname,
        orderType: orderInfo.orderType,
        payType: weChatPaymentParams.type,
        orderNo: orderInfo.orderNo,
        type: 4,
        payWay: weChatPaymentParams.payWay,
        payId: weChatPaymentParams.payId,
        h5InfoType: 'Wap'
      })

      // 跳转到外部支付页面
      const redirectUrl = weChatPaymentParams.callBackUrl || '/home'
      window.location.href = `${paymentData.data}&redirect_url=${redirectUrl}`

      return { status: 1, msg: '正在跳转支付页面...' }
    } catch (error: any) {
      console.error('H5支付失败:', error)
      return {
        status: 0,
        msg: error.message || '支付失败'
      }
    } finally {
      isProcessing.value = false
    }
  }

  // 主要的支付处理函数 - 替代Current目录的功能
  const processPayment = async (): Promise<PaymentResult> => {
    try {
      const urlParams = getQueryParams()
      const weChatPaymentParams = JSON.parse(
        window.localStorage.getItem('WeChatPayment') || '{}'
      )

      if (!urlParams.state || !weChatPaymentParams.appid) {
        throw new Error('支付参数错误')
      }

      // 解析订单信息
      let orderInfo: { orderNo: string; orderType: number }
      try {
        orderInfo = urlParams.code 
          ? JSON.parse(decodeURIComponent(urlParams.state))
          : JSON.parse(urlParams.state)
      } catch (error) {
        throw new Error('订单信息解析失败')
      }

      // 根据环境选择支付方式
      if (isWeixinBrowser()) {
        return await handleWeixinPay(weChatPaymentParams, orderInfo)
      } else {
        return await handleH5Pay(weChatPaymentParams, orderInfo)
      }
    } catch (error: any) {
      console.error('支付处理失败:', error)
      return {
        status: 0,
        msg: error.message || '支付处理失败'
      }
    }
  }

  return {
    isProcessing,
    processPayment,
    isWeixinBrowser,
    getQueryParams
  }
}

/**
 * 支付处理hooks - 处理所有支付方式
 */
export function usePayment() {
  const wechatPayment = useWeChatPayment()

  // 处理支付逻辑 - 替代HandlePayment函数
  const handlePayment = async (params: PaymentParameter): Promise<PaymentResult> => {
    return new Promise((resolve) => {
      try {
        // 微信支付
        if (params.Payment.appid && params.Payment.appid !== 'NOT_APPID') {
          const weChatPaymentParams = Object.assign({}, params.Payment, {
            callBackUrl: window.location.origin + '/home'
          })
          
          // 存储支付参数到localStorage
          window.localStorage.setItem('WeChatPayment', JSON.stringify(weChatPaymentParams))
          closeToast()
          
          // 创建支付页面URL
          const paymentUrl = new URL(window.location.origin + '/wechat-payment')
          paymentUrl.searchParams.set('state', JSON.stringify(params.state))
          
          // 跳转到微信支付处理页面
          window.location.href = paymentUrl.toString()
          
          resolve({ status: 1, msg: '正在跳转微信支付...' })
        }
        // 支付宝支付或其他第三方支付
        else {
          apiClient.getPaymentParameter({
            notifyUrl: window.location.protocol + '//' + window.location.hostname,
            orderType: params.state.orderType,
            payType: params.Payment.type,
            orderNo: params.state.orderNo,
            type: 1,
            payWay: params.Payment.payWay,
            payId: params.Payment.payId
          }).then(({ code, data, msg }) => {
            if (!code) {
              resolve({ status: 0, msg: msg || '支付失败' })
              return
            }
            
            closeToast()
            const userAgent = window.navigator.userAgent.toLowerCase()
            const isIOS = /iphone|ipad|ipod/.test(userAgent)
            
            // iPhone且是支付宝官方支付直接跳转
            if (isIOS && params.Payment.type === 'alipay') {
              window.location.href = data
              resolve({ status: 1, msg: '正在跳转支付宝...' })
              return
            }
            
            // appid为{NOT_APPID}直接转入链接
            if (params.Payment.appid === 'NOT_APPID') {
              if (typeof data === 'string') {
                window.location.href = data
              } else {
                // 处理扫码支付
                let qrcodeUrl = window.location.origin + '/#/device/qrcode-payment'
                if (data.type === '1') {
                  qrcodeUrl += '?complex=' + encodeURIComponent(JSON.stringify(data))
                } else {
                  window.location.href = data.url
                  return
                }
                window.location.href = qrcodeUrl
              }
              resolve({ status: 1, msg: '正在跳转支付页面...' })
              return
            }
            
            // 第三方支付处理
            let qrcodeUrl = window.location.origin + '/#/device/qrcode-payment'
            if (typeof data === 'string') {
              qrcodeUrl += '?url=' + encodeURIComponent(data)
            } else {
              if (data.type === '1') {
                qrcodeUrl += '?complex=' + encodeURIComponent(JSON.stringify(data))
              } else {
                window.location.href = data.url
                return
              }
            }
            window.location.href = qrcodeUrl
            resolve({ status: 1, msg: '正在跳转支付页面...' })
          }).catch((error) => {
            closeToast()
            resolve({ status: 0, msg: error.message || '支付失败，请稍后重试' })
          })
        }
      } catch (error: any) {
        closeToast()
        resolve({ status: 0, msg: error.message || '支付处理失败' })
      }
    })
  }

  return {
    handlePayment,
    ...wechatPayment
  }
}

// 声明微信JS Bridge的类型
declare global {
  interface Window {
    WeixinJSBridge: {
      invoke: (method: string, params: any, callback: (result: any) => void) => void
    }
  }
}