/**
 * 商城业务模块
 * 
 * 电商购物业务的完整模块实现：
 * 1. 模块配置和生命周期
 * 2. 页面和路由定义
 * 3. 状态管理集成
 * 4. API服务集成
 */

import type { RouteRecordRaw } from 'vue-router'
import type {
  Module,
  ModuleConfig,
  ModuleContext,
  ModulePageConfig,
  ModuleApiConfig
} from '../types'
import { MODULE_PRIORITY } from '../types'
// 路由配置现在由统一路由系统管理，不再需要单独的routes文件
const mallRoutes: any[] = []
const configurablePages: string[] = []
const staticPages: string[] = []

// 商城模块配置
const mallModuleConfig: ModuleConfig = {
  id: 'mall',
  name: '商城系统',
  version: '1.0.0',
  description: '电商购物业务模块',
  author: 'lowcode-team',
  
  // 业务配置
  business: 'mall',
  enabled: false, // 默认未启用
  priority: MODULE_PRIORITY.BUSINESS,
  
  // 依赖配置
  dependencies: [],
  optionalDependencies: ['device'], // 可选依赖设备模块
  
  // 路由配置
  routePrefix: '/mall',
  
  // 页面配置 (从路由配置推导)
  pages: configurablePages.map(path => ({
    path,
    name: path.replace('/mall/', 'Mall'),
    component: path.replace('/mall/', ''),
    configurable: true,
    meta: { title: '可配置页面' }
  })),
  
  defaultPage: '/mall/home',
  
  // API配置
  apis: [
    {
      name: 'mall-api',
      baseURL: '/api/mall',
      timeout: 8000,
      headers: {
        'Content-Type': 'application/json'
      }
    },
    {
      name: 'payment-api',
      baseURL: '/api/payment',
      timeout: 15000
    }
  ],
  
  // 权限配置
  permissions: [
    'mall.read',
    'mall.write',
    'mall.order',
    'mall.payment'
  ],
  
  // 自定义配置
  custom: {
    theme: {
      primaryColor: '#ff6b35',
      backgroundColor: '#ffffff'
    },
    features: {
      shoppingCart: true,
      wishlist: true,
      reviews: true,
      recommendations: true
    }
  }
}

// 商城模块实现
export class MallModule implements Module {
  config = mallModuleConfig
  status: 'unloaded' | 'loading' | 'loaded' | 'error' | 'unloading' = 'unloaded'
  
  private context?: ModuleContext
  private stores: any[] = []
  private components: any[] = []

  /**
   * 模块安装
   */
  async install(context: ModuleContext): Promise<void> {
    this.context = context
    
    console.log('🛒 [MallModule] 开始安装商城模块...')
    
    // 注册状态管理
    await this.registerStores()
    
    // 注册组件
    await this.registerComponents()
    
    // 初始化API服务
    await this.initializeApis()
    
    // 设置事件监听
    this.setupEventListeners()
    
    console.log('✅ [MallModule] 商城模块安装完成')
  }

  /**
   * 模块卸载
   */
  async uninstall(): Promise<void> {
    console.log('🔄 [MallModule] 开始卸载商城模块...')
    
    // 清理事件监听
    this.cleanupEventListeners()
    
    // 清理状态管理
    this.cleanupStores()
    
    // 清理组件
    this.cleanupComponents()
    
    console.log('✅ [MallModule] 商城模块卸载完成')
  }

  /**
   * 获取路由配置
   */
  getRoutes(): RouteRecordRaw[] {
    // 直接返回从路由文件导入的路由配置
    return mallRoutes
  }

  /**
   * 获取页面配置
   */
  getPages(): ModulePageConfig[] {
    return this.config.pages || []
  }

  /**
   * 获取API配置
   */
  getApis(): ModuleApiConfig[] {
    return this.config.apis || []
  }

  /**
   * 获取状态管理
   */
  getStores(): any[] {
    return this.stores
  }

  /**
   * 获取组件
   */
  getComponents(): any[] {
    return this.components
  }

  /**
   * 注册状态管理
   */
  private async registerStores(): Promise<void> {
    // 这里可以注册商城相关的Pinia stores
    // 例如：productsStore, cartStore, ordersStore等
    console.log('📊 [MallModule] 注册状态管理...')
  }

  /**
   * 注册组件
   */
  private async registerComponents(): Promise<void> {
    // 这里可以注册商城相关的Vue组件
    // 例如：ProductCard, CartItem, OrderItem等
    console.log('🧩 [MallModule] 注册组件...')
  }

  /**
   * 初始化API服务
   */
  private async initializeApis(): Promise<void> {
    // 这里可以初始化商城相关的API服务
    console.log('🔌 [MallModule] 初始化API服务...')
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    if (!this.context) return

    const emitter = this.context.eventBus.createModuleEmitter('mall')
    
    // 监听商城相关事件
    this.context.eventBus.on('mall:add-to-cart', (event) => {
      console.log('🛒 [MallModule] 收到添加购物车事件:', event)
    })

    this.context.eventBus.on('mall:checkout', (event) => {
      console.log('💳 [MallModule] 收到结算事件:', event)
    })

    this.context.eventBus.on('mall:order-created', (event) => {
      console.log('📦 [MallModule] 收到订单创建事件:', event)
    })
  }

  /**
   * 清理事件监听
   */
  private cleanupEventListeners(): void {
    if (!this.context) return

    this.context.eventBus.off('mall:add-to-cart')
    this.context.eventBus.off('mall:checkout')
    this.context.eventBus.off('mall:order-created')
  }

  /**
   * 清理状态管理
   */
  private cleanupStores(): void {
    this.stores = []
  }

  /**
   * 清理组件
   */
  private cleanupComponents(): void {
    this.components = []
  }
}

// 导出模块实例
export const mallModule = new MallModule()

// 默认导出
export default {
  config: mallModuleConfig,
  factory: async (context: ModuleContext) => {
    console.log('🏭 [MallModule] 工厂函数被调用，创建商城模块实例')
    return mallModule
  }
}
