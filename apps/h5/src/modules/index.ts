/**
 * H5模块系统统一导出
 * 
 * 提供完整的模块化架构支持：
 * 1. 模块管理器 - 统一管理所有业务模块
 * 2. 模块加载器 - 模块生命周期管理
 * 3. 事件总线 - 模块间通信
 * 4. 类型定义 - 完整的TypeScript支持
 */

// 核心类导出
export { ModuleManager, createModuleManager, getModuleManager } from './ModuleManager'
export { ModuleLoaderImpl } from './ModuleLoader'
export { ModuleEventBusImpl, moduleEventBus } from './ModuleEventBus'

// 业务模块导出
export { DeviceModule, deviceModule } from './device'
export { MallModule, mallModule } from './mall'

// 类型定义导出
export type {
  Module,
  ModuleConfig,
  ModuleContext,
  ModulePageConfig,
  ModuleApiConfig,
  ModuleLoader,
  ModuleEventBus,
  ModuleEvent,
  ModuleRegistration,
  ModuleLoadOptions,
  ModuleStats,
  ModuleStatus,
  ModuleLifecycle,
  ModuleDefinition,
  ModuleExports,
  ModuleFactory
} from './types'

// 常量导出
export { MODULE_EVENTS, MODULE_PRIORITY, ModuleError } from './types'

// 工具函数
export const ModuleUtils = {
  /**
   * 创建模块事件
   */
  createEvent(type: string, source: string, data?: any, target?: string) {
    return {
      type,
      source,
      target,
      data,
      timestamp: Date.now()
    }
  },

  /**
   * 验证模块配置
   */
  validateModuleConfig(config: any): boolean {
    return !!(
      config &&
      config.id &&
      config.name &&
      config.version &&
      config.business
    )
  },

  /**
   * 获取模块优先级描述
   */
  getPriorityDescription(priority: number): string {
    if (priority >= 1000) return '系统级'
    if (priority >= 900) return '核心级'
    if (priority >= 500) return '业务级'
    if (priority >= 100) return '插件级'
    return '自定义级'
  },

  /**
   * 格式化模块状态
   */
  formatModuleStatus(status: ModuleStatus): string {
    const statusMap = {
      'unloaded': '未加载',
      'loading': '加载中',
      'loaded': '已加载',
      'error': '错误',
      'unloading': '卸载中'
    }
    return statusMap[status] || status
  },

  /**
   * 计算模块依赖树
   */
  buildDependencyTree(modules: Module[]): Record<string, string[]> {
    const tree: Record<string, string[]> = {}
    
    modules.forEach(module => {
      const dependencies = module.config.dependencies || []
      tree[module.config.id] = dependencies
    })
    
    return tree
  },

  /**
   * 检查循环依赖
   */
  checkCircularDependencies(modules: Module[]): string[] {
    const tree = this.buildDependencyTree(modules)
    const visited = new Set<string>()
    const recursionStack = new Set<string>()
    const cycles: string[] = []

    const dfs = (moduleId: string, path: string[] = []): void => {
      if (recursionStack.has(moduleId)) {
        cycles.push(`循环依赖: ${path.join(' -> ')} -> ${moduleId}`)
        return
      }

      if (visited.has(moduleId)) return

      visited.add(moduleId)
      recursionStack.add(moduleId)

      const dependencies = tree[moduleId] || []
      dependencies.forEach(depId => {
        dfs(depId, [...path, moduleId])
      })

      recursionStack.delete(moduleId)
    }

    Object.keys(tree).forEach(moduleId => {
      if (!visited.has(moduleId)) {
        dfs(moduleId)
      }
    })

    return cycles
  }
}

// 模块系统初始化函数
export const initializeModuleSystem = async (app: any, router: any) => {
  console.log('🚀 [ModuleSystem] 初始化模块系统...')

  try {
    // 动态导入模块管理器创建函数
    const { createModuleManager } = await import('./ModuleManager')

    // 创建模块管理器
    const moduleManager = createModuleManager(app, router)
    
    // 初始化模块管理器
    await moduleManager.initialize()
    
    // 在开发环境下挂载到全局
    if (import.meta.env.DEV) {
      // 动态导入事件总线
      const { moduleEventBus } = await import('./ModuleEventBus')

      ;(window as any).__MODULE_SYSTEM__ = {
        moduleManager,
        eventBus: moduleEventBus,
        utils: ModuleUtils
      }
      console.log('🔧 [ModuleSystem] 开发模式：模块系统已挂载到 window.__MODULE_SYSTEM__')
    }
    
    console.log('✅ [ModuleSystem] 模块系统初始化完成')
    return moduleManager
    
  } catch (error) {
    console.error('❌ [ModuleSystem] 模块系统初始化失败:', error)
    throw error
  }
}

// 获取模块系统统计信息
export const getModuleSystemStats = async () => {
  try {
    // 动态导入需要的函数
    const { getModuleManager } = await import('./ModuleManager')
    const { moduleEventBus } = await import('./ModuleEventBus')

    const moduleManager = getModuleManager()
    const stats = moduleManager.getStats()
    const eventStats = moduleEventBus.getStats()

    return {
      modules: stats,
      events: eventStats,
      definitions: moduleManager.getModuleDefinitions().length,
      loaded: moduleManager.getLoadedModules().length,
      timestamp: Date.now()
    }
  } catch (error) {
    console.error('获取模块系统统计信息失败:', error)
    return null
  }
}

// 默认导出
export default {
  ModuleUtils,
  initializeModuleSystem,
  getModuleSystemStats
}
