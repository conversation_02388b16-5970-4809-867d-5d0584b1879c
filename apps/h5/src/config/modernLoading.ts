// 现代化Loading配置
export interface ModernLoadingConfig {
  // 核心配置
  type: 'minimal' | 'glass' | 'neon' | 'particle' | 'morphing' | 'breathing' | 'fluid'
  size: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  
  // 视觉设计
  theme: 'light' | 'dark' | 'auto'
  accentColor: string
  blur: boolean
  opacity: number
  
  // 动画配置
  duration: number
  easing: 'ease' | 'elastic' | 'bounce' | 'linear'
  
  // 布局
  position: 'center' | 'top' | 'inline'
  backdrop: boolean
  backdropBlur: number
  
  // 交互
  cancelable: boolean
  timeout?: number
  
  // 高级特效
  glassmorphism: boolean
  neomorphism: boolean
  particles: boolean
  glow: boolean
}

// 现代化默认配置
export const modernDefaults: ModernLoadingConfig = {
  type: 'minimal',
  size: 'md',
  theme: 'auto',
  accentColor: '#3B82F6', // 现代蓝
  blur: true,
  opacity: 0.95,
  duration: 1000,
  easing: 'ease',
  position: 'center',
  backdrop: true,
  backdropBlur: 20,
  cancelable: false,
  glassmorphism: true,
  neomorphism: false,
  particles: false,
  glow: true
}

// 现代化主题预设
export const modernThemes = {
  // 🌟 极简主义
  minimal: {
    type: 'minimal',
    accentColor: '#1A1A1A',
    theme: 'light',
    glassmorphism: false,
    blur: false,
    backdrop: true,
    backdropBlur: 8
  },

  // 💎 玻璃态
  glass: {
    type: 'glass',
    accentColor: '#3B82F6',
    theme: 'auto',
    glassmorphism: true,
    blur: true,
    backdrop: true,
    backdropBlur: 24,
    glow: true
  },

  // ⚡ 霓虹科技
  neon: {
    type: 'neon',
    accentColor: '#00F5FF',
    theme: 'dark',
    glassmorphism: false,
    glow: true,
    backdrop: true,
    backdropBlur: 16,
    particles: true
  },

  // 🌊 流体动效
  fluid: {
    type: 'fluid',
    accentColor: '#8B5CF6',
    theme: 'auto',
    glassmorphism: true,
    blur: true,
    backdrop: true,
    backdropBlur: 20,
    easing: 'elastic'
  },

  // 🎯 呼吸灯
  breathing: {
    type: 'breathing',
    accentColor: '#10B981',
    theme: 'auto',
    glassmorphism: true,
    glow: true,
    backdrop: true,
    backdropBlur: 12,
    easing: 'ease'
  },

  // 🔮 变形
  morphing: {
    type: 'morphing',
    accentColor: '#F59E0B',
    theme: 'auto',
    glassmorphism: true,
    blur: true,
    backdrop: true,
    backdropBlur: 18,
    easing: 'elastic'
  },

  // 🌌 粒子
  particle: {
    type: 'particle',
    accentColor: '#EC4899',
    theme: 'dark',
    particles: true,
    glow: true,
    backdrop: true,
    backdropBlur: 20,
    glassmorphism: false
  }
} as const

// 业务场景现代化主题
export const modernBusinessThemes = {
  auth: {
    ...modernThemes.glass,
    accentColor: '#3B82F6',
    size: 'lg' as const,
    timeout: 8000
  },
  
  payment: {
    ...modernThemes.breathing,
    accentColor: '#059669',
    size: 'md' as const,
    timeout: 15000
  },
  
  upload: {
    ...modernThemes.fluid,
    accentColor: '#7C3AED',
    size: 'lg' as const
  },
  
  processing: {
    ...modernThemes.minimal,
    accentColor: '#374151',
    size: 'sm' as const
  },
  
  ai: {
    ...modernThemes.neon,
    accentColor: '#00D4FF',
    size: 'xl' as const,
    particles: true
  }
} as const

// 当前应用主题 - 现代化设置
export const MODERN_LOADING_THEME = 'glass' // 默认使用玻璃态
export const MODERN_SIZE = 'md'
export const MODERN_DARK_MODE = 'auto' // auto | light | dark

// 获取当前现代化主题
export function getModernTheme(): ModernLoadingConfig {
  const baseTheme = modernThemes[MODERN_LOADING_THEME as keyof typeof modernThemes]
  return {
    ...modernDefaults,
    ...baseTheme,
    size: MODERN_SIZE as any,
    theme: MODERN_DARK_MODE as any
  }
}

// 获取业务主题
export function getModernBusinessTheme(scene: keyof typeof modernBusinessThemes): ModernLoadingConfig {
  return {
    ...modernDefaults,
    ...modernBusinessThemes[scene]
  }
}