// 全局Loading配置
export interface LoadingConfig {
  // 基础配置
  type: 'spinner' | 'dots' | 'wave' | 'pulse' | 'bounce' | 'circular' | 'gradient'
  size: 'small' | 'medium' | 'large'
  color: string
  backgroundColor: string
  text?: string
  textColor?: string
  
  // 动画配置
  duration: number // 动画持续时间(ms)
  overlay: boolean // 是否显示遮罩
  overlayOpacity: number // 遮罩透明度 0-1
  
  // 位置配置
  position: 'center' | 'top' | 'bottom'
  zIndex: number
  
  // 高级配置
  showProgress?: boolean // 是否显示进度
  customIcon?: string // 自定义图标
  gradient?: {
    from: string
    to: string
    direction: string
  }
}

// 默认配置
export const defaultLoadingConfig: LoadingConfig = {
  type: 'spinner',
  size: 'medium',
  color: '#1989fa',
  backgroundColor: 'rgba(255, 255, 255, 0.9)',
  duration: 1200,
  overlay: true,
  overlayOpacity: 0.7,
  position: 'center',
  zIndex: 9999,
  textColor: '#666',
  showProgress: false
}

// 预设主题
export const loadingThemes = {
  // 蓝色科技风
  tech: {
    type: 'circular' as const,
    color: '#00d4ff',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    text: '加载中...',
    textColor: '#00d4ff',
    gradient: {
      from: '#00d4ff',
      to: '#0099cc',
      direction: '45deg'
    }
  },
  
  // 绿色清新风  
  fresh: {
    type: 'wave' as const,
    color: '#52c41a',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    text: '请稍候...',
    textColor: '#52c41a'
  },
  
  // 紫色渐变风
  gradient: {
    type: 'gradient' as const,
    color: '#722ed1',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    text: '正在处理...',
    textColor: '#fff',
    gradient: {
      from: '#722ed1',
      to: '#eb2f96',
      direction: '135deg'
    }
  },
  
  // 橙色活力风
  vibrant: {
    type: 'bounce' as const,
    color: '#fa8c16',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    text: '加载中...',
    textColor: '#fa8c16'
  },
  
  // 简约黑白风
  minimal: {
    type: 'dots' as const,
    color: '#000',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    text: '',
    textColor: '#000'
  }
}

// 应用配置 - 可以在这里切换主题
export const currentLoadingConfig: LoadingConfig = {
  ...defaultLoadingConfig,
  ...loadingThemes.tech, // 当前使用科技风主题
  size: 'large'
}