/**
 * H5应用环境变量配置
 * 统一管理所有环境变量，避免硬编码
 */

// 环境变量配置
export const ENV_CONFIG = {
  // 基础配置
  NODE_ENV: import.meta.env.NODE_ENV || 'development',
  IS_DEV: import.meta.env.NODE_ENV === 'development',
  IS_PROD: import.meta.env.NODE_ENV === 'production',
  
  // 服务配置
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002',
  API_PREFIX: import.meta.env.VITE_API_PREFIX || '/api',
  PUBLIC_API_PREFIX: import.meta.env.VITE_PUBLIC_API_PREFIX || '/api/public',
  
  // 其他服务
  DESIGNER_BASE_URL: import.meta.env.VITE_DESIGNER_BASE_URL || 'http://localhost:3001',
  WS_URL: import.meta.env.VITE_WS_URL || 'ws://localhost:3002',
  
  // 调试配置
  DEBUG: import.meta.env.VITE_DEBUG === 'true' || import.meta.env.VITE_DEBUG_MODE === 'true',
  LOG_LEVEL: import.meta.env.VITE_LOG_LEVEL || 'debug',
  
  // 应用配置
  APP_TITLE: import.meta.env.VITE_APP_TITLE || '低代码H5应用',
  APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  
  // 兼容旧版本配置
  DEVICE_AN_API_URL: import.meta.env.VITE_DEVICE_AN_API_URL,
  LOWCODE_API_URL: import.meta.env.VITE_LOWCODE_API_URL,
} as const

// URL构建工具
export const URL_BUILDER = {
  // 构建API URL
  api: (path: string) => {
    const baseUrl = ENV_CONFIG.API_BASE_URL.replace(/\/$/, '')
    const apiPath = path.startsWith('/') ? path : `/${path}`
    return `${baseUrl}${ENV_CONFIG.API_PREFIX}${apiPath}`
  },
  
  // 构建公共API URL
  publicApi: (path: string) => {
    const baseUrl = ENV_CONFIG.API_BASE_URL.replace(/\/$/, '')
    const apiPath = path.startsWith('/') ? path : `/${path}`
    return `${baseUrl}${ENV_CONFIG.PUBLIC_API_PREFIX}${apiPath}`
  },
  
  // 构建设计器 URL
  designer: (path: string = '') => {
    const baseUrl = ENV_CONFIG.DESIGNER_BASE_URL.replace(/\/$/, '')
    const designerPath = path.startsWith('/') ? path : `/${path}`
    return `${baseUrl}${designerPath}`
  },
  
  // 构建WebSocket URL
  ws: (path: string = '') => {
    const baseUrl = ENV_CONFIG.WS_URL.replace(/\/$/, '')
    const wsPath = path.startsWith('/') ? path : `/${path}`
    return `${baseUrl}${wsPath}`
  },
  
  // 获取当前页面的基础URL（用于生产环境的相对路径）
  getCurrentBase: () => {
    if (typeof window !== 'undefined') {
      return `${window.location.protocol}//${window.location.host}`
    }
    return ENV_CONFIG.API_BASE_URL
  }
}

// 环境检查工具
export const ENV_UTILS = {
  isDevelopment: () => ENV_CONFIG.IS_DEV,
  isProduction: () => ENV_CONFIG.IS_PROD,
  isDebugMode: () => ENV_CONFIG.DEBUG,
  getLogLevel: () => ENV_CONFIG.LOG_LEVEL,
}

// 开发环境下打印配置信息
if (ENV_CONFIG.IS_DEV && ENV_CONFIG.DEBUG) {
  console.log('🔧 H5应用环境配置:', ENV_CONFIG)
}
