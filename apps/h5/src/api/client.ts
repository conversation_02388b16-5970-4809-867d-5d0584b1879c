/**
 * H5应用API客户端
 * 基于新的API架构，提供简洁的业务API接口
 */

import { apiRequest } from '@lowcode/aslib/core'

// ==================== 配置类型 ====================

interface APIResponse<T> {
  code: number | boolean
  data: T
  msg: string
  success?: boolean
}

// ==================== API客户端类 ====================

class APIClient {
  constructor() {
    console.log('🚀 H5 API客户端初始化 - 基于统一架构')
  }

  // 简化的API请求方法 - 完全依赖新架构的认证
  private async request<T>(config: {
    url: string
    method: 'GET' | 'POST' | 'PUT' | 'DELETE'
    data?: any
    params?: any
  }): Promise<APIResponse<T>> {
    try {
      console.log(`📡 [H5 API] ${config.method} ${config.url}`)

      // 直接使用新架构的apiRequest，认证由适配器处理
      const response = await apiRequest<T>(config)

      console.log(`✅ [H5 API] 请求成功:`, response)
      return response as APIResponse<T>
    } catch (error) {
      console.error(`❌ [H5 API] 请求失败:`, error)
      throw error
    }
  }

  // ==================== 应用配置相关API ====================

  async getAppConfig(appId: string): Promise<APIResponse<any>> {
    return this.request({
      url: `/public/app/${appId}`,
      method: 'GET'
    })
  }

  async getAppPages(appId: string): Promise<APIResponse<any[]>> {
    return this.request({
      url: `/public/app/${appId}/pages`,
      method: 'GET'
    })
  }

  async getPageConfig(pageId: string): Promise<APIResponse<any>> {
    return this.request({
      url: `/public/page/${pageId}`,
      method: 'GET'
    })
  }

  // ==================== 设备相关API ====================

  async getDeviceInfo(): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/device/getDeviceInfo',
      method: 'GET'
    })
  }

  async updateDevice(): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/device/updateDevice',
      method: 'PUT'
    })
  }

  async getDeviceCards(): Promise<APIResponse<any[]>> {
    return this.request({
      url: '/frontDevice/device/getDeviceCardList',
      method: 'GET'
    })
  }

  async deviceStatusOperate(operationType: number): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/device/statusOperate',
      method: 'POST',
      data: { operationType }
    })
  }

  // ==================== 套餐相关API ====================

  async getPackageList(params?: { packType?: number } | { classId?: number }): Promise<APIResponse<any[]>> {
    return this.request({
      url: '/frontDevice/package/getPackageList',
      method: 'GET',
      params
    })
  }

  // ==================== 余额相关API ====================

  async getBalanceList(): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/prestore/getPrestoreLimit',
      method: 'GET'
    })
  }

  async getBalanceDetails(params: { page: number; pageSize: number }): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/deviceBalance/getList',
      method: 'GET',
      params
    })
  }

  async getBalanceTemplate(): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/device/getBalanceList',
      method: 'GET'
    })
  }

  async createBalanceOrder(prestoreId: number): Promise<APIResponse<any>> {
    const formData = new FormData()
    formData.append('prestoreId', prestoreId.toString())
    return this.request({
      url: '/frontDevice/prestore/notPayOrder',
      method: 'POST',
      data: formData
    })
  }

  async getMonthBalanceData(): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/deviceBalance/getMonthData',
      method: 'GET'
    })
  }

  // ==================== 支付相关API ====================

  async getPaymentMethods(params: { userId: number; type: number }): Promise<APIResponse<any[]>> {
    return this.request({
      // url: '/frontDevice/payment/getPaymentMethods',
      url: '/front/index/getPayUse',
      method: 'GET',
      params
    })
  }

  async createPayment(params: {
    orderType: number
    payType: string
    orderNo: string
    notifyUrl: string
    payId: number
    type?: number
    payWay?: string
  }): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/payment/create',
      method: 'POST',
      data: params
    })
  }

  async balancePaymentOrder(params: {
    orderNo: string
    payPwd?: string
    code?: string
  }): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/payment/balancePayment',
      method: 'POST',
      data: params
    })
  }

  // ==================== 实名认证相关API ====================

  async getDeviceCardRealNameAddress(params: {
    deviceId: number
    number: number
  }): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/realname/getAddress',
      method: 'GET',
      params
    })
  }

  // ==================== 套餐订单相关API ====================

  async createPackageOrder(params: {packageId: number; takeeffectType: number; cycleId: number | null }): Promise<APIResponse<any>> {
    const formData = new FormData
    formData.append('packageId', params.packageId.toString())
    formData.append('takeeffectType', params.takeeffectType.toString())
    formData.append('cycleId', params.cycleId ? params.cycleId.toString() : '')
    return this.request({
      url: '/frontDevice/package/notPayOrder',
      method: 'POST',
      data: formData
    })
  }

  async getPackageOrder(params: { orderId: string }): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/package/getOrder',
      method: 'GET',
      params
    })
  }

  async getPackageOrderRule(packageId: number): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/devicePackage/getDevicePackageGz',
      method: 'GET',
      params: { packageId }
    })
  }

  async getPackagePaymentMethods(packageId: number): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/pay/getPayList',
      method: 'GET',
       params: { packageId }
    })
  }

  async getPackageOrderDetails(params: { packageId: number; takeeffectType: number; cycleId: number|null }): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/package/getPackageHuiXian',
      method: 'GET',
       params
    })
  }
  async getPaymentParameter(params: {
  orderType: number //支付场景  9设备购买预存 10设备购买套餐 11设备购买优惠券
  payType: string //支付方式
  orderNo: string //订单号
  notifyUrl: string //域名
  payId: number //商户ID
  type?: number //第三方支付类型
  payWay?: string //第四方支付类型
  openId?: string //微信OPENID
}): Promise<APIResponse<any>> {
    return this.request({
      url: '/pay/getPayParameter',
      method: 'GET',
       params
    })
  }

  //   GetPaymentMethods = (params: PaymentMethods) => {
  //   return requestJava({
  //     url: 'front/index/getPayUse',
  //     method: 'get',
  //     params
  //   })
  // }

  async getPackageOrderList(params: { page: number; pageSize: number; orderState?: number }): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/devicePackageOrder/getPackOrderList',
      method: 'GET',
      params
    })
  }

  async balancePaymentPackageOrder(params: {
    orderNo: string
    payPwd?: string
    code?: string
  }): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/payment/balancePaymentPackage',
      method: 'POST',
      data: params
    })
  }

  // ==================== 其他API ====================

  async sendSmsCode(params: { type: string }): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/sms/send',
      method: 'POST',
      data: params
    })
  }

  async editPassword(password: string): Promise<APIResponse<any>> {
    return this.request({
      url: '/frontDevice/user/editPassword',
      method: 'POST',
      data: { password }
    })
  }

  // ==================== 工具方法 ====================

  clearAuth() {
    localStorage.removeItem('ZX-DEVICE-TOKEN')
    console.log('🔑 [H5 API] 已清除认证信息')
  }

  async checkHealth(): Promise<boolean> {
    try {
      await this.request({
        url: '/health',
        method: 'GET'
      })
      return true
    } catch {
      return false
    }
  }
}

// ==================== 导出 ====================

export const apiClient = new APIClient()
export type { APIResponse }
export default apiClient
