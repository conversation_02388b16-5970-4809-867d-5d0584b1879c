<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-content">
      <div class="error-icon">
        <Icon icon="mdi:alert-circle" size="48" />
      </div>
      <h3>页面出现异常</h3>
      <p v-if="showDetails">{{ errorMessage }}</p>
      <div class="error-actions">
        <a-button type="primary" @click="retry">重试</a-button>
        <a-button @click="goHome">返回首页</a-button>
        <a-button v-if="!showDetails" type="text" @click="toggleDetails">
          显示详情
        </a-button>
        <a-button v-else type="text" @click="toggleDetails">
          隐藏详情
        </a-button>
      </div>
      <div v-if="showDetails" class="error-details">
        <a-collapse>
          <a-collapse-panel key="error" header="错误详情">
            <pre>{{ errorDetails }}</pre>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </div>
  </div>
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured } from 'vue'
import { useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'

const hasError = ref(false)
const errorMessage = ref('')
const errorDetails = ref('')
const showDetails = ref(false)

const router = useRouter()

// 捕获子组件错误
onErrorCaptured((error: Error, instance, info) => {
  console.error('Error captured by ErrorBoundary:', error)
  
  hasError.value = true
  errorMessage.value = error.message || '未知错误'
  errorDetails.value = `
错误信息: ${error.message}
错误堆栈: ${error.stack}
组件信息: ${info}
时间: ${new Date().toLocaleString()}
用户代理: ${navigator.userAgent}
  `.trim()
  
  // 上报错误
  reportError(error, instance, info)
  
  // 阻止错误继续传播
  return false
})

const retry = () => {
  hasError.value = false
  errorMessage.value = ''
  errorDetails.value = ''
  showDetails.value = false
  
  // 重新加载当前路由
  router.go(0)
}

const goHome = () => {
  hasError.value = false
  router.push('/')
}

const toggleDetails = () => {
  showDetails.value = !showDetails.value
}

const reportError = (error: Error, instance: any, info: string) => {
  // 错误上报逻辑
  try {
    fetch('/api/errors/report', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message: error.message,
        stack: error.stack,
        componentInfo: info,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: Date.now()
      })
    }).catch(reportError => {
      console.error('Failed to report error:', reportError)
    })
  } catch (reportError) {
    console.error('Error in error reporting:', reportError)
  }
}
</script>

<style scoped lang="scss">
.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 24px;
  background: #f8fafc;
}

.error-content {
  text-align: center;
  max-width: 500px;
  background: white;
  padding: 40px 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  .error-icon {
    margin-bottom: 16px;
    color: #f56565;
  }
  
  h3 {
    margin-bottom: 8px;
    font-size: 20px;
    font-weight: 600;
    color: #1a202c;
  }
  
  p {
    margin-bottom: 24px;
    color: #718096;
    line-height: 1.5;
    font-size: 14px;
  }
  
  .error-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 16px;
    
    .ant-btn {
      border-radius: 8px;
      font-weight: 500;
    }
  }
  
  .error-details {
    text-align: left;
    margin-top: 16px;
    
    pre {
      background: #f7fafc;
      padding: 12px;
      border-radius: 6px;
      font-size: 12px;
      line-height: 1.4;
      overflow-x: auto;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }
}

@media (max-width: 768px) {
  .error-boundary {
    padding: 16px;
  }
  
  .error-content {
    padding: 32px 20px;
    
    .error-actions {
      flex-direction: column;
      
      .ant-btn {
        width: 100%;
      }
    }
  }
}
</style>
