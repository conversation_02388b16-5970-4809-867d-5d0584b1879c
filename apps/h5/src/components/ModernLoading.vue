<template>
  <Teleport to="body">
    <Transition name="modern-fade" appear>
      <div 
        v-if="visible" 
        class="modern-loading"
        :class="[
          `theme-${config.theme}`,
          `position-${config.position}`,
          { 
            'with-backdrop': config.backdrop,
            'glassmorphism': config.glassmorphism,
            'neomorphism': config.neomorphism,
            'with-glow': config.glow
          }
        ]"
        :style="backdropStyle"
        @click="handleBackdropClick"
      >
        <!-- 粒子背景 -->
        <div v-if="config.particles" class="particles-bg">
          <div 
            v-for="i in 20" 
            :key="i"
            class="particle"
            :style="getParticleStyle(i)"
          ></div>
        </div>

        <!-- 主容器 -->
        <div class="loading-container" :style="containerStyle">
          
          <!-- 极简加载器 -->
          <div v-if="config.type === 'minimal'" class="minimal-loader" :style="loaderStyle">
            <div class="minimal-dot"></div>
          </div>

          <!-- 玻璃态加载器 -->
          <div v-else-if="config.type === 'glass'" class="glass-loader" :style="loaderStyle">
            <div class="glass-ring">
              <div class="glass-segment" v-for="i in 8" :key="i"></div>
            </div>
          </div>

          <!-- 霓虹加载器 -->
          <div v-else-if="config.type === 'neon'" class="neon-loader" :style="loaderStyle">
            <div class="neon-core"></div>
            <div class="neon-ring"></div>
            <div class="neon-pulse"></div>
          </div>

          <!-- 流体加载器 -->
          <div v-else-if="config.type === 'fluid'" class="fluid-loader" :style="loaderStyle">
            <div class="fluid-blob"></div>
            <div class="fluid-wave"></div>
          </div>

          <!-- 呼吸加载器 -->
          <div v-else-if="config.type === 'breathing'" class="breathing-loader" :style="loaderStyle">
            <div class="breathing-circle"></div>
            <div class="breathing-glow"></div>
          </div>

          <!-- 变形加载器 -->
          <div v-else-if="config.type === 'morphing'" class="morphing-loader" :style="loaderStyle">
            <div class="morphing-shape"></div>
          </div>

          <!-- 粒子加载器 -->
          <div v-else-if="config.type === 'particle'" class="particle-loader" :style="loaderStyle">
            <div 
              v-for="i in 12" 
              :key="i"
              class="particle-dot"
              :style="{ '--delay': `${i * 0.1}s` }"
            ></div>
          </div>

          <!-- 进度指示器 -->
          <div v-if="showProgress && progress !== undefined" class="progress-indicator">
            <div class="progress-track">
              <div 
                class="progress-fill" 
                :style="{ width: `${progress}%`, backgroundColor: config.accentColor }"
              ></div>
            </div>
            <div class="progress-text">{{ Math.round(progress) }}%</div>
          </div>

          <!-- 文本 -->
          <div v-if="text" class="loading-text" :style="textStyle">
            {{ text }}
          </div>

          <!-- 取消按钮 -->
          <button 
            v-if="config.cancelable" 
            class="cancel-btn"
            @click="$emit('cancel')"
          >
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import type { ModernLoadingConfig } from '../config/modernLoading'

interface Props {
  visible: boolean
  config: ModernLoadingConfig
  progress?: number
  text?: string
  showProgress?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  progress: undefined,
  showProgress: false
})

const emit = defineEmits<{
  cancel: []
}>()

// 自动超时
let timeoutId: number | undefined

onMounted(() => {
  if (props.config.timeout) {
    timeoutId = window.setTimeout(() => {
      emit('cancel')
    }, props.config.timeout)
  }
})

onUnmounted(() => {
  if (timeoutId) {
    clearTimeout(timeoutId)
  }
})

// 样式计算
const backdropStyle = computed(() => ({
  '--accent-color': props.config.accentColor,
  '--blur-amount': `${props.config.backdropBlur}px`,
  '--opacity': props.config.opacity,
  '--duration': `${props.config.duration}ms`,
  zIndex: 9999
}))

const containerStyle = computed(() => {
  const sizeMap = {
    xs: '32px',
    sm: '40px', 
    md: '48px',
    lg: '64px',
    xl: '80px'
  }
  
  return {
    width: sizeMap[props.config.size],
    height: sizeMap[props.config.size]
  }
})

const loaderStyle = computed(() => ({
  '--easing': props.config.easing,
  color: props.config.accentColor
}))

const textStyle = computed(() => ({
  color: props.config.theme === 'dark' ? '#ffffff' : '#374151',
  fontSize: props.config.size === 'xs' ? '12px' : 
           props.config.size === 'sm' ? '13px' :
           props.config.size === 'md' ? '14px' :
           props.config.size === 'lg' ? '15px' : '16px'
}))

// 粒子样式
const getParticleStyle = (index: number) => {
  const angle = (index / 20) * 360
  const radius = 100 + Math.random() * 50
  const x = Math.cos(angle * Math.PI / 180) * radius
  const y = Math.sin(angle * Math.PI / 180) * radius
  
  return {
    left: `calc(50% + ${x}px)`,
    top: `calc(50% + ${y}px)`,
    animationDelay: `${Math.random() * 2}s`,
    animationDuration: `${2 + Math.random() * 2}s`
  }
}

// 背景点击处理
const handleBackdropClick = () => {
  if (props.config.cancelable) {
    emit('cancel')
  }
}
</script>

<style lang="scss" scoped>
.modern-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;

  &.with-backdrop {
    pointer-events: all;
    backdrop-filter: blur(var(--blur-amount));
    -webkit-backdrop-filter: blur(var(--blur-amount));
  }

  &.theme-light.with-backdrop {
    background: rgba(255, 255, 255, 0.8);
  }

  &.theme-dark.with-backdrop {
    background: rgba(0, 0, 0, 0.8);
  }

  &.theme-auto.with-backdrop {
    background: rgba(255, 255, 255, 0.85);
    
    @media (prefers-color-scheme: dark) {
      background: rgba(0, 0, 0, 0.85);
    }
  }

  &.position-top {
    align-items: flex-start;
    padding-top: 20vh;
  }

  &.position-inline {
    position: relative;
    min-height: 200px;
  }
}

.particles-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 3px;
  height: 3px;
  background: var(--accent-color);
  border-radius: 50%;
  opacity: 0.6;
  animation: particle-float 4s infinite ease-in-out;
}

.loading-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px;
  border-radius: 16px;
  pointer-events: auto;

  .glassmorphism & {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .neomorphism & {
    background: #f0f0f0;
    box-shadow: 
      20px 20px 60px #d1d1d1,
      -20px -20px 60px #ffffff;
  }

  .with-glow & {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.3);
  }
}

// 极简加载器
.minimal-loader {
  position: relative;
  
  .minimal-dot {
    width: 100%;
    height: 100%;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: minimal-spin var(--duration) linear infinite;
  }
}

// 玻璃态加载器
.glass-loader {
  position: relative;
  
  .glass-ring {
    width: 100%;
    height: 100%;
    position: relative;
  }
  
  .glass-segment {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    opacity: 0.7;
    animation: glass-rotate var(--duration) ease-in-out infinite;
    
    &:nth-child(2) { animation-delay: 0.1s; opacity: 0.5; }
    &:nth-child(3) { animation-delay: 0.2s; opacity: 0.3; }
    &:nth-child(4) { animation-delay: 0.3s; opacity: 0.2; }
  }
}

// 霓虹加载器
.neon-loader {
  position: relative;
  
  .neon-core {
    width: 40%;
    height: 40%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: currentColor;
    border-radius: 50%;
    box-shadow: 0 0 20px currentColor;
    animation: neon-pulse 2s ease-in-out infinite;
  }
  
  .neon-ring {
    width: 100%;
    height: 100%;
    border: 2px solid currentColor;
    border-radius: 50%;
    box-shadow: 
      0 0 10px currentColor,
      inset 0 0 10px currentColor;
    animation: neon-rotate var(--duration) linear infinite;
  }
  
  .neon-pulse {
    position: absolute;
    width: 120%;
    height: 120%;
    top: -10%;
    left: -10%;
    border: 1px solid currentColor;
    border-radius: 50%;
    opacity: 0;
    animation: neon-expand 2s ease-out infinite;
  }
}

// 流体加载器
.fluid-loader {
  position: relative;
  overflow: hidden;
  border-radius: 50%;
  
  .fluid-blob {
    width: 100%;
    height: 100%;
    background: currentColor;
    border-radius: 50%;
    animation: fluid-morph 3s ease-in-out infinite;
  }
  
  .fluid-wave {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, currentColor, transparent);
    border-radius: 50%;
    opacity: 0.3;
    transform: translate(-50%, -50%);
    animation: fluid-wave 2s linear infinite;
  }
}

// 呼吸加载器
.breathing-loader {
  position: relative;
  
  .breathing-circle {
    width: 100%;
    height: 100%;
    background: currentColor;
    border-radius: 50%;
    animation: breathing-scale 2s ease-in-out infinite;
  }
  
  .breathing-glow {
    position: absolute;
    top: -20%;
    left: -20%;
    width: 140%;
    height: 140%;
    background: radial-gradient(circle, currentColor 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0.3;
    animation: breathing-glow 2s ease-in-out infinite;
  }
}

// 变形加载器
.morphing-loader {
  .morphing-shape {
    width: 100%;
    height: 100%;
    background: currentColor;
    animation: morphing-transform 2s ease-in-out infinite;
  }
}

// 粒子加载器
.particle-loader {
  position: relative;
  
  .particle-dot {
    position: absolute;
    width: 8px;
    height: 8px;
    background: currentColor;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform-origin: 0 24px;
    animation: particle-orbit 2s linear infinite;
    animation-delay: var(--delay);
    
    @for $i from 1 through 12 {
      &:nth-child(#{$i}) {
        transform: rotate(#{($i - 1) * 30}deg);
      }
    }
  }
}

// 进度指示器
.progress-indicator {
  width: 120px;
  
  .progress-track {
    width: 100%;
    height: 2px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 1px;
    overflow: hidden;
  }
  
  .progress-fill {
    height: 100%;
    border-radius: 1px;
    transition: width 0.3s ease;
  }
  
  .progress-text {
    text-align: center;
    margin-top: 8px;
    font-size: 12px;
    font-weight: 500;
    opacity: 0.8;
  }
}

.loading-text {
  font-weight: 500;
  letter-spacing: 0.5px;
  text-align: center;
}

.cancel-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: currentColor;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
  transition: opacity 0.2s ease;
  
  &:hover {
    opacity: 1;
  }
}

// 过渡动画
.modern-fade-enter-active,
.modern-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-fade-enter-from,
.modern-fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
  backdrop-filter: blur(0px);
}

// 关键帧动画
@keyframes minimal-spin {
  to { transform: rotate(360deg); }
}

@keyframes glass-rotate {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

@keyframes neon-pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.2); }
}

@keyframes neon-rotate {
  to { transform: rotate(360deg); }
}

@keyframes neon-expand {
  0% { opacity: 0.8; transform: scale(1); }
  100% { opacity: 0; transform: scale(1.5); }
}

@keyframes fluid-morph {
  0%, 100% { border-radius: 50%; }
  25% { border-radius: 60% 40% 60% 40%; }
  50% { border-radius: 40% 60% 40% 60%; }
  75% { border-radius: 60% 40% 60% 40%; }
}

@keyframes fluid-wave {
  to { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes breathing-scale {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(0.8); opacity: 0.7; }
}

@keyframes breathing-glow {
  0%, 100% { opacity: 0.2; transform: scale(1); }
  50% { opacity: 0.4; transform: scale(1.1); }
}

@keyframes morphing-transform {
  0% { border-radius: 50%; transform: rotate(0deg); }
  25% { border-radius: 20%; transform: rotate(90deg); }
  50% { border-radius: 50%; transform: rotate(180deg); }
  75% { border-radius: 20%; transform: rotate(270deg); }
  100% { border-radius: 50%; transform: rotate(360deg); }
}

@keyframes particle-orbit {
  to { transform: rotate(360deg); }
}

@keyframes particle-float {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
  50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
}
</style>