<!-- ✅ 完全复制device-an的ErrorBox.vue -->
<script setup lang="ts">
import { useMaskStore } from '../stores/mask'
const useMask = useMaskStore()
import { useRouter } from 'vue-router'
const router = useRouter()
const close = (msg: string) => {
  if (msg === 'NOT_LOGIN') {
    router.push('/device/login')
  }
  useMask.show = false
}
</script>

<template>
  <div class="ErrorBox">
    <div class="ErrorBox-label">温馨提示</div>

    <div class="ErrorBox-content">
      {{ useMask.Error.detail }}

      <div class="ErrorBox-content-time">
        {{ useMask.Error.time }}
      </div>
    </div>

    <div class="ErrorBox-btn" @click="close(useMask.Error.detail)">确认</div>
  </div>
</template>

<style lang="scss" scoped>
@import '../styles/variables.scss';

.ErrorBox {
  width: 100%;
  background: #fff;
  border-radius: $radius;
  padding: $padding;

  &-label {
    text-align: center;
    font-size: 0.9rem;
    font-weight: bold;
    margin-bottom: $padding;
  }

  &-content {
    padding: $padding;
    background-color: $background;
    font-size: 0.8rem;
    margin: $padding 0;
    max-height: 60vh;
    overflow-y: scroll;
    white-space: normal;
    word-wrap: break-word;
    border-radius: $radius;

    &-time {
      text-align: right;
      margin-top: 1rem;
      font-size: 0.7rem;
      color: #666;
    }
  }

  &-btn {
    height: 2rem;
    border-radius: $radius;
    background-color: $primary;
    text-align: center;
    line-height: 2rem;
    color: #fff;
    font-size: 0.7rem;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: darken($primary, 10%);
    }
  }
}
</style>
