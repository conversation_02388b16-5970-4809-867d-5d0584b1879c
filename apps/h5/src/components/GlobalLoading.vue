<template>
  <Transition name="loading-fade">
    <div 
      v-if="visible" 
      class="global-loading"
      :class="[
        `loading-${config.position}`,
        { 'loading-overlay': config.overlay }
      ]"
      :style="overlayStyle"
    >
      <div class="loading-container" :style="containerStyle">
        <!-- Spinner 加载器 -->
        <div 
          v-if="config.type === 'spinner'" 
          class="loading-spinner"
          :style="spinnerStyle"
        >
          <div class="spinner-ring"></div>
        </div>

        <!-- 点点加载器 -->
        <div 
          v-else-if="config.type === 'dots'" 
          class="loading-dots"
          :style="{ color: config.color }"
        >
          <div class="dot" v-for="i in 3" :key="i"></div>
        </div>

        <!-- 波浪加载器 -->
        <div 
          v-else-if="config.type === 'wave'" 
          class="loading-wave"
          :style="{ color: config.color }"
        >
          <div class="wave-bar" v-for="i in 5" :key="i"></div>
        </div>

        <!-- 脉冲加载器 -->
        <div 
          v-else-if="config.type === 'pulse'" 
          class="loading-pulse"
          :style="pulseStyle"
        >
          <div class="pulse-ring"></div>
          <div class="pulse-core"></div>
        </div>

        <!-- 弹跳加载器 -->
        <div 
          v-else-if="config.type === 'bounce'" 
          class="loading-bounce"
          :style="{ color: config.color }"
        >
          <div class="bounce-ball" v-for="i in 3" :key="i"></div>
        </div>

        <!-- 圆形进度加载器 -->
        <div 
          v-else-if="config.type === 'circular'" 
          class="loading-circular"
          :style="circularStyle"
        >
          <svg class="circular-svg" viewBox="0 0 50 50">
            <circle 
              class="circular-path" 
              cx="25" 
              cy="25" 
              r="20" 
              fill="none" 
              :stroke="config.color"
              stroke-width="3"
              stroke-linecap="round"
            />
          </svg>
        </div>

        <!-- 渐变加载器 -->
        <div 
          v-else-if="config.type === 'gradient'" 
          class="loading-gradient"
          :style="gradientStyle"
        >
          <div class="gradient-spinner"></div>
        </div>

        <!-- 进度条 (可选) -->
        <div v-if="config.showProgress && progress !== undefined" class="loading-progress">
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ 
                width: `${progress}%`, 
                backgroundColor: config.color 
              }"
            ></div>
          </div>
          <div class="progress-text" :style="{ color: config.textColor }">
            {{ progress }}%
          </div>
        </div>

        <!-- 加载文本 -->
        <div 
          v-if="config.text" 
          class="loading-text"
          :style="{ 
            color: config.textColor,
            fontSize: textSize
          }"
        >
          {{ config.text }}
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { LoadingConfig } from '../config/loading'

interface Props {
  visible: boolean
  config: LoadingConfig
  progress?: number
}

const props = withDefaults(defineProps<Props>(), {
  progress: undefined
})

// 计算样式
const overlayStyle = computed(() => ({
  backgroundColor: props.config.overlay 
    ? `rgba(0, 0, 0, ${props.config.overlayOpacity})` 
    : 'transparent',
  zIndex: props.config.zIndex
}))

const containerStyle = computed(() => ({
  backgroundColor: props.config.backgroundColor,
  borderRadius: '12px',
  padding: '24px',
  boxShadow: props.config.overlay ? '0 8px 32px rgba(0, 0, 0, 0.12)' : 'none'
}))

const spinnerStyle = computed(() => {
  const sizes = { small: '24px', medium: '32px', large: '48px' }
  return {
    width: sizes[props.config.size],
    height: sizes[props.config.size],
    borderColor: `${props.config.color}33`,
    borderTopColor: props.config.color,
    animationDuration: `${props.config.duration}ms`
  }
})

const pulseStyle = computed(() => {
  const sizes = { small: '32px', medium: '48px', large: '64px' }
  return {
    width: sizes[props.config.size],
    height: sizes[props.config.size],
    '--pulse-color': props.config.color
  }
})

const circularStyle = computed(() => {
  const sizes = { small: '32px', medium: '48px', large: '64px' }
  return {
    width: sizes[props.config.size],
    height: sizes[props.config.size],
    animationDuration: `${props.config.duration}ms`
  }
})

const gradientStyle = computed(() => {
  const sizes = { small: '32px', medium: '48px', large: '64px' }
  const gradient = props.config.gradient
  return {
    width: sizes[props.config.size],
    height: sizes[props.config.size],
    background: gradient 
      ? `linear-gradient(${gradient.direction}, ${gradient.from}, ${gradient.to})`
      : props.config.color
  }
})

const textSize = computed(() => {
  const sizes = { small: '12px', medium: '14px', large: '16px' }
  return sizes[props.config.size]
})
</script>

<style lang="scss" scoped>
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;

  &.loading-overlay {
    pointer-events: all;
  }

  &.loading-top {
    align-items: flex-start;
    padding-top: 20vh;
  }

  &.loading-bottom {
    align-items: flex-end;
    padding-bottom: 20vh;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  max-width: 300px;
  text-align: center;
}

// Spinner 动画
.loading-spinner {
  border: 3px solid;
  border-radius: 50%;
  border-top-style: solid;
  animation: spin var(--duration, 1200ms) linear infinite;

  .spinner-ring {
    width: 100%;
    height: 100%;
  }
}

// 点点动画
.loading-dots {
  display: flex;
  gap: 4px;

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: currentColor;
    animation: dots-bounce 1.4s ease-in-out infinite both;

    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

// 波浪动画
.loading-wave {
  display: flex;
  gap: 2px;
  align-items: end;

  .wave-bar {
    width: 4px;
    height: 20px;
    background-color: currentColor;
    border-radius: 2px;
    animation: wave-scale 1.2s ease-in-out infinite;

    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.1s; }
    &:nth-child(3) { animation-delay: 0.2s; }
    &:nth-child(4) { animation-delay: 0.3s; }
    &:nth-child(5) { animation-delay: 0.4s; }
  }
}

// 脉冲动画
.loading-pulse {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .pulse-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 2px solid var(--pulse-color);
    border-radius: 50%;
    opacity: 0.6;
    animation: pulse-ring 2s ease-out infinite;
  }

  .pulse-core {
    width: 60%;
    height: 60%;
    background-color: var(--pulse-color);
    border-radius: 50%;
    animation: pulse-core 2s ease-in-out infinite;
  }
}

// 弹跳动画
.loading-bounce {
  display: flex;
  gap: 6px;

  .bounce-ball {
    width: 12px;
    height: 12px;
    background-color: currentColor;
    border-radius: 50%;
    animation: bounce-scale 1.4s ease-in-out infinite both;

    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

// 圆形进度动画
.loading-circular {
  .circular-svg {
    animation: circular-rotate var(--duration, 1200ms) linear infinite;
  }

  .circular-path {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: 0;
    animation: circular-dash 1.5s ease-in-out infinite;
  }
}

// 渐变动画
.loading-gradient {
  border-radius: 50%;
  position: relative;
  overflow: hidden;

  .gradient-spinner {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(
      from 0deg,
      transparent,
      transparent,
      transparent,
      currentColor
    );
    animation: gradient-spin 1.2s linear infinite;
  }

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 70%;
    height: 70%;
    background: var(--bg-color, white);
    border-radius: 50%;
    transform: translate(-50%, -50%);
  }
}

// 进度条
.loading-progress {
  width: 200px;

  .progress-bar {
    width: 100%;
    height: 6px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
  }

  .progress-text {
    margin-top: 8px;
    font-size: 12px;
    font-weight: 500;
  }
}

.loading-text {
  font-weight: 500;
  letter-spacing: 0.5px;
  margin-top: 8px;
}

// 过渡动画
.loading-fade-enter-active,
.loading-fade-leave-active {
  transition: all 0.3s ease;
}

.loading-fade-enter-from,
.loading-fade-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

// 关键帧动画
@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes dots-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes wave-scale {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes pulse-core {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(0.8);
    opacity: 0.7;
  }
}

@keyframes bounce-scale {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes circular-rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes circular-dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

@keyframes gradient-spin {
  to {
    transform: rotate(360deg);
  }
}
</style>