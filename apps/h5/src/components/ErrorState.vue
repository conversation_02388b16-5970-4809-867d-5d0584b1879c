<template>
  <div class="error-state">
    <!-- 主要内容容器 -->
    <div class="error-content">
      <!-- 错误插图 -->
      <div class="error-illustration">
        <div class="illustration-container">
          <!-- 背景圆圈 -->
          <div class="bg-circle"></div>

          <!-- 主要图形 -->
          <div class="main-circle">
            <div class="error-text">
              <div class="error-code">{{ errorCode }}</div>
              <div class="error-line"></div>
            </div>
          </div>

          <!-- 装饰元素 -->
          <div class="decoration decoration-1"></div>
          <div class="decoration decoration-2"></div>
          <div class="decoration decoration-3"></div>
        </div>
      </div>

      <!-- 文字内容 -->
      <div class="text-content">
        <h1 class="error-title">{{ title }}</h1>
        <p class="error-description">{{ description }}</p>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <button @click="handlePrimaryAction" class="primary-button">
          <svg viewBox="0 0 24 24" class="button-icon">
            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" fill="currentColor"/>
          </svg>
          {{ primaryButtonText }}
        </button>

        <div class="secondary-buttons">
          <button @click="handleRefresh" class="secondary-button">
            <svg viewBox="0 0 24 24" class="button-icon">
              <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" fill="currentColor"/>
            </svg>
            刷新
          </button>

          <button @click="handleBack" class="secondary-button">
            <svg viewBox="0 0 24 24" class="button-icon">
              <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" fill="currentColor"/>
            </svg>
            返回
          </button>
        </div>
      </div>

      <!-- 底部提示 -->
      <div class="bottom-hint">
        <p class="hint-text">技术支持：安生低代码引擎</p>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="background-decorations">
      <div class="bg-decoration bg-decoration-1"></div>
      <div class="bg-decoration bg-decoration-2"></div>
      <div class="bg-decoration bg-decoration-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  errorCode?: string
  title?: string
  description?: string
  primaryButtonText?: string
  onPrimaryAction?: () => void
  onRefresh?: () => void
  onBack?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  errorCode: '404',
  title: '页面走丢了1',
  description: '抱歉，您访问的页面暂时无法找到\n可能是页面地址有误或页面已被删除',
  primaryButtonText: '返回首页',
  onPrimaryAction: () => window.location.href = '/home',
  onRefresh: () => window.location.reload(),
  onBack: () => window.history.back()
})

const handlePrimaryAction = () => {
  props.onPrimaryAction?.()
}

const handleRefresh = () => {
  props.onRefresh?.()
}

const handleBack = () => {
  props.onBack?.()
}
</script>

<style scoped lang="scss">
.error-state {
  min-height: 100vh;
  background: linear-gradient(135deg, #dbeafe 0%, #ffffff 50%, #faf5ff 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

.error-content {
  max-width: 14rem;
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.error-illustration {
  position: relative;
  
  .illustration-container {
    width: 6rem;
    height: 6rem;
    margin: 0 auto;
    position: relative;
  }

  .bg-circle {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, #dbeafe 0%, #e9d5ff 100%);
    border-radius: 50%;
    opacity: 0.5;
  }

  .main-circle {
    position: absolute;
    inset: 0.5rem;
    background: white;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .error-text {
    text-align: center;
    
    .error-code {
      font-size: 1.5rem;
      font-weight: bold;
      color: #d1d5db;
      margin-bottom: 0.25rem;
    }

    .error-line {
      width: 2rem;
      height: 0.125rem;
      background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%);
      border-radius: 9999px;
      margin: 0 auto;
    }
  }

  .decoration {
    position: absolute;
    border-radius: 50%;
    
    &.decoration-1 {
      top: -0.125rem;
      right: -0.125rem;
      width: 1rem;
      height: 1rem;
      background: #fbbf24;
      animation: bounce 2s infinite;
    }

    &.decoration-2 {
      bottom: 0rem;
      left: 0rem;
      width: 0.75rem;
      height: 0.75rem;
      background: #ec4899;
      animation: pulse 2s infinite;
    }

    &.decoration-3 {
      top: 25%;
      left: -0.375rem;
      width: 0.5rem;
      height: 0.5rem;
      background: #10b981;
      animation: ping 2s infinite;
    }
  }
}

.text-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  .error-title {
    font-size: 1.125rem;
    font-weight: bold;
    color: #1f2937;
  }

  .error-description {
    color: #6b7280;
    font-size: 0.75rem;
    line-height: 1.4;
    white-space: pre-line;
  }
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;

  .primary-button {
    width: 100%;
    background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    border: none;
    border-radius: 9999px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
    }

    .button-icon {
      width: 0.75rem;
      height: 0.75rem;
    }
  }

  .secondary-buttons {
    display: flex;
    gap: 0.375rem;

    .secondary-button {
      flex: 1;
      border: 1px solid #e5e7eb;
      background: transparent;
      color: #6b7280;
      border-radius: 9999px;
      padding: 0.375rem 0.5rem;
      font-size: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.125rem;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #f9fafb;
        border-color: #d1d5db;
      }

      .button-icon {
        width: 0.75rem;
        height: 0.75rem;
      }
    }
  }
}

.bottom-hint {
  padding-top: 1rem;

  .hint-text {
    font-size: 0.65rem;
    color: #9ca3af;
  }
}

.background-decorations {
  position: fixed;
  inset: 0;
  pointer-events: none;
  overflow: hidden;

  .bg-decoration {
    position: absolute;
    border-radius: 50%;
    
    &.bg-decoration-1 {
      top: 25%;
      left: 25%;
      width: 0.375rem;
      height: 0.375rem;
      background: #93c5fd;
      animation: float 3s ease-in-out infinite;
    }

    &.bg-decoration-2 {
      top: 75%;
      right: 25%;
      width: 0.5rem;
      height: 0.5rem;
      background: #c4b5fd;
      animation: float-delayed 4s ease-in-out infinite 1s;
    }

    &.bg-decoration-3 {
      bottom: 25%;
      left: 33%;
      width: 0.25rem;
      height: 0.25rem;
      background: #f9a8d4;
      animation: float 3s ease-in-out infinite;
    }
  }
}

/* 动画 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes float-delayed {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}
</style>
