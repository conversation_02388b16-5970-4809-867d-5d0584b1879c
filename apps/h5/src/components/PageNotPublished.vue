<template>
  <div class="page-not-published">
    <div class="error-content">
      <!-- 插图 -->
      <div class="error-illustration">
        <div class="illustration-container">
          <div class="bg-circle"></div>
          <div class="main-circle">
            <div class="error-text">
              <div class="error-icon">📄</div>
              <div class="error-line"></div>
            </div>
          </div>
          <!-- 装饰元素 -->
          <div class="decoration decoration-1"></div>
          <div class="decoration decoration-2"></div>
          <div class="decoration decoration-3"></div>
        </div>
      </div>

      <!-- 文字内容 -->
      <div class="text-content">
        <h1 class="error-title">页面暂未发布</h1>

        <p class="error-description">
          {{ errorMessage || '该页面正在维护中，请稍后再试' }}
        </p>

        <!-- 页面信息显示 -->
        <div class="app-hint" v-if="pageInfo">
          <strong>页面信息：</strong>
          <br>
          <small>{{ pageInfo.name }}{{ pageInfo.appName ? ` (${pageInfo.appName})` : '' }}</small>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <!-- 主要操作按钮 -->
        <button @click="refresh" class="primary-button">
          <svg viewBox="0 0 24 24" class="button-icon">
            <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" fill="currentColor"/>
          </svg>
          刷新页面
        </button>
      </div>

      <!-- 开发者提示 -->
      <div class="developer-hint">
        <p class="help-title">开发者提示：</p>
        <ul class="help-list">
          <li>确保页面已在管理后台发布</li>
          <li>检查页面配置是否完整</li>
          <li>发布后点击刷新按钮重新加载</li>
        </ul>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="background-decorations">
      <div class="bg-decoration bg-decoration-1"></div>
      <div class="bg-decoration bg-decoration-2"></div>
      <div class="bg-decoration bg-decoration-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  errorMessage?: string
  pageInfo?: {
    id: string
    name: string
    appId: string
    appName?: string
  }
}

const props = defineProps<Props>()

// 刷新页面
function refresh() {
  window.location.reload()
}
</script>

<style scoped lang="scss">
.page-not-published {
  min-height: 100vh;
  background: linear-gradient(135deg, #dbeafe 0%, #ffffff 50%, #faf5ff 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

.error-content {
  max-width: 14rem;
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.error-illustration {
  position: relative;

  .illustration-container {
    width: 6rem;
    height: 6rem;
    margin: 0 auto;
    position: relative;
  }

  .bg-circle {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    border-radius: 50%;
    opacity: 0.3;
  }

  .main-circle {
    position: absolute;
    inset: 0.5rem;
    background: white;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .error-text {
    text-align: center;

    .error-icon {
      font-size: 1.8rem;
      color: #f59e0b;
      margin-bottom: 0.25rem;
    }

    .error-line {
      width: 2rem;
      height: 0.125rem;
      background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
      border-radius: 9999px;
      margin: 0 auto;
    }
  }

  .decoration {
    position: absolute;
    border-radius: 50%;

    &.decoration-1 {
      top: -0.125rem;
      right: -0.125rem;
      width: 1rem;
      height: 1rem;
      background: #fbbf24;
      animation: bounce 2s infinite;
    }

    &.decoration-2 {
      bottom: 0rem;
      left: 0rem;
      width: 0.75rem;
      height: 0.75rem;
      background: #ec4899;
      animation: pulse 2s infinite;
    }

    &.decoration-3 {
      top: 25%;
      left: -0.375rem;
      width: 0.5rem;
      height: 0.5rem;
      background: #10b981;
      animation: ping 2s infinite;
    }
  }
}

.text-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  .error-title {
    font-size: 1.125rem;
    font-weight: bold;
    color: #1f2937;
  }

  .error-description {
    color: #6b7280;
    font-size: 0.75rem;
    line-height: 1.4;
  }

  .app-hint {
    background: rgba(251, 191, 36, 0.1);
    border: 1px solid rgba(251, 191, 36, 0.3);
    border-radius: 0.375rem;
    padding: 0.5rem;
    font-size: 0.75rem;
    color: #92400e;
    text-align: left;

    strong {
      color: #78350f;
    }

    small {
      color: #a16207;
    }
  }
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;

  .primary-button {
    width: 100%;
    background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
    color: white;
    border: none;
    border-radius: 9999px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .button-icon {
      width: 0.75rem;
      height: 0.75rem;
    }
  }
}

.developer-hint {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 0.5rem;
  text-align: left;

  .help-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: #374151;
    margin: 0 0 0.375rem 0;
  }

  .help-list {
    margin: 0;
    padding-left: 1rem;
    font-size: 0.6875rem;
    color: #6b7280;
    line-height: 1.4;

    li {
      margin-bottom: 0.125rem;
    }
  }
}

.background-decorations {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: -1;

  .bg-decoration {
    position: absolute;
    border-radius: 50%;
    opacity: 0.1;

    &.bg-decoration-1 {
      top: 10%;
      left: 10%;
      width: 4rem;
      height: 4rem;
      background: #fbbf24;
      animation: float 6s ease-in-out infinite;
    }

    &.bg-decoration-2 {
      top: 60%;
      right: 15%;
      width: 3rem;
      height: 3rem;
      background: #ec4899;
      animation: float 8s ease-in-out infinite reverse;
    }

    &.bg-decoration-3 {
      bottom: 20%;
      left: 20%;
      width: 2rem;
      height: 2rem;
      background: #10b981;
      animation: float 7s ease-in-out infinite;
    }
  }
}

// 动画定义
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 移动端适配
@media (max-width: 768px) {
  .page-not-published {
    padding: 0.75rem;
  }

  .error-content {
    max-width: 16rem;
  }

  .error-illustration .illustration-container {
    width: 5rem;
    height: 5rem;
  }

  .text-content {
    .error-title {
      font-size: 1rem;
    }

    .error-description {
      font-size: 0.6875rem;
    }
  }

  .developer-hint {
    .help-title {
      font-size: 0.6875rem;
    }

    .help-list {
      font-size: 0.625rem;
    }
  }
}
</style>
