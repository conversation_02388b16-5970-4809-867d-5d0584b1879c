<!--
  全局遮罩组件
  
  替代原来的dxMask组件，提供全局的错误提示和遮罩功能
-->
<template>
  <div v-if="maskStore.visible" class="global-mask" @click="handleMaskClick">
    <div class="mask-content" @click.stop>
      <!-- 错误信息 -->
      <div v-if="maskStore.type === 'error'" class="error-content">
        <div class="error-icon">⚠️</div>
        <div class="error-title">{{ maskStore.title || '操作失败' }}</div>
        <div class="error-message">{{ maskStore.message }}</div>
        <div class="error-actions">
          <button class="error-btn" @click="handleConfirm">确定</button>
        </div>
      </div>
      
      <!-- 确认对话框 -->
      <div v-else-if="maskStore.type === 'confirm'" class="confirm-content">
        <div class="confirm-title">{{ maskStore.title || '确认操作' }}</div>
        <div class="confirm-message">{{ maskStore.message }}</div>
        <div class="confirm-actions">
          <button class="confirm-btn cancel" @click="handleCancel">取消</button>
          <button class="confirm-btn confirm" @click="handleConfirm">确定</button>
        </div>
      </div>
      
      <!-- 加载中 -->
      <div v-else-if="maskStore.type === 'loading'" class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-message">{{ maskStore.message || '加载中...' }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useMaskStore } from '../stores/mask'

const maskStore = useMaskStore()

// 处理遮罩点击
function handleMaskClick() {
  if (maskStore.clickMaskToClose) {
    maskStore.hide()
  }
}

// 处理确认
function handleConfirm() {
  if (maskStore.onConfirm) {
    maskStore.onConfirm()
  }
  maskStore.hide()
}

// 处理取消
function handleCancel() {
  if (maskStore.onCancel) {
    maskStore.onCancel()
  }
  maskStore.hide()
}
</script>

<style scoped>
.global-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.mask-content {
  background: white;
  border-radius: 12px;
  max-width: 320px;
  width: 100%;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* 错误内容样式 */
.error-content {
  padding: 24px;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.error-message {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 24px;
}

.error-actions {
  display: flex;
  justify-content: center;
}

.error-btn {
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.error-btn:hover {
  background: #ff7875;
}

/* 确认对话框样式 */
.confirm-content {
  padding: 24px;
  text-align: center;
}

.confirm-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.confirm-message {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 24px;
}

.confirm-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.confirm-btn {
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  flex: 1;
}

.confirm-btn.cancel {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn.cancel:hover {
  background: #e8e8e8;
}

.confirm-btn.confirm {
  background: #1890ff;
  color: white;
}

.confirm-btn.confirm:hover {
  background: #40a9ff;
}

/* 加载中样式 */
.loading-content {
  padding: 32px 24px;
  text-align: center;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-message {
  font-size: 14px;
  color: #666;
}
</style>
