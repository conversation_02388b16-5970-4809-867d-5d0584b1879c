<template>
  <div v-if="shouldShow" class="smart-tabbar" :class="tabbarClass" :style="tabbarStyle">
    <div
      v-for="item in visibleItems"
      :key="item.id"
      class="tabbar-item"
      :class="{ active: isActive(item) }"
      :style="getItemStyle(item)"
      @click="handleItemClick(item)"
    >
      <!-- Iconify图标 -->
      <div class="item-icon">
        <Icon
          :icon="parseIcon(item.icon).icon"
          :size="getIconSize()"
          :style="{ color: parseIcon(item.icon).color || getItemStyle(item).color }"
        />
        <!-- 徽章 -->
        <span v-if="item.badge" class="badge">{{ item.badge }}</span>
        <!-- 小红点 -->
        <span v-if="item.dot" class="dot"></span>
      </div>

      <!-- 文本 -->
      <div v-if="showText" class="item-text">{{ item.text }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'
// 不再需要 NavigationHandler，直接使用 router.push
import { getCurrentAppId } from '../../services/SmartAppIdManager'
import { appDataManager } from '../../services/AppDataManager'

// 定义emits
const emit = defineEmits<{
  'visibility-change': [visible: boolean]
}>()

interface TabBarItem {
  id: string
  text: string
  icon: string
  routeType: 'static' | 'dynamic' | 'external'
  path: string
  appId?: string
  external?: string
  visible: boolean
  order: number
  customColor?: string
  badge?: string | number
  dot?: boolean
}

interface TabBarConfig {
  visible: boolean
  activeColor: string
  inactiveColor: string
  backgroundColor: string
  height: number
  items: TabBarItem[]
  // 新增字段
  type?: string
  iconTextGap?: string
  borderRadius?: string
  showText?: boolean
}

const route = useRoute()
const router = useRouter()

// 不再需要 NavigationHandler，直接使用 router.push

const config = ref<TabBarConfig | null>(null)
const activeItem = ref<string>('')

// 🎯 添加稳定显示状态，防止页面切换时TabBar闪烁
const stableShow = ref(false) // 稳定的显示状态
const lastValidConfig = ref<TabBarConfig | null>(null) // 上次有效的配置

// 加载TabBar配置 - 简化版本
const loadConfig = async () => {
  console.log('🔧 loadConfig函数开始执行')
  try {
    // 使用智能AppID管理器获取当前appId，简化参数获取逻辑
    const appId = getCurrentAppId()
    console.log('🔧 使用智能管理器获取的appId:', appId)

    // 🎯 优先尝试从API获取配置，无论appId是什么值
    if (appId) {
      try {
        console.log('🌐 请求TabBar配置:', appId)
        const fullConfig = await appDataManager.getAppFullConfig(appId)

        if (fullConfig && fullConfig.tabBar) {
          // 转换现有的tabBar格式到新的配置格式
          const tabBarData = fullConfig.tabBar
          console.log('🔄 TabBar原始数据:', tabBarData)
          console.log('🔄 enabled值:', tabBarData.enabled, typeof tabBarData.enabled)
          console.log('🔄 tabs数组:', tabBarData.tabs)

          config.value = {
            visible: Boolean(tabBarData.enabled),
            activeColor: tabBarData.style?.activeColor || '#1890ff',
            inactiveColor: tabBarData.style?.inactiveColor || '#666666',
            backgroundColor: tabBarData.style?.backgroundColor || '#ffffff',
            height: parseInt(tabBarData.style?.height || '50'),
            // 新增字段
            type: tabBarData.type || 'default',
            iconTextGap: tabBarData.style?.iconTextGap || '2',
            borderRadius: tabBarData.style?.borderRadius || '0',
            showText: tabBarData.style?.showText !== false,
            items: tabBarData.tabs.map((tab: any, index: number) => {
              console.log('🔍 处理TabBar项目:', tab)
              return {
                id: tab.id,
                text: tab.label,
                icon: tab.icon,
                routeType: 'page', // 统一使用page类型，由NavigationHandler智能处理
                path: tab.path,
                external: tab.external, // 支持外部链接
                visible: true,
                order: index + 1
              }
            })
          }
          console.log('✅ TabBar配置加载成功, 可见性:', config.value.visible, '项目数量:', config.value.items.length)
          console.log('🔍 TabBar最终配置:', config.value)

          return
        } else {
          console.warn('⚠️ 应用配置中没有TabBar数据:', fullConfig)
        }
      } catch (apiError: any) {
        console.warn('⚠️ 应用接口加载失败:', apiError.message)
      }
    } else {
      console.warn('⚠️ 没有有效的AppID')
    }

    // 2. 使用空配置
    config.value = getDefaultConfig()
    console.log('📱 使用默认TabBar配置:', config.value)

  } catch (error) {
    console.error('❌ 加载TabBar配置失败:', error)
    config.value = getDefaultConfig()
  }
}

// 解析图标配置（支持 "icon|color" 格式）
const parseIcon = (iconValue: string) => {
  if (!iconValue || typeof iconValue !== 'string') {
    return { icon: '', color: '' }
  }

  const parts = iconValue.split('|')
  return {
    icon: parts[0] || '',
    color: parts[1] || ''
  }
}

// 默认配置 - 修改为空配置，完全依赖动态加载
const getDefaultConfig = (): TabBarConfig => ({
  visible: false,  // 🎯 修改：默认不显示，必须通过API动态加载
  activeColor: '#1989fa',
  inactiveColor: '#7d7e80',
  backgroundColor: '#ffffff',
  height: 50,
  items: []  // 🎯 修改：默认无项目，必须通过API动态加载
})

// 🎯 简化路径检查：直接使用API配置的路径
const isTabBarRoute = () => {
  if (!config.value?.items) return false

  const currentPath = route.path

  // 直接检查当前路径是否在TabBar配置中
  const isConfiguredRoute = config.value.items.some(item => {
    return item.path === currentPath
  })

  console.log('🔍 TabBar路由检查:', {
    当前路径: currentPath,
    是否配置路由: isConfiguredRoute,
    配置的路径: config.value.items.map(item => item.path)
  })

  return isConfiguredRoute
}
// 计算属性 - 使用动态路径映射，避免硬编码
const shouldShow = computed(() => {
  const notLoginPage = !route.path.includes('/login')
  const notWebView = !route.path.includes('/webview')
  const not404Page = !route.path.includes('/404')
  const notErrorPage = !route.path.includes('/app-error')

  // 🎯 基础页面过滤
  if (!notLoginPage || !notWebView || !not404Page || !notErrorPage) {
    console.log('🚫 TabBar隐藏 - 在排除页面:', route.path)
    return false
  }

  // 🎯 必须有实际配置才能判断
  const hasConfig = !!config.value
  const isVisible = config.value?.visible === true
  const hasItems = config.value?.items && config.value.items.length > 0

  if (!hasConfig || !isVisible || !hasItems) {
    console.log('🚫 TabBar隐藏 - 配置问题:', {
      有配置: hasConfig,
      配置可见: isVisible,
      有项目: hasItems
    })
    return false
  }

  // 🎯 使用动态路径匹配，而不是硬编码路径
  const isConfiguredRoute = isTabBarRoute()
  
  if (!isConfiguredRoute) {
    console.log('🚫 TabBar隐藏 - 路径未在配置中:', route.path)
    return false
  }

  console.log('✅ TabBar显示 - 所有条件满足:', {
    路径: route.path,
    有配置: hasConfig,
    配置可见: isVisible,
    有项目: hasItems,
    项目数量: config.value?.items?.length || 0,
    是配置路由: isConfiguredRoute
  })

  return true
})

// TabBar类型样式类
const tabbarClass = computed(() => {
  // 🎯 优先使用当前配置，如果没有则使用上次有效配置
  const currentConfig = config.value || (stableShow.value ? lastValidConfig.value : null)
  const configData = currentConfig as any
  const type = configData?.type || 'default'
  return `tabbar-${type}`
})

// 是否显示文字
const showText = computed(() => {
  // 🎯 修改：只使用实际配置
  if (!config.value) return true // 默认显示文字
  const configData = config.value as any
  return configData?.showText !== false
})

// 🔧 移除动态样式，改为CSS固定样式以获得更好的布局控制

// 获取图标大小
const getIconSize = () => {
  const configData = config.value as any
  const type = configData?.type || 'default'
  const sizeMap: Record<string, number> = {
    'default': 20,
    'rounded': 22,
    'minimal': 18,
    'floating': 24,
    'capsule': 22,    // 🎨 胶囊式TabBar
    'modern': 24      // 🎨 现代简约TabBar
  }
  return sizeMap[type] || 20
}

const visibleItems = computed(() => {
  // 🎯 修改：只使用实际配置，没有配置时返回空数组
  if (!config.value || !config.value.items) return []
  return config.value.items
    .filter(item => item.visible)
    .sort((a, b) => a.order - b.order)
})

const tabbarStyle = computed(() => {
  // 🎯 修改：只使用实际配置，没有配置时返回空样式
  if (!config.value) return {}

  const configData = config.value as any
  const baseStyle = {
    backgroundColor: config.value.backgroundColor || '#ffffff',
    height: `${config.value.height || 50}px`
  }

  // 根据类型添加特殊样式
  const type = configData?.type || 'default'
  const borderRadius = configData?.borderRadius || '0'

  if (type === 'capsule') {
    // 🎨 胶囊式TabBar样式
    return {
      backgroundColor: 'rgba(0, 0, 0, 0.85)',
      height: '56px',
      '--active-color': config.value.activeColor || '#ffffff',
      '--inactive-color': config.value.inactiveColor || 'rgba(255, 255, 255, 0.6)'
    }
  } else if (type === 'modern') {
    // 🎨 现代简约TabBar样式
    return {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      height: '64px',
      '--active-color': config.value.activeColor || '#007AFF',
      '--inactive-color': config.value.inactiveColor || '#8E8E93'
    }
  } else if (type === 'rounded') {
    return {
      ...baseStyle,
      borderTopLeftRadius: `${borderRadius}px`,
      borderTopRightRadius: `${borderRadius}px`
    }
  } else if (type === 'floating') {
    return {
      ...baseStyle,
      margin: '0 16px 16px 16px',
      borderRadius: `${borderRadius}px`,
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      left: '16px',
      right: '16px',
      width: 'auto'
    }
  } else if (type === 'minimal') {
    return {
      ...baseStyle,
      borderTop: 'none',
      boxShadow: 'none'
    }
  }

  return baseStyle
})

// 方法
const isActive = (item: TabBarItem): boolean => {
  return activeItem.value === item.id
}

const getItemStyle = (item: TabBarItem) => {
  // 🎯 确保有配置时才设置样式
  if (!config.value) return { color: '#7d7e80' }

  const configData = config.value as any
  const type = configData?.type || 'default'
  const isActiveItem = isActive(item)

  // 🎨 根据TabBar类型使用不同的颜色方案
  let activeColor: string
  let inactiveColor: string

  switch (type) {
    case 'capsule':
      activeColor = '#ffffff'
      inactiveColor = 'rgba(255, 255, 255, 0.6)'
      break
    case 'modern':
      activeColor = config.value?.activeColor || '#007AFF'
      inactiveColor = config.value?.inactiveColor || '#8E8E93'
      break
    default:
      activeColor = item.customColor || config.value?.activeColor || '#1989fa'
      inactiveColor = config.value?.inactiveColor || '#7d7e80'
  }

  const color = isActiveItem ? activeColor : inactiveColor

  return { color }
}

const handleItemClick = async (item: TabBarItem) => {
  activeItem.value = item.id
  console.log('🔄 TabBar点击:', item)

  try {
    if (item.external) {
      // 外部链接
      window.open(item.external, '_blank')
    } else {
      // 🎯 直接使用原始路径跳转，不经过NavigationHandler的路径转换
      console.log('🔄 TabBar直接跳转到:', item.path)
      await router.push(item.path)
    }
  } catch (error) {
    console.error('❌ TabBar跳转失败:', error)
  }
}

const updateActiveItem = () => {
  const currentPath = route.path

  // 🎯 简化：直接路径匹配
  const matchedItem = visibleItems.value.find(item => {
    return currentPath === item.path
  })

  if (matchedItem) {
    activeItem.value = matchedItem.id
    console.log('🎯 TabBar激活项目:', {
      当前路径: currentPath,
      匹配项目: matchedItem.id,
      项目路径: matchedItem.path
    })
  } else {
    console.log('🔍 TabBar未找到匹配项目:', {
      当前路径: currentPath,
      可用项目: visibleItems.value.map(item => ({ id: item.id, path: item.path }))
    })
  }
}

// 加载配置的函数
const loadTabBarConfig = async () => {
  console.log('🔄 重新加载TabBar配置，当前路由:', route.path)

  try {
    // 🎯 开始加载时，如果有上次有效配置，暂时保持显示
    if (lastValidConfig.value) {
      console.log('🎯 保持上次有效配置显示，避免闪烁')
    } else {
      stableShow.value = false
    }

    config.value = null
    await loadConfig()
    updateActiveItem()

    console.log('🔍 配置加载完成:', {
      hasConfig: !!config.value,
      visible: (config.value as any)?.visible,
      itemsCount: (config.value as any)?.items?.length,
      shouldShow: shouldShow.value
    })
  } catch (error) {
    console.error('❌ TabBar配置加载失败:', error)
  }
}

// 🎯 检查路径是否需要TabBar的函数
const needsTabBarCheck = (path: string) => {
  // 如果没有配置，无法判断，需要加载
  if (!config.value?.items) return true

  // 🎯 简化：直接路径匹配
  return config.value.items.some(item => {
    return item.path === path
  })
}

// 监听路由变化 - 使用动态路径检查，减少不必要的配置重载
watch(() => route.path, async (newPath, oldPath) => {
  console.log('🚦 TabBar检测到路由变化:', { from: oldPath, to: newPath })

  // 只有在需要TabBar的页面或没有配置时才重新加载
  if (!config.value || needsTabBarCheck(newPath)) {
    console.log('🔄 需要加载TabBar配置')
    await loadTabBarConfig()
  } else {
    console.log('🔄 页面切换不需要重新加载TabBar配置')
  }
}, { immediate: true })

// 生命周期 - 简化版本
onMounted(async () => {
  console.log('🚀 TabBar组件挂载完成')

  // 🎯 初始化时，如果在有效页面，预设稳定状态
  const currentPath = route.path
  const isValidPage = (currentPath.startsWith('/home') || currentPath.startsWith('/device/')) &&
                     !currentPath.includes('/login') &&
                     !currentPath.includes('/webview') &&
                     !currentPath.includes('/404')

  if (isValidPage) {
    console.log('🎯 初始化在有效页面，预设稳定显示状态')
    // 不立即设置为true，等配置加载完成后再设置
  }
})

// 监听路由变化 - 简化稳定状态管理
watch(() => route.path, (newPath, oldPath) => {
  updateActiveItem()

  // 🎯 路由变化时的稳定状态管理 - 简化版本
  if (newPath !== oldPath) {
    // 如果从有效页面切换到另一个有效页面，保持稳定显示一段时间
    const isValidPage = (newPath.startsWith('/home') || newPath.startsWith('/device/')) &&
                       !newPath.includes('/login') &&
                       !newPath.includes('/webview') &&
                       !newPath.includes('/404')

    if (isValidPage && stableShow.value) {
      // 保持显示状态，给配置加载一些时间
      console.log('🎯 路由切换，保持TabBar稳定显示')
    } else if (!isValidPage) {
      // 切换到无效页面，立即隐藏
      stableShow.value = false
      console.log('🎯 切换到无效页面，隐藏TabBar')
    }
  }
})

// 监听配置变化
watch(() => config.value, () => {
  updateActiveItem()
}, { deep: true })

// 监听TabBar显示状态变化，通知父组件
watch(shouldShow, (newValue) => {
  emit('visibility-change', !!newValue)
}, { immediate: true })
</script>

<style scoped lang="scss">
.smart-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  // 🎨 现代化深色主题胶囊式TabBar
  &.tabbar-capsule {
    bottom: 24px;
    left: 50%;
    right: auto;
    transform: translateX(-50%);
    width: auto;
    max-width: 340px;
    min-width: 300px;

    // 🎨 深色主题背景 (#131316)
    background: linear-gradient(
      135deg,
      rgba(19, 19, 22, 0.95) 0%,
      rgba(25, 25, 30, 0.95) 100%
    );

    border-radius: 28px;
    padding: 10px 16px;

    // 🎨 多层阴影系统 - 玻璃拟态效果
    box-shadow:
      // 主要阴影
      0 16px 40px rgba(0, 0, 0, 0.4),
      // 内发光边框
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(255, 255, 255, 0.05),
      // 外发光
      0 0 0 1px rgba(255, 255, 255, 0.08),
      // 深度阴影
      0 8px 24px rgba(0, 0, 0, 0.3);

    // 🎨 玻璃拟态效果
    backdrop-filter: blur(24px) saturate(1.2);
    border: 1px solid rgba(255, 255, 255, 0.12);

    // 🎨 添加微妙的发光动画
    animation: capsule-glow 4s ease-in-out infinite;

    // 🎨 微妙的渐变边框
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 28px;
      padding: 1px;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.1) 100%
      );
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: xor;
      pointer-events: none;
    }

    // 🎨 Shimmer效果 - 现代化光泽
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 28px;
      background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 100%
      );
      background-size: 200% 100%;
      animation: shimmer 3s ease-in-out infinite;
      pointer-events: none;
      opacity: 0.6;
    }

    .tabbar-item {
      flex: 1;
      padding: 12px 8px;
      border-radius: 20px;
      margin: 0 3px;
      position: relative;
      overflow: hidden;
      text-align: center;
      cursor: pointer;

      // 🎨 流畅的动画过渡 - macOS风格
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

      // 🎨 悬停效果 - 玻璃拟态
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.1) 0%,
          rgba(255, 255, 255, 0.05) 100%
        );
        border-radius: 20px;
        opacity: 0;
        transition: all 0.3s ease;
        backdrop-filter: blur(8px);
      }

      // 🎨 悬停状态
      &:hover::before {
        opacity: 1;
        transform: scale(1.02);
      }

      // 🎨 激活状态 - 现代化设计
      &.active {
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.18) 0%,
          rgba(255, 255, 255, 0.12) 100%
        );
        transform: scale(1.08) translateY(-2px);
        box-shadow:
          0 8px 16px rgba(0, 0, 0, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);

        &::before {
          opacity: 0;
        }

        .item-icon {
          transform: translateY(-1px) scale(1.1);
          filter: drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3));
          animation: icon-float 2s ease-in-out infinite;
        }

        .item-text {
          color: rgba(255, 255, 255, 1) !important;  // 🔧 修复：激活状态文字完全不透明
          transform: scale(1);  // 🔧 修复：不缩放文字，保持原始大小
          font-weight: 700;  // 🔧 修复：激活状态更粗字重
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);  // 🔧 修复：增强阴影
        }
      }

      // 🎨 图标样式
      .item-icon {
        display: inline-block;
        // margin-bottom: 3px;  // 🔧 修复：减少图标和文字间距
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        color: rgba(255, 255, 255, 0.95);  // 🔧 修复：提高图标亮度
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
      }

      // 🎨 文字样式 - 现代化字体
      .item-text {
        color: rgba(255, 255, 255, 0.95);  // 🔧 修复：大幅提高文字亮度，确保可见
        font-size: 11px;  // 🔧 修复：稍微增大字体，提高可读性
        font-weight: 600;  // 🔧 修复：增加字重，提高可见性
        line-height: 1.2;
        margin-top: 0;  // 🔧 修复：移除多余的上边距
        letter-spacing: 0;  // 🔧 修复：移除负字间距
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);  // 🔧 修复：增强文字阴影，提高对比度
      }

      // 🎨 触摸反馈 - 移动端优化
      &:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
      }
    }

    // 🔧 胶囊TabBar特殊样式覆盖 - 确保样式独立性
    .item-icon {
      // margin-bottom: 3px !important;  // 强制覆盖通用样式
      color: rgba(255, 255, 255, 0.95) !important;
    }

    .item-text {
      margin-top: 0 !important;  // 强制覆盖通用样式
      color: rgba(255, 255, 255, 0.95) !important;
      font-size: 11px !important;
      font-weight: 600 !important;
    }
  }

  // 🎨 现代简约TabBar（一比一还原参考图片）
  &.tabbar-modern {
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(248, 248, 248, 0.98);  // 🎨 更接近参考图的浅灰背景
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
    padding: 6px 0 max(6px, env(safe-area-inset-bottom));
    height: 60px;  // 🎨 固定高度

    .tabbar-item {
      flex: 1;
      // padding: 4px 8px;
      position: relative;
      transition: all 0.2s ease-out;

      // 🎨 移除背景圆圈效果，使用更简洁的设计
      &.active {
        .item-icon {
          color: #007AFF !important;  // 🎨 iOS蓝色
          transform: scale(1.1);
        }

        .item-text {
          color: #007AFF !important;  // 🎨 iOS蓝色
          font-weight: 600;
          transform: scale(0.95);  // 🎨 轻微缩放效果
        }
      }

      .item-icon {
        display: inline-block;
        // margin-bottom: 2px;
        transition: all 0.2s ease-out;
        color: #8E8E93;  // 🎨 iOS灰色
      }

      .item-text {
        color: #8E8E93;  // 🎨 iOS灰色
        font-size: 10px;  // 🎨 更小的字体
        font-weight: 500;
        line-height: 1.2;
        margin-top: 2px;
        transition: all 0.2s ease-out;
        letter-spacing: -0.2px;  // 🎨 紧凑字间距
      }
    }
  }

  // 默认样式（保持兼容性）
  &.tabbar-default {
    border-top: 1px solid #eee;
    background: #ffffff;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  }

  // 圆角样式
  &.tabbar-rounded {
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    border-top: none;
    box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
    background: #ffffff;
  }

  // 极简样式
  &.tabbar-minimal {
    border-top: none;
    box-shadow: none;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
  }

  // 悬浮样式
  &.tabbar-floating {
    bottom: 16px;
    left: 16px;
    right: 16px;
    width: auto;
    border-radius: 24px;
    border-top: none;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
  }
}

.tabbar-item {
  flex: 1;
  display: block;  // 🔧 修复：改为block布局，避免flex导致的间距问题
  text-align: center;
  padding: 2px 4px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &.active {
    transform: scale(1.05);

    .item-icon {
      animation: tab-bounce 0.3s ease;
    }
  }

  // 悬浮样式的特殊处理
  .tabbar-floating & {
    // padding: 8px 4px;
    border-radius: 12px;
    margin: 0 4px;

    &.active {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.1);
    }
  }

  // 极简样式的特殊处理
  .tabbar-minimal & {
    padding: 8px 4px;

    &.active {
      transform: translateY(-2px);
    }
  }
}

.item-icon {
  position: relative;
  display: inline-block;  // 🔧 修复：确保图标正确显示
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  // margin-bottom: 3px;  // 🔧 修复：调整通用图标和文字间距

  .badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #ff4757, #ff3742);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
    animation: badge-pulse 2s infinite;
  }

  .dot {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #ff4757, #ff3742);
    border-radius: 50%;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
    animation: dot-pulse 2s infinite;
  }
}

.item-text {
  font-size: 11px;  // 🔧 调整：稍微减小字体
  font-weight: 500;
  line-height: 1.2;
  margin-top: 1px;  // 🔧 修复：减少文字和图标间距
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// 🎨 现代化动画效果
@keyframes tab-bounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.15); }
  100% { transform: scale(1.08); }
}

@keyframes badge-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
  }
  50% {
    transform: scale(1.1);
    opacity: 0.9;
    box-shadow: 0 4px 16px rgba(255, 71, 87, 0.6);
  }
}

@keyframes dot-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.6);
  }
}

@keyframes icon-float {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-2px) scale(1.05);
  }
}

@keyframes capsule-glow {
  0%, 100% {
    box-shadow:
      0 16px 40px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.08),
      0 8px 24px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow:
      0 20px 50px rgba(0, 0, 0, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.15),
      0 0 0 1px rgba(255, 255, 255, 0.12),
      0 12px 32px rgba(0, 0, 0, 0.4);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 🎨 确保页面内容不被TabBar遮挡 - 支持不同类型 */
:global(body) {
  padding-bottom: 50px;

  // 胶囊式TabBar不需要额外的底部间距
  &:has(.tabbar-capsule) {
    padding-bottom: 0;
  }

  // 现代简约TabBar需要考虑安全区域
  &:has(.tabbar-modern) {
    padding-bottom: max(50px, calc(50px + env(safe-area-inset-bottom)));
  }
}
</style>
