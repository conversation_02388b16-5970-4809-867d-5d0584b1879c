<!-- 临时 SvgIcon 组件 - 后续需要完整迁移 device-an 的图标系统 -->
<template>
  <span class="svg-icon" :class="className">
    <!-- 使用 Iconify 作为临时替代 -->
    <Icon :icon="getIconName(name)" />
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'

interface Props {
  name: string
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  className: ''
})

// 映射 device-an 的图标名称到 Iconify 图标
const getIconName = (name: string): string => {
  const iconMap: Record<string, string> = {
    'wifi': 'mdi:wifi',
    'card': 'mdi:card',
    'tip': 'mdi:information',
    'home': 'mdi:home',
    'person': 'mdi:account',
    'phone': 'mdi:phone',
    'service': 'mdi:headset',
    'balance': 'mdi:wallet',
    'data': 'mdi:database',
    'time': 'mdi:clock',
    'signal': 'mdi:signal',
    'warning': 'mdi:alert',
    'secure': 'mdi:shield-check',
    'edit': 'mdi:pencil',
    'copy': 'mdi:content-copy',
    'share': 'mdi:share',
    'exit': 'mdi:exit-to-app',
    'close': 'mdi:close',
    'right': 'mdi:chevron-right',
    'add': 'mdi:plus',
    'minus': 'mdi:minus',
    'view': 'mdi:eye',
    'viewNot': 'mdi:eye-off',
    'switch': 'mdi:swap-horizontal',
    'restart': 'mdi:restart',
    'renew': 'mdi:refresh',
    'more': 'mdi:dots-horizontal',
    'gift': 'mdi:gift',
    'electricity': 'mdi:lightning-bolt',
    'days': 'mdi:calendar',
    'code': 'mdi:qrcode',
    'password': 'mdi:lock',
    'active': 'mdi:check-circle',
    'true': 'mdi:check',
    'to': 'mdi:arrow-right',
    'moeny': 'mdi:currency-cny',
    'netWork': 'mdi:network',
    'select': 'mdi:check-circle',
    'wechat': 'mdi:wechat',
    'alipay': 'mdi:alipay',
    'unionpay': 'mdi:credit-card',
    'payment': 'mdi:credit-card-outline',
    'clear': 'mdi:backspace-outline'
  }
  
  return iconMap[name] || 'mdi:help-circle'
}
</script>

<style lang="scss" scoped>
.svg-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
}
</style>
