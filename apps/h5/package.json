{"name": "@lowcode/h5", "version": "0.1.0", "description": "Low-code H5 mobile application", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@iconify/vue": "^4.1.1", "@lowcode/aslib": "workspace:*", "axios": "^1.10.0", "dayjs": "^1.11.13", "js-md5": "^0.8.3", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "qrcode": "^1.5.4", "vant": "^4.9.16", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "@vue/tsconfig": "^0.5.1", "sass": "^1.77.4", "typescript": "^5.4.0", "vite": "^5.2.8", "vue-tsc": "^2.0.11"}}