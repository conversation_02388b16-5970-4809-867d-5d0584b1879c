{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": [
    "env.d.ts",
    "src/**/*",
    "src/**/*.vue",
    "../../packages/ui/src/**/*",
    "../../packages/core/src/**/*"
  ],
  "exclude": ["src/**/__tests__/*"],
  "compilerOptions": {
    "composite": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@lowcode/ui": ["../../packages/ui/src"],
      "@lowcode/core": ["../../packages/core/src"],

      // 模块路径映射
      "@/modules/*": ["./src/modules/*"],
      "@/components/*": ["./src/components/*"],
      "@/composables/*": ["./src/composables/*"],
      "@/stores/*": ["./src/stores/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/api/*": ["./src/api/*"],
      "@/services/*": ["./src/services/*"],
      "@/views/*": ["./src/views/*"],
      "@/router/*": ["./src/router/*"],
      "@/styles/*": ["./src/styles/*"],

      // 设备模块路径映射
      "@device": ["./src/modules/device"],
      "@device/*": ["./src/modules/device/*"],
      "@device/components": ["./src/modules/device/components"],
      "@device/components/*": ["./src/modules/device/components/*"],
      "@device/pages": ["./src/modules/device/pages"],
      "@device/pages/*": ["./src/modules/device/pages/*"],
      "@device/stores": ["./src/modules/device/stores"],
      "@device/stores/*": ["./src/modules/device/stores/*"],
      "@device/composables": ["./src/modules/device/composables"],
      "@device/composables/*": ["./src/modules/device/composables/*"],
      "@device/types": ["./src/modules/device/types"],
      "@device/types/*": ["./src/modules/device/types/*"],
      "@device/utils": ["./src/modules/device/utils"],
      "@device/utils/*": ["./src/modules/device/utils/*"],

      // 商城模块路径映射（为将来扩展准备）
      "@mall": ["./src/modules/mall"],
      "@mall/*": ["./src/modules/mall/*"],
      "@mall/components": ["./src/modules/mall/components"],
      "@mall/components/*": ["./src/modules/mall/components/*"],
      "@mall/pages": ["./src/modules/mall/pages"],
      "@mall/pages/*": ["./src/modules/mall/pages/*"],
      "@mall/stores": ["./src/modules/mall/stores"],
      "@mall/stores/*": ["./src/modules/mall/stores/*"]
    }
  }
}
