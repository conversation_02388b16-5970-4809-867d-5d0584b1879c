import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@lowcode/aslib': resolve(__dirname, '../../packages/aslib/src'),
      '@lowcode/aslib/ui': resolve(__dirname, '../../packages/aslib/src/ui'),
      '@lowcode/aslib/core': resolve(__dirname, '../../packages/aslib/src/core'),
      '@lowcode/aslib/hooks': resolve(__dirname, '../../packages/aslib/src/hooks'),

      // 模块别名
      '@/modules': resolve(__dirname, 'src/modules'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/composables': resolve(__dirname, 'src/composables'),
      '@/stores': resolve(__dirname, 'src/stores'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/api': resolve(__dirname, 'src/api'),
      '@/services': resolve(__dirname, 'src/services'),
      '@/views': resolve(__dirname, 'src/views'),
      '@/router': resolve(__dirname, 'src/router'),
      '@/styles': resolve(__dirname, 'src/styles'),

      // 设备模块别名
      '@device': resolve(__dirname, 'src/modules/device'),
      '@device/components': resolve(__dirname, 'src/modules/device/components'),
      '@device/pages': resolve(__dirname, 'src/modules/device/pages'),
      '@device/stores': resolve(__dirname, 'src/modules/device/stores'),
      '@device/composables': resolve(__dirname, 'src/modules/device/composables'),
      '@device/types': resolve(__dirname, 'src/modules/device/types'),
      '@device/utils': resolve(__dirname, 'src/modules/device/utils'),

      // 商城模块别名（为将来扩展准备）
      '@mall': resolve(__dirname, 'src/modules/mall'),
      '@mall/components': resolve(__dirname, 'src/modules/mall/components'),
      '@mall/pages': resolve(__dirname, 'src/modules/mall/pages'),
      '@mall/stores': resolve(__dirname, 'src/modules/mall/stores')
    }
  },
  // ✅ 完全复制 device-an 的 CSS 配置
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import './src/styles/variables.scss';`
      }
    }
  },
  server: {
    port: 3000, // Vite配置中直接写死端口，通过命令行参数 --port 来覆盖
    host: true,
    proxy: {
      // Device-An Java API代理 (主要API)
      '/Fan': {
        target: 'https://ml.liangaiyun.com/java/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/Fan/, ''),
        configure: (proxy) => {
          proxy.on('proxyReq', (_, req) => {
            console.log('🔄 [代理] Device-An Java API:', req.url)
          })
        }
      },
      // Device-An Python API代理
      '/Lin': {
        target: 'https://ml.liangaiyun.com/py/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/Lin/, ''),
        configure: (proxy) => {
          proxy.on('proxyReq', (_, req) => {
            console.log('🔄 [代理] Device-An Python API:', req.url)
          })
        }
      },
      // 低代码平台API代理
      '/api': {
        target: 'http://localhost:3002', // Vite代理配置中直接写死，通过环境变量在运行时处理
        changeOrigin: true,
        configure: (proxy) => {
          proxy.on('proxyReq', (_, req) => {
            console.log('🔄 [代理] 低代码平台API:', req.url)
          })
        }
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  }
})
