# Device-An 项目迁移标准和规范

## 📋 **总体原则**

### 1. **API接口迁移原则**
- **保持完整性**: 不分散相关API接口到不同文件
- **遵循原始结构**: 完全按照device-an的API调用顺序和逻辑
- **集中管理**: 功能相关的API接口放在同一个store中
- **调用时机一致**: 确保API调用时机与原项目完全一致
- **完整错误处理**: 包含所有异常情况的处理逻辑

### 2. **文件组织结构**
```
apps/h5/src/
├── stores/
│   ├── device.ts          # 设备相关API和状态管理
│   ├── package.ts         # 套餐相关API和状态管理
│   ├── balance.ts         # 余额相关API和状态管理
│   ├── payment.ts         # 支付相关API和状态管理
│   └── wechat.ts          # 微信和系统配置API
├── views/
│   ├── Home.vue           # 首页 (对应LayoutHome.vue)
│   ├── PackageList.vue    # 套餐列表
│   ├── PackagePayment.vue # 套餐支付
│   ├── BalanceList.vue    # 余额管理
│   ├── BalanceDetails.vue # 余额明细
│   ├── RealName.vue       # 实名认证
│   └── EditDevice.vue     # 设备设置
└── components/
    ├── DeviceInfo.vue     # 设备信息组件
    ├── PackageCard.vue    # 套餐卡片组件
    └── ...                # 其他业务组件
```

## 🔧 **API迁移标准**

### 1. **API配置标准**
```typescript
// ✅ 完全复制device-an的配置
const getBaseURL = () => {
  const mode = import.meta.env.MODE || 'development'
  switch (mode) {
    case 'development':
      return {
        java: '/Fan',  // 与device-an完全一致
        python: '/Lin'
      }
    case 'production':
      return {
        java: window.location.protocol + '//' + window.location.hostname + '/java/',
        python: window.location.protocol + '//' + window.location.hostname + '/py/'
      }
    default:
      return {
        java: 'https://cmp.tqzhkj.com/',
        python: 'https://cmp.tqzhkj.com/'
      }
  }
}
```

### 2. **请求拦截器标准**
```typescript
// ✅ 完全复制device-an的请求拦截器
javaAPI.interceptors.request.use((req) => {
  const { ZHIXUN_YAN, ZHIXUN_DATE } = generateKey()
  const deviceToken = getDeviceToken() || 'NOT_LOGIN'
  
  req.headers['deviceToken'] = deviceToken
  req.headers['Conten-Date'] = ZHIXUN_DATE
  req.headers['Conten-Zx'] = ZHIXUN_YAN
  
  return req
})
```

### 3. **响应拦截器标准**
```typescript
// ✅ 完全复制device-an的响应处理逻辑
javaAPI.interceptors.response.use((res) => {
  // 只对明确包含code字段且code为false的响应显示错误
  if (res.data && typeof res.data === 'object' && 
      res.data.hasOwnProperty('code') && !res.data.code) {
    // 显示错误信息
    showFailToast(res.data.msg || '网络错误，请稍后重试')
  }
  return res.data
})
```

## 📊 **Store设计标准**

### 1. **设备Store结构**
```typescript
// apps/h5/src/stores/device.ts
export const useDeviceStore = defineStore('device', () => {
  // ✅ 状态定义 - 完全对应device-an的数据结构
  const key = ref<string>('')
  const details = ref<DeviceDetailsData>({} as DeviceDetailsData)
  const cards = ref<DeviceCardData[]>([])
  const realNameCards = ref<DeviceRealNameCardData[]>([])
  
  // ✅ API方法 - 保持与device-an相同的调用顺序
  const login = async (loginData: LoginData) => { /* ... */ }
  const fetchDeviceDetails = async () => { /* ... */ }
  const fetchDeviceCards = async () => { /* ... */ }
  const fetchRealNameCards = async () => { /* ... */ }
  
  // ✅ 初始化方法 - 按device-an的顺序调用API
  const initialize = async () => {
    await fetchDeviceDetails()
    await fetchDeviceCards()
    await fetchRealNameCards()
  }
})
```

### 2. **API调用时机标准**
- **登录时**: 只调用登录API，获取token
- **路由守卫**: 调用initialize()获取完整设备信息
- **页面刷新**: 检查token，重新获取设备信息
- **用户操作**: 根据具体业务需求调用相应API

## 🎨 **样式迁移标准**

### 1. **SCSS变量完全复制**
```scss
// ✅ 完全复制device-an的样式变量
$primary: rgb(59, 130, 246);
$background: rgb(243, 244, 246);
$radius: 0.6rem;
$shadow: 0 0.4rem 2rem rgba(0, 0, 0, 0.05);
$padding: 0.7rem;

@mixin WhiteBox {
  background-color: #fff;
  border-radius: $radius;
  box-shadow: $shadow;
}
```

### 2. **组件样式迁移**
- **完全复制**: 直接复制device-an的CSS类名和样式规则
- **保持结构**: 维持原有的DOM结构和类名层次
- **响应式**: 保持原有的响应式断点和适配逻辑

## 🔄 **页面迁移流程**

### 1. **分析阶段**
1. 分析原页面的API调用顺序和时机
2. 识别页面中使用的所有API接口
3. 分析数据流和状态管理逻辑
4. 记录错误处理和边界情况

### 2. **迁移阶段**
1. 创建对应的Store文件
2. 迁移所有相关API接口到Store中
3. 保持API调用的原始顺序和逻辑
4. 迁移页面组件和样式
5. 集成API调用到页面生命周期

### 3. **验证阶段**
1. 验证API调用时机和顺序
2. 验证数据格式和状态管理
3. 验证错误处理和用户反馈
4. 验证样式和交互效果

## ⚠️ **注意事项**

### 1. **禁止的操作**
- ❌ 不要将相关API分散到不同文件
- ❌ 不要简化或重组原有的API调用逻辑
- ❌ 不要修改原有的数据结构和字段名
- ❌ 不要改变API的调用时机和顺序

### 2. **必须保持的一致性**
- ✅ API调用的参数格式和返回格式
- ✅ 错误处理的方式和用户提示
- ✅ 数据状态的管理和更新逻辑
- ✅ 页面的视觉效果和交互行为

### 3. **质量检查清单**
- [ ] API接口是否完整迁移
- [ ] 调用时机是否与原项目一致
- [ ] 错误处理是否完整
- [ ] 样式是否与原项目一致
- [ ] 交互逻辑是否正确
- [ ] 数据流是否正常
