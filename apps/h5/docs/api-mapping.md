# Device-An API接口完整映射表

## 📋 **API接口分类和迁移状态**

### 🔐 **设备相关API (device.ts)**

| 原API名称 | 端点 | 方法 | 状态 | 迁移位置 | 调用时机 |
|-----------|------|------|------|----------|----------|
| `LoginDevice` | `front/deviceIndex/deviceLogin` | POST | ✅ 已迁移 | `device.ts` | 用户登录时 |
| `GetDeviceDetails` | `frontDevice/device/getDeviceInfo` | GET | ✅ 已迁移 | `device.ts` | 登录后/路由守卫 |
| `GetDeviceCards` | `frontDevice/device/getDeviceCardList` | GET | ✅ 已迁移 | `device.ts` | 登录后/路由守卫 |
| `GetDeviceRuleDetails` | `front/deviceIndex/getGroupData` | GET | ⏳ 待迁移 | `device.ts` | 需要规则信息时 |
| `GetDeviceRealNameCards` | `frontDevice/device/getDeviceRealNameCardList` | GET | ✅ 已迁移 | `device.ts` | 登录后/路由守卫 |
| `RenewDevice` | `frontDevice/device/updateDevice` | PUT | ⏳ 待迁移 | `device.ts` | 用户点击续费 |

### 📦 **套餐相关API (package.ts)**

| 原API名称 | 端点 | 方法 | 状态 | 迁移位置 | 调用时机 |
|-----------|------|------|------|----------|----------|
| `GetPackageClass` | `frontDevice/package/getPackageClass` | GET | ⏳ 待迁移 | `package.ts` | 套餐页面加载 |
| `GetPackageType` | `frontDevice/package/getPackageType` | GET | ⏳ 待迁移 | `package.ts` | 套餐页面加载 |
| `GetPackageList` | `frontDevice/package/getPackageList` | GET | ⏳ 待迁移 | `package.ts` | 套餐页面加载 |
| `CreatePackageOrder` | `frontDevice/package/notPayOrder` | POST | ⏳ 待迁移 | `package.ts` | 用户选择套餐 |
| `GetPackageOrder` | `frontDevice/devicePackageOrder/getPackOrderList` | GET | ⏳ 待迁移 | `package.ts` | 查看订单历史 |

### 💰 **余额相关API (balance.ts)**

| 原API名称 | 端点 | 方法 | 状态 | 迁移位置 | 调用时机 |
|-----------|------|------|------|----------|----------|
| `GetBalanceList` | `frontDevice/prestore/getPrestoreLimit` | GET | ⏳ 待迁移 | `balance.ts` | 余额页面加载 |
| `GetBalanceTemplate` | `frontDevice/device/getBalanceList` | GET | ⏳ 待迁移 | `balance.ts` | 余额页面加载 |
| `CreateBalanceOrder` | `frontDevice/prestore/notPayOrder` | POST | ⏳ 待迁移 | `balance.ts` | 用户选择充值 |
| `GetBalanceDetails` | `frontDevice/deviceBalance/getList` | GET | ⏳ 待迁移 | `balance.ts` | 查看余额明细 |
| `GetMonthBalanceData` | `frontDevice/deviceBalance/getMonthData` | GET | ⏳ 待迁移 | `balance.ts` | 月度统计 |

### 💳 **支付相关API (payment.ts)**

| 原API名称 | 端点 | 方法 | 状态 | 迁移位置 | 调用时机 |
|-----------|------|------|------|----------|----------|
| `GetPaymentMethods` | `front/payment/getPaymentMethods` | GET | ⏳ 待迁移 | `payment.ts` | 支付页面加载 |
| `GetPaymentParameter` | `front/payment/getPaymentParameter` | GET | ⏳ 待迁移 | `payment.ts` | 选择支付方式 |
| `CreatePayment` | `front/payment/create` | POST | ⏳ 待迁移 | `payment.ts` | 确认支付 |

### 👤 **用户相关API (wechat.ts)**

| 原API名称 | 端点 | 方法 | 状态 | 迁移位置 | 调用时机 |
|-----------|------|------|------|----------|----------|
| `GetWeChatConfig` | `pay/getWxJsParams` | GET | ⏳ 待迁移 | `wechat.ts` | 微信支付时 |
| `GetUserWeChatEmpower` | `backend/backstage/wxpublic/getinfo/` | POST | ⏳ 待迁移 | `wechat.ts` | 微信授权 |
| `GetManageConfig` | `backend/backstage/getsitesettings/` | GET | ✅ 已迁移 | `wechat.ts` | 应用启动时 |
| `GetRechargeConfig` | `front/setup/getFrontSetup` | GET | ✅ 已迁移 | `wechat.ts` | 应用启动时 |
| `GetNotice` | `backend/backstage/hz5deviceannouncement/` | GET | ⏳ 待迁移 | `wechat.ts` | 获取公告 |

## 🔄 **API调用顺序和依赖关系**

### 1. **应用启动时**
```
1. GetManageConfig (系统配置)
2. GetRechargeConfig (充值配置)
```

### 2. **用户登录时**
```
1. LoginDevice (设备登录)
2. GetDeviceDetails (设备详情)
3. GetDeviceCards (设备卡片)
4. GetDeviceRealNameCards (实名卡片)
```

### 3. **套餐页面加载时**
```
1. GetPackageClass (套餐分类)
2. GetPackageType (套餐类型)
3. GetPackageList (套餐列表)
```

### 4. **余额页面加载时**
```
1. GetBalanceList (余额选项)
2. GetBalanceTemplate (余额模板)
3. GetBalanceDetails (余额明细)
```

### 5. **支付流程**
```
1. CreatePackageOrder/CreateBalanceOrder (创建订单)
2. GetPaymentMethods (获取支付方式)
3. GetPaymentParameter (获取支付参数)
4. CreatePayment (创建支付)
```

## 📝 **迁移检查清单**

### ✅ **已完成的迁移**
- [x] 设备登录API
- [x] 设备详情API
- [x] 设备卡片API
- [x] 实名卡片API
- [x] 系统配置API
- [x] 充值配置API

### ⏳ **待迁移的API**
- [ ] 设备规则API
- [ ] 设备续费API
- [ ] 套餐相关API (5个)
- [ ] 余额相关API (5个)
- [ ] 支付相关API (3个)
- [ ] 用户相关API (3个)

### 🎯 **迁移优先级**
1. **高优先级**: 套餐相关API (核心业务流程)
2. **中优先级**: 余额相关API (重要功能)
3. **低优先级**: 支付相关API (支持功能)
4. **最低优先级**: 其他用户API (辅助功能)

## 🔧 **技术实现要点**

### 1. **错误处理标准**
```typescript
// ✅ 统一的错误处理格式
try {
  const response = await apiCall()
  if (response && response.code) {
    // 成功处理
  } else if (response && response.id) {
    // 直接返回数据对象
  } else {
    // 错误处理
    throw new Error(response?.msg || '请求失败')
  }
} catch (error) {
  console.error('API调用失败:', error)
  // 错误已由拦截器处理，这里记录日志
}
```

### 2. **数据格式转换**
```typescript
// ✅ FormData转换 (来自device-an)
const toFormData = (data: Record<string, any>): FormData => {
  const formData = new FormData()
  for (const key in data) {
    if (data[key] !== null && data[key] !== '' && data[key] !== undefined) {
      formData.append(key, data[key])
    }
  }
  return formData
}
```

### 3. **状态管理模式**
```typescript
// ✅ 统一的状态管理模式
const useXxxStore = defineStore('xxx', () => {
  // 状态定义
  const data = ref<DataType[]>([])
  const loading = ref<boolean>(false)
  
  // API方法
  const fetchData = async () => { /* ... */ }
  
  // 初始化方法
  const initialize = async () => { /* ... */ }
  
  return { data, loading, fetchData, initialize }
})
```
