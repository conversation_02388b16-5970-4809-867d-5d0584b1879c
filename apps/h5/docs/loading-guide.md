# 全局Loading使用指南 🎨

这是一个功能强大、高度可定制的全局Loading组件系统，支持多种动画效果和主题风格。

## ✨ 特性

- 🎨 **10种动画效果**: spinner, dots, wave, pulse, bounce, circular, gradient等
- 🎭 **10个预设主题**: 科技蓝、清新绿、渐变紫、活力橙等
- 📱 **响应式设计**: 支持不同尺寸(small/medium/large)
- 🎯 **业务场景**: 登录、支付、上传等专用主题
- 📊 **进度显示**: 支持手动和自动进度条
- ⚙️ **配置文件**: 通过配置文件轻松切换主题
- 🔄 **TypeScript**: 完整的类型支持

## 🚀 快速开始

### 1. 基础使用

```typescript
import { useLoading } from '@/composables/useGlobalLoading'

const loading = useLoading()

// 显示loading
loading.show()

// 隐藏loading
loading.hide()

// 带文本显示
loading.show({ text: '加载中...' })

// 带进度显示
loading.show({ 
  text: '上传中...', 
  showProgress: true 
})
```

### 2. 切换主题

```typescript
// 方法1: 通过配置文件切换 (推荐)
// 编辑 src/config/loadingThemes.ts
export const APP_LOADING_THEME = 'tech' // 全局主题

// 方法2: 运行时切换主题
import { loadingPresets } from '@/config/loadingThemes'

loading.show(loadingPresets.gradient)
```

### 3. 业务场景使用

```typescript
import { getBusinessTheme } from '@/config/loadingThemes'

// 登录场景
loading.show(getBusinessTheme('login'))

// 支付场景  
loading.show(getBusinessTheme('payment'))

// 文件上传场景
loading.show(getBusinessTheme('upload'))
```

## 🎨 主题预览

### 科技蓝主题 (tech)
```typescript
{
  type: 'circular',
  color: '#00d4ff',
  backgroundColor: 'rgba(15, 23, 42, 0.95)',
  text: '系统处理中...',
  gradient: { from: '#00d4ff', to: '#0066cc' }
}
```

### 清新绿主题 (nature)
```typescript
{
  type: 'wave',
  color: '#52c41a',
  backgroundColor: 'rgba(255, 255, 255, 0.95)',
  text: '加载中，请稍候...'
}
```

### 渐变紫主题 (gradient)
```typescript
{
  type: 'gradient',
  color: '#722ed1',
  backgroundColor: 'rgba(0, 0, 0, 0.8)',
  gradient: { from: '#722ed1', to: '#eb2f96' }
}
```

## 📱 在Vue组件中使用

```vue
<template>
  <div>
    <button @click="handleLogin">登录</button>
    <button @click="handlePayment">支付</button>
    <button @click="simulateUpload">上传文件</button>
  </div>
</template>

<script setup lang="ts">
import { useLoading } from '@/composables/useGlobalLoading'
import { getBusinessTheme, loadingPresets } from '@/config/loadingThemes'

const loading = useLoading()

// 登录示例
const handleLogin = async () => {
  loading.show(getBusinessTheme('login'))
  
  try {
    // 登录API调用
    await loginApi()
    loading.hide()
  } catch (error) {
    loading.hide()
    // 错误处理
  }
}

// 支付示例
const handlePayment = async () => {
  loading.show({
    ...getBusinessTheme('payment'),
    text: '微信支付处理中...'
  })
  
  // 模拟支付进度
  for (let i = 0; i <= 100; i += 10) {
    loading.updateProgress(i)
    loading.updateText(`支付进度: ${i}%`)
    await new Promise(resolve => setTimeout(resolve, 200))
  }
  
  loading.hide()
}

// 文件上传示例
const simulateUpload = async () => {
  // 使用模拟功能
  await loading.simulate(3000, {
    ...loadingPresets.gradient,
    text: '文件上传中...'
  })
}
</script>
```

## ⚙️ 配置选项

### LoadingConfig 接口

```typescript
interface LoadingConfig {
  // 动画类型
  type: 'spinner' | 'dots' | 'wave' | 'pulse' | 'bounce' | 'circular' | 'gradient'
  
  // 尺寸
  size: 'small' | 'medium' | 'large'
  
  // 颜色
  color: string                // 主色调
  backgroundColor: string      // 背景色
  textColor?: string          // 文字颜色
  
  // 文本
  text?: string               // 显示文本
  
  // 位置
  position: 'center' | 'top' | 'bottom'
  
  // 遮罩
  overlay: boolean            // 是否显示遮罩
  overlayOpacity: number      // 遮罩透明度
  
  // 动画
  duration: number            // 动画持续时间
  
  // 进度
  showProgress?: boolean      // 是否显示进度条
  
  // 渐变 (gradient类型专用)
  gradient?: {
    from: string
    to: string  
    direction: string
  }
  
  // 高级
  zIndex: number              // 层级
  customIcon?: string         // 自定义图标
}
```

## 🎯 API 方法

### loading.show(options?)
显示Loading
```typescript
loading.show({
  type: 'spinner',
  text: '加载中...',
  showProgress: true,
  color: '#1989fa'
})
```

### loading.hide(force?)
隐藏Loading
```typescript
loading.hide()        // 普通隐藏
loading.hide(true)    // 强制隐藏所有
```

### loading.updateProgress(progress)
更新进度
```typescript
loading.updateProgress(50)  // 设置为50%
```

### loading.updateText(text)
更新文本
```typescript
loading.updateText('正在保存...')
```

### loading.setTheme(theme)
切换主题
```typescript
loading.setTheme(loadingPresets.tech)
```

### loading.simulate(duration, options)
模拟加载过程
```typescript
await loading.simulate(3000, {
  text: '初始化中...',
  type: 'gradient'
})
```

## 🔧 自定义主题

### 1. 修改预设主题
编辑 `src/config/loadingThemes.ts`:

```typescript
export const APP_LOADING_THEME = 'gradient' // 改为你喜欢的主题
```

### 2. 创建自定义主题
```typescript
const myCustomTheme = {
  type: 'circular',
  size: 'large',
  color: '#ff6b6b',
  backgroundColor: 'rgba(0, 0, 0, 0.8)',
  text: '我的自定义加载...',
  textColor: '#fff',
  gradient: {
    from: '#ff6b6b',
    to: '#4ecdc4',
    direction: '45deg'
  }
}

loading.show(myCustomTheme)
```

### 3. 业务场景定制
```typescript
// 在 loadingThemes.ts 中添加
export const businessThemes = {
  myCustomScene: {
    ...loadingPresets.tech,
    text: '自定义业务处理中...',
    showProgress: true
  }
}
```

## 🎨 动画类型说明

- **spinner**: 经典旋转圆环
- **dots**: 跳跃的点点
- **wave**: 波浪式条形动画
- **pulse**: 脉冲扩散效果
- **bounce**: 弹跳小球
- **circular**: 圆形进度动画
- **gradient**: 渐变旋转效果

## 📝 使用建议

1. **全局主题**: 在 `loadingThemes.ts` 中设置 `APP_LOADING_THEME`
2. **业务场景**: 使用 `getBusinessTheme()` 获取专用主题
3. **进度显示**: 长时间操作建议开启 `showProgress`
4. **文本提示**: 为用户提供清晰的操作反馈
5. **主题一致性**: 保持应用内Loading风格统一

## 🔄 与支付系统集成

```typescript
// 在 dxPayment.vue 中使用
import { useLoading } from '@/composables/useGlobalLoading'
import { getBusinessTheme } from '@/config/loadingThemes'

const loading = useLoading()

const handlePayment = async () => {
  // 显示支付Loading
  loading.show(getBusinessTheme('payment'))
  
  try {
    const result = await paymentApi()
    loading.hide()
    
    if (result.success) {
      // 显示成功Loading
      loading.show({
        type: 'circular',
        color: '#52c41a',
        text: '支付成功！'
      })
      
      setTimeout(() => {
        loading.hide()
        router.push('/success')
      }, 2000)
    }
  } catch (error) {
    loading.hide()
  }
}
```

这个Loading系统完全可以通过配置文件进行定制，支持多种美观的动画效果，让你的应用体验更加流畅和专业！ 🎉