# H5应用生产环境配置

# ==================== API配置 ====================

# 统一API服务地址（生产环境根据实际部署修改）
VITE_API_BASE_URL=/
VITE_API_PREFIX=/api
VITE_PUBLIC_API_PREFIX=/api/public

# 其他服务配置
VITE_DESIGNER_BASE_URL=https://designer.your-domain.com
VITE_WS_URL=wss://api.your-domain.com

# 兼容旧版本配置（逐步迁移）
VITE_DEVICE_AN_API_URL=https://api.device-an.com
VITE_LOWCODE_API_URL=https://api.lowcode.com

# ==================== 安全配置 ====================

# 认证密钥（生产环境必须设置为安全的密钥）
VITE_AUTH_SECRET=your-production-secret-key-here

# ==================== 应用配置 ====================

# 应用标题
VITE_APP_TITLE=低代码H5应用

# 应用版本
VITE_APP_VERSION=1.0.0

# 应用环境
VITE_APP_ENV=production

# ==================== 功能开关 ====================

# 是否启用调试模式
VITE_DEBUG_MODE=false

# 是否启用性能监控
VITE_ENABLE_PERFORMANCE_MONITOR=true

# 是否启用错误上报
VITE_ENABLE_ERROR_REPORTING=true

# ==================== 第三方服务 ====================

# 错误监控服务
VITE_SENTRY_DSN=

# 统计分析服务
VITE_ANALYTICS_ID=

# CDN地址
VITE_CDN_URL=
