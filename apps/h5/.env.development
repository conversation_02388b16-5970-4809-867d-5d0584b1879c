# H5应用开发环境配置

# ==================== API配置 ====================

# 统一API服务地址
VITE_API_BASE_URL=http://localhost:3002
VITE_API_PREFIX=/api
VITE_PUBLIC_API_PREFIX=/api/public

# 其他服务配置
VITE_DESIGNER_BASE_URL=http://localhost:3001
VITE_WS_URL=ws://localhost:3002

# 兼容旧版本配置
VITE_DEVICE_AN_API_URL=/Fan
VITE_LOWCODE_API_URL=http://localhost:3002

# ==================== 安全配置 ====================

# 认证密钥（开发环境可以使用默认值，生产环境必须更改）
VITE_AUTH_SECRET=django-insecure-_=django-insecure-_=django-insecure-_=0rpi4cfhzdus5ih*4^8p%j)zdg%y2i^_d6_tbe(z$tfk!yp%

# ==================== 应用配置 ====================

# 应用标题
VITE_APP_TITLE=低代码H5应用(开发)

# 应用版本
VITE_APP_VERSION=1.0.0

# 应用环境
VITE_APP_ENV=development

# 默认应用ID
VITE_DEFAULT_APP_ID=default-device-app

# ==================== 功能开关 ====================

# 是否启用调试模式
VITE_DEBUG_MODE=true

# 是否启用性能监控
VITE_ENABLE_PERFORMANCE_MONITOR=true

# 是否启用错误上报
VITE_ENABLE_ERROR_REPORTING=false

# ==================== 开发配置 ====================

# 开发服务器端口
VITE_DEV_PORT=3000

# 是否启用HTTPS
VITE_HTTPS=false
