/**
 * UniApp-X 存储管理钩子
 * 
 * 适配uni.setStorage和uni.getStorage API
 * 提供响应式的本地存储操作
 */

import { ref, computed, watch, Ref } from 'vue'

// ==================== 类型定义 ====================

/**
 * 存储类型 (UniApp-X只支持一种存储)
 */
export type StorageType = 'storage'

/**
 * 序列化器接口
 */
export interface Serializer<T> {
  read: (value: string) => T
  write: (value: T) => string
}

/**
 * 存储配置接口
 */
export interface StorageConfig<T> {
  /** 默认值 */
  defaultValue?: T
  /** 自定义序列化器 */
  serializer?: Serializer<T>
  /** 是否在写入时立即同步 */
  syncDefaults?: boolean
  /** 存储键前缀 */
  keyPrefix?: string
  /** 是否启用加密 */
  encrypt?: boolean
  /** 加密密钥 */
  encryptKey?: string
}

// ==================== 内置序列化器 ====================

/**
 * 默认序列化器
 */
const defaultSerializer: Serializer<any> = {
  read: (value: string) => {
    try {
      return JSON.parse(value)
    } catch {
      return value
    }
  },
  write: (value: any) => {
    return typeof value === 'string' ? value : JSON.stringify(value)
  }
}

/**
 * 字符串序列化器
 */
const stringSerializer: Serializer<string> = {
  read: (value: string) => value,
  write: (value: string) => value
}

/**
 * 数字序列化器
 */
const numberSerializer: Serializer<number> = {
  read: (value: string) => Number(value),
  write: (value: number) => String(value)
}

/**
 * 布尔序列化器
 */
const booleanSerializer: Serializer<boolean> = {
  read: (value: string) => value === 'true',
  write: (value: boolean) => String(value)
}

/**
 * 日期序列化器
 */
const dateSerializer: Serializer<Date> = {
  read: (value: string) => new Date(value),
  write: (value: Date) => value.toISOString()
}

// ==================== 加密工具 ====================

/**
 * 简单的Base64编码（UniApp-X兼容）
 */
function simpleEncode(text: string): string {
  try {
    // UniApp-X环境下使用简单编码
    const encoded = Buffer.from(text, 'utf8').toString('base64')
    return encoded
  } catch {
    return text
  }
}

/**
 * 简单的Base64解码
 */
function simpleDecode(encoded: string): string {
  try {
    const decoded = Buffer.from(encoded, 'base64').toString('utf8')
    return decoded
  } catch {
    return encoded
  }
}

// ==================== 主要钩子函数 ====================

/**
 * UniApp-X 本地存储管理钩子
 * 基于uni.setStorage和uni.getStorage
 */
export function useStorage<T>(
  key: string,
  config: StorageConfig<T> = {}
): [Ref<T>, (value: T) => void, () => void] {
  
  const {
    defaultValue,
    serializer = defaultSerializer,
    syncDefaults = true,
    keyPrefix = '',
    encrypt = false,
    encryptKey = 'lowcode_default_key'
  } = config
  
  const fullKey = keyPrefix ? `${keyPrefix}${key}` : key
  
  /**
   * 读取存储值
   */
  const read = (): T => {
    try {
      const value = uni.getStorageSync(fullKey)
      if (value === '' || value === null || value === undefined) {
        return defaultValue as T
      }
      
      let decodedValue = value
      if (encrypt) {
        decodedValue = simpleDecode(value + encryptKey)
      }
      
      return serializer.read(decodedValue)
    } catch (error) {
      console.warn(`Failed to read storage key "${fullKey}":`, error)
      return defaultValue as T
    }
  }
  
  /**
   * 写入存储值
   */
  const write = (value: T): void => {
    try {
      let serialized = serializer.write(value)
      if (encrypt) {
        serialized = simpleEncode(serialized + encryptKey)
      }
      
      uni.setStorageSync(fullKey, serialized)
    } catch (error) {
      console.error(`Failed to write storage key "${fullKey}":`, error)
    }
  }
  
  /**
   * 删除存储值
   */
  const remove = (): void => {
    try {
      uni.removeStorageSync(fullKey)
      storedValue.value = defaultValue as T
    } catch (error) {
      console.error(`Failed to remove storage key "${fullKey}":`, error)
    }
  }
  
  // 创建响应式引用
  const storedValue = ref<T>(read())
  
  // 监听值变化，自动写入存储
  watch(
    storedValue,
    (newValue) => {
      write(newValue)
    },
    { deep: true }
  )
  
  // 如果启用同步默认值且当前值为空，写入默认值
  if (syncDefaults && defaultValue !== undefined && storedValue.value === undefined) {
    storedValue.value = defaultValue
  }
  
  return [storedValue as Ref<T>, (value: T) => { storedValue.value = value }, remove]
}

/**
 * 批量存储管理钩子
 */
export function useBatchStorage<T extends Record<string, any>>(
  keys: (keyof T)[],
  config: StorageConfig<any> = {}
) {
  const storageItems = {} as Record<keyof T, ReturnType<typeof useStorage>>
  
  // 为每个键创建存储项
  keys.forEach(key => {
    storageItems[key] = useStorage(String(key), config)
  })
  
  /**
   * 获取所有值
   */
  const getAll = (): Partial<T> => {
    const result = {} as Partial<T>
    keys.forEach(key => {
      result[key] = storageItems[key][0].value as T[keyof T]
    })
    return result
  }
  
  /**
   * 设置所有值
   */
  const setAll = (values: Partial<T>): void => {
    Object.entries(values).forEach(([key, value]) => {
      if (storageItems[key as keyof T]) {
        storageItems[key as keyof T][1](value)
      }
    })
  }
  
  /**
   * 清空所有值
   */
  const clearAll = (): void => {
    keys.forEach(key => {
      storageItems[key][2]()
    })
  }
  
  /**
   * 响应式的所有值
   */
  const allValues = computed(() => getAll())
  
  return {
    items: storageItems,
    getAll,
    setAll,
    clearAll,
    allValues
  }
}

/**
 * 存储工具函数集合
 */
export const storageUtils = {
  /**
   * 获取存储信息
   */
  async getStorageInfo(): Promise<any> {
    return new Promise((resolve, reject) => {
      uni.getStorageInfo({
        success: resolve,
        fail: reject
      })
    })
  },
  
  /**
   * 清空存储
   */
  clearStorage(): void {
    try {
      uni.clearStorageSync()
    } catch (error) {
      console.error('Failed to clear storage:', error)
    }
  },
  
  /**
   * 异步获取存储值
   */
  async getStorageAsync(key: string): Promise<any> {
    return new Promise((resolve, reject) => {
      uni.getStorage({
        key,
        success: (res) => resolve(res.data),
        fail: reject
      })
    })
  },
  
  /**
   * 异步设置存储值
   */
  async setStorageAsync(key: string, data: any): Promise<void> {
    return new Promise((resolve, reject) => {
      uni.setStorage({
        key,
        data,
        success: () => resolve(),
        fail: reject
      })
    })
  },
  
  /**
   * 异步删除存储值
   */
  async removeStorageAsync(key: string): Promise<void> {
    return new Promise((resolve, reject) => {
      uni.removeStorage({
        key,
        success: () => resolve(),
        fail: reject
      })
    })
  },
  
  /**
   * 按前缀删除存储项
   */
  async removeByPrefix(prefix: string): Promise<number> {
    try {
      const info = await this.getStorageInfo()
      const keysToRemove = info.keys.filter((key: string) => key.startsWith(prefix))
      
      for (const key of keysToRemove) {
        await this.removeStorageAsync(key)
      }
      
      return keysToRemove.length
    } catch (error) {
      console.error('Failed to remove by prefix:', error)
      return 0
    }
  },
  
  /**
   * 导出存储数据
   */
  async exportStorage(): Promise<Record<string, any>> {
    try {
      const info = await this.getStorageInfo()
      const data: Record<string, any> = {}
      
      for (const key of info.keys) {
        try {
          data[key] = await this.getStorageAsync(key)
        } catch {
          // 忽略单个key的错误
        }
      }
      
      return data
    } catch (error) {
      console.error('Failed to export storage:', error)
      return {}
    }
  },
  
  /**
   * 导入存储数据
   */
  async importStorage(data: Record<string, any>): Promise<void> {
    for (const [key, value] of Object.entries(data)) {
      try {
        await this.setStorageAsync(key, value)
      } catch (error) {
        console.error(`Failed to import key "${key}":`, error)
      }
    }
  }
}

// 导出序列化器
export const serializers = {
  default: defaultSerializer,
  string: stringSerializer,
  number: numberSerializer,
  boolean: booleanSerializer,
  date: dateSerializer
}