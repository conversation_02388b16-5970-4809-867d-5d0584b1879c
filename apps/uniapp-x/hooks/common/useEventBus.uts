/**
 * UniApp-X 事件总线钩子
 * 
 * 基于uni.$emit和uni.$on的事件通信系统
 * 提供全局事件管理功能
 */

import { ref, onUnmounted } from 'vue'

// ==================== 类型定义 ====================

/**
 * 事件监听器类型
 */
export type EventListener<T = any> = (data: T) => void

/**
 * 事件配置接口
 */
export interface EventConfig {
  /** 是否只监听一次 */
  once?: boolean
  /** 监听器优先级 */
  priority?: number
  /** 监听器标识符 */
  id?: string
}

/**
 * 事件历史记录
 */
export interface EventHistory {
  event: string
  data: any
  timestamp: number
  source?: string
}

// ==================== 事件总线实现 ====================

/**
 * UniApp-X 事件总线钩子
 * 基于uni全局事件系统
 */
export function useEventBus() {
  
  // 本地监听器存储（用于组件卸载时清理）
  const localListeners = ref<Map<string, Set<EventListener>>>(new Map())
  
  // 事件历史记录
  const eventHistory = ref<EventHistory[]>([])
  const maxHistorySize = 100
  
  /**
   * 发布事件
   */
  const emit = <T = any>(event: string, data?: T, source?: string) => {
    console.log(`📤 [EventBus] 发布事件: ${event}`, data)
    
    // 记录事件历史
    eventHistory.value.push({
      event,
      data,
      timestamp: Date.now(),
      source
    })
    
    // 限制历史记录大小
    if (eventHistory.value.length > maxHistorySize) {
      eventHistory.value.shift()
    }
    
    // 使用uni全局事件系统
    uni.$emit(event, data)
  }
  
  /**
   * 监听事件
   */
  const on = <T = any>(
    event: string, 
    listener: EventListener<T>, 
    config: EventConfig = {}
  ) => {
    const { once = false, priority = 0, id } = config
    
    console.log(`📥 [EventBus] 监听事件: ${event}`, { once, priority, id })
    
    // 包装监听器以支持额外功能
    const wrappedListener = (data: T) => {
      try {
        listener(data)
        
        // 如果是一次性监听器，监听后自动移除
        if (once) {
          off(event, wrappedListener)
        }
      } catch (error) {
        console.error(`❌ [EventBus] 事件监听器错误 [${event}]:`, error)
      }
    }
    
    // 存储到本地监听器映射（用于组件卸载清理）
    if (!localListeners.value.has(event)) {
      localListeners.value.set(event, new Set())
    }
    localListeners.value.get(event)!.add(wrappedListener)
    
    // 使用uni全局事件系统
    uni.$on(event, wrappedListener)
    
    // 返回取消监听的函数
    return () => off(event, wrappedListener)
  }
  
  /**
   * 取消监听事件
   */
  const off = <T = any>(event: string, listener?: EventListener<T>) => {
    if (listener) {
      console.log(`📤 [EventBus] 取消监听事件: ${event}`)
      
      // 从本地映射中移除
      const eventListeners = localListeners.value.get(event)
      if (eventListeners) {
        eventListeners.delete(listener)
        if (eventListeners.size === 0) {
          localListeners.value.delete(event)
        }
      }
      
      // 从uni全局事件系统移除
      uni.$off(event, listener)
    } else {
      console.log(`📤 [EventBus] 取消所有监听: ${event}`)
      
      // 移除所有该事件的监听器
      localListeners.value.delete(event)
      uni.$off(event)
    }
  }
  
  /**
   * 监听一次事件（语法糖）
   */
  const once = <T = any>(event: string, listener: EventListener<T>) => {
    return on(event, listener, { once: true })
  }
  
  /**
   * 批量监听事件
   */
  const onMultiple = <T = any>(
    events: string[], 
    listener: EventListener<{ event: string; data: T }>,
    config: EventConfig = {}
  ) => {
    const unsubscribers: Array<() => void> = []
    
    events.forEach(event => {
      const unsubscribe = on(event, (data: T) => {
        listener({ event, data })
      }, config)
      unsubscribers.push(unsubscribe)
    })
    
    // 返回批量取消监听的函数
    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe())
    }
  }
  
  /**
   * 等待事件（Promise版本）
   */
  const waitFor = <T = any>(event: string, timeout?: number): Promise<T> => {
    return new Promise((resolve, reject) => {
      const unsubscribe = once(event, (data: T) => {
        resolve(data)
      })
      
      // 设置超时
      if (timeout) {
        setTimeout(() => {
          unsubscribe()
          reject(new Error(`等待事件 "${event}" 超时 (${timeout}ms)`))
        }, timeout)
      }
    })
  }
  
  /**
   * 检查是否有监听器
   */
  const hasListeners = (event: string): boolean => {
    return localListeners.value.has(event) && 
           localListeners.value.get(event)!.size > 0
  }
  
  /**
   * 获取事件监听器数量
   */
  const getListenerCount = (event: string): number => {
    return localListeners.value.get(event)?.size || 0
  }
  
  /**
   * 获取所有监听的事件名
   */
  const getEventNames = (): string[] => {
    return Array.from(localListeners.value.keys())
  }
  
  /**
   * 清除所有本地监听器
   */
  const clearAllListeners = () => {
    console.log('🗑️ [EventBus] 清除所有监听器')
    
    localListeners.value.forEach((listeners, event) => {
      listeners.forEach(listener => {
        uni.$off(event, listener)
      })
    })
    
    localListeners.value.clear()
  }
  
  /**
   * 获取事件历史
   */
  const getEventHistory = (event?: string): EventHistory[] => {
    if (event) {
      return eventHistory.value.filter(h => h.event === event)
    }
    return [...eventHistory.value]
  }
  
  /**
   * 清除事件历史
   */
  const clearEventHistory = () => {
    eventHistory.value = []
    console.log('🗑️ [EventBus] 事件历史已清除')
  }
  
  /**
   * 重放事件（基于历史记录）
   */
  const replayEvent = (event: string, fromTimestamp?: number) => {
    const history = getEventHistory(event)
    const replayEvents = fromTimestamp 
      ? history.filter(h => h.timestamp >= fromTimestamp)
      : history
    
    console.log(`🔄 [EventBus] 重放事件 ${event}, 共 ${replayEvents.length} 条`)
    
    replayEvents.forEach(h => {
      emit(h.event, h.data, `replay:${h.source || 'unknown'}`)
    })
  }
  
  // ==================== 组件级别的事件管理 ====================
  
  /**
   * 创建带命名空间的事件总线
   */
  const createNamespacedBus = (namespace: string) => {
    const prefixEvent = (event: string) => `${namespace}:${event}`
    
    return {
      emit: <T = any>(event: string, data?: T) => emit(prefixEvent(event), data, namespace),
      on: <T = any>(event: string, listener: EventListener<T>, config?: EventConfig) => 
        on(prefixEvent(event), listener, config),
      off: <T = any>(event: string, listener?: EventListener<T>) => off(prefixEvent(event), listener),
      once: <T = any>(event: string, listener: EventListener<T>) => once(prefixEvent(event), listener),
      waitFor: <T = any>(event: string, timeout?: number) => waitFor(prefixEvent(event), timeout),
      hasListeners: (event: string) => hasListeners(prefixEvent(event)),
      getListenerCount: (event: string) => getListenerCount(prefixEvent(event))
    }
  }
  
  // ==================== 生命周期管理 ====================
  
  // 组件卸载时自动清理监听器
  onUnmounted(() => {
    clearAllListeners()
  })
  
  return {
    // 基础方法
    emit,
    on,
    off,
    once,
    onMultiple,
    waitFor,
    
    // 查询方法
    hasListeners,
    getListenerCount,
    getEventNames,
    
    // 管理方法
    clearAllListeners,
    
    // 历史相关
    getEventHistory,
    clearEventHistory,
    replayEvent,
    
    // 高级功能
    createNamespacedBus,
    
    // 响应式数据
    eventHistory: eventHistory,
    localListeners: localListeners
  }
}

// ==================== 全局事件总线实例 ====================

/**
 * 全局事件总线实例
 */
let globalEventBus: ReturnType<typeof useEventBus> | null = null

/**
 * 获取全局事件总线
 */
export const getGlobalEventBus = () => {
  if (!globalEventBus) {
    globalEventBus = useEventBus()
  }
  return globalEventBus
}

/**
 * 便捷的全局事件方法
 */
export const globalEmit = <T = any>(event: string, data?: T) => {
  return getGlobalEventBus().emit(event, data, 'global')
}

export const globalOn = <T = any>(event: string, listener: EventListener<T>, config?: EventConfig) => {
  return getGlobalEventBus().on(event, listener, config)
}

export const globalOff = <T = any>(event: string, listener?: EventListener<T>) => {
  return getGlobalEventBus().off(event, listener)
}

export const globalOnce = <T = any>(event: string, listener: EventListener<T>) => {
  return getGlobalEventBus().once(event, listener)
}