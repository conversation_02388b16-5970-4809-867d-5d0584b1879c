<script lang="uts">
	let firstBackTime = 0
	export default {
		onLaunch: function () {
			console.log('App Launch')
			uni.navigateTo({
				url:'/pages/modules/device/Login/Login'
			})
		},
		onShow: function () {
			console.log('App Show')
			uni.navigateTo({
				url:'/pages/modules/device/Login/Login'
			})
		},
		onHide: function () {
			console.log('App Hide')
		},
		// #ifdef APP-ANDROID
		onLastPageBackPress: function () {
			console.log('App LastPageBackPress')
			if (firstBackTime == 0) {
				uni.showToast({
					title: '再按一次退出应用',
					position: 'bottom',
				})
				firstBackTime = Date.now()
				setTimeout(() => {
					firstBackTime = 0
				}, 2000)
			} else if (Date.now() - firstBackTime < 2000) {
				firstBackTime = Date.now()
				uni.exit()
			}
		},
		// #endif
		onExit: function () {
			console.log('App Exit')
		},
	}
</script>

<style>
	/*每个页面公共css */
	.uni-row {
		flex-direction: row;
	}

	.uni-column {
		flex-direction: column;
	}
</style>