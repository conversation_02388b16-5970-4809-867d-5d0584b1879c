<!--
  DxIcon 组件测试页面 - 使用官方 @iconify/vue 组件
-->
<template>
  <!-- #ifdef APP -->
  <scroll-view class="container" style="flex: 1">
  <!-- #endif -->
    <view class="icon-test-page">
      <view class="page-header">
        <text class="page-title">DxIcon 图标组件测试</text>
        <text class="page-subtitle">使用官方 @iconify/vue 组件</text>
      </view>

      <!-- Iconify 图标测试 -->
      <view class="section">
        <text class="section-title">Iconify 图标 (官方组件)</text>
        <view class="icon-grid">
          <view class="icon-item" v-for="icon in iconifyIcons" :key="icon.name">
            <DxIcon 
              :name="icon.name" 
              type="iconify" 
              :size="32" 
              :color="icon.color"
              :clickable="true"
              @click="handleIconClick('iconify', icon.name)"
            />
            <text class="icon-label">{{ icon.label }}</text>
          </view>
        </view>
      </view>

      <!-- 字体图标测试 -->
      <view class="section">
        <text class="section-title">字体图标</text>
        <view class="icon-grid">
          <view class="icon-item" v-for="icon in fontIcons" :key="icon.name">
            <DxIcon 
              :name="icon.name" 
              type="font" 
              :size="28" 
              color="#666"
              :clickable="true"
              @click="handleIconClick('font', icon.name)"
            />
            <text class="icon-label">{{ icon.label }}</text>
          </view>
        </view>
      </view>

      <!-- Uni 官方图标测试 -->
      <view class="section">
        <text class="section-title">Uni 官方图标</text>
        <view class="icon-grid">
          <view class="icon-item" v-for="icon in uniIcons" :key="icon.name">
            <DxIcon 
              :name="icon.name" 
              type="uni" 
              :size="30" 
              color="#007aff"
              :clickable="true"
              @click="handleIconClick('uni', icon.name)"
            />
            <text class="icon-label">{{ icon.label }}</text>
          </view>
        </view>
      </view>

      <!-- 大小测试 -->
      <view class="section">
        <text class="section-title">不同大小</text>
        <view class="size-test">
          <DxIcon name="mdi:home" type="iconify" :size="16" color="#333" />
          <DxIcon name="mdi:home" type="iconify" :size="24" color="#333" />
          <DxIcon name="mdi:home" type="iconify" :size="32" color="#333" />
          <DxIcon name="mdi:home" type="iconify" :size="48" color="#333" />
          <DxIcon name="mdi:home" type="iconify" :size="64" color="#333" />
        </view>
      </view>

      <!-- 点击统计 -->
      <view class="section">
        <text class="section-title">点击统计</text>
        <view class="click-stats">
          <text class="stats-text">总点击次数: {{ clickCount }}</text>
          <text class="stats-text">最后点击: {{ lastClickedIcon }}</text>
        </view>
      </view>
    </view>
  <!-- #ifdef APP -->
  </scroll-view>
  <!-- #endif -->
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DxIcon from '../../components/Common/DxIcon.uvue'

// ==================== 响应式数据 ====================

const clickCount = ref(0)
const lastClickedIcon = ref('无')

// ==================== 图标数据 ====================

const iconifyIcons = [
  { name: 'mdi:home', label: '首页', color: '#007aff' },
  { name: 'mdi:account', label: '用户', color: '#34c759' },
  { name: 'mdi:cog', label: '设置', color: '#ff9500' },
  { name: 'mdi:heart', label: '喜欢', color: '#ff3b30' },
  { name: 'mdi:star', label: '收藏', color: '#ffd700' },
  { name: 'mdi:bell', label: '通知', color: '#5856d6' },
  { name: 'mdi:email', label: '邮件', color: '#007aff' },
  { name: 'mdi:phone', label: '电话', color: '#34c759' },
  { name: 'heroicons:home', label: 'Hero首页', color: '#007aff' },
  { name: 'feather:home', label: 'Feather首页', color: '#007aff' },
  { name: 'tabler:home', label: 'Tabler首页', color: '#007aff' },
  { name: 'lucide:home', label: 'Lucide首页', color: '#007aff' }
]

const fontIcons = [
  { name: 'home', label: '首页' },
  { name: 'user', label: '用户' },
  { name: 'setting', label: '设置' },
  { name: 'search', label: '搜索' }
]

const uniIcons = [
  { name: 'arrow-left', label: '左箭头' },
  { name: 'arrow-right', label: '右箭头' },
  { name: 'close', label: '关闭' },
  { name: 'check', label: '确认' },
  { name: 'plus', label: '加号' },
  { name: 'minus', label: '减号' },
  { name: 'star', label: '星星' },
  { name: 'heart', label: '心形' }
]

// ==================== 事件处理 ====================

const handleIconClick = (type: string, name: string) => {
  clickCount.value++
  lastClickedIcon.value = `${type}: ${name}`
  
  uni.showToast({
    title: `点击了 ${type} 图标: ${name}`,
    icon: 'none',
    duration: 1500
  })
}
</script>

<style scoped>
.icon-test-page {
  padding: 20px;
  background-color: #f5f5f5;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 14px;
  color: #666;
}

.section {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}

.icon-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  border-radius: 6px;
  background-color: #f8f9fa;
  min-width: 80px;
}

.icon-label {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
  text-align: center;
}

.size-test {
  display: flex;
  align-items: center;
  gap: 20px;
}

.click-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stats-text {
  font-size: 14px;
  color: #333;
}
</style>
