<!--
  DxIcon 组件测试页面
  演示各种图标类型和用法
-->
<template>
  <!-- #ifdef APP -->
  <scroll-view class="container" style="flex: 1">
  <!-- #endif -->
    <view class="icon-test-page">
      <view class="page-header">
        <text class="page-title">DxIcon 图标组件测试</text>
        <text class="page-subtitle">多端适配的通用图标组件</text>
      </view>

      <!-- Iconify 图标测试 -->
      <view class="section">
        <text class="section-title">Iconify 图标</text>
        <view class="icon-grid">
          <view class="icon-item" v-for="icon in iconifyIcons" :key="icon.name">
            <DxIcon 
              :name="icon.name" 
              type="iconify" 
              :size="32" 
              :color="icon.color"
              :clickable="true"
              @click="handleIconClick('iconify', icon.name)"
            />
            <text class="icon-label">{{ icon.label }}</text>
          </view>
        </view>
      </view>

      <!-- 字体图标测试 -->
      <view class="section">
        <text class="section-title">字体图标</text>
        <view class="icon-grid">
          <view class="icon-item" v-for="icon in fontIcons" :key="icon.name">
            <DxIcon 
              :name="icon.name" 
              type="font" 
              :size="28" 
              color="#666"
              font-prefix="iconfont"
              :clickable="true"
              @click="handleIconClick('font', icon.name)"
            />
            <text class="icon-label">{{ icon.label }}</text>
          </view>
        </view>
      </view>

      <!-- Uni 官方图标测试 -->
      <view class="section">
        <text class="section-title">Uni 官方图标</text>
        <view class="icon-grid">
          <view class="icon-item" v-for="icon in uniIcons" :key="icon.name">
            <DxIcon 
              :name="icon.name" 
              type="uni" 
              :size="30" 
              color="#007aff"
              :clickable="true"
              @click="handleIconClick('uni', icon.name)"
            />
            <text class="icon-label">{{ icon.label }}</text>
          </view>
        </view>
      </view>

      <!-- 图片图标测试 -->
      <view class="section">
        <text class="section-title">图片图标</text>
        <view class="icon-grid">
          <view class="icon-item" v-for="icon in imageIcons" :key="icon.name">
            <DxIcon 
              :name="icon.name" 
              type="image" 
              :size="36" 
              image-mode="aspectFit"
              :clickable="true"
              @click="handleIconClick('image', icon.name)"
              @image-error="handleImageError"
              @image-load="handleImageLoad"
            />
            <text class="icon-label">{{ icon.label }}</text>
          </view>
        </view>
      </view>

      <!-- 自定义样式测试 -->
      <view class="section">
        <text class="section-title">自定义样式</text>
        <view class="custom-icons">
          <DxIcon 
            name="mdi:star" 
            type="iconify" 
            :size="40" 
            color="#ffd700"
            custom-class="star-icon"
            :custom-style="starStyle"
            :clickable="true"
            @click="toggleStar"
          />
          
          <DxIcon 
            name="mdi:heart" 
            type="iconify" 
            :size="36" 
            :color="heartColor"
            :custom-style="heartStyle"
            :clickable="true"
            @click="toggleHeart"
          />
          
          <DxIcon 
            name="mdi:thumb-up" 
            type="iconify" 
            :size="32" 
            color="#007aff"
            :custom-style="thumbStyle"
            :clickable="true"
            @click="handleThumbUp"
          />
        </view>
      </view>

      <!-- 大小测试 -->
      <view class="section">
        <text class="section-title">不同大小</text>
        <view class="size-test">
          <DxIcon name="mdi:home" type="iconify" :size="16" color="#333" />
          <DxIcon name="mdi:home" type="iconify" :size="24" color="#333" />
          <DxIcon name="mdi:home" type="iconify" :size="32" color="#333" />
          <DxIcon name="mdi:home" type="iconify" :size="48" color="#333" />
          <DxIcon name="mdi:home" type="iconify" :size="64" color="#333" />
        </view>
      </view>

      <!-- 降级测试 -->
      <view class="section">
        <text class="section-title">降级测试</text>
        <view class="fallback-test">
          <text class="test-desc">当 iconify 不支持时，自动降级到字体图标：</text>
          <DxIcon 
            name="home" 
            type="iconify" 
            :size="32" 
            color="#ff6b6b"
            font-prefix="iconfont"
            fallback="⌂"
          />
        </view>
      </view>

      <!-- 点击统计 -->
      <view class="section">
        <text class="section-title">点击统计</text>
        <view class="click-stats">
          <text class="stats-text">总点击次数: {{ clickCount }}</text>
          <text class="stats-text">最后点击: {{ lastClickedIcon }}</text>
        </view>
      </view>
    </view>
  <!-- #ifdef APP -->
  </scroll-view>
  <!-- #endif -->
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import DxIcon from '../../components/Common/DxIcon.uvue'

// ==================== 响应式数据 ====================

const clickCount = ref(0)
const lastClickedIcon = ref('无')
const isStarFilled = ref(false)
const isHeartFilled = ref(false)
const thumbUpCount = ref(0)

// ==================== 图标数据 ====================

const iconifyIcons = [
  { name: 'mdi:home', label: '首页', color: '#007aff' },
  { name: 'mdi:account', label: '用户', color: '#34c759' },
  { name: 'mdi:settings', label: '设置', color: '#ff9500' },
  { name: 'mdi:heart', label: '喜欢', color: '#ff3b30' },
  { name: 'mdi:star', label: '收藏', color: '#ffd700' },
  { name: 'mdi:bell', label: '通知', color: '#5856d6' },
  { name: 'mdi:email', label: '邮件', color: '#007aff' },
  { name: 'mdi:phone', label: '电话', color: '#34c759' }
]

const fontIcons = [
  { name: 'home', label: '首页' },
  { name: 'user', label: '用户' },
  { name: 'setting', label: '设置' },
  { name: 'search', label: '搜索' }
]

const uniIcons = [
  { name: 'arrow-left', label: '左箭头' },
  { name: 'arrow-right', label: '右箭头' },
  { name: 'close', label: '关闭' },
  { name: 'check', label: '确认' },
  { name: 'plus', label: '加号' },
  { name: 'minus', label: '减号' },
  { name: 'star', label: '星星' },
  { name: 'heart', label: '心形' }
]

const imageIcons = [
  { name: 'https://via.placeholder.com/64/007aff/ffffff?text=A', label: '在线图A' },
  { name: 'https://via.placeholder.com/64/34c759/ffffff?text=B', label: '在线图B' },
  { name: 'logo.png', label: '本地图标' }
]

// ==================== 计算属性 ====================

const starStyle = computed(() => ({
  borderRadius: '50%',
  backgroundColor: isStarFilled.value ? '#fff3cd' : 'transparent',
  padding: '8px',
  border: '2px solid #ffd700'
}))

const heartColor = computed(() => isHeartFilled.value ? '#ff3b30' : '#ccc')

const heartStyle = computed(() => ({
  transform: isHeartFilled.value ? 'scale(1.2)' : 'scale(1)',
  transition: 'all 0.3s ease'
}))

const thumbStyle = computed(() => ({
  transform: `rotate(${thumbUpCount.value * 15}deg)`,
  transition: 'transform 0.3s ease'
}))

// ==================== 事件处理 ====================

const handleIconClick = (type: string, name: string) => {
  clickCount.value++
  lastClickedIcon.value = `${type}: ${name}`
  
  uni.showToast({
    title: `点击了 ${type} 图标: ${name}`,
    icon: 'none',
    duration: 1500
  })
}

const toggleStar = () => {
  isStarFilled.value = !isStarFilled.value
  handleIconClick('iconify', 'star')
}

const toggleHeart = () => {
  isHeartFilled.value = !isHeartFilled.value
  handleIconClick('iconify', 'heart')
}

const handleThumbUp = () => {
  thumbUpCount.value++
  handleIconClick('iconify', 'thumb-up')
}

const handleImageError = (event: any) => {
  console.warn('图片加载失败:', event)
  uni.showToast({
    title: '图片加载失败',
    icon: 'none'
  })
}

const handleImageLoad = (event: any) => {
  console.log('图片加载成功:', event)
}
</script>
