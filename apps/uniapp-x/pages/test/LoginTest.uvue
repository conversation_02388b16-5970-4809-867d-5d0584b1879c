<!--
  登录页面测试 - 用于验证 H5 端 UI 复制效果
-->
<template>
  <!-- #ifdef APP -->
  <scroll-view class="container" style="flex: 1">
  <!-- #endif -->
    <view class="test-page">
      <view class="test-header">
        <text class="test-title">登录页面 UI 测试</text>
        <text class="test-subtitle">验证 H5 端 UI 一比一复制效果</text>
      </view>

      <view class="test-actions">
        <button class="test-btn" @click="goToLogin">
          跳转到登录页面
        </button>
        <button class="test-btn secondary" @click="showInfo">
          查看页面信息
        </button>
      </view>

      <view class="test-info">
        <text class="info-title">页面特性</text>
        <view class="info-list">
          <text class="info-item">✅ 完全复制 H5 端 UserLogin.vue 结构</text>
          <text class="info-item">✅ 完全复制 H5 端 DeviceNum.vue 样式</text>
          <text class="info-item">✅ 使用 uni-icons 替代 SvgIcon</text>
          <text class="info-item">✅ 保持原有的 SCSS 变量和 mixins</text>
          <text class="info-item">✅ 支持多端适配（APP/小程序/H5）</text>
        </view>
      </view>
    </view>
  <!-- #ifdef APP -->
  </scroll-view>
  <!-- #endif -->
</template>

<script setup lang="ts">
// 跳转到登录页面
const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/modules/device/Login/Login'
  })
}

// 显示页面信息
const showInfo = () => {
  uni.showModal({
    title: '登录页面信息',
    content: '已完成 H5 端登录 UI 的一比一复制，包括：\n\n1. UserLogin 组件结构\n2. DeviceNum 组件样式\n3. SCSS 变量和 mixins\n4. 响应式布局\n5. 多端兼容性',
    showCancel: false,
    confirmText: '知道了'
  })
}
</script>

<style scoped>
.test-page {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.test-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.test-subtitle {
  font-size: 28rpx;
  color: #666;
}

.test-actions {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-bottom: 60rpx;
}

.test-btn {
  height: 88rpx;
  background: linear-gradient(135deg, rgba(86, 153, 247), rgba(46, 108, 237));
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.test-btn.secondary {
  background: #f0f0f0;
  color: #333;
}

.test-info {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.info-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
</style>
