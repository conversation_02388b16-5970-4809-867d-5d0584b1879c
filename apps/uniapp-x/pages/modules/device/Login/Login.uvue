<template>
  <view class="login-page">
    <view class="login-container">
      <!-- 登录图标和标题 -->
      <view class="login-header">
        <view class="login-icon">
          <text class="login-icon-text">📶</text>
        </view>
        <text class="login-title">设备登录</text>
      </view>

      <!-- 登录类型切换 -->
      <view class="login-nav">
        <view 
          class="login-nav-item"
          :class="{ 'login-nav-active': loginType === 'device' }"
          @click="loginType = 'device'"
        >
          设备卡登录1
		  <l-icon name="circle" />
		 <l-icon name="work" color="#1989fa" />
		 <l-icon name="ri:account-box-fill" />
<!-- <l-icon name="https://fastly.jsdelivr.net/npm/@vant/assets/icon-demo.png"></l-icon> -->

        </view>
      </view>

      <!-- 设备卡登录表单 -->
      <view v-if="loginType === 'device'" class="login-form">
        <!-- 设备号输入 -->
        <view class="form-item">
          <view class="input-wrapper">
            <text class="input-icon">💳</text>
            <input
              class="input-field"
              type="text"
              v-model="deviceNo"
              placeholder="请输入设备卡号"
              :disabled="loading"
              @input="onDeviceNoInput"
            />
          </view>
          <view class="input-tip">
            <text class="tip-icon">💡</text>
            <text class="tip-text">设备卡号通常位于设备背面或者包装盒上</text>
          </view>
        </view>

        <!-- 登录按钮 -->
        <button 
          class="login-btn"
          :class="{ 'login-btn-disabled': loading || !deviceNo.trim() }"
          @click="handleDeviceLogin"
          :disabled="loading || !deviceNo.trim()"
        >
          {{ loading ? '登录中...' : '登录' }}
        </button>
      </view>

      <!-- 开发环境调试信息 -->
      <!-- #ifdef MP-WEIXIN -->
      <view v-if="isDev" class="debug-info">
        <text class="debug-title">开发调试</text>
        <text class="debug-text">设备号: {{ deviceNo }}</text>
        <text class="debug-text">加载状态: {{ loading }}</text>
      </view>
      <!-- #endif -->
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useGlobalData } from '../../../../hooks/device/useGlobalData'
import { requestService } from '../../../../services/RequestService'

// ==================== 响应式数据 ====================

const { 
  setDeviceDetails, 
  setDeviceKey, 
  setUserToken, 
  setDeviceLoading,
  deviceData,
  userData 
} = useGlobalData()

const loginType = ref<'device' | 'phone'>('device')
const deviceNo = ref<string>('')
const loading = ref<boolean>(false)
const isDev = ref<boolean>(false)

// ==================== 生命周期 ====================

onMounted(async () => {
  console.log('🔄 [Login] 登录页面初始化...')
  
  // 检查开发环境
  // #ifdef MP-WEIXIN
  isDev.value = true
  // #endif
  
  // 清除之前的加载状态
  setDeviceLoading(false)
  
  // 尝试获取系统配置
  try {
    await fetchSystemConfig()
    console.log('✅ [Login] 系统配置获取成功')
  } catch (error) {
    console.error('❌ [Login] 系统配置获取失败:', error)
  }
  
  // 开发环境预填设备号
  if (isDev.value) {
    // deviceNo.value = '863780070053924' // 可以取消注释用于测试
  }
})

// ==================== 业务方法 ====================

/**
 * 获取系统配置
 */
const fetchSystemConfig = async () => {
  try {
    // 获取管理配置
    const manageConfig = await requestService.get('/api/system/manage-config')
    console.log('📋 [Login] 管理配置:', manageConfig)
    
    // 获取充值配置
    const rechargeConfig = await requestService.get('/api/system/recharge-config')
    console.log('💰 [Login] 充值配置:', rechargeConfig)
    
  } catch (error) {
    console.warn('⚠️ [Login] 配置获取失败，继续使用默认配置')
  }
}

/**
 * 设备号输入处理
 */
const onDeviceNoInput = (e: any) => {
  deviceNo.value = e.detail.value
}

/**
 * 设备登录处理
 */
const handleDeviceLogin = async () => {
  // 防止重复点击
  if (loading.value) return
  
  const trimmedDeviceNo = deviceNo.value.trim()
  if (!trimmedDeviceNo) {
    uni.showToast({
      title: '请输入设备卡号',
      icon: 'none',
      duration: 2000
    })
    return
  }
  
  loading.value = true
  setDeviceLoading(true)
  
  try {
    console.log('🔐 [Login] 开始设备登录:', trimmedDeviceNo)
    
    // 调用登录API
    const loginResult = await requestService.post('/api/device/login', {
      deviceNo: trimmedDeviceNo,
      groupId: 2 // 与H5端保持一致
    })
    
    console.log('✅ [Login] 登录响应:', loginResult)
    
    // 处理登录成功响应
    if (loginResult.code === 200 && loginResult.data) {
      const { device, token, user } = loginResult.data
      
      // 保存设备信息
      if (device) {
        setDeviceDetails(device)
        if (device.key) {
          setDeviceKey(device.key)
        }
      }
      
      // 保存用户token
      if (token) {
        setUserToken(token)
      }
      
      // 保存用户信息
      if (user) {
        // setUserProfile(user) // 如果有用户信息的话
      }
      
      // 显示登录成功提示
      uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500
      })
      
      // 处理登录后跳转
      await handleLoginSuccess()
      
    } else {
      throw new Error(loginResult.message || '登录失败')
    }
    
  } catch (error: any) {
    console.error('❌ [Login] 登录失败:', error)
    
    // 显示错误提示
    uni.showToast({
      title: error.message || '登录失败，请重试',
      icon: 'none',
      duration: 2000
    })
    
  } finally {
    loading.value = false
    setDeviceLoading(false)
  }
}

/**
 * 登录成功后的跳转处理
 */
const handleLoginSuccess = async () => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options
  
  console.log('🔄 [Login] 处理登录后跳转:', options)
  
  try {
    // 检查重定向参数
    const redirectUrl = options.redirect
    const appId = options.appId || uni.getStorageSync('current-app-id')
    
    if (redirectUrl) {
      // 有重定向地址，直接跳转
      console.log('🚀 [Login] 重定向到:', redirectUrl)
      
      if (appId && appId !== 'home') {
        // 设置低代码应用状态
        uni.setStorageSync('isLowcodeApp', 'true')
        uni.setStorageSync('current-app-id', appId)
      }
      
      // UniApp-X中需要处理URL格式
      if (redirectUrl.startsWith('/')) {
        await uni.reLaunch({ url: redirectUrl })
      } else {
        await uni.reLaunch({ url: `/${redirectUrl}` })
      }
      
    } else if (appId && appId !== 'home') {
      // 有appId，跳转到对应的动态页面
      console.log('🚀 [Login] 跳转到应用:', appId)
      
      uni.setStorageSync('isLowcodeApp', 'true')
      uni.setStorageSync('current-app-id', appId)
      
      await uni.reLaunch({ 
        url: `/pages/dynamic/dynamic?appId=${appId}&pageId=home` 
      })
      
    } else if (appId === 'home') {
      // home应用，跳转到主页
      console.log('🚀 [Login] 跳转到主页')
      
      await uni.reLaunch({ 
        url: `/pages/dynamic/dynamic?appId=home&pageId=index` 
      })
      
    } else {
      // 默认跳转到套餐列表
      console.log('🚀 [Login] 跳转到套餐列表')
      
      await uni.reLaunch({ 
        url: '/pages/modules/device/PackageList/PackageList' 
      })
    }
    
  } catch (error: any) {
    console.error('❌ [Login] 跳转失败:', error)
    
    // 跳转失败，回退到套餐列表
    await uni.reLaunch({ 
      url: '/pages/modules/device/PackageList/PackageList' 
    })
  }
}

// ==================== 页面生命周期 ====================

// 下拉刷新
const onPullDownRefresh = () => {
  fetchSystemConfig().finally(() => {
    uni.stopPullDownRefresh()
  })
}

// 导出给页面使用
defineExpose({
  onPullDownRefresh,
  deviceNo,
  loading,
  handleDeviceLogin
})
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-top: 80rpx;
}

.login-container {
  padding: 60rpx 40rpx;
}

/* 登录头部 */
.login-header {
  text-align: center;
  margin-bottom: 80rpx;
}

.login-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
}

.login-icon-text {
  font-size: 60rpx;
}

.login-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
}

/* 登录导航 */
.login-nav {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 60rpx;
}

.login-nav-item {
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  font-weight: bold;
  border-radius: 40rpx;
  transition: all 0.3s ease;
}

.login-nav-active {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
}

/* 登录表单 */
.login-form {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  padding: 40rpx;
  backdrop-filter: blur(10rpx);
}

.form-item {
  margin-bottom: 40rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 0 30rpx;
  height: 100rpx;
}

.input-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  color: #666;
}

.input-field {
  flex: 1;
  font-size: 30rpx;
  border: none;
  background: transparent;
  color: #333;
}

.input-field:disabled {
  opacity: 0.6;
}

.input-tip {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  padding-left: 30rpx;
}

.tip-icon {
  font-size: 24rpx;
  margin-right: 10rpx;
  color: rgba(255, 255, 255, 0.8);
}

.tip-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50rpx;
  border: none;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4);
}

.login-btn-disabled {
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

/* 开发调试信息 */
.debug-info {
  margin-top: 60rpx;
  padding: 30rpx;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
}

.debug-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
  display: block;
}

.debug-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10rpx;
  display: block;
}
</style>
