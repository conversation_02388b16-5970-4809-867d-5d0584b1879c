<template>
  <!-- #ifdef APP -->
  <scroll-view class="container" style="flex: 1">
  <!-- #endif -->
    <!-- ✅ 完全复制 H5 端的 UserLogin 结构 -->
    <view class="UserLogin">
      <view class="UserLogin-box">
        <!-- ✅ 完全复制 UserLogin-box-svg 结构 -->
        <view class="UserLogin-box-svg">
          <view class="UserLogin-box-svg-icon">
            <!-- 使用 uni 官方图标替代 SvgIcon -->
            <uni-icons type="wifi" size="34" color="#fff"></uni-icons>
          </view>
          <text class="UserLogin-box-svg-label">登录</text>
        </view>

        <!-- ✅ 完全复制 UserLogin-box-nav 结构 -->
        <view class="UserLogin-box-nav">
          <view
            class="UserLogin-box-nav-box"
            :class="{ 'UserLogin-box-nav-box-active': loginType === 'device' }"
            @click="loginType = 'device'"
          >
            设备卡登录
          </view>
          <!-- 手机号登录暂时隐藏，与H5端保持一致 -->
        </view>

        <!-- ✅ 完全复制 DeviceNum.vue 的结构和样式 -->
        <view v-if="loginType === 'device'" class="DeviceNum">
          <view class="DeviceNum-ipt">
            <!-- 使用 uni 官方图标替代 SvgIcon -->
            <uni-icons type="card" size="16" color="#999" class="DeviceNum-ipt-icon"></uni-icons>
            <input
              type="text"
              class="DeviceNum-ipt-ipt"
              v-model="deviceNo"
              placeholder="请输入设备卡号"
              :disabled="loading"
              @input="onDeviceNoInput"
            />
          </view>
          <view class="DeviceNum-tip">
            <!-- 使用 uni 官方图标替代 SvgIcon -->
            <uni-icons type="info" size="16" color="#666" class="DeviceNum-tip-icon"></uni-icons>
            <text class="DeviceNum-tip-text">设备卡号通常在设备背面或者包装盒上</text>
          </view>

          <view
            class="DeviceNum-btn"
            @click="handleDeviceLogin"
            :class="{ disabled: loading || !deviceNo.trim() }"
          >
            <text class="DeviceNum-btn-text">{{ loading ? '登录中...' : '登录' }}</text>
          </view>
        </view>
      </view>
    </view>
  <!-- #ifdef APP -->
  </scroll-view>
  <!-- #endif -->
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useGlobalData } from '../../../../hooks/device/useGlobalData'
import { requestService } from '../../../../services/RequestService'

// ==================== 响应式数据 ====================

const { 
  setDeviceDetails, 
  setDeviceKey, 
  setUserToken, 
  setDeviceLoading,
  deviceData,
  userData 
} = useGlobalData()

const loginType = ref<'device' | 'phone'>('device')
const deviceNo = ref<string>('')
const loading = ref<boolean>(false)
const isDev = ref<boolean>(false)

// ==================== 生命周期 ====================

onMounted(async () => {
  console.log('🔄 [Login] 登录页面初始化...')
  
  // 检查开发环境
  // #ifdef MP-WEIXIN
  isDev.value = true
  // #endif
  
  // 清除之前的加载状态
  setDeviceLoading(false)
  
  // 尝试获取系统配置
  try {
    await fetchSystemConfig()
    console.log('✅ [Login] 系统配置获取成功')
  } catch (error) {
    console.error('❌ [Login] 系统配置获取失败:', error)
  }
  
  // 开发环境预填设备号
  if (isDev.value) {
    // deviceNo.value = '863780070053924' // 可以取消注释用于测试
  }
})

// ==================== 业务方法 ====================

/**
 * 获取系统配置
 */
const fetchSystemConfig = async () => {
  try {
    // 获取管理配置
    const manageConfig = await requestService.get('/api/system/manage-config')
    console.log('📋 [Login] 管理配置:', manageConfig)
    
    // 获取充值配置
    const rechargeConfig = await requestService.get('/api/system/recharge-config')
    console.log('💰 [Login] 充值配置:', rechargeConfig)
    
  } catch (error) {
    console.warn('⚠️ [Login] 配置获取失败，继续使用默认配置')
  }
}

/**
 * 设备号输入处理
 */
const onDeviceNoInput = (e: any) => {
  deviceNo.value = e.detail.value
}

/**
 * 设备登录处理
 */
const handleDeviceLogin = async () => {
  // 防止重复点击
  if (loading.value) return
  
  const trimmedDeviceNo = deviceNo.value.trim()
  if (!trimmedDeviceNo) {
    uni.showToast({
      title: '请输入设备卡号',
      icon: 'none',
      duration: 2000
    })
    return
  }
  
  loading.value = true
  setDeviceLoading(true)
  
  try {
    console.log('🔐 [Login] 开始设备登录:', trimmedDeviceNo)
    
    // 调用登录API
    const loginResult = await requestService.post('/api/device/login', {
      deviceNo: trimmedDeviceNo,
      groupId: 2 // 与H5端保持一致
    })
    
    console.log('✅ [Login] 登录响应:', loginResult)
    
    // 处理登录成功响应
    if (loginResult.code === 200 && loginResult.data) {
      const { device, token, user } = loginResult.data
      
      // 保存设备信息
      if (device) {
        setDeviceDetails(device)
        if (device.key) {
          setDeviceKey(device.key)
        }
      }
      
      // 保存用户token
      if (token) {
        setUserToken(token)
      }
      
      // 保存用户信息
      if (user) {
        // setUserProfile(user) // 如果有用户信息的话
      }
      
      // 显示登录成功提示
      uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500
      })
      
      // 处理登录后跳转
      await handleLoginSuccess()
      
    } else {
      throw new Error(loginResult.message || '登录失败')
    }
    
  } catch (error: any) {
    console.error('❌ [Login] 登录失败:', error)
    
    // 显示错误提示
    uni.showToast({
      title: error.message || '登录失败，请重试',
      icon: 'none',
      duration: 2000
    })
    
  } finally {
    loading.value = false
    setDeviceLoading(false)
  }
}

/**
 * 登录成功后的跳转处理
 */
const handleLoginSuccess = async () => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options
  
  console.log('🔄 [Login] 处理登录后跳转:', options)
  
  try {
    // 检查重定向参数
    const redirectUrl = options.redirect
    const appId = options.appId || uni.getStorageSync('current-app-id')
    
    if (redirectUrl) {
      // 有重定向地址，直接跳转
      console.log('🚀 [Login] 重定向到:', redirectUrl)
      
      if (appId && appId !== 'home') {
        // 设置低代码应用状态
        uni.setStorageSync('isLowcodeApp', 'true')
        uni.setStorageSync('current-app-id', appId)
      }
      
      // UniApp-X中需要处理URL格式
      if (redirectUrl.startsWith('/')) {
        await uni.reLaunch({ url: redirectUrl })
      } else {
        await uni.reLaunch({ url: `/${redirectUrl}` })
      }
      
    } else if (appId && appId !== 'home') {
      // 有appId，跳转到对应的动态页面
      console.log('🚀 [Login] 跳转到应用:', appId)
      
      uni.setStorageSync('isLowcodeApp', 'true')
      uni.setStorageSync('current-app-id', appId)
      
      await uni.reLaunch({ 
        url: `/pages/dynamic/dynamic?appId=${appId}&pageId=home` 
      })
      
    } else if (appId === 'home') {
      // home应用，跳转到主页
      console.log('🚀 [Login] 跳转到主页')
      
      await uni.reLaunch({ 
        url: `/pages/dynamic/dynamic?appId=home&pageId=index` 
      })
      
    } else {
      // 默认跳转到套餐列表
      console.log('🚀 [Login] 跳转到套餐列表')
      
      await uni.reLaunch({ 
        url: '/pages/modules/device/PackageList/PackageList' 
      })
    }
    
  } catch (error: any) {
    console.error('❌ [Login] 跳转失败:', error)
    
    // 跳转失败，回退到套餐列表
    await uni.reLaunch({ 
      url: '/pages/modules/device/PackageList/PackageList' 
    })
  }
}

// ==================== 页面生命周期 ====================

// 下拉刷新
const onPullDownRefresh = () => {
  fetchSystemConfig().finally(() => {
    uni.stopPullDownRefresh()
  })
}

// 导出给页面使用
defineExpose({
  onPullDownRefresh,
  deviceNo,
  loading,
  handleDeviceLogin
})
</script>

<style lang="scss" scoped>
// ✅ 完全复制 H5 端的 SCSS 变量
$primary: rgb(59, 130, 246);
$success: #22c55e;
$warning: #f59e0b;
$error: #ef4444;
$info: #7f7fd5;
$wait: #999;

// 背景色
$background: rgb(243, 244, 246);
$box-background: rgb(249, 250, 251);

// 边框和分割线
$border: #eee;

// 圆角
$radius: 0.6rem;

// 阴影
$shadow: 0 0.4rem 2rem rgba(0, 0, 0, 0.05);

// 间距
$padding: 0.7rem;

// Mixin - 白色盒子
@mixin WhiteBox {
  background-color: #fff;
  border-radius: $radius;
  box-shadow: $shadow;
}

// Mixin - 内边距盒子
@mixin PaddingBox {
  box-sizing: border-box;
  padding: $padding;
}

// Mixin - 页面盒子
@mixin PageBox {
  min-height: 100vh;
  background-color: $background;
  box-sizing: border-box;
  padding: $padding;
}

// ✅ 完全复制 UserLogin.vue 的样式
.UserLogin {
  @include PageBox;
  box-sizing: border-box;
  position: relative;
  padding-top: 4rem;

  &-box {
    @include PaddingBox;

    &-svg {
      text-align: center;

      &-icon {
        height: 3rem;
        width: 3rem;
        margin: 0 auto;
        background: linear-gradient(135deg, rgba(86, 153, 247), rgba(46, 108, 237));
        color: #fff;
        border-radius: 0.8rem;
        font-size: 1.7rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      &-label {
        font-size: 1rem;
        font-weight: bold;
        margin-top: 0.5rem;
      }
    }

    &-nav {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: calc($padding * 1.5);

      &-box {
        margin: 0 0.6rem;
        font-size: 0.7rem;
        height: 1.4rem;
        color: #666;
        font-weight: bold;
        transition: color 0.15s linear;
      }

      &-box-active {
        color: $primary;
        box-sizing: border-box;
        border-bottom: 0.1rem solid $primary;
      }
    }
  }
}

// ✅ 完全复制 DeviceNum.vue 的样式
.DeviceNum {
  margin-top: calc($padding * 1);
  @include PaddingBox;

  &-ipt {
    display: flex;
    justify-content: start;
    align-items: center;
    background-color: $background;
    height: 2.6rem;
    border-radius: $radius;
    box-sizing: border-box;
    padding: 0 0.8rem;

    &-icon {
      margin-right: 0.4rem;
      color: #999;
      flex-shrink: 0;
      display: flex;
      align-items: center;
    }

    &-ipt {
      background-color: transparent !important;
      border: none;
      font-size: 0.8rem;
      flex: 1;
      outline: none;

      &:disabled {
        opacity: 0.6;
      }
    }
  }

  &-tip {
    font-size: 0.65rem;
    margin-top: 0.4rem;
    color: #666;
    display: flex;
    align-items: center;

    &-icon {
      font-size: 0.8rem;
      margin-right: 0.2rem;
      flex-shrink: 0;
    }

    &-text {
      flex: 1;
    }
  }

  &-btn {
    margin-top: 1.5rem;
    background-color: $primary;
    text-align: center;
    line-height: 2.4rem;
    border-radius: $radius;
    color: #fff;
    font-size: 0.85rem;
    cursor: pointer;
    transition: opacity 0.3s ease;

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &:active:not(.disabled) {
      opacity: 0.8;
    }

    &-text {
      color: #fff;
      font-size: 0.85rem;
    }
  }
}
</style>
