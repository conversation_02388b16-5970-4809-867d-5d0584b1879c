<template>
  <view class="dynamic-page">
    <!-- 页面加载中 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">页面加载中...</text>
      </view>
    </view>

    <!-- 页面加载错误 -->
    <view v-else-if="error" class="error-container">
      <view class="error-content">
        <text class="error-title">页面加载失败</text>
        <text class="error-message">{{ error }}</text>
        <button class="retry-button" @click="loadPageConfig">重新加载</button>
      </view>
    </view>

    <!-- 动态页面内容 -->
    <view v-else-if="pageConfig" class="page-content" :style="pageStyle">
      <!-- 动态渲染组件 -->
      <view class="components-container">
        <view
          v-for="comp in pageConfig.components"
          :key="comp.id"
          class="component-wrapper"
          :data-component-id="comp.id"
          :data-component-type="comp.type"
        >
          <!-- 动态组件渲染 -->
          <DynamicComponent
            :componentType="comp.type"
            :componentProps="comp.props"
            :componentStyle="comp.style"
            :componentEvents="comp.events"
            @componentEvent="handleComponentEvent"
          />
        </view>
      </view>
    </view>

    <!-- 空页面状态 -->
    <view v-else class="empty-container">
      <text class="empty-text">暂无页面内容</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { loadPageConfig as loadPageConfigService, type PageConfig, type LoadConfigOptions } from '../../services/PageConfigService'
import { handleComponentEvent as handleComponentEventService, type ComponentEvent } from '../../services/EventHandlerService'
import DynamicComponent from '../../components/DynamicComponent/DynamicComponent.uvue'

// 页面状态
const loading = ref(false)
const error = ref<string | null>(null)
const pageConfig = ref<PageConfig | null>(null)

// 页面样式计算
const pageStyle = computed(() => {
  if (!pageConfig.value?.style) return {}
  
  const style = pageConfig.value.style
  return {
    ...style,
    minHeight: '100vh',
    backgroundColor: style.backgroundColor || '#f5f5f5'
  }
})

// 加载页面配置
const loadPageConfig = async () => {
  loading.value = true
  error.value = null
  
  try {
    // 获取页面参数
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const options = currentPage.options
    
    console.log('🔍 页面参数:', options)
    
    if (!options.appId || !options.pageId) {
      throw new Error('缺少必要参数：appId 或 pageId')
    }
    
    // 使用PageConfigService加载配置
    const config = await loadPageConfigService({
      appId: options.appId,
      pageId: options.pageId,
      version: options.version,
      cache: true
    } as LoadConfigOptions)
    
    pageConfig.value = config
    
    console.log('✅ 页面配置加载成功:', pageConfig.value)
    
    // 设置页面标题
    if (pageConfig.value.name) {
      uni.setNavigationBarTitle({
        title: pageConfig.value.name
      })
    }
    
  } catch (err: any) {
    console.error('❌ 页面配置加载失败:', err)
    error.value = err.message || '页面加载失败'
  } finally {
    loading.value = false
  }
}

// 处理组件事件
const handleComponentEvent = (eventData: any) => {
  console.log('🎯 组件事件:', eventData)
  
  // 构造事件对象
  const componentEvent: ComponentEvent = {
    componentId: eventData.componentId || 'unknown',
    componentType: eventData.componentType || 'unknown',
    eventType: eventData.eventType || 'click',
    eventData: eventData.eventConfig || eventData
  }
  
  // 使用EventHandlerService处理事件
  handleComponentEventService(componentEvent)
}

// 页面加载时初始化
onMounted(() => {
  loadPageConfig()
  
  uni.navigateTo({
  	url:'/pages/modules/device/Login/Login'
  })
})

// 下拉刷新
const onPullDownRefresh = () => {
  loadPageConfig().finally(() => {
    uni.stopPullDownRefresh()
  })
}

// 导出给页面使用
defineExpose({
  onPullDownRefresh
})
</script>

<style lang="scss">
.dynamic-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  
  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 32rpx;
  }
  
  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 6rpx solid #e5e5e5;
    border-top: 6rpx solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  .loading-text {
    font-size: 28rpx;
    color: #666;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 32rpx;
  
  .error-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24rpx;
    text-align: center;
  }
  
  .error-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
  
  .error-message {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
  }
  
  .retry-button {
    background-color: #1890ff;
    color: white;
    border: none;
    padding: 20rpx 40rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
  }
}

/* 空状态 */
.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

/* 页面内容 */
.page-content {
  min-height: 100vh;
}

/* 组件容器 */
.components-container {
  .component-wrapper {
    width: 100%;
  }
}
</style>
