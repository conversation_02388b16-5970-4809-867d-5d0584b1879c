/**
 * 通用网络请求服务
 * 
 * 基于uni.request封装的网络请求层
 * 提供统一的请求拦截、错误处理、缓存等功能
 */

export interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  data?: any
  header?: Record<string, string>
  timeout?: number
  responseType?: 'text' | 'arraybuffer'
  enableCache?: boolean
  cacheTime?: number
}

export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
  timestamp?: number
}

interface CacheItem {
  data: any
  expireTime: number
}

export class RequestService {
  private baseURL: string = ''
  private defaultHeaders: Record<string, string> = {}
  private cache = new Map<string, CacheItem>()
  private requestInterceptors: Array<(config: RequestConfig) => RequestConfig> = []
  private responseInterceptors: Array<(response: any) => any> = []

  constructor(baseURL: string = '') {
    this.baseURL = baseURL || this.getDefaultBaseURL()
    this.setupDefaultHeaders()
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor: (config: RequestConfig) => RequestConfig): void {
    this.requestInterceptors.push(interceptor)
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor: (response: any) => any): void {
    this.responseInterceptors.push(interceptor)
  }

  /**
   * 设置默认请求头
   */
  setDefaultHeaders(headers: Record<string, string>): void {
    this.defaultHeaders = { ...this.defaultHeaders, ...headers }
  }

  /**
   * 设置认证token
   */
  setAuthToken(token: string): void {
    this.setDefaultHeaders({
      'Authorization': `Bearer ${token}`
    })
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, params?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'GET',
      data: params,
      ...config
    })
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      ...config
    })
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      ...config
    })
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, config?: Partial<RequestConfig>): Promise<T> {
    return this.request<T>({
      url,
      method: 'DELETE',
      ...config
    })
  }

  /**
   * 通用请求方法
   */
  async request<T = any>(config: RequestConfig): Promise<T> {
    // 应用请求拦截器
    let finalConfig = { ...config }
    for (const interceptor of this.requestInterceptors) {
      finalConfig = interceptor(finalConfig)
    }

    const {
      url,
      method = 'GET',
      data,
      header = {},
      timeout = 10000,
      responseType = 'text',
      enableCache = false,
      cacheTime = 5 * 60 * 1000 // 5分钟
    } = finalConfig

    // 构建完整URL
    const fullURL = this.buildFullURL(url)
    
    // 构建缓存key
    const cacheKey = this.buildCacheKey(method, fullURL, data)
    
    // 检查缓存
    if (method === 'GET' && enableCache) {
      const cached = this.getFromCache(cacheKey)
      if (cached) {
        console.log(`📦 [RequestService] 使用缓存数据: ${fullURL}`)
        return cached
      }
    }

    // 合并请求头
    const finalHeaders = {
      ...this.defaultHeaders,
      ...header
    }

    console.log(`🌐 [RequestService] 发送请求: ${method} ${fullURL}`)

    try {
      const response = await this.executeRequest({
        url: fullURL,
        method,
        data,
        header: finalHeaders,
        timeout,
        responseType
      })

      // 应用响应拦截器
      let finalResponse = response
      for (const interceptor of this.responseInterceptors) {
        finalResponse = interceptor(finalResponse)
      }

      // 缓存GET请求结果
      if (method === 'GET' && enableCache) {
        this.setToCache(cacheKey, finalResponse, cacheTime)
      }

      console.log(`✅ [RequestService] 请求成功: ${method} ${fullURL}`)
      return finalResponse

    } catch (error: any) {
      console.error(`❌ [RequestService] 请求失败: ${method} ${fullURL}`, error)
      throw this.handleError(error, finalConfig)
    }
  }

  /**
   * 执行实际的uni.request
   */
  private executeRequest(options: {
    url: string
    method: string
    data?: any
    header: Record<string, string>
    timeout: number
    responseType: string
  }): Promise<any> {
    const { url, method, data, header, timeout, responseType } = options

    return new Promise((resolve, reject) => {
      const requestOptions: UniNamespace.RequestOptions = {
        url,
        method: method as any,
        header,
        timeout,
        responseType: responseType as any,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data)
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.errMsg || 'Request failed'}`))
          }
        },
        fail: (err) => {
          reject(new Error(err.errMsg || 'Network request failed'))
        }
      }

      // 根据请求方法设置数据
      if (method === 'GET' && data) {
        // GET请求将数据转为查询参数
        const params = new URLSearchParams(data).toString()
        requestOptions.url = `${url}${url.includes('?') ? '&' : '?'}${params}`
      } else if (data) {
        // 其他请求将数据放在body中
        requestOptions.data = data
      }

      uni.request(requestOptions)
    })
  }

  /**
   * 构建完整URL
   */
  private buildFullURL(url: string): string {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url
    }
    return `${this.baseURL}${url.startsWith('/') ? url : '/' + url}`
  }

  /**
   * 构建缓存key
   */
  private buildCacheKey(method: string, url: string, data?: any): string {
    const dataStr = data ? JSON.stringify(data) : ''
    return `${method}:${url}:${dataStr}`
  }

  /**
   * 从缓存获取数据
   */
  private getFromCache(key: string): any {
    const item = this.cache.get(key)
    if (item && Date.now() < item.expireTime) {
      return item.data
    }
    if (item) {
      this.cache.delete(key)
    }
    return null
  }

  /**
   * 设置缓存数据
   */
  private setToCache(key: string, data: any, cacheTime: number): void {
    this.cache.set(key, {
      data,
      expireTime: Date.now() + cacheTime
    })
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear()
    console.log(`🗑️ [RequestService] 缓存已清除`)
  }

  /**
   * 错误处理
   */
  private handleError(error: Error, config: RequestConfig): Error {
    // 可以在这里添加统一的错误处理逻辑
    // 例如：自动重试、错误上报、用户提示等
    
    if (error.message.includes('timeout')) {
      return new Error('请求超时，请检查网络连接')
    }
    
    if (error.message.includes('Network')) {
      return new Error('网络连接异常，请检查网络设置')
    }
    
    return error
  }

  /**
   * 获取默认基础URL
   */
  private getDefaultBaseURL(): string {
    // #ifdef H5
    return location.origin
    // #endif
    
    // #ifdef MP
    return 'https://api.lowcode.example.com'
    // #endif
    
    // #ifdef APP-PLUS
    return 'https://api.lowcode.example.com'
    // #endif
    
    return 'https://api.lowcode.example.com'
  }

  /**
   * 设置默认请求头
   */
  private setupDefaultHeaders(): void {
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'UniApp'
    }

    // 添加平台标识
    // #ifdef H5
    this.defaultHeaders['X-Platform'] = 'H5'
    // #endif
    
    // #ifdef MP-WEIXIN
    this.defaultHeaders['X-Platform'] = 'WeChat-MiniProgram'
    // #endif
    
    // #ifdef APP-PLUS
    this.defaultHeaders['X-Platform'] = 'App'
    // #endif
  }
}

// 创建默认实例
export const requestService = new RequestService()

// 设置默认拦截器
requestService.addRequestInterceptor((config) => {
  // 可以在这里添加全局请求拦截逻辑
  // 例如：添加时间戳、签名等
  return config
})

requestService.addResponseInterceptor((response) => {
  // 可以在这里添加全局响应拦截逻辑
  // 例如：统一错误处理、数据格式化等
  return response
})

// 导出便捷方法
export const { get, post, put, delete: del } = requestService