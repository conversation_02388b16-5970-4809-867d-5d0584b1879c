/**
 * 事件处理服务
 * 
 * 处理动态组件的事件响应和业务逻辑
 * 适配UniApp-X路由和API调用
 */

export interface ComponentEvent {
  componentId: string
  componentType: string
  eventType: string
  eventData: any
  target?: string
  params?: Record<string, any>
}

export interface EventConfig {
  type: 'navigation' | 'api' | 'action' | 'custom'
  target?: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  params?: Record<string, any>
  data?: any
  success?: string
  fail?: string
}

export class EventHandlerService {
  
  /**
   * 处理组件事件
   */
  async handleComponentEvent(event: ComponentEvent): Promise<void> {
    const { componentId, componentType, eventType, eventData } = event
    
    console.log(`🎯 [EventHandlerService] 处理组件事件:`, {
      componentId,
      componentType,
      eventType,
      eventData
    })

    try {
      // 根据事件配置类型处理
      const eventConfig = eventData as EventConfig
      
      switch (eventConfig.type) {
        case 'navigation':
          await this.handleNavigation(eventConfig)
          break
          
        case 'api':
          await this.handleApiCall(eventConfig)
          break
          
        case 'action':
          await this.handleAction(eventConfig)
          break
          
        case 'custom':
          await this.handleCustomEvent(eventConfig, event)
          break
          
        default:
          console.warn(`⚠️ [EventHandlerService] 未知事件类型: ${eventConfig.type}`)
      }
      
    } catch (error: any) {
      console.error(`❌ [EventHandlerService] 事件处理失败:`, error)
      
      // 显示错误提示
      uni.showToast({
        title: error.message || '操作失败',
        icon: 'none',
        duration: 2000
      })
    }
  }

  /**
   * 处理导航事件
   */
  private async handleNavigation(config: EventConfig): Promise<void> {
    const { target, params } = config
    
    if (!target) {
      throw new Error('导航目标不能为空')
    }

    console.log(`🧭 [EventHandlerService] 执行导航: ${target}`)

    // 构建导航URL
    let url = target
    if (params && Object.keys(params).length > 0) {
      const queryString = new URLSearchParams(params).toString()
      url = `${target}${target.includes('?') ? '&' : '?'}${queryString}`
    }

    // 根据路径类型选择导航方式
    if (target.startsWith('/pages/')) {
      // 普通页面导航
      await uni.navigateTo({ url })
    } else if (target.startsWith('/pages/modules/')) {
      // 分包页面导航
      await uni.navigateTo({ url })
    } else if (target.includes('tabBar')) {
      // TabBar页面切换
      await uni.switchTab({ url: target })
    } else if (target.startsWith('http')) {
      // 外部链接，使用WebView
      const webviewUrl = `/pages/webview/webview?url=${encodeURIComponent(target)}`
      await uni.navigateTo({ url: webviewUrl })
    } else {
      // 其他情况，默认使用navigateTo
      await uni.navigateTo({ url })
    }
  }

  /**
   * 处理API调用事件
   */
  private async handleApiCall(config: EventConfig): Promise<void> {
    const { target, method = 'POST', params, data, success, fail } = config
    
    if (!target) {
      throw new Error('API目标地址不能为空')
    }

    console.log(`🌐 [EventHandlerService] 执行API调用: ${method} ${target}`)

    try {
      const response = await this.request({
        url: target,
        method,
        data: data || params,
        header: {
          'Content-Type': 'application/json'
        }
      })

      console.log(`✅ [EventHandlerService] API调用成功:`, response)

      // 处理成功回调
      if (success) {
        await this.handleAction({ type: 'action', target: success, params: response })
      } else {
        // 默认成功提示
        uni.showToast({
          title: '操作成功',
          icon: 'success',
          duration: 1500
        })
      }

    } catch (error: any) {
      console.error(`❌ [EventHandlerService] API调用失败:`, error)

      // 处理失败回调
      if (fail) {
        await this.handleAction({ type: 'action', target: fail, params: { error: error.message } })
      } else {
        throw error
      }
    }
  }

  /**
   * 处理动作事件
   */
  private async handleAction(config: EventConfig): Promise<void> {
    const { target, params } = config
    
    if (!target) {
      throw new Error('动作目标不能为空')
    }

    console.log(`⚡ [EventHandlerService] 执行动作: ${target}`)

    switch (target) {
      case 'showToast':
        uni.showToast({
          title: params?.message || '操作完成',
          icon: params?.icon || 'success',
          duration: params?.duration || 1500
        })
        break

      case 'showModal':
        uni.showModal({
          title: params?.title || '提示',
          content: params?.content || '',
          showCancel: params?.showCancel !== false,
          success: (res) => {
            if (res.confirm && params?.confirm) {
              this.handleAction({ type: 'action', target: params.confirm })
            }
          }
        })
        break

      case 'showLoading':
        uni.showLoading({
          title: params?.title || '加载中...',
          mask: params?.mask !== false
        })
        break

      case 'hideLoading':
        uni.hideLoading()
        break

      case 'refresh':
        // 刷新当前页面
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]
        if (currentPage && typeof currentPage.onPullDownRefresh === 'function') {
          currentPage.onPullDownRefresh()
        }
        break

      case 'goBack':
        uni.navigateBack({
          delta: params?.delta || 1
        })
        break

      case 'reLaunch':
        if (params?.url) {
          uni.reLaunch({ url: params.url })
        }
        break

      default:
        console.warn(`⚠️ [EventHandlerService] 未知动作: ${target}`)
    }
  }

  /**
   * 处理自定义事件
   */
  private async handleCustomEvent(config: EventConfig, originalEvent: ComponentEvent): Promise<void> {
    const { target, params } = config
    
    console.log(`🔧 [EventHandlerService] 执行自定义事件: ${target}`)

    // 发布自定义事件到全局事件总线
    uni.$emit(target || 'customEvent', {
      ...params,
      originalEvent
    })

    // 也可以在这里添加其他自定义逻辑
    // 例如调用特定的业务模块处理函数
  }

  /**
   * 统一网络请求方法
   */
  private async request(options: {
    url: string
    method: 'GET' | 'POST' | 'PUT' | 'DELETE'
    data?: any
    header?: Record<string, string>
  }): Promise<any> {
    return new Promise((resolve, reject) => {
      uni.request({
        ...options,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data)
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.errMsg}`))
          }
        },
        fail: (err) => {
          reject(new Error(err.errMsg || 'Request failed'))
        }
      })
    })
  }
}

// 创建默认实例
export const eventHandlerService = new EventHandlerService()

// 便捷方法
export const handleComponentEvent = (event: ComponentEvent) => 
  eventHandlerService.handleComponentEvent(event)