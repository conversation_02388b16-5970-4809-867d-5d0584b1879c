/**
 * 页面配置服务
 * 
 * 提供页面配置的加载、缓存和管理功能
 * 适配UniApp-X使用uni.request替代axios
 */

export interface PageConfig {
  id: string
  name: string
  style: {
    backgroundColor?: string
    padding?: string
    [key: string]: any
  }
  components: Array<{
    id: string
    type: string
    props: Record<string, any>
    style: Record<string, any>
    events?: Record<string, any>
  }>
}

export interface LoadConfigOptions {
  appId: string
  pageId: string
  version?: string
  cache?: boolean
}

interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

export class PageConfigService {
  private baseUrl: string = ''
  private cache = new Map<string, PageConfig>()
  private cacheTimeout = 5 * 60 * 1000 // 5分钟缓存

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl || this.getDefaultBaseUrl()
  }

  /**
   * 加载页面配置
   */
  async loadPageConfig(options: LoadConfigOptions): Promise<PageConfig> {
    const { appId, pageId, version, cache = true } = options
    const cacheKey = `${appId}:${pageId}:${version || 'latest'}`

    // 检查缓存
    if (cache && this.cache.has(cacheKey)) {
      console.log(`📦 [PageConfigService] 使用缓存配置: ${cacheKey}`)
      return this.cache.get(cacheKey)!
    }

    try {
      console.log(`🌐 [PageConfigService] 请求页面配置: ${cacheKey}`)
      
      const response = await this.request<ApiResponse<PageConfig>>({
        url: `/api/pages/config`,
        method: 'GET',
        data: {
          appId,
          pageId,
          version
        }
      })

      if (response.code !== 200) {
        throw new Error(response.message || '页面配置加载失败')
      }

      const config = response.data
      
      // 缓存配置
      if (cache) {
        this.cache.set(cacheKey, config)
        
        // 设置缓存过期
        setTimeout(() => {
          this.cache.delete(cacheKey)
        }, this.cacheTimeout)
      }

      console.log(`✅ [PageConfigService] 页面配置加载成功: ${config.name}`)
      return config

    } catch (error: any) {
      console.error(`❌ [PageConfigService] 页面配置加载失败:`, error)
      
      // 降级到模拟数据
      return this.getFallbackConfig(options)
    }
  }

  /**
   * 预加载页面配置
   */
  async preloadPageConfig(options: LoadConfigOptions): Promise<void> {
    try {
      await this.loadPageConfig({ ...options, cache: true })
    } catch (error) {
      console.warn(`⚠️ [PageConfigService] 预加载失败，将在需要时重试`, error)
    }
  }

  /**
   * 批量加载页面配置
   */
  async loadMultipleConfigs(requests: LoadConfigOptions[]): Promise<PageConfig[]> {
    const results = await Promise.allSettled(
      requests.map(options => this.loadPageConfig(options))
    )

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value
      } else {
        console.error(`❌ [PageConfigService] 批量加载失败 [${index}]:`, result.reason)
        return this.getFallbackConfig(requests[index])
      }
    })
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear()
    console.log(`🗑️ [PageConfigService] 缓存已清除`)
  }

  /**
   * 清除指定缓存
   */
  clearConfigCache(appId: string, pageId: string, version?: string): void {
    const cacheKey = `${appId}:${pageId}:${version || 'latest'}`
    if (this.cache.delete(cacheKey)) {
      console.log(`🗑️ [PageConfigService] 已清除缓存: ${cacheKey}`)
    }
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }

  /**
   * 统一网络请求方法
   */
  private async request<T>(options: {
    url: string
    method: 'GET' | 'POST' | 'PUT' | 'DELETE'
    data?: any
    header?: Record<string, string>
    timeout?: number
  }): Promise<T> {
    const { url, method, data, header = {}, timeout = 10000 } = options
    
    // 构建完整URL
    const fullUrl = url.startsWith('http') ? url : `${this.baseUrl}${url}`
    
    // 设置默认请求头
    const defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...header
    }

    return new Promise((resolve, reject) => {
      const requestOptions: UniNamespace.RequestOptions = {
        url: fullUrl,
        method: method as any,
        header: defaultHeaders,
        timeout,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data as T)
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.errMsg || 'Request failed'}`))
          }
        },
        fail: (err) => {
          reject(new Error(err.errMsg || 'Network request failed'))
        }
      }

      // 根据请求方法设置数据
      if (method === 'GET') {
        // GET请求将数据转为查询参数
        if (data) {
          const params = new URLSearchParams(data).toString()
          requestOptions.url = `${fullUrl}${fullUrl.includes('?') ? '&' : '?'}${params}`
        }
      } else {
        // POST/PUT/DELETE请求将数据放在body中
        requestOptions.data = data
      }

      uni.request(requestOptions)
    })
  }

  /**
   * 获取默认基础URL
   */
  private getDefaultBaseUrl(): string {
    // #ifdef H5
    return location.origin
    // #endif
    
    // #ifdef MP
    return 'https://api.lowcode.example.com'
    // #endif
    
    // #ifdef APP-PLUS
    return 'https://api.lowcode.example.com'
    // #endif
    
    return 'https://api.lowcode.example.com'
  }

  /**
   * 降级配置
   */
  private getFallbackConfig(options: LoadConfigOptions): PageConfig {
    console.log(`🔄 [PageConfigService] 使用降级配置: ${options.appId}:${options.pageId}`)
    
    return {
      id: options.pageId,
      name: '降级页面',
      style: {
        backgroundColor: '#ffffff',
        padding: '32rpx'
      },
      components: [
        {
          id: 'fallback-header',
          type: 'Header',
          props: {
            title: '页面暂时无法加载',
            subtitle: '请检查网络连接后重试'
          },
          style: {
            textAlign: 'center',
            marginBottom: '32rpx'
          }
        },
        {
          id: 'fallback-content',
          type: 'Text',
          props: {
            content: '我们正在努力修复这个问题，请稍后再试。'
          },
          style: {
            fontSize: '28rpx',
            color: '#666',
            textAlign: 'center'
          }
        }
      ]
    }
  }
}

// 创建默认实例
export const pageConfigService = new PageConfigService()

// 便捷方法
export const loadPageConfig = (options: LoadConfigOptions) => 
  pageConfigService.loadPageConfig(options)

export const preloadPageConfig = (options: LoadConfigOptions) => 
  pageConfigService.preloadPageConfig(options)

export const clearPageConfigCache = () => 
  pageConfigService.clearCache()