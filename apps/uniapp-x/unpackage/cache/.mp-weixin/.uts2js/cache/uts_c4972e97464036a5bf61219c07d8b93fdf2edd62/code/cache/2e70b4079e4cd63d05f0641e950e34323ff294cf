{"code": "import { defineComponent as _defineComponent } from 'vue';\nimport { o as _o, gei as _gei, sei as _sei } from \"vue\";\nexport default /*#__PURE__*/ _defineComponent({\n    __name: 'LoginTest',\n    setup(__props) {\n        // 跳转到登录页面\n        const goToLogin = () => {\n            uni.navigateTo({\n                url: '/pages/modules/device/Login/Login'\n            });\n        };\n        // 显示页面信息\n        const showInfo = () => {\n            uni.showModal(new UTSJSONObject({\n                title: '登录页面信息',\n                content: '已完成 H5 端登录 UI 的一比一复制，包括：\\n\\n1. UserLogin 组件结构\\n2. DeviceNum 组件样式\\n3. SCSS 变量和 mixins\\n4. 响应式布局\\n5. 多端兼容性',\n                showCancel: false,\n                confirmText: '知道了'\n            }));\n        };\n        return (_ctx = null, _cache = null) => {\n            const __returned__ = {\n                a: _o(goToLogin),\n                b: _o(showInfo),\n                c: _sei(_gei(_ctx, ''), 'view')\n            };\n            return __returned__;\n        };\n    }\n});\n//# sourceMappingURL=/Users/<USER>/Desktop/project/lowcode_as-main/apps/uniapp-x/pages/test/LoginTest.uvue?vue&type=script&setup=true&lang.uts.js.map", "references": ["/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli-vite/node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli-vite/node_modules/@vue/runtime-core/dist/runtime-core.d.ts"], "uniExtApis": ["uni.navigateTo", "uni.showModal"], "map": "{\"version\":3,\"file\":\"LoginTest.uvue?vue&type=script&setup=true&lang.uts.js\",\"sourceRoot\":\"\",\"sources\":[\"LoginTest.uvue?vue&type=script&setup=true&lang.uts\"],\"names\":[],\"mappings\":\"AAAA,OAAO,EAAE,eAAe,IAAI,gBAAgB,EAAE,MAAM,KAAK,CAAA;AACzD,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,IAAI,IAAI,EAAE,GAAG,IAAI,IAAI,EAAE,MAAM,KAAK,CAAA;AAGvD,eAAe,aAAa,CAAA,gBAAgB,CAAC;IAC3C,MAAM,EAAE,WAAW;IACnB,KAAK,CAAC,OAAO;QAEf,UAAU;QACV,MAAM,SAAS,GAAG;YAChB,GAAG,CAAC,UAAU,CAAC;gBACb,GAAG,EAAE,mCAAmC;aACzC,CAAC,CAAA;QACJ,CAAC,CAAA;QAED,SAAS;QACT,MAAM,QAAQ,GAAG;YACf,GAAG,CAAC,SAAS,mBAAC;gBACZ,KAAK,EAAE,QAAQ;gBACf,OAAO,EAAE,0GAA0G;gBACnH,UAAU,EAAE,KAAK;gBACjB,WAAW,EAAE,KAAK;aACnB,EAAC,CAAA;QACJ,CAAC,CAAA;QAED,OAAO,CAAC,IAAI,OAAA,EAAE,MAAM,OAAA;YAClB,MAAM,YAAY,GAAG;gBACrB,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC;gBAChB,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC;gBACf,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC;aAChC,CAAA;YACC,OAAO,YAAY,CAAA;QACrB,CAAC,CAAA;IACD,CAAC;CAEA,CAAC,CAAA\"}"}