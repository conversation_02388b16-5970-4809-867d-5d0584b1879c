{"code": "import { __awaiter } from \"tslib\";\nimport { defineComponent as _defineComponent } from 'vue';\nimport { resolveComponent as _resolveComponent, p as _p, unref as _unref, o as _o, toDisplayString as _toDisplayString, t as _t, gei as _gei, sei as _sei, e as _e } from \"vue\";\nconst __BINDING_COMPONENTS__ = '{\"l-icon\":{\"name\":\"_easycom_l_icon\",\"type\":\"unknown\"},\"DxIcon\":{\"name\":\"_unref(DxIcon)\",\"type\":\"setup\"}}';\nif (!Array) {\n    const _easycom_l_icon_1 = _resolveComponent(\"l-icon\");\n    (_easycom_l_icon_1)();\n}\nimport _easycom_l_icon from '/Users/<USER>/Desktop/project/lowcode_as-main/apps/uniapp-x/uni_modules/lime-icon/components/l-icon/l-icon.uvue';\nif (!Math) {\n    (_easycom_l_icon + _unref(DxIcon))();\n}\nimport { ref, onMounted } from 'vue';\nimport { useGlobalData } from '../../../../hooks/device/useGlobalData';\nimport { requestService } from '../../../../services/RequestService';\nimport DxIcon from \"/components/Common/DxIcon.uvue\";\n// ==================== 响应式数据 ====================\nexport default /*#__PURE__*/ _defineComponent({\n    __name: 'Login',\n    setup(__props, _a) {\n        var __expose = _a.expose;\n        const _b = useGlobalData(), setDeviceDetails = _b.setDeviceDetails, setDeviceKey = _b.setDeviceKey, setUserToken = _b.setUserToken, setDeviceLoading = _b.setDeviceLoading, deviceData = _b.deviceData, userData = _b.userData;\n        const loginType = ref('device');\n        const deviceNo = ref('');\n        const loading = ref(false);\n        const isDev = ref(false);\n        // ==================== 生命周期 ====================\n        onMounted(() => { return __awaiter(this, void 0, void 0, function* () {\n            uni.__f__('log', 'at pages/modules/device/Login/Login.uvue:100', '🔄 [Login] 登录页面初始化...');\n            // 检查开发环境\n            isDev.value = true;\n            // 清除之前的加载状态\n            setDeviceLoading(false);\n            // 尝试获取系统配置\n            try {\n                yield fetchSystemConfig();\n                uni.__f__('log', 'at pages/modules/device/Login/Login.uvue:113', '✅ [Login] 系统配置获取成功');\n            }\n            catch (error) {\n                uni.__f__('error', 'at pages/modules/device/Login/Login.uvue:115', '❌ [Login] 系统配置获取失败:', error);\n            }\n            // 开发环境预填设备号\n            if (isDev.value) {\n                // deviceNo.value = '863780070053924' // 可以取消注释用于测试\n            }\n        }); });\n        // ==================== 业务方法 ====================\n        /**\n         * 获取系统配置\n         */\n        const fetchSystemConfig = () => { return __awaiter(this, void 0, void 0, function* () {\n            try {\n                // 获取管理配置\n                const manageConfig = yield requestService.get('/api/system/manage-config');\n                uni.__f__('log', 'at pages/modules/device/Login/Login.uvue:133', '📋 [Login] 管理配置:', manageConfig);\n                // 获取充值配置\n                const rechargeConfig = yield requestService.get('/api/system/recharge-config');\n                uni.__f__('log', 'at pages/modules/device/Login/Login.uvue:137', '💰 [Login] 充值配置:', rechargeConfig);\n            }\n            catch (error) {\n                uni.__f__('warn', 'at pages/modules/device/Login/Login.uvue:140', '⚠️ [Login] 配置获取失败，继续使用默认配置');\n            }\n        }); };\n        /**\n         * 设备号输入处理\n         */\n        const onDeviceNoInput = (e = null) => {\n            deviceNo.value = e.detail.value;\n        };\n        /**\n         * 设备登录处理\n         */\n        const handleDeviceLogin = () => { return __awaiter(this, void 0, void 0, function* () {\n            // 防止重复点击\n            if (loading.value)\n                return Promise.resolve(null);\n            const trimmedDeviceNo = deviceNo.value.trim();\n            if (!trimmedDeviceNo) {\n                uni.showToast({\n                    title: '请输入设备卡号',\n                    icon: 'none',\n                    duration: 2000\n                });\n                return Promise.resolve(null);\n            }\n            loading.value = true;\n            setDeviceLoading(true);\n            try {\n                uni.__f__('log', 'at pages/modules/device/Login/Login.uvue:172', '🔐 [Login] 开始设备登录:', trimmedDeviceNo);\n                // 调用登录API\n                const loginResult = yield requestService.post('/api/device/login', new UTSJSONObject({\n                    deviceNo: trimmedDeviceNo,\n                    groupId: 2 // 与H5端保持一致\n                }));\n                uni.__f__('log', 'at pages/modules/device/Login/Login.uvue:180', '✅ [Login] 登录响应:', loginResult);\n                // 处理登录成功响应\n                if (loginResult.code === 200 && loginResult.data) {\n                    const _a = loginResult.data, device = _a.device, token = _a.token, user = _a.user;\n                    // 保存设备信息\n                    if (device) {\n                        setDeviceDetails(device);\n                        if (device.key) {\n                            setDeviceKey(device.key);\n                        }\n                    }\n                    // 保存用户token\n                    if (token) {\n                        setUserToken(token);\n                    }\n                    // 保存用户信息\n                    if (user) {\n                        // setUserProfile(user) // 如果有用户信息的话\n                    }\n                    // 显示登录成功提示\n                    uni.showToast({\n                        title: '登录成功',\n                        icon: 'success',\n                        duration: 1500\n                    });\n                    // 处理登录后跳转\n                    yield handleLoginSuccess();\n                }\n                else {\n                    throw new Error(loginResult.message || '登录失败');\n                }\n            }\n            catch (error) {\n                uni.__f__('error', 'at pages/modules/device/Login/Login.uvue:219', '❌ [Login] 登录失败:', error);\n                // 显示错误提示\n                uni.showToast({\n                    title: error.message || '登录失败，请重试',\n                    icon: 'none',\n                    duration: 2000\n                });\n            }\n            finally {\n                loading.value = false;\n                setDeviceLoading(false);\n            }\n        }); };\n        /**\n         * 登录成功后的跳转处理\n         */\n        const handleLoginSuccess = () => { return __awaiter(this, void 0, void 0, function* () {\n            // 获取页面参数\n            const pages = getCurrentPages();\n            const currentPage = pages[pages.length - 1];\n            const options = currentPage.options;\n            uni.__f__('log', 'at pages/modules/device/Login/Login.uvue:243', '🔄 [Login] 处理登录后跳转:', options);\n            try {\n                // 检查重定向参数\n                const redirectUrl = options.redirect;\n                const appId = options.appId || uni.getStorageSync('current-app-id');\n                if (redirectUrl) {\n                    // 有重定向地址，直接跳转\n                    uni.__f__('log', 'at pages/modules/device/Login/Login.uvue:252', '🚀 [Login] 重定向到:', redirectUrl);\n                    if (appId && appId !== 'home') {\n                        // 设置低代码应用状态\n                        uni.setStorageSync('isLowcodeApp', 'true');\n                        uni.setStorageSync('current-app-id', appId);\n                    }\n                    // UniApp-X中需要处理URL格式\n                    if (redirectUrl.startsWith('/')) {\n                        yield uni.reLaunch({ url: redirectUrl });\n                    }\n                    else {\n                        yield uni.reLaunch({ url: `/${redirectUrl}` });\n                    }\n                }\n                else if (appId && appId !== 'home') {\n                    // 有appId，跳转到对应的动态页面\n                    uni.__f__('log', 'at pages/modules/device/Login/Login.uvue:269', '🚀 [Login] 跳转到应用:', appId);\n                    uni.setStorageSync('isLowcodeApp', 'true');\n                    uni.setStorageSync('current-app-id', appId);\n                    yield uni.reLaunch({\n                        url: `/pages/dynamic/dynamic?appId=${appId}&pageId=home`\n                    });\n                }\n                else if (appId === 'home') {\n                    // home应用，跳转到主页\n                    uni.__f__('log', 'at pages/modules/device/Login/Login.uvue:280', '🚀 [Login] 跳转到主页');\n                    yield uni.reLaunch({\n                        url: `/pages/dynamic/dynamic?appId=home&pageId=index`\n                    });\n                }\n                else {\n                    // 默认跳转到套餐列表\n                    uni.__f__('log', 'at pages/modules/device/Login/Login.uvue:288', '🚀 [Login] 跳转到套餐列表');\n                    yield uni.reLaunch({\n                        url: '/pages/modules/device/PackageList/PackageList'\n                    });\n                }\n            }\n            catch (error) {\n                uni.__f__('error', 'at pages/modules/device/Login/Login.uvue:296', '❌ [Login] 跳转失败:', error);\n                // 跳转失败，回退到套餐列表\n                yield uni.reLaunch({\n                    url: '/pages/modules/device/PackageList/PackageList'\n                });\n            }\n        }); };\n        // ==================== 页面生命周期 ====================\n        // 下拉刷新\n        const onPullDownRefresh = () => {\n            fetchSystemConfig().finally(() => {\n                uni.stopPullDownRefresh();\n            });\n        };\n        // 导出给页面使用\n        __expose(new UTSJSONObject({\n            onPullDownRefresh,\n            deviceNo,\n            loading,\n            handleDeviceLogin\n        }));\n        return (_ctx = null, _cache = null) => {\n            const __returned__ = _e(new UTSJSONObject({\n                a: _p(new UTSJSONObject({\n                    name: 'circle'\n                })),\n                b: _p(new UTSJSONObject({\n                    name: 'work',\n                    color: '#1989fa'\n                })),\n                c: _p(new UTSJSONObject({\n                    name: 'ri:account-box-fill'\n                })),\n                d: _p(new UTSJSONObject({\n                    name: 'mdi:home',\n                    type: 'iconify'\n                })),\n                e: loginType.value === 'device' ? 1 : '',\n                f: _o(($event = null) => { return loginType.value = 'device'; }),\n                g: loginType.value === 'device'\n            }), loginType.value === 'device' ? new UTSJSONObject({\n                h: loading.value,\n                i: _o([($event = null) => { return deviceNo.value = $event.detail.value; }, onDeviceNoInput]),\n                j: deviceNo.value,\n                k: _t(loading.value ? '登录中...' : '登录'),\n                l: loading.value || !deviceNo.value.trim() ? 1 : '',\n                m: _o(handleDeviceLogin),\n                n: loading.value || !deviceNo.value.trim()\n            }) : new UTSJSONObject({}), new UTSJSONObject({\n                o: isDev.value\n            }), isDev.value ? new UTSJSONObject({\n                p: _t(deviceNo.value),\n                q: _t(loading.value)\n            }) : new UTSJSONObject({}), new UTSJSONObject({\n                r: _sei(_gei(_ctx, ''), 'view')\n            }));\n            return __returned__;\n        };\n    }\n});\n//# sourceMappingURL=/Users/<USER>/Desktop/project/lowcode_as-main/apps/uniapp-x/pages/modules/device/Login/Login.uvue?vue&type=script&setup=true&lang.uts.js.map", "references": ["/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli-vite/node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli-vite/node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "/Users/<USER>/Desktop/project/lowcode_as-main/apps/uniapp-x/uni_modules/lime-icon/components/l-icon/l-icon.uvue.ts", "/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli-vite/node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "/Users/<USER>/Desktop/project/lowcode_as-main/apps/uniapp-x/hooks/device/useGlobalData.uts", "/Users/<USER>/Desktop/project/lowcode_as-main/apps/uniapp-x/services/RequestService.uts"], "uniExtApis": ["uni.__f__", "uni.showToast", "uni.getStorageSync", "uni.setStorageSync", "uni.reLaunch", "uni.stopPullDownRefresh"], "map": "{\"version\":3,\"file\":\"Login.uvue?vue&type=script&setup=true&lang.uts.js\",\"sourceRoot\":\"\",\"sources\":[\"Login.uvue?vue&type=script&setup=true&lang.uts\"],\"names\":[],\"mappings\":\";AAAA,OAAO,EAAE,eAAe,IAAI,gBAAgB,EAAE,MAAM,KAAK,CAAA;AACzD,OAAO,EAAE,gBAAgB,IAAI,iBAAiB,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK,IAAI,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE,eAAe,IAAI,gBAAgB,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,IAAI,IAAI,EAAE,GAAG,IAAI,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE,MAAM,KAAK,CAAA;AAC/K,MAAM,sBAAsB,GAAG,0GAA0G,CAAA;AACzI,IAAI,CAAC,KAAK,EAAE;IAAC,MAAM,iBAAe,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAAA,CAAC,iBAAe,CAAC,EAAE,CAAA;CAAC;AACrF,OAAO,eAAe,MAAM,kHAAkH,CAAC;AAC/I,IAAI,CAAC,IAAI,EAAE;IAAE,CAAC,eAAe,GAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAA;CAAE;AAEjD,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,MAAM,KAAK,CAAA;AACpC,OAAO,EAAE,aAAa,EAAE,MAAM,wCAAwC,CAAA;AACtE,OAAO,EAAE,cAAc,EAAE,MAAM,qCAAqC,CAAA;AACpE,OAAO,MAAM,MAAO,gCAAgC,CAAC;AAErD,kDAAkD;AAGlD,eAAe,aAAa,CAAA,gBAAgB,CAAC;IAC3C,MAAM,EAAE,OAAO;IACf,KAAK,CAAC,OAAO,EAAE,EAAoB;YAAV,QAAQ,YAAA;QAE7B,MAAA,KAOF,aAAa,EAAE,EANjB,gBAAgB,sBAAA,EAChB,YAAY,kBAAA,EACZ,YAAY,kBAAA,EACZ,gBAAgB,sBAAA,EAChB,UAAU,gBAAA,EACV,QAAQ,cACS,CAAA;QAEnB,MAAM,SAAS,GAAG,GAAG,CAAqB,QAAQ,CAAC,CAAA;QACnD,MAAM,QAAQ,GAAG,GAAG,CAAS,EAAE,CAAC,CAAA;QAChC,MAAM,OAAO,GAAG,GAAG,CAAU,KAAK,CAAC,CAAA;QACnC,MAAM,KAAK,GAAG,GAAG,CAAU,KAAK,CAAC,CAAA;QAEjC,iDAAiD;QAEjD,SAAS,CAAC;YACR,GAAG,CAAC,KAAK,CAAC,KAAK,EAAC,8CAA8C,EAAC,uBAAuB,CAAC,CAAA;YAEvF,SAAS;YAET,KAAK,CAAC,KAAK,GAAG,IAAI,CAAA;YAGlB,YAAY;YACZ,gBAAgB,CAAC,KAAK,CAAC,CAAA;YAEvB,WAAW;YACX,IAAI;gBACF,MAAM,iBAAiB,EAAE,CAAA;gBACzB,GAAG,CAAC,KAAK,CAAC,KAAK,EAAC,8CAA8C,EAAC,oBAAoB,CAAC,CAAA;aACrF;YAAC,OAAO,KAAK,EAAE;gBACd,GAAG,CAAC,KAAK,CAAC,OAAO,EAAC,8CAA8C,EAAC,qBAAqB,EAAE,KAAK,CAAC,CAAA;aAC/F;YAED,YAAY;YACZ,IAAI,KAAK,CAAC,KAAK,EAAE;gBACf,mDAAmD;aACpD;QACH,CAAC,IAAA,CAAC,CAAA;QAEF,iDAAiD;QAEjD;;WAEG;QACH,MAAM,iBAAiB,GAAG;YACxB,IAAI;gBACF,SAAS;gBACT,MAAM,YAAY,GAAG,MAAM,cAAc,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAA;gBAC1E,GAAG,CAAC,KAAK,CAAC,KAAK,EAAC,8CAA8C,EAAC,kBAAkB,EAAE,YAAY,CAAC,CAAA;gBAEhG,SAAS;gBACT,MAAM,cAAc,GAAG,MAAM,cAAc,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAA;gBAC9E,GAAG,CAAC,KAAK,CAAC,KAAK,EAAC,8CAA8C,EAAC,kBAAkB,EAAE,cAAc,CAAC,CAAA;aAEnG;YAAC,OAAO,KAAK,EAAE;gBACd,GAAG,CAAC,KAAK,CAAC,MAAM,EAAC,8CAA8C,EAAC,4BAA4B,CAAC,CAAA;aAC9F;QACH,CAAC,IAAA,CAAA;QAED;;WAEG;QACH,MAAM,eAAe,GAAG,CAAC,QAAM;YAC7B,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,CAAA;QACjC,CAAC,CAAA;QAED;;WAEG;QACH,MAAM,iBAAiB,GAAG;YACxB,SAAS;YACT,IAAI,OAAO,CAAC,KAAK;gBAAE,6BAAM;YAEzB,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;YAC7C,IAAI,CAAC,eAAe,EAAE;gBACpB,GAAG,CAAC,SAAS,CAAC;oBACZ,KAAK,EAAE,SAAS;oBAChB,IAAI,EAAE,MAAM;oBACZ,QAAQ,EAAE,IAAI;iBACf,CAAC,CAAA;gBACF,6BAAM;aACP;YAED,OAAO,CAAC,KAAK,GAAG,IAAI,CAAA;YACpB,gBAAgB,CAAC,IAAI,CAAC,CAAA;YAEtB,IAAI;gBACF,GAAG,CAAC,KAAK,CAAC,KAAK,EAAC,8CAA8C,EAAC,oBAAoB,EAAE,eAAe,CAAC,CAAA;gBAErG,UAAU;gBACV,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,mBAAmB,oBAAE;oBACjE,QAAQ,EAAE,eAAe;oBACzB,OAAO,EAAE,CAAC,CAAC,WAAW;iBACvB,EAAC,CAAA;gBAEF,GAAG,CAAC,KAAK,CAAC,KAAK,EAAC,8CAA8C,EAAC,iBAAiB,EAAE,WAAW,CAAC,CAAA;gBAE9F,WAAW;gBACX,IAAI,WAAW,CAAC,IAAI,KAAK,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE;oBAC1C,MAAA,KAA0B,WAAW,CAAC,IAAI,EAAxC,MAAM,YAAA,EAAE,KAAK,WAAA,EAAE,IAAI,UAAqB,CAAA;oBAEhD,SAAS;oBACT,IAAI,MAAM,EAAE;wBACV,gBAAgB,CAAC,MAAM,CAAC,CAAA;wBACxB,IAAI,MAAM,CAAC,GAAG,EAAE;4BACd,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;yBACzB;qBACF;oBAED,YAAY;oBACZ,IAAI,KAAK,EAAE;wBACT,YAAY,CAAC,KAAK,CAAC,CAAA;qBACpB;oBAED,SAAS;oBACT,IAAI,IAAI,EAAE;wBACR,oCAAoC;qBACrC;oBAED,WAAW;oBACX,GAAG,CAAC,SAAS,CAAC;wBACZ,KAAK,EAAE,MAAM;wBACb,IAAI,EAAE,SAAS;wBACf,QAAQ,EAAE,IAAI;qBACf,CAAC,CAAA;oBAEF,UAAU;oBACV,MAAM,kBAAkB,EAAE,CAAA;iBAE3B;qBAAM;oBACL,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,OAAO,IAAI,MAAM,CAAC,CAAA;iBAC/C;aAEF;YAAC,OAAO,KAAU,EAAE;gBACnB,GAAG,CAAC,KAAK,CAAC,OAAO,EAAC,8CAA8C,EAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;gBAE1F,SAAS;gBACT,GAAG,CAAC,SAAS,CAAC;oBACZ,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,UAAU;oBAClC,IAAI,EAAE,MAAM;oBACZ,QAAQ,EAAE,IAAI;iBACf,CAAC,CAAA;aAEH;oBAAS;gBACR,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;gBACrB,gBAAgB,CAAC,KAAK,CAAC,CAAA;aACxB;QACH,CAAC,IAAA,CAAA;QAED;;WAEG;QACH,MAAM,kBAAkB,GAAG;YACzB,SAAS;YACT,MAAM,KAAK,GAAG,eAAe,EAAE,CAAA;YAC/B,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;YAC3C,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAA;YAEnC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAC,8CAA8C,EAAC,qBAAqB,EAAE,OAAO,CAAC,CAAA;YAE9F,IAAI;gBACF,UAAU;gBACV,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAA;gBACpC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,GAAG,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAA;gBAEnE,IAAI,WAAW,EAAE;oBACf,cAAc;oBACd,GAAG,CAAC,KAAK,CAAC,KAAK,EAAC,8CAA8C,EAAC,kBAAkB,EAAE,WAAW,CAAC,CAAA;oBAE/F,IAAI,KAAK,IAAI,KAAK,KAAK,MAAM,EAAE;wBAC7B,YAAY;wBACZ,GAAG,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,CAAC,CAAA;wBAC1C,GAAG,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAA;qBAC5C;oBAED,qBAAqB;oBACrB,IAAI,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;wBAC/B,MAAM,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,CAAA;qBACzC;yBAAM;wBACL,MAAM,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,IAAI,WAAW,EAAE,EAAE,CAAC,CAAA;qBAC/C;iBAEF;qBAAM,IAAI,KAAK,IAAI,KAAK,KAAK,MAAM,EAAE;oBACpC,oBAAoB;oBACpB,GAAG,CAAC,KAAK,CAAC,KAAK,EAAC,8CAA8C,EAAC,mBAAmB,EAAE,KAAK,CAAC,CAAA;oBAE1F,GAAG,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,CAAC,CAAA;oBAC1C,GAAG,CAAC,cAAc,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAA;oBAE3C,MAAM,GAAG,CAAC,QAAQ,CAAC;wBACjB,GAAG,EAAE,gCAAgC,KAAK,cAAc;qBACzD,CAAC,CAAA;iBAEH;qBAAM,IAAI,KAAK,KAAK,MAAM,EAAE;oBAC3B,eAAe;oBACf,GAAG,CAAC,KAAK,CAAC,KAAK,EAAC,8CAA8C,EAAC,kBAAkB,CAAC,CAAA;oBAElF,MAAM,GAAG,CAAC,QAAQ,CAAC;wBACjB,GAAG,EAAE,gDAAgD;qBACtD,CAAC,CAAA;iBAEH;qBAAM;oBACL,YAAY;oBACZ,GAAG,CAAC,KAAK,CAAC,KAAK,EAAC,8CAA8C,EAAC,oBAAoB,CAAC,CAAA;oBAEpF,MAAM,GAAG,CAAC,QAAQ,CAAC;wBACjB,GAAG,EAAE,+CAA+C;qBACrD,CAAC,CAAA;iBACH;aAEF;YAAC,OAAO,KAAU,EAAE;gBACnB,GAAG,CAAC,KAAK,CAAC,OAAO,EAAC,8CAA8C,EAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;gBAE1F,eAAe;gBACf,MAAM,GAAG,CAAC,QAAQ,CAAC;oBACjB,GAAG,EAAE,+CAA+C;iBACrD,CAAC,CAAA;aACH;QACH,CAAC,IAAA,CAAA;QAED,mDAAmD;QAEnD,OAAO;QACP,MAAM,iBAAiB,GAAG;YACxB,iBAAiB,EAAE,CAAC,OAAO,CAAC;gBAC1B,GAAG,CAAC,mBAAmB,EAAE,CAAA;YAC3B,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;QAED,UAAU;QACV,QAAQ,mBAAC;YACP,iBAAiB;YACjB,QAAQ;YACR,OAAO;YACP,iBAAiB;SAClB,EAAC,CAAA;QAEF,OAAO,CAAC,IAAI,OAAA,EAAE,MAAM,OAAA;YAClB,MAAM,YAAY,GAAG,EAAE,mBAAC;gBACxB,CAAC,EAAE,EAAE,mBAAC;oBACJ,IAAI,EAAE,QAAQ;iBACf,EAAC;gBACF,CAAC,EAAE,EAAE,mBAAC;oBACJ,IAAI,EAAE,MAAM;oBACZ,KAAK,EAAE,SAAS;iBACjB,EAAC;gBACF,CAAC,EAAE,EAAE,mBAAC;oBACJ,IAAI,EAAE,qBAAqB;iBAC5B,EAAC;gBACF,CAAC,EAAE,EAAE,mBAAC;oBACJ,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,SAAS;iBAChB,EAAC;gBACF,CAAC,EAAE,SAAS,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACxC,CAAC,EAAE,EAAE,CAAC,CAAA,MAAM,OAAA,OAAI,OAAA,SAAS,CAAC,KAAK,GAAG,QAAQ,EAA1B,CAA0B,CAAC;gBAC3C,CAAC,EAAE,SAAS,CAAC,KAAK,KAAK,QAAQ;aAChC,GAAE,SAAS,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,mBAAC;gBAChC,CAAC,EAAE,OAAO,CAAC,KAAK;gBAChB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA,MAAM,OAAA,OAAI,OAAA,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,EAApC,CAAoC,EAAE,eAAe,CAAC,CAAC;gBACxE,CAAC,EAAE,QAAQ,CAAC,KAAK;gBACjB,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;gBACtC,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACnD,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;gBACxB,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;aAC3C,EAAC,CAAC,mBAAC,EAAE,CAAA,oBAAE;gBACN,CAAC,EAAE,KAAK,CAAC,KAAK;aACf,GAAE,KAAK,CAAC,KAAK,CAAC,CAAC,mBAAC;gBACf,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;gBACrB,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;aACrB,EAAC,CAAC,mBAAC,EAAE,CAAA,oBAAE;gBACN,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC;aAChC,EAAC,CAAA;YACA,OAAO,YAAY,CAAA;QACrB,CAAC,CAAA;IACD,CAAC;CAEA,CAAC,CAAA\"}"}