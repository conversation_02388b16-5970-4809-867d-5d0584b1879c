<!--
  HomeDetails 组件 - UniApp-X版本
  显示设备详细信息，包括WiFi信息、设备信息和套餐信息
-->
<template>
  <view class="home-details">
    <!-- 折叠头部 -->
    <view class="details-header" @click="toggleDetails">
      <text class="details-title">{{ config.title || '设备详情' }}</text>
      <text class="toggle-icon" :class="{ 'expanded': isOpen }">{{ isOpen ? '▼' : '▶' }}</text>
    </view>

    <!-- 详情内容 -->
    <view v-show="isOpen" class="details-content">
      
      <!-- WiFi信息区域 -->
      <view v-if="config.showWifiInfo" class="info-section">
        <view class="section-title">
          <text class="title-icon">📶</text>
          <text class="title-text">WiFi信息</text>
        </view>
        <view class="info-list">
          <view class="info-item">
            <text class="info-label">WiFi名称</text>
            <text class="info-value">{{ deviceDetails.wifiName || '未连接' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">IP地址</text>
            <text class="info-value">{{ deviceDetails.ipAddress || '***********' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">信号强度</text>
            <text class="info-value">{{ getSignalText(deviceDetails.signalStrength) }}</text>
          </view>
        </view>
      </view>

      <!-- 设备信息区域 -->
      <view v-if="config.showDeviceInfo" class="info-section">
        <view class="section-title">
          <text class="title-icon">📱</text>
          <text class="title-text">设备信息</text>
        </view>
        <view class="info-list">
          <view class="info-item">
            <text class="info-label">设备号</text>
            <text class="info-value">{{ deviceDetails.deviceNo || 'N/A' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">IMEI</text>
            <text class="info-value">{{ deviceDetails.imei || 'N/A' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">设备型号</text>
            <text class="info-value">{{ deviceDetails.deviceModel || '智能路由器' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">网络类型</text>
            <text class="info-value">{{ getNetworkType(deviceDetails.networkType) }}</text>
          </view>
        </view>
      </view>

      <!-- 套餐信息区域 -->
      <view v-if="config.showPackageInfo" class="info-section">
        <view class="section-title">
          <text class="title-icon">📦</text>
          <text class="title-text">套餐信息</text>
        </view>
        <view class="info-list">
          <view class="info-item">
            <text class="info-label">当前套餐</text>
            <text class="info-value">{{ deviceDetails.packageName || '无限流量套餐' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">到期时间</text>
            <text class="info-value">{{ formatDate(deviceDetails.expireTime) }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">套餐状态</text>
            <text class="info-value status-text" :class="getStatusClass(deviceDetails.status)">
              {{ getStatusText(deviceDetails.status) }}
            </text>
          </view>
        </view>
      </view>

      <!-- 操作按钮区域 -->
      <view v-if="config.showActions" class="actions-section">
        <button 
          v-for="action in config.actions" 
          :key="action.id"
          class="action-btn"
          :class="action.type || 'default'"
          @click="handleAction(action)"
        >
          {{ action.text }}
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useGlobalData } from '../../hooks/device/useGlobalData'

// ==================== 类型定义 ====================

interface HomeDetailsConfig {
  title?: string
  defaultOpen?: boolean
  showWifiInfo?: boolean
  showDeviceInfo?: boolean
  showPackageInfo?: boolean
  showActions?: boolean
  actions?: Array<{
    id: string
    text: string
    type?: string
    action?: string
    params?: Record<string, any>
  }>
}

interface HomeDetailsData {
  wifiName?: string
  ipAddress?: string
  signalStrength?: number
  deviceNo?: string
  imei?: string
  deviceModel?: string
  networkType?: number
  packageName?: string
  expireTime?: string
  status?: number
  [key: string]: any
}

interface Props {
  /** 组件配置 */
  config?: HomeDetailsConfig
  /** 外部数据（主要用于设计器预览） */
  data?: HomeDetailsData
  /** 是否为设计器模式 */
  designMode?: boolean
}

// ==================== Props和默认值 ====================

const props = withDefaults(defineProps<Props>(), {
  designMode: false,
  config: () => ({
    title: '设备详情',
    defaultOpen: false,
    showWifiInfo: true,
    showDeviceInfo: true,
    showPackageInfo: true,
    showActions: true,
    actions: [
      { id: 'refresh', text: '刷新信息', type: 'primary', action: 'refresh' },
      { id: 'settings', text: '设备设置', type: 'default', action: 'navigate', params: { url: '/pages/modules/device/EditDevice/EditDevice' } }
    ]
  }),
  data: () => ({
    wifiName: 'ChinaNet-WiFi',
    ipAddress: '***********',
    signalStrength: 85,
    deviceNo: '863780070053924',
    imei: '863780070053924',
    deviceModel: '智能路由器X1',
    networkType: 4,
    packageName: '无限流量套餐',
    expireTime: '2024-12-31',
    status: 1
  })
})

// ==================== 事件定义 ====================

const emit = defineEmits<{
  click: [eventData: {
    elementType: 'action' | 'toggle'
    elementId: string
    elementData?: any
    componentType: string
  }]
}>()

// ==================== 状态管理 ====================

const { deviceData } = useGlobalData()

// 合并配置
const config = computed(() => ({
  title: '设备详情',
  defaultOpen: false,
  showWifiInfo: true,
  showDeviceInfo: true,
  showPackageInfo: true,
  showActions: true,
  actions: [
    { id: 'refresh', text: '刷新信息', type: 'primary', action: 'refresh' },
    { id: 'settings', text: '设备设置', type: 'default', action: 'navigate', params: { url: '/pages/modules/device/EditDevice/EditDevice' } }
  ],
  ...props.config
}))

const isOpen = ref(config.value.defaultOpen || false)

// 设备详情数据
const deviceDetails = computed(() => {
  // 设计器模式：使用演示数据
  if (props.designMode) {
    return props.data || {
      wifiName: 'ChinaNet-WiFi',
      ipAddress: '***********',
      signalStrength: 85,
      deviceNo: '863780070053924',
      imei: '863780070053924',
      deviceModel: '智能路由器X1',
      networkType: 4,
      packageName: '无限流量套餐',
      expireTime: '2024-12-31',
      status: 1
    }
  }

  // 运行时：优先使用全局数据
  if (deviceData.value.details && Object.keys(deviceData.value.details).length > 0) {
    return deviceData.value.details
  }

  // 兜底：使用外部传入数据或默认数据
  return props.data || {
    wifiName: 'ChinaNet-WiFi',
    ipAddress: '***********',
    signalStrength: 85,
    deviceNo: '863780070053924',
    imei: '863780070053924',
    deviceModel: '智能路由器X1',
    networkType: 4,
    packageName: '无限流量套餐',
    expireTime: '2024-12-31',
    status: 1
  }
})

// ==================== 工具函数 ====================

// 获取信号强度文本
const getSignalText = (strength: number | undefined): string => {
  if (!strength) return '未知'
  if (strength >= 80) return '强'
  if (strength >= 60) return '良好'
  if (strength >= 40) return '一般'
  return '弱'
}

// 获取网络类型
const getNetworkType = (type: number | undefined): string => {
  switch (type) {
    case 4: return '4G'
    case 5: return '5G'
    case 3: return '3G'
    case 2: return '2G'
    default: return '未知'
  }
}

// 获取状态文本
const getStatusText = (status: number | undefined): string => {
  switch (status) {
    case 1: return '正常'
    case 2: return '欠费'
    case 3: return '停机'
    case 4: return '异常'
    default: return '未知'
  }
}

// 获取状态样式类
const getStatusClass = (status: number | undefined): string => {
  switch (status) {
    case 1: return 'status-normal'
    case 2: return 'status-arrears'
    case 3: return 'status-suspended'
    case 4: return 'status-error'
    default: return 'status-unknown'
  }
}

// 格式化日期
const formatDate = (dateStr: string | undefined): string => {
  if (!dateStr) return '未设置'
  try {
    const date = new Date(dateStr)
    return date.toLocaleDateString('zh-CN')
  } catch {
    return dateStr
  }
}

// ==================== 事件处理 ====================

// 切换展开/收起
const toggleDetails = () => {
  isOpen.value = !isOpen.value
  
  emit('click', {
    elementType: 'toggle',
    elementId: 'toggle-details',
    elementData: { isOpen: isOpen.value },
    componentType: 'HomeDetails'
  })
}

// 处理操作按钮点击
const handleAction = (action: any) => {
  emit('click', {
    elementType: 'action',
    elementId: action.id,
    elementData: {
      action: action.action,
      params: action.params
    },
    componentType: 'HomeDetails'
  })
}

// ==================== 数据监听 ====================

watch(() => props.data, (newData) => {
  console.log('🔄 [HomeDetails] 外部数据变化', newData)
}, { deep: true, immediate: true })

watch(() => config.value, (newConfig) => {
  if (newConfig && newConfig.defaultOpen !== undefined) {
    isOpen.value = newConfig.defaultOpen
  }
}, { deep: true, immediate: true })
</script>

<style lang="scss" scoped>
.home-details {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin: 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 头部 */
.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  background-color: #fafafa;
  border-bottom: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.details-header:active {
  background-color: #f0f0f0;
}

.details-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.toggle-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.toggle-icon.expanded {
  transform: rotate(90deg);
}

/* 内容区域 */
.details-content {
  padding: 32rpx;
}

/* 信息区域 */
.info-section {
  margin-bottom: 40rpx;
}

.info-section:last-of-type {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 信息列表 */
.info-list {
  background-color: #fafafa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 26rpx;
  color: #666;
}

.info-value {
  font-size: 26rpx;
  color: #333;
}

/* 状态样式 */
.status-text {
  font-weight: bold;
}

.status-normal {
  color: #52c41a;
}

.status-arrears {
  color: #fa8c16;
}

.status-suspended {
  color: #ff4d4f;
}

.status-error {
  color: #8c8c8c;
}

.status-unknown {
  color: #999;
}

/* 操作按钮区域 */
.actions-section {
  margin-top: 32rpx;
  display: flex;
  gap: 24rpx;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background-color: #1890ff;
  color: #ffffff;
}

.action-btn.primary:active {
  background-color: #096dd9;
}

.action-btn.default {
  background-color: #f5f5f5;
  color: #333;
  border: 2rpx solid #d9d9d9;
}

.action-btn.default:active {
  background-color: #e6f7ff;
  border-color: #1890ff;
}
</style>