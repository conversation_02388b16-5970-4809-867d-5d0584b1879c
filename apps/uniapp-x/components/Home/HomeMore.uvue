<!--
  HomeMore 组件 - UniApp-X版本
  显示快捷功能菜单网格
-->
<template>
  <view class="home-more">
    <view v-if="config.showTitle" class="home-more-title">
      <text class="title-text">{{ config.title || '更多功能' }}</text>
    </view>

    <view class="home-more-grid" :style="gridStyle">
      <view 
        v-for="item in menuItems" 
        :key="item.id"
        class="menu-item"
        :class="{ 'menu-item-disabled': item.disabled }"
        @click="handleMenuClick(item)"
      >
        <view class="menu-icon">
          <text class="icon-text">{{ item.icon || '📱' }}</text>
        </view>
        <text class="menu-name">{{ item.name }}</text>
        <text v-if="item.badge" class="menu-badge">{{ item.badge }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref, watch, onBeforeUnmount } from 'vue'

// ==================== 类型定义 ====================

interface HomeMoreMenuItem {
  id: string
  name: string
  icon?: string
  color?: string
  disabled?: boolean
  visible?: boolean
  order?: number
  action?: string
  params?: Record<string, any>
  badge?: string | number
}

interface HomeMoreConfig {
  showTitle?: boolean
  title?: string
  columns?: number
  menuItems?: HomeMoreMenuItem[]
  styleConfig?: {
    gap?: string
    itemHeight?: string
    iconSize?: string
    fontSize?: string
  }
}

interface HomeMoreData {
  menuItems?: HomeMoreMenuItem[]
}

interface Props {
  /** 组件配置 */
  config?: HomeMoreConfig
  /** 外部数据（主要用于设计器预览） */
  data?: HomeMoreData
  /** 是否为设计器模式 */
  designMode?: boolean
}

// ==================== Props和默认值 ====================

const props = withDefaults(defineProps<Props>(), {
  designMode: false,
  config: () => ({
    showTitle: true,
    title: '更多功能',
    columns: 4,
    menuItems: [
      { id: '1', name: '套餐订购', icon: '📦', order: 1, action: 'navigate', params: { url: '/pages/modules/device/PackageList/PackageList' } },
      { id: '2', name: '余额充值', icon: '💰', order: 2, action: 'navigate', params: { url: '/pages/modules/device/BalancePayment/BalancePayment' } },
      { id: '3', name: '使用明细', icon: '📊', order: 3, action: 'navigate', params: { url: '/pages/modules/device/BalanceList/BalanceList' } },
      { id: '4', name: '设备设置', icon: '⚙️', order: 4, action: 'navigate', params: { url: '/pages/modules/device/EditDevice/EditDevice' } },
      { id: '5', name: '实名认证', icon: '🆔', order: 5, action: 'navigate', params: { url: '/pages/modules/device/RealName/RealName' } },
      { id: '6', name: '扫码支付', icon: '📱', order: 6, action: 'navigate', params: { url: '/pages/modules/device/QrCodePayment/QrCodePayment' } },
      { id: '7', name: '修改密码', icon: '🔒', order: 7, action: 'navigate', params: { url: '/pages/modules/device/EditPassword/EditPassword' } },
      { id: '8', name: '设备通知', icon: '🔔', order: 8, action: 'navigate', params: { url: '/pages/modules/device/DeviceNotice/DeviceNotice' } }
    ],
    styleConfig: {
      gap: '32rpx',
      itemHeight: '180rpx',
      iconSize: '64rpx',
      fontSize: '24rpx'
    }
  }),
  data: () => ({
    menuItems: []
  })
})

// ==================== 事件定义 ====================

const emit = defineEmits<{
  click: [eventData: {
    elementType: 'menu' | 'action'
    elementId: string
    elementData?: any
    componentType: string
  }]
}>()

// ==================== 状态管理 ====================

// 菜单项列表
const menuItems = ref<HomeMoreMenuItem[]>([])
let initializationTimer: ReturnType<typeof setTimeout> | null = null

// ==================== 计算属性 ====================

// 合并配置
const config = computed(() => ({
  showTitle: true,
  title: '更多功能',
  columns: 4,
  menuItems: [
    { id: '1', name: '套餐订购', icon: '📦', order: 1, action: 'navigate', params: { url: '/pages/modules/device/PackageList/PackageList' } },
    { id: '2', name: '余额充值', icon: '💰', order: 2, action: 'navigate', params: { url: '/pages/modules/device/BalancePayment/BalancePayment' } },
    { id: '3', name: '使用明细', icon: '📊', order: 3, action: 'navigate', params: { url: '/pages/modules/device/BalanceList/BalanceList' } },
    { id: '4', name: '设备设置', icon: '⚙️', order: 4, action: 'navigate', params: { url: '/pages/modules/device/EditDevice/EditDevice' } },
    { id: '5', name: '实名认证', icon: '🆔', order: 5, action: 'navigate', params: { url: '/pages/modules/device/RealName/RealName' } },
    { id: '6', name: '扫码支付', icon: '📱', order: 6, action: 'navigate', params: { url: '/pages/modules/device/QrCodePayment/QrCodePayment' } },
    { id: '7', name: '修改密码', icon: '🔒', order: 7, action: 'navigate', params: { url: '/pages/modules/device/EditPassword/EditPassword' } },
    { id: '8', name: '设备通知', icon: '🔔', order: 8, action: 'navigate', params: { url: '/pages/modules/device/DeviceNotice/DeviceNotice' } }
  ],
  styleConfig: {
    gap: '32rpx',
    itemHeight: '180rpx',
    iconSize: '64rpx',
    fontSize: '24rpx'
  },
  ...props.config
}))

// 网格样式
const gridStyle = computed(() => {
  const columns = config.value.columns || 4
  const gap = config.value.styleConfig?.gap || '32rpx'
  
  return {
    display: 'grid',
    gridTemplateColumns: `repeat(${columns}, 1fr)`,
    gap: gap
  }
})

// ==================== 数据管理 ====================

// 初始化菜单项（防抖处理）
function initializeMenuItems() {
  // 清除之前的定时器
  if (initializationTimer) {
    clearTimeout(initializationTimer)
  }
  
  // 延迟执行，避免重复调用
  initializationTimer = setTimeout(() => {
    // 优先使用外部数据，然后是配置数据
    const sourceItems = props.data?.menuItems || config.value.menuItems || []

    console.log('🔄 [HomeMore] 初始化菜单项', {
      原始数据: sourceItems,
      排序前: sourceItems.map(item => ({ id: item.id, name: item.name, order: item.order }))
    })

    // 过滤可见的菜单项并按order排序
    menuItems.value = sourceItems
      .filter((item: HomeMoreMenuItem) => item.visible !== false)
      .sort((a: HomeMoreMenuItem, b: HomeMoreMenuItem) => (a.order || 0) - (b.order || 0))

    console.log('✅ [HomeMore] 菜单项排序完成', {
      排序后: menuItems.value.map(item => ({ id: item.id, name: item.name, order: item.order }))
    })
  }, 10) // 10ms 防抖
}

// ==================== 事件处理 ====================

// 菜单项点击处理
function handleMenuClick(item: HomeMoreMenuItem) {
  if (item.disabled) {
    console.warn('菜单项已禁用:', item.name)
    return
  }

  console.log('🔄 [HomeMore] 菜单项点击:', item)

  // 发送统一点击事件
  emit('click', {
    elementType: 'menu',
    elementId: item.id,
    elementData: {
      menuItem: item,
      action: item.action,
      params: item.params
    },
    componentType: 'HomeMore'
  })
}

// ==================== 数据监听 ====================

// 监听数据变化，统一处理
watch(() => [props.data, config.value.menuItems], ([newData, newMenuItems]) => {
  console.log('🔄 [HomeMore] 监听到数据变化', {
    新外部数据: newData,
    新配置菜单: newMenuItems?.length || 0
  })
  initializeMenuItems()
}, { deep: true, immediate: true })

// ==================== 生命周期 ====================

onBeforeUnmount(() => {
  if (initializationTimer) {
    clearTimeout(initializationTimer)
    initializationTimer = null
  }
})
</script>

<style lang="scss" scoped>
.home-more {
  box-sizing: border-box;
  padding: 32rpx;
  background-color: #ffffff;
}

/* 标题区域 */
.home-more-title {
  margin-bottom: 32rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 网格容器 */
.home-more-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
}

/* 菜单项 */
.menu-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 180rpx;
  padding: 24rpx 16rpx;
  border-radius: 16rpx;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.menu-item:active {
  background-color: #e6f7ff;
  transform: scale(0.95);
}

.menu-item-disabled {
  opacity: 0.5;
  background-color: #f5f5f5;
}

.menu-item-disabled:active {
  transform: none;
  background-color: #f5f5f5;
}

/* 菜单图标 */
.menu-icon {
  margin-bottom: 16rpx;
}

.icon-text {
  font-size: 64rpx;
  line-height: 1;
}

/* 菜单名称 */
.menu-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.menu-item-disabled .menu-name {
  color: #999;
}

/* 徽章 */
.menu-badge {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  background-color: #ff4d4f;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .home-more-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media screen and (max-width: 600rpx) {
  .home-more-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
  }
  
  .menu-item {
    height: 160rpx;
    padding: 20rpx 12rpx;
  }
  
  .icon-text {
    font-size: 56rpx;
  }
  
  .menu-name {
    font-size: 22rpx;
  }
}
</style>