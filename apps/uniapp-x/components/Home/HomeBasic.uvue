<!--
  HomeBasic 组件 - UniApp-X版本
  显示设备基础信息，包括网络状态、流量使用情况和账户余额
-->
<template>
  <view class="home-basic">
    <!-- 网络状态头部 -->
    <view v-if="config.showNetworkStatus" class="home-basic-head">
      <view class="home-basic-head-left">
        <text class="home-basic-head-icon">📶</text>
        <text class="home-basic-head-title">{{ config.networkTitle }}</text>
        <view class="status-tag" :class="currentStatus.class">
          <text class="status-text">{{ currentStatus.label }}</text>
        </view>
      </view>
      <view class="home-basic-head-right" @click="handleMore">
        <text class="more-icon">⋯</text>
      </view>
    </view>

    <!-- 流量进度显示 -->
    <view v-if="config.showFlowProgress" class="home-basic-flow">
      <view class="flow-labels">
        <text class="flow-label-left">剩余流量</text>
        <text class="flow-label-right">套餐总量</text>
      </view>

      <view class="flow-data">
        <text class="flow-remain">{{ formatFlow(deviceDetails.vResidueFlow || 0) }}</text>
        <view class="flow-total">
          <text class="flow-used">{{ formatFlow(deviceDetails.vUseFlow || 0) }}</text>
          <text class="flow-separator"> / </text>
          <text class="flow-max">{{ formatFlow(deviceDetails.vTotalFlow || 0) }}</text>
        </view>
      </view>

      <!-- 进度条 -->
      <view class="flow-progress">
        <view class="flow-progress-bar" :style="{ width: progress }"></view>
      </view>

      <view class="flow-bottom">
        <text class="flow-used-percent">已用{{ progress }}</text>
        <text class="flow-renew" @click="handleRenew">{{ config.renewText }}</text>
      </view>
    </view>

    <!-- 账户余额 -->
    <view v-if="config.showBalance" class="home-basic-balance">
      <view class="balance-head">
        <text class="balance-label">{{ config.balanceLabel }}</text>
        <view class="balance-recharge" @click="handleRecharge">
          <text class="recharge-text">{{ config.rechargeText }}</text>
          <text class="recharge-icon">💰</text>
        </view>
      </view>

      <view class="balance-bottom">
        <view class="balance-money">
          <text class="currency">￥</text>
          <text class="amount">{{ deviceDetails.balance || '0.00' }}</text>
        </view>
        <text class="balance-tip">可用于购买套餐</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { useGlobalData } from '../../hooks/device/useGlobalData'

// ==================== 类型定义 ====================

interface HomeBasicConfig {
  showNetworkStatus?: boolean
  showFlowProgress?: boolean
  showBalance?: boolean
  networkTitle?: string
  renewText?: string
  balanceLabel?: string
  rechargeText?: string
  clickableAreas?: Array<{
    position: string
    enabled: boolean
    elementType: string
    eventId: string
    defaultId: string
  }>
}

interface HomeBasicData {
  status?: number
  packageName?: string
  vUseFlow?: number
  vTotalFlow?: number
  balance?: number | string
  vResidueFlow?: number
}

interface Props {
  /** 组件配置 */
  config?: HomeBasicConfig
  /** 外部数据（主要用于设计器预览） */
  data?: HomeBasicData
  /** 是否为设计器模式 */
  designMode?: boolean
}

// ==================== Props和默认值 ====================

const props = withDefaults(defineProps<Props>(), {
  designMode: false,
  config: () => ({
    showNetworkStatus: true,
    showFlowProgress: true,
    showBalance: true,
    networkTitle: '网络状态',
    renewText: '续费',
    balanceLabel: '账户余额',
    rechargeText: '充值',
    clickableAreas: [
      {
        position: 'top-right',
        enabled: true,
        elementType: 'button',
        eventId: 'more',
        defaultId: 'home-basic-more'
      },
      {
        position: 'flow-area',
        enabled: true,
        elementType: 'action',
        eventId: 'renew',
        defaultId: 'home-basic-renew'
      },
      {
        position: 'balance-area',
        enabled: true,
        elementType: 'action',
        eventId: 'recharge',
        defaultId: 'home-basic-recharge'
      }
    ]
  }),
  data: () => ({
    status: 1,
    packageName: '无限流量套餐',
    vUseFlow: 1024 * 1024 * 512, // 512MB
    vTotalFlow: 1024 * 1024 * 1024 * 10, // 10GB
    balance: '88.88',
    vResidueFlow: 1024 * 1024 * 1024 * 9.5 // 9.5GB
  })
})

// ==================== 事件定义 ====================

const emit = defineEmits<{
  click: [eventData: {
    elementType: 'button' | 'action'
    elementId: string
    elementData?: any
    componentType: string
  }]
}>()

// ==================== 状态管理 ====================

const { deviceData } = useGlobalData()

// 合并配置
const config = computed(() => ({
  showNetworkStatus: true,
  showFlowProgress: true,
  showBalance: true,
  networkTitle: '网络状态',
  renewText: '续费',
  balanceLabel: '账户余额',
  rechargeText: '充值',
  ...props.config
}))

// 设备状态配置
const DEVICE_STATUS_CONFIG = {
  1: { label: '正常', class: 'status-normal' },
  2: { label: '欠费', class: 'status-arrears' },
  3: { label: '停机', class: 'status-suspended' },
  4: { label: '异常', class: 'status-error' }
}

// 设备详情数据
const deviceDetails = computed(() => {
  // 设计器模式：使用演示数据
  if (props.designMode) {
    return props.data || {
      status: 1,
      packageName: '无限流量套餐',
      vUseFlow: 1024 * 1024 * 512,
      vTotalFlow: 1024 * 1024 * 1024 * 10,
      balance: '88.88',
      vResidueFlow: 1024 * 1024 * 1024 * 9.5
    }
  }

  // 运行时：优先使用全局数据
  if (deviceData.value.details && Object.keys(deviceData.value.details).length > 0) {
    const globalData = deviceData.value.details
    
    return {
      status: globalData.status,
      packageName: globalData.packageName,
      vUseFlow: globalData.vUseFlow,
      vTotalFlow: globalData.vTotalFlow,
      balance: globalData.balance,
      vResidueFlow: globalData.vResidueFlow || (globalData.vTotalFlow - globalData.vUseFlow)
    }
  }

  // 兜底：使用外部传入数据或默认数据
  return props.data || {
    status: 1,
    packageName: '无限流量套餐',
    vUseFlow: 1024 * 1024 * 512,
    vTotalFlow: 1024 * 1024 * 1024 * 10,
    balance: '88.88',
    vResidueFlow: 1024 * 1024 * 1024 * 9.5
  }
})

// ==================== 计算属性 ====================

// 当前设备状态
const currentStatus = computed(() => {
  const status = deviceDetails.value?.status || 1
  return DEVICE_STATUS_CONFIG[status as keyof typeof DEVICE_STATUS_CONFIG] || DEVICE_STATUS_CONFIG[1]
})

// 流量进度百分比
const progress = computed(() => {
  const { vResidueFlow, vTotalFlow } = deviceDetails.value
  if (!vTotalFlow || !vResidueFlow) return '0%'

  const result = (vResidueFlow / vTotalFlow) * 100
  return isNaN(result) ? '0%' : result.toFixed(2) + '%'
})

// ==================== 工具函数 ====================

// 格式化流量显示
const formatFlow = (bytes: number): string => {
  if (!bytes || bytes === 0) return '0MB'
  
  const gb = bytes / (1024 * 1024 * 1024)
  const mb = bytes / (1024 * 1024)
  
  if (gb >= 1) {
    return gb.toFixed(2) + 'GB'
  } else if (mb >= 1) {
    return mb.toFixed(0) + 'MB'
  } else {
    return (bytes / 1024).toFixed(0) + 'KB'
  }
}

// ==================== 事件处理 ====================

// 获取特定位置的区域配置
function getAreaConfig(position: string) {
  const areas = config.value.clickableAreas || []
  return areas.find(area => area.position === position && area.enabled)
}

// 统一的区域点击处理器
function handleAreaClick(position: string) {
  const areaConfig = getAreaConfig(position)
  if (!areaConfig) {
    console.warn(`未找到位置为 ${position} 的区域配置`)
    return
  }

  emit('click', {
    elementType: areaConfig.elementType as 'button' | 'action',
    elementId: areaConfig.eventId || areaConfig.defaultId,
    elementData: areaConfig,
    componentType: 'HomeBasic'
  })
}

// 具体的按钮处理函数
function handleMore() {
  handleAreaClick('top-right')
}

function handleRenew() {
  handleAreaClick('flow-area')
}

function handleRecharge() {
  handleAreaClick('balance-area')
}

// ==================== 数据监听 ====================

watch(() => props.data, (newData) => {
  console.log('🔄 [HomeBasic] 外部数据变化', newData)
}, { deep: true, immediate: true })
</script>

<style lang="scss" scoped>
.home-basic {
  box-sizing: border-box;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
  background-color: #ffffff;
}

/* 头部网络状态 */
.home-basic-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 32rpx;
}

.home-basic-head-left {
  display: flex;
  align-items: center;
}

.home-basic-head-icon {
  color: #1890ff;
  font-size: 36rpx;
  margin-right: 16rpx;
}

.home-basic-head-title {
  margin-right: 16rpx;
  font-size: 28rpx;
  color: #333;
}

.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
}

.status-normal {
  background-color: #f6ffed;
  border: 2rpx solid #b7eb8f;
}

.status-normal .status-text {
  color: #52c41a;
}

.status-arrears {
  background-color: #fff2e8;
  border: 2rpx solid #ffbb96;
}

.status-arrears .status-text {
  color: #fa8c16;
}

.status-suspended {
  background-color: #fff1f0;
  border: 2rpx solid #ffccc7;
}

.status-suspended .status-text {
  color: #ff4d4f;
}

.status-error {
  background-color: #f0f0f0;
  border: 2rpx solid #d9d9d9;
}

.status-error .status-text {
  color: #8c8c8c;
}

.home-basic-head-right {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 52rpx;
  height: 52rpx;
  background-color: #f5f5f5;
  border-radius: 50%;
  color: #999;
  transition: all 0.3s ease;
}

.home-basic-head-right:active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.more-icon {
  font-size: 32rpx;
  font-weight: bold;
}

/* 流量进度区域 */
.home-basic-flow {
  margin-top: 32rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  background-color: #fafafa;
}

.flow-labels {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #666;
  font-size: 24rpx;
}

.flow-data {
  margin: 16rpx 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flow-remain {
  font-weight: bold;
  color: #333;
  font-size: 32rpx;
}

.flow-total {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.flow-used {
  color: #1890ff;
}

.flow-separator {
  color: #666;
  margin: 0 4rpx;
}

.flow-max {
  color: #666;
}

.flow-progress {
  height: 16rpx;
  border-radius: 8rpx;
  background-color: #e5e7eb;
  position: relative;
  overflow: hidden;
}

.flow-progress-bar {
  height: 100%;
  background-color: #1890ff;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.flow-bottom {
  margin-top: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 22rpx;
}

.flow-used-percent {
  color: #666;
}

.flow-renew {
  color: #1890ff;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.flow-renew:active {
  background-color: rgba(24, 144, 255, 0.1);
}

/* 账户余额区域 */
.home-basic-balance {
  margin-top: 32rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  background-color: #fafafa;
}

.balance-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-label {
  font-size: 24rpx;
  color: #666;
}

.balance-recharge {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: #1890ff;
  padding: 8rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.balance-recharge:active {
  background-color: rgba(24, 144, 255, 0.1);
}

.recharge-text {
  margin-right: 4rpx;
}

.recharge-icon {
  font-size: 20rpx;
}

.balance-bottom {
  margin-top: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-money {
  display: flex;
  align-items: baseline;
}

.currency {
  font-size: 24rpx;
  color: #333;
  margin-right: 4rpx;
}

.amount {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.balance-tip {
  font-size: 22rpx;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  white-space: nowrap;
}
</style>