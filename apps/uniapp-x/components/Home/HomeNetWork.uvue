<!--
  HomeNetWork 组件 - UniApp-X版本
  显示网络连接状态和网络质量信息
-->
<template>
  <view class="home-network">
    <!-- 网络状态头部 -->
    <view class="network-header">
      <view class="network-title">
        <text class="title-icon">🌐</text>
        <text class="title-text">{{ config.title || '网络状态' }}</text>
      </view>
      <view class="network-status" :class="getNetworkStatusClass()">
        <text class="status-dot"></text>
        <text class="status-text">{{ getNetworkStatusText() }}</text>
      </view>
    </view>

    <!-- 网络信息卡片 -->
    <view class="network-cards">
      
      <!-- 连接状态卡片 -->
      <view v-if="config.showConnectionInfo" class="network-card">
        <view class="card-header">
          <text class="card-icon">📶</text>
          <text class="card-title">连接状态</text>
        </view>
        <view class="card-content">
          <view class="connection-info">
            <text class="connection-type">{{ getConnectionType() }}</text>
            <text class="connection-strength">信号强度: {{ getSignalStrength() }}</text>
          </view>
          <view class="connection-speed">
            <view class="speed-item">
              <text class="speed-label">下载</text>
              <text class="speed-value">{{ formatSpeed(networkData.downloadSpeed) }}</text>
            </view>
            <view class="speed-item">
              <text class="speed-label">上传</text>
              <text class="speed-value">{{ formatSpeed(networkData.uploadSpeed) }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 网络质量卡片 -->
      <view v-if="config.showQualityInfo" class="network-card">
        <view class="card-header">
          <text class="card-icon">📊</text>
          <text class="card-title">网络质量</text>
        </view>
        <view class="card-content">
          <view class="quality-metrics">
            <view class="metric-item">
              <text class="metric-label">延迟</text>
              <text class="metric-value" :class="getPingClass()">{{ networkData.ping || 0 }}ms</text>
            </view>
            <view class="metric-item">
              <text class="metric-label">丢包率</text>
              <text class="metric-value" :class="getPacketLossClass()">{{ networkData.packetLoss || 0 }}%</text>
            </view>
            <view class="metric-item">
              <text class="metric-label">稳定性</text>
              <text class="metric-value" :class="getStabilityClass()">{{ getStabilityText() }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 流量统计卡片 -->
      <view v-if="config.showTrafficInfo" class="network-card">
        <view class="card-header">
          <text class="card-icon">📈</text>
          <text class="card-title">流量统计</text>
        </view>
        <view class="card-content">
          <view class="traffic-stats">
            <view class="traffic-item">
              <text class="traffic-label">今日已用</text>
              <text class="traffic-value">{{ formatTraffic(networkData.todayUsed) }}</text>
            </view>
            <view class="traffic-item">
              <text class="traffic-label">本月已用</text>
              <text class="traffic-value">{{ formatTraffic(networkData.monthUsed) }}</text>
            </view>
          </view>
          <view class="traffic-chart">
            <view class="chart-bar">
              <view 
                class="chart-progress" 
                :style="{ width: getTrafficPercent() }"
              ></view>
            </view>
            <text class="chart-text">{{ getTrafficPercent() }} 流量使用率</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view v-if="config.showActions" class="network-actions">
      <button 
        v-for="action in config.actions" 
        :key="action.id"
        class="action-btn"
        :class="action.type || 'default'"
        @click="handleAction(action)"
      >
        <text class="btn-icon">{{ action.icon || '⚡' }}</text>
        <text class="btn-text">{{ action.text }}</text>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, watch, ref, onMounted, onUnmounted } from 'vue'
import { useGlobalData } from '../../hooks/device/useGlobalData'

// ==================== 类型定义 ====================

interface HomeNetWorkConfig {
  title?: string
  showConnectionInfo?: boolean
  showQualityInfo?: boolean
  showTrafficInfo?: boolean
  showActions?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
  actions?: Array<{
    id: string
    text: string
    icon?: string
    type?: string
    action?: string
    params?: Record<string, any>
  }>
}

interface HomeNetWorkData {
  isConnected?: boolean
  connectionType?: string
  signalStrength?: number
  downloadSpeed?: number
  uploadSpeed?: number
  ping?: number
  packetLoss?: number
  stability?: number
  todayUsed?: number
  monthUsed?: number
  totalQuota?: number
  [key: string]: any
}

interface Props {
  /** 组件配置 */
  config?: HomeNetWorkConfig
  /** 外部数据（主要用于设计器预览） */
  data?: HomeNetWorkData
  /** 是否为设计器模式 */
  designMode?: boolean
}

// ==================== Props和默认值 ====================

const props = withDefaults(defineProps<Props>(), {
  designMode: false,
  config: () => ({
    title: '网络状态',
    showConnectionInfo: true,
    showQualityInfo: true,
    showTrafficInfo: true,
    showActions: true,
    autoRefresh: true,
    refreshInterval: 30000,
    actions: [
      { id: 'speed-test', text: '测速', icon: '🚀', type: 'primary', action: 'speed-test' },
      { id: 'refresh', text: '刷新', icon: '🔄', type: 'default', action: 'refresh' }
    ]
  }),
  data: () => ({
    isConnected: true,
    connectionType: '4G',
    signalStrength: 85,
    downloadSpeed: 50.5,
    uploadSpeed: 20.3,
    ping: 25,
    packetLoss: 0.5,
    stability: 95,
    todayUsed: 1024 * 1024 * 512,
    monthUsed: 1024 * 1024 * 1024 * 5.2,
    totalQuota: 1024 * 1024 * 1024 * 10
  })
})

// ==================== 事件定义 ====================

const emit = defineEmits<{
  click: [eventData: {
    elementType: 'action' | 'refresh'
    elementId: string
    elementData?: any
    componentType: string
  }]
}>()

// ==================== 状态管理 ====================

const { deviceData } = useGlobalData()

// 合并配置
const config = computed(() => ({
  title: '网络状态',
  showConnectionInfo: true,
  showQualityInfo: true,
  showTrafficInfo: true,
  showActions: true,
  autoRefresh: true,
  refreshInterval: 30000,
  actions: [
    { id: 'speed-test', text: '测速', icon: '🚀', type: 'primary', action: 'speed-test' },
    { id: 'refresh', text: '刷新', icon: '🔄', type: 'default', action: 'refresh' }
  ],
  ...props.config
}))

let refreshTimer: ReturnType<typeof setTimeout> | null = null

// 网络数据
const networkData = computed(() => {
  // 设计器模式：使用演示数据
  if (props.designMode) {
    return props.data || {
      isConnected: true,
      connectionType: '4G',
      signalStrength: 85,
      downloadSpeed: 50.5,
      uploadSpeed: 20.3,
      ping: 25,
      packetLoss: 0.5,
      stability: 95,
      todayUsed: 1024 * 1024 * 512,
      monthUsed: 1024 * 1024 * 1024 * 5.2,
      totalQuota: 1024 * 1024 * 1024 * 10
    }
  }

  // 运行时：优先使用全局数据
  if (deviceData.value.details && Object.keys(deviceData.value.details).length > 0) {
    const globalData = deviceData.value.details
    return {
      isConnected: globalData.isConnected ?? true,
      connectionType: globalData.connectionType || '4G',
      signalStrength: globalData.signalStrength || 85,
      downloadSpeed: globalData.downloadSpeed || 50.5,
      uploadSpeed: globalData.uploadSpeed || 20.3,
      ping: globalData.ping || 25,
      packetLoss: globalData.packetLoss || 0.5,
      stability: globalData.stability || 95,
      todayUsed: globalData.todayUsed || 1024 * 1024 * 512,
      monthUsed: globalData.monthUsed || 1024 * 1024 * 1024 * 5.2,
      totalQuota: globalData.totalQuota || 1024 * 1024 * 1024 * 10
    }
  }

  // 兜底：使用外部传入数据或默认数据
  return props.data || {
    isConnected: true,
    connectionType: '4G',
    signalStrength: 85,
    downloadSpeed: 50.5,
    uploadSpeed: 20.3,
    ping: 25,
    packetLoss: 0.5,
    stability: 95,
    todayUsed: 1024 * 1024 * 512,
    monthUsed: 1024 * 1024 * 1024 * 5.2,
    totalQuota: 1024 * 1024 * 1024 * 10
  }
})

// ==================== 工具函数 ====================

// 获取网络状态类
const getNetworkStatusClass = (): string => {
  return networkData.value.isConnected ? 'status-connected' : 'status-disconnected'
}

// 获取网络状态文本
const getNetworkStatusText = (): string => {
  return networkData.value.isConnected ? '已连接' : '未连接'
}

// 获取连接类型
const getConnectionType = (): string => {
  return networkData.value.connectionType || '未知'
}

// 获取信号强度
const getSignalStrength = (): string => {
  const strength = networkData.value.signalStrength || 0
  if (strength >= 80) return '强'
  if (strength >= 60) return '良好'
  if (strength >= 40) return '一般'
  return '弱'
}

// 格式化速度
const formatSpeed = (speed: number | undefined): string => {
  if (!speed) return '0 Mbps'
  return `${speed.toFixed(1)} Mbps`
}

// 获取延迟样式类
const getPingClass = (): string => {
  const ping = networkData.value.ping || 0
  if (ping <= 30) return 'metric-excellent'
  if (ping <= 60) return 'metric-good'
  if (ping <= 100) return 'metric-fair'
  return 'metric-poor'
}

// 获取丢包率样式类
const getPacketLossClass = (): string => {
  const loss = networkData.value.packetLoss || 0
  if (loss <= 1) return 'metric-excellent'
  if (loss <= 3) return 'metric-good'
  if (loss <= 5) return 'metric-fair'
  return 'metric-poor'
}

// 获取稳定性样式类
const getStabilityClass = (): string => {
  const stability = networkData.value.stability || 0
  if (stability >= 95) return 'metric-excellent'
  if (stability >= 85) return 'metric-good'
  if (stability >= 70) return 'metric-fair'
  return 'metric-poor'
}

// 获取稳定性文本
const getStabilityText = (): string => {
  const stability = networkData.value.stability || 0
  if (stability >= 95) return '优秀'
  if (stability >= 85) return '良好'
  if (stability >= 70) return '一般'
  return '较差'
}

// 格式化流量
const formatTraffic = (bytes: number | undefined): string => {
  if (!bytes) return '0 MB'
  
  const gb = bytes / (1024 * 1024 * 1024)
  const mb = bytes / (1024 * 1024)
  
  if (gb >= 1) {
    return `${gb.toFixed(2)} GB`
  } else {
    return `${mb.toFixed(0)} MB`
  }
}

// 获取流量使用百分比
const getTrafficPercent = (): string => {
  const used = networkData.value.monthUsed || 0
  const total = networkData.value.totalQuota || 1
  const percent = (used / total) * 100
  return `${Math.min(percent, 100).toFixed(1)}%`
}

// ==================== 事件处理 ====================

// 处理操作按钮点击
const handleAction = (action: any) => {
  emit('click', {
    elementType: 'action',
    elementId: action.id,
    elementData: {
      action: action.action,
      params: action.params
    },
    componentType: 'HomeNetWork'
  })
}

// 自动刷新
const startAutoRefresh = () => {
  if (!config.value.autoRefresh) return
  
  refreshTimer = setTimeout(() => {
    emit('click', {
      elementType: 'refresh',
      elementId: 'auto-refresh',
      elementData: { type: 'auto' },
      componentType: 'HomeNetWork'
    })
    startAutoRefresh() // 递归调用
  }, config.value.refreshInterval || 30000)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearTimeout(refreshTimer)
    refreshTimer = null
  }
}

// ==================== 生命周期 ====================

onMounted(() => {
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})

// ==================== 数据监听 ====================

watch(() => props.data, (newData) => {
  console.log('🔄 [HomeNetWork] 外部数据变化', newData)
}, { deep: true, immediate: true })

watch(() => config.value.autoRefresh, (newValue) => {
  if (newValue) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
})
</script>

<style lang="scss" scoped>
.home-network {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin: 32rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 头部 */
.network-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.network-title {
  display: flex;
  align-items: center;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.network-status {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
}

.status-connected {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-disconnected {
  background-color: #fff1f0;
  color: #ff4d4f;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.status-connected .status-dot {
  background-color: #52c41a;
}

.status-disconnected .status-dot {
  background-color: #ff4d4f;
}

/* 网络卡片 */
.network-cards {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.network-card {
  background-color: #fafafa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.card-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.card-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 连接信息 */
.connection-info {
  margin-bottom: 20rpx;
}

.connection-type {
  font-size: 32rpx;
  font-weight: bold;
  color: #1890ff;
  margin-right: 24rpx;
}

.connection-strength {
  font-size: 24rpx;
  color: #666;
}

.connection-speed {
  display: flex;
  gap: 32rpx;
}

.speed-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.speed-label {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.speed-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

/* 质量指标 */
.quality-metrics {
  display: flex;
  justify-content: space-between;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.metric-label {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.metric-value {
  font-size: 24rpx;
  font-weight: bold;
}

.metric-excellent {
  color: #52c41a;
}

.metric-good {
  color: #1890ff;
}

.metric-fair {
  color: #fa8c16;
}

.metric-poor {
  color: #ff4d4f;
}

/* 流量统计 */
.traffic-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.traffic-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.traffic-label {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.traffic-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.traffic-chart {
  margin-top: 16rpx;
}

.chart-bar {
  height: 12rpx;
  background-color: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.chart-progress {
  height: 100%;
  background-color: #1890ff;
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.chart-text {
  font-size: 22rpx;
  color: #666;
  text-align: center;
  display: block;
}

/* 操作按钮 */
.network-actions {
  margin-top: 32rpx;
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background-color: #1890ff;
  color: #ffffff;
}

.action-btn.primary:active {
  background-color: #096dd9;
}

.action-btn.default {
  background-color: #f5f5f5;
  color: #333;
  border: 2rpx solid #d9d9d9;
}

.action-btn.default:active {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-weight: bold;
}
</style>