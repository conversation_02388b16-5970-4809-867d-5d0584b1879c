# DxIcon 通用图标组件

一个适配 uni-app x 多端的通用图标组件，支持 iconify、字体图标、uni官方图标、在线图片等多种图标类型，并能根据平台自动降级。

## 特性

- 🎯 **多端适配**：支持 App、小程序、Web 端
- 🔄 **自动降级**：根据平台能力自动选择最佳图标类型
- 🎨 **多种类型**：支持 iconify (官方组件)、字体图标、uni图标、图片
- ⚡ **高性能**：直接使用官方 @iconify/vue 组件，支持完整图标库
- 🛠 **易扩展**：支持自定义样式和事件
- 📦 **官方支持**：使用 @iconify/vue 和 @iconify/json 官方包

## 平台兼容性

| 类型 | 说明 | App | 小程序 | Web | 降级方案 |
|------|------|-----|--------|-----|----------|
| `iconify` | Iconify图标库(官方组件) | ✅ | ✅ | ✅ | 字体图标 |
| `font` | 字体图标 | ✅ | ✅ | ✅ | Unicode字符 |
| `uni` | Uni官方图标 | ✅ | ✅ | ✅ | 内置字符 |
| `image` | 在线/本地图片 | ✅ | ✅ | ✅ | 字体图标 |

## 基础用法

### Iconify 图标（推荐）

直接使用官方 @iconify/vue 组件，支持完整的 iconify 图标库（10万+ 图标）。

```vue
<template>
  <!-- 基础使用 -->
  <DxIcon name="mdi:home" type="iconify" />
  
  <!-- 自定义大小和颜色 -->
  <DxIcon 
    name="mdi:heart" 
    type="iconify" 
    :size="32" 
    color="#ff4757" 
  />
  
  <!-- 可点击图标 -->
  <DxIcon 
    name="mdi:cog" 
    type="iconify" 
    :clickable="true"
    @click="handleSettingsClick"
  />
</template>
```

**支持的图标前缀：**
- `mdi:` - Material Design Icons (推荐)
- `heroicons:` - Heroicons
- `feather:` - Feather Icons

### 字体图标

```vue
<template>
  <!-- 基础字体图标 -->
  <DxIcon name="home" type="font" />
  
  <!-- 自定义字体前缀 -->
  <DxIcon 
    name="user" 
    type="font" 
    font-prefix="iconfont"
    :size="24"
    color="#333"
  />
</template>
```

### Uni 官方图标

```vue
<template>
  <!-- Uni 官方图标 -->
  <DxIcon name="arrow-left" type="uni" />
  <DxIcon name="close" type="uni" />
  <DxIcon name="check" type="uni" />
</template>
```

### 图片图标

```vue
<template>
  <!-- 在线图片 -->
  <DxIcon 
    name="https://example.com/icon.png" 
    type="image" 
    :size="32"
  />
  
  <!-- 本地图片 -->
  <DxIcon 
    name="/static/logo.png" 
    type="image" 
    :size="48"
    image-mode="aspectFit"
  />
</template>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `name` | `string` | - | 图标名称 |
| `type` | `'iconify' \| 'font' \| 'uni' \| 'image'` | `'iconify'` | 图标类型 |
| `size` | `number \| string` | `24` | 图标大小 |
| `color` | `string` | `'#333333'` | 图标颜色 |
| `clickable` | `boolean` | `false` | 是否可点击 |
| `font-prefix` | `string` | `''` | 字体图标前缀 |
| `image-mode` | `string` | `'aspectFit'` | 图片模式 |
| `fallback` | `string` | `'?'` | 降级显示内容 |
| `custom-class` | `string` | `''` | 自定义CSS类 |
| `custom-style` | `object` | `{}` | 自定义样式 |

## Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| `click` | 点击事件 | `event` |
| `load` | 图标加载成功 | `event` |
| `error` | 图标加载失败 | `event` |

## 实际使用示例

### 导航栏

```vue
<template>
  <view class="navbar">
    <DxIcon 
      name="mdi:arrow-left" 
      type="iconify" 
      :size="24" 
      color="#333"
      :clickable="true"
      @click="goBack"
    />
    <text class="title">页面标题</text>
    <DxIcon 
      name="mdi:cog" 
      type="iconify" 
      :size="24" 
      color="#333"
      :clickable="true"
      @click="openSettings"
    />
  </view>
</template>
```

### 底部导航

```vue
<template>
  <view class="tabbar">
    <view class="tab-item" @click="switchTab(0)">
      <DxIcon 
        name="mdi:home" 
        type="iconify" 
        :size="24" 
        :color="activeTab === 0 ? '#007aff' : '#999'"
      />
      <text>首页</text>
    </view>
    <!-- 更多标签... -->
  </view>
</template>
```

### 功能按钮

```vue
<template>
  <view class="actions">
    <view class="action-btn" @click="handleLike">
      <DxIcon 
        name="mdi:heart" 
        type="iconify" 
        :size="20" 
        :color="isLiked ? '#ff3b30' : '#999'"
      />
      <text>{{ isLiked ? '已喜欢' : '喜欢' }}</text>
    </view>
  </view>
</template>
```

## 注意事项

1. **H5端优势**：在 H5 端会自动使用 @iconify/vue 官方组件，支持完整的 iconify 图标库
2. **其他端降级**：在 App 和小程序端使用内置映射，仅支持预定义的常用图标
3. **性能考虑**：内置映射避免了网络请求，提升了加载速度
4. **扩展性**：可以通过修改 `ICONIFY_UNICODE_MAP` 添加更多图标映射

## 依赖

- `@iconify/vue`: ^4.1.1 (H5端使用)
- `@iconify/json`: ^2.2.170 (图标数据)
