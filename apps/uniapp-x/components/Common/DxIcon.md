# DxIcon 通用图标组件

一个适配多端的通用图标组件，支持 iconify、字体图标、uni官方图标、在线图片等多种图标类型，并能根据平台自动降级。

## 特性

- 🎯 **多端适配**：支持 App、小程序、Web 端
- 🔄 **自动降级**：根据平台能力自动选择最佳图标类型
- 🎨 **多种类型**：支持 iconify、字体图标、uni图标、图片、SVG
- ⚡ **高性能**：智能缓存和懒加载
- 🛠 **易扩展**：支持自定义样式和事件

## 支持的图标类型

| 类型 | 说明 | App | 小程序 | Web | 降级方案 |
|------|------|-----|--------|-----|----------|
| `iconify` | Iconify图标库 | ✅ | ✅ | ✅ | 字体图标 |
| `font` | 字体图标 | ✅ | ✅ | ✅ | Unicode字符 |
| `uni` | Uni官方图标 | ✅ | ✅ | ✅ | 内置字符 |
| `image` | 在线/本地图片 | ✅ | ✅ | ✅ | 字体图标 |
| `svg` | SVG矢量图 | ✅ | ❌ | ✅ | 字体图标 |

## 基础用法

### Iconify 图标（推荐）

```vue
<template>
  <!-- 基础使用 -->
  <DxIcon name="mdi:home" type="iconify" />
  
  <!-- 自定义大小和颜色 -->
  <DxIcon 
    name="mdi:heart" 
    type="iconify" 
    :size="32" 
    color="#ff4757" 
  />
  
  <!-- 可点击图标 -->
  <DxIcon 
    name="mdi:settings" 
    type="iconify" 
    :clickable="true"
    @click="handleSettingsClick"
  />
</template>
```

### 字体图标

```vue
<template>
  <!-- 使用 iconfont -->
  <DxIcon 
    name="home" 
    type="font" 
    font-prefix="iconfont"
    :size="24"
    color="#333"
  />
  
  <!-- 使用 Unicode 字符 -->
  <DxIcon 
    name="&#xe600;" 
    type="font" 
    :size="20"
  />
</template>
```

### Uni 官方图标

```vue
<template>
  <!-- 内置图标映射 -->
  <DxIcon name="arrow-left" type="uni" />
  <DxIcon name="close" type="uni" />
  <DxIcon name="check" type="uni" />
  <DxIcon name="star" type="uni" />
</template>
```

### 图片图标

```vue
<template>
  <!-- 在线图片 -->
  <DxIcon 
    name="https://example.com/icon.png" 
    type="image" 
    :size="40"
    image-mode="aspectFit"
  />
  
  <!-- 本地图片 -->
  <DxIcon 
    name="logo.png" 
    type="image" 
    :size="32"
  />
</template>
```

### SVG 图标

```vue
<template>
  <!-- SVG 文件 -->
  <DxIcon 
    name="custom-icon" 
    type="svg" 
    svg-src="/static/icons/custom.svg"
    :size="28"
    color="#007aff"
  />
</template>
```

## 高级用法

### 自动降级策略

```vue
<template>
  <!-- 优先使用 iconify，不支持时自动降级到字体图标 -->
  <DxIcon 
    name="home" 
    type="iconify" 
    font-prefix="iconfont"
    fallback="⌂"
  />
</template>
```

### 自定义样式

```vue
<template>
  <DxIcon 
    name="mdi:star"
    type="iconify"
    :size="30"
    color="#ffd700"
    custom-class="my-icon"
    :custom-style="{
      borderRadius: '50%',
      backgroundColor: '#f0f0f0',
      padding: '8px'
    }"
  />
</template>

<style>
.my-icon {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
</style>
```

### 事件处理

```vue
<template>
  <DxIcon 
    name="mdi:image"
    type="image"
    :clickable="true"
    @click="handleClick"
    @image-error="handleImageError"
    @image-load="handleImageLoad"
  />
</template>

<script setup lang="ts">
const handleClick = (event: any) => {
  console.log('图标被点击', event)
}

const handleImageError = (event: any) => {
  console.log('图片加载失败', event)
}

const handleImageLoad = (event: any) => {
  console.log('图片加载成功', event)
}
</script>
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `name` | `string` | - | 图标名称/内容（必填） |
| `type` | `IconType` | `'iconify'` | 图标类型 |
| `size` | `number \| string` | `24` | 图标大小 |
| `color` | `string` | `'#333333'` | 图标颜色 |
| `customClass` | `string` | `''` | 自定义类名 |
| `customStyle` | `Record<string, any>` | `{}` | 自定义样式 |
| `iconifyPrefix` | `string` | `''` | iconify 前缀 |
| `fontPrefix` | `string` | `'iconfont'` | 字体图标类名前缀 |
| `imageMode` | `ImageMode` | `'aspectFit'` | 图片模式 |
| `svgSrc` | `string` | `''` | SVG 源文件路径 |
| `clickable` | `boolean` | `false` | 是否可点击 |
| `fallback` | `string` | `'●'` | 降级显示的图标 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `click` | `event` | 图标点击事件（需要设置 `clickable` 为 `true`） |
| `imageError` | `event` | 图片加载失败事件 |
| `imageLoad` | `event` | 图片加载成功事件 |

## 类型定义

```typescript
type IconType = 'iconify' | 'font' | 'uni' | 'image' | 'svg'
type ImageMode = 'scaleToFill' | 'aspectFit' | 'aspectFill' | 'widthFix' | 'heightFix'
```

## 平台兼容性

### 条件编译说明

组件内部使用条件编译来适配不同平台：

```typescript
// App 和 H5 支持 iconify
// #ifdef APP-PLUS || H5
const isIconifySupported = true
// #endif

// 小程序需要检测插件支持
// #ifdef MP
const isIconifySupported = checkLimeIconPlugin()
// #endif

// SVG 支持情况
// #ifdef APP-PLUS || H5
const isSvgSupported = true
// #endif
// #ifdef MP
const isSvgSupported = false
// #endif
```

## 最佳实践

1. **优先使用 iconify**：图标资源丰富，支持按需加载
2. **设置降级方案**：确保在不支持的平台上有备选方案
3. **合理设置大小**：根据使用场景选择合适的图标大小
4. **统一图标风格**：在同一应用中保持图标风格一致
5. **性能优化**：对于大量图标使用场景，考虑预加载和缓存

## 注意事项

- 使用 iconify 需要确保项目中已安装 `lime-icon` 插件
- 使用 SVG 需要确保项目中已安装 `lime-svg` 插件
- 字体图标需要提前引入对应的字体文件
- 图片图标建议使用 CDN 或本地资源，避免跨域问题
