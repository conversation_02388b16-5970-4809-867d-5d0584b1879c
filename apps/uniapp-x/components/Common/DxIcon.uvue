<!--
  DxIcon 通用图标组件 - UniApp-X版本
  支持多种图标类型：iconify、字体图标、uni官方图标、在线图片
  适配 App、小程序、Web 端
-->
<template>
  <view class="dx-icon" :class="iconClass" :style="computedStyle" @click="handleClick">
    <!-- #ifdef H5 -->
    <!-- Iconify 图标 (H5端使用官方组件) -->
    <Icon
      v-if="iconType === 'iconify'"
      :icon="props.name"
      :style="iconifyVueStyle"
    />
    <!-- #endif -->

    <!-- #ifndef H5 -->
    <!-- Iconify 图标 (非H5端使用内置图标数据) -->
    <text
      v-if="iconType === 'iconify' && iconifyUnicode"
      class="dx-icon-iconify"
      :style="iconifyStyle"
    >
      {{ iconifyUnicode }}
    </text>

    <!-- Iconify 图标 (SVG方式，如果支持) -->
    <image
      v-else-if="iconType === 'iconify' && iconifySvgUrl && !iconifyLoadError"
      class="dx-icon-iconify-svg"
      :src="iconifySvgUrl"
      :style="iconifyImageStyle"
      mode="aspectFit"
      @error="handleIconifyError"
      @load="handleIconifyLoad"
    />

    <!-- Iconify 降级到字体图标 -->
    <text
      v-else-if="iconType === 'iconify'"
      class="dx-icon-font"
      :style="fontIconStyle"
    >
      {{ iconifyFallbackContent }}
    </text>
    <!-- #endif -->
    
    <!-- 字体图标 (iconfont) -->
    <text 
      v-else-if="iconType === 'font' || (iconType === 'iconify' && !isIconifySupported)"
      class="dx-icon-font"
      :class="fontIconClass"
      :style="fontIconStyle"
    >
      {{ fontIconContent }}
    </text>
    
    <!-- Uni 官方图标 -->
    <text 
      v-else-if="iconType === 'uni'"
      class="dx-icon-uni"
      :class="uniIconClass"
      :style="uniIconStyle"
    >
      {{ uniIconContent }}
    </text>
    
    <!-- 在线图片图标 -->
    <image
      v-else-if="iconType === 'image'"
      class="dx-icon-image"
      :src="imageSrc"
      :mode="imageMode"
      :style="imageStyle"
      @error="handleImageError"
      @load="handleImageLoad"
    />
    
    <!-- SVG 图标 (使用lime-svg插件) -->
    <l-svg
      v-else-if="iconType === 'svg' && isSvgSupported"
      :src="svgSrc"
      :width="size"
      :height="size"
      :color="color"
    />
    
    <!-- 降级显示 -->
    <text v-else class="dx-icon-fallback" :style="fallbackStyle">
      {{ fallbackIcon }}
    </text>
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
// #ifdef H5
import { Icon } from '@iconify/vue'
// #endif

// ==================== 类型定义 ====================

type IconType = 'iconify' | 'font' | 'uni' | 'image' | 'svg'
type ImageMode = 'scaleToFill' | 'aspectFit' | 'aspectFill' | 'widthFix' | 'heightFix'

interface Props {
  /** 图标名称/内容 */
  name: string
  /** 图标类型 */
  type?: IconType
  /** 图标大小 */
  size?: number | string
  /** 图标颜色 */
  color?: string
  /** 自定义类名 */
  customClass?: string
  /** 自定义样式 */
  customStyle?: Record<string, any>
  /** iconify 前缀 */
  iconifyPrefix?: string
  /** 字体图标类名前缀 */
  fontPrefix?: string
  /** 图片模式 */
  imageMode?: ImageMode
  /** SVG 源 */
  svgSrc?: string
  /** 是否可点击 */
  clickable?: boolean
  /** 降级图标 */
  fallback?: string
}

// ==================== Props 定义 ====================

const props = withDefaults(defineProps<Props>(), {
  type: 'iconify',
  size: 24,
  color: '#333333',
  customClass: '',
  customStyle: () => ({}),
  iconifyPrefix: '',
  fontPrefix: 'iconfont',
  imageMode: 'aspectFit',
  svgSrc: '',
  clickable: false,
  fallback: '●'
})

// ==================== Emits 定义 ====================

const emit = defineEmits<{
  click: [event: any]
  imageError: [event: any]
  imageLoad: [event: any]
}>()

// ==================== 响应式数据 ====================

const imageLoadError = ref(false)
const iconifyLoadError = ref(false)
const iconifyCache = new Map<string, string>()

// ==================== Iconify 相关 ====================

/** 内置 Iconify 图标映射 (常用图标的 Unicode 字符) */
const ICONIFY_UNICODE_MAP: Record<string, string> = {
  // Material Design Icons
  'mdi:home': '🏠',
  'mdi:account': '👤',
  'mdi:cog': '⚙️',
  'mdi:heart': '❤️',
  'mdi:star': '⭐',
  'mdi:bell': '🔔',
  'mdi:email': '📧',
  'mdi:phone': '📞',
  'mdi:search': '🔍',
  'mdi:menu': '☰',
  'mdi:close': '✕',
  'mdi:check': '✓',
  'mdi:plus': '➕',
  'mdi:minus': '➖',
  'mdi:arrow-left': '←',
  'mdi:arrow-right': '→',
  'mdi:arrow-up': '↑',
  'mdi:arrow-down': '↓',
  'mdi:thumb-up': '👍',
  'mdi:share': '📤',
  'mdi:download': '⬇️',
  'mdi:upload': '⬆️',
  'mdi:delete': '🗑️',
  'mdi:edit': '✏️',
  'mdi:camera': '📷',
  'mdi:image': '🖼️',
  'mdi:video': '🎥',
  'mdi:music': '🎵',
  'mdi:folder': '📁',
  'mdi:file': '📄',
  'mdi:calendar': '📅',
  'mdi:clock': '🕐',
  'mdi:location': '📍',
  'mdi:wifi': '📶',
  'mdi:battery': '🔋',
  'mdi:settings': '⚙️',
  'mdi:lock': '🔒',
  'mdi:unlock': '🔓',
  'mdi:eye': '👁️',
  'mdi:eye-off': '🙈',

  // Heroicons
  'heroicons:home': '🏠',
  'heroicons:user': '👤',
  'heroicons:cog': '⚙️',
  'heroicons:heart': '❤️',
  'heroicons:star': '⭐',

  // Feather Icons
  'feather:home': '🏠',
  'feather:user': '👤',
  'feather:settings': '⚙️',
  'feather:heart': '❤️',
  'feather:star': '⭐'
}

/** Iconify API 基础URL (备用方案) */
const ICONIFY_API_BASE = 'https://api.iconify.design'

/** 获取 Iconify Unicode 字符 */
const getIconifyUnicode = (iconName: string): string => {
  return ICONIFY_UNICODE_MAP[iconName] || ''
}

/** 获取 Iconify SVG URL (备用方案) */
const getIconifySvgUrl = (iconName: string): string => {
  // 检查缓存
  if (iconifyCache.has(iconName)) {
    return iconifyCache.get(iconName) || ''
  }

  // 解析图标名称 (例: mdi:home -> mdi/home)
  const [prefix, name] = iconName.includes(':') ? iconName.split(':') : ['', iconName]

  if (!prefix || !name) {
    console.warn(`[DxIcon] 无效的 iconify 图标名称: ${iconName}`)
    return ''
  }

  // 构建 SVG URL
  const svgUrl = `${ICONIFY_API_BASE}/${prefix}/${name}.svg`

  // 添加颜色参数
  const colorParam = props.color && props.color !== '#333333' ? `?color=${encodeURIComponent(props.color)}` : ''
  const finalUrl = `${svgUrl}${colorParam}`

  // 缓存结果
  iconifyCache.set(iconName, finalUrl)

  return finalUrl
}

// ==================== 计算属性 ====================

/** 当前图标类型 */
const iconType = computed((): IconType => {
  // 如果图片加载失败，降级到字体图标
  if (props.type === 'image' && imageLoadError.value) {
    return 'font'
  }
  // 如果 iconify 加载失败，降级到字体图标
  if (props.type === 'iconify' && iconifyLoadError.value) {
    return 'font'
  }
  return props.type
})

/** Iconify Unicode 字符 */
const iconifyUnicode = computed(() => {
  if (props.type === 'iconify') {
    return getIconifyUnicode(props.name)
  }
  return ''
})

/** Iconify Vue 组件样式 (H5端) */
const iconifyVueStyle = computed(() => ({
  fontSize: typeof props.size === 'number' ? `${props.size}px` : props.size,
  color: props.color,
  width: typeof props.size === 'number' ? `${props.size}px` : props.size,
  height: typeof props.size === 'number' ? `${props.size}px` : props.size,
  display: 'inline-block'
}))

/** Iconify 样式 (非H5端) */
const iconifyStyle = computed(() => ({
  fontSize: typeof props.size === 'number' ? `${props.size}px` : props.size,
  color: props.color,
  lineHeight: '1'
}))

/** Iconify SVG URL (备用方案) */
const iconifySvgUrl = computed(() => {
  if (props.type === 'iconify' && !iconifyUnicode.value && !iconifyLoadError.value) {
    return getIconifySvgUrl(props.name)
  }
  return ''
})

/** Iconify 图片样式 */
const iconifyImageStyle = computed(() => ({
  width: typeof props.size === 'number' ? `${props.size}px` : props.size,
  height: typeof props.size === 'number' ? `${props.size}px` : props.size
}))

/** Iconify 降级内容 */
const iconifyFallbackContent = computed(() => {
  // 从图标名称推断可能的字符
  const iconMap: Record<string, string> = {
    'home': '⌂',
    'user': '👤',
    'settings': '⚙',
    'heart': '♥',
    'star': '★',
    'search': '🔍',
    'close': '×',
    'check': '✓',
    'arrow-left': '←',
    'arrow-right': '→',
    'arrow-up': '↑',
    'arrow-down': '↓',
    'plus': '+',
    'minus': '-'
  }

  // 提取图标名称的后半部分 (例: mdi:home -> home)
  const iconName = props.name.includes(':') ? props.name.split(':')[1] : props.name

  return iconMap[iconName] || props.fallback
})

/** 是否支持 Iconify (通过官方API) */
const isIconifySupported = computed((): boolean => {
  // 所有平台都支持通过 image 组件加载 SVG
  return true
})

/** 是否支持 SVG */
const isSvgSupported = computed((): boolean => {
  // #ifdef APP-PLUS
  return true
  // #endif
  
  // #ifdef H5
  return true
  // #endif
  
  // #ifdef MP
  return false
  // #endif
  
  return false
})

/** 图标容器类名 */
const iconClass = computed(() => {
  const classes = ['dx-icon-container']
  
  if (props.clickable) {
    classes.push('dx-icon-clickable')
  }
  
  if (props.customClass) {
    classes.push(props.customClass)
  }
  
  return classes
})

/** 容器样式 */
const computedStyle = computed(() => {
  const baseStyle = {
    width: typeof props.size === 'number' ? `${props.size}px` : props.size,
    height: typeof props.size === 'number' ? `${props.size}px` : props.size,
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center'
  }
  
  return { ...baseStyle, ...props.customStyle }
})

/** 字体图标类名 */
const fontIconClass = computed(() => {
  if (iconType.value === 'font' || (props.type === 'iconify' && !isIconifySupported.value)) {
    return `${props.fontPrefix} ${props.fontPrefix}-${props.name}`
  }
  return ''
})

/** 字体图标样式 */
const fontIconStyle = computed(() => ({
  fontSize: typeof props.size === 'number' ? `${props.size}px` : props.size,
  color: props.color,
  lineHeight: '1'
}))

/** 字体图标内容 */
const fontIconContent = computed(() => {
  // 如果是Unicode字符，直接返回
  if (props.name.startsWith('\\u') || props.name.startsWith('&#')) {
    return props.name
  }
  return ''
})

/** Uni 官方图标类名 */
const uniIconClass = computed(() => {
  if (iconType.value === 'uni') {
    return `uni-icon-${props.name}`
  }
  return ''
})

/** Uni 官方图标样式 */
const uniIconStyle = computed(() => ({
  fontSize: typeof props.size === 'number' ? `${props.size}px` : props.size,
  color: props.color,
  lineHeight: '1'
}))

/** Uni 官方图标内容 */
const uniIconContent = computed(() => {
  // Uni 官方图标映射
  const uniIconMap: Record<string, string> = {
    'arrow-left': '←',
    'arrow-right': '→',
    'arrow-up': '↑',
    'arrow-down': '↓',
    'close': '×',
    'check': '✓',
    'plus': '+',
    'minus': '-',
    'star': '★',
    'heart': '♥',
    'home': '⌂',
    'search': '🔍',
    'setting': '⚙',
    'user': '👤'
  }
  
  return uniIconMap[props.name] || props.fallback
})

/** 图片源地址 */
const imageSrc = computed(() => {
  if (iconType.value === 'image') {
    // 如果name是完整URL，直接使用
    if (props.name.startsWith('http') || props.name.startsWith('//')) {
      return props.name
    }
    // 否则拼接为本地路径
    return `/static/icons/${props.name}`
  }
  return ''
})

/** 图片样式 */
const imageStyle = computed(() => ({
  width: typeof props.size === 'number' ? `${props.size}px` : props.size,
  height: typeof props.size === 'number' ? `${props.size}px` : props.size
}))

/** 降级样式 */
const fallbackStyle = computed(() => ({
  fontSize: typeof props.size === 'number' ? `${props.size}px` : props.size,
  color: props.color,
  lineHeight: '1'
}))

/** 降级图标 */
const fallbackIcon = computed(() => {
  return props.fallback
})

// ==================== 事件处理 ====================

/** 点击事件 */
const handleClick = (event: any) => {
  if (props.clickable) {
    emit('click', event)
  }
}

/** 图片加载错误 */
const handleImageError = (event: any) => {
  console.warn(`[DxIcon] 图片加载失败: ${imageSrc.value}`)
  imageLoadError.value = true
  emit('imageError', event)
}

/** 图片加载成功 */
const handleImageLoad = (event: any) => {
  imageLoadError.value = false
  emit('imageLoad', event)
}

/** Iconify 图标加载错误 */
const handleIconifyError = (event: any) => {
  console.warn(`[DxIcon] Iconify 图标加载失败: ${props.name}`)
  iconifyLoadError.value = true

  // 清除缓存中的错误URL
  iconifyCache.delete(props.name)

  uni.showToast({
    title: `图标 ${props.name} 加载失败`,
    icon: 'none',
    duration: 1500
  })
}

/** Iconify 图标加载成功 */
const handleIconifyLoad = (event: any) => {
  iconifyLoadError.value = false
  console.log(`[DxIcon] Iconify 图标加载成功: ${props.name}`)
}
</script>

<style scoped>
.dx-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
}

.dx-icon-container {
  position: relative;
}

.dx-icon-clickable {
  cursor: pointer;
}

.dx-icon-clickable:hover {
  opacity: 0.8;
}

.dx-icon-clickable:active {
  opacity: 0.6;
}

.dx-icon-font,
.dx-icon-uni,
.dx-icon-fallback {
  display: inline-block;
  text-align: center;
  vertical-align: middle;
}

.dx-icon-image,
.dx-icon-iconify-svg {
  display: block;
}

.dx-icon-iconify {
  display: inline-block;
  text-align: center;
  vertical-align: middle;
}

/* 字体图标基础样式 */
.iconfont {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .dx-icon-clickable:hover {
    opacity: 1;
  }
}
</style>
