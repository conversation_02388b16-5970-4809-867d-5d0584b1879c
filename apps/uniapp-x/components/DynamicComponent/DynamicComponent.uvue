<!--
  DynamicComponent 动态组件渲染器 - UniApp-X版本
  根据组件类型和配置动态渲染对应的组件
-->
<template>
  <view class="dynamic-component" :style="computedStyle">
    <!-- HomeBasic 组件 -->
    <HomeBasic
      v-if="componentType === 'HomeBasic'"
      :config="componentProps"
      :data="componentProps"
      :design-mode="false"
      @click="handleComponentEvent"
    />
    
    <!-- HomeMore 组件 -->
    <HomeMore
      v-else-if="componentType === 'HomeMore'"
      :config="componentProps"
      :data="componentProps"
      :design-mode="false"
      @click="handleComponentEvent"
    />
    
    <!-- HomeDetails 组件 -->
    <HomeDetails
      v-else-if="componentType === 'HomeDetails'"
      :config="componentProps"
      :data="componentProps"
      :design-mode="false"
      @click="handleComponentEvent"
    />
    
    <!-- HomeNetWork 组件 -->
    <HomeNetWork
      v-else-if="componentType === 'HomeNetWork'"
      :config="componentProps"
      :data="componentProps"
      :design-mode="false"
      @click="handleComponentEvent"
    />
    
    <!-- Header 组件 -->
    <view v-else-if="componentType === 'Header'" class="header-component">
      <view class="header-content">
        <text class="header-title">{{ componentProps.title || '标题' }}</text>
        <text v-if="componentProps.subtitle" class="header-subtitle">{{ componentProps.subtitle }}</text>
      </view>
    </view>
    
    <!-- Text 组件 -->
    <view v-else-if="componentType === 'Text'" class="text-component">
      <text class="text-content" :style="textStyle">{{ componentProps.content || '文本内容' }}</text>
    </view>
    
    <!-- Button 组件 -->
    <button 
      v-else-if="componentType === 'Button'" 
      class="button-component"
      :class="buttonClass"
      :style="buttonStyle"
      @click="handleButtonClick"
    >
      {{ componentProps.text || '按钮' }}
    </button>
    
    <!-- Image 组件 -->
    <image
      v-else-if="componentType === 'Image'"
      class="image-component"
      :src="componentProps.src || '/static/logo.png'"
      :mode="componentProps.mode || 'aspectFit'"
      :style="imageStyle"
      @click="handleComponentEvent"
    />
    
    <!-- Divider 分割线组件 -->
    <view v-else-if="componentType === 'Divider'" class="divider-component">
      <view class="divider-line" :style="dividerStyle"></view>
      <text v-if="componentProps.text" class="divider-text">{{ componentProps.text }}</text>
    </view>
    
    <!-- Space 间距组件 -->
    <view v-else-if="componentType === 'Space'" class="space-component" :style="spaceStyle"></view>
    
    <!-- Container 容器组件 -->
    <view v-else-if="componentType === 'Container'" class="container-component" :style="containerStyle">
      <DynamicComponent
        v-for="(child, index) in componentProps.children"
        :key="child.id || index"
        :component-type="child.type"
        :component-props="child.props"
        :component-style="child.style"
        :component-events="child.events"
        @component-event="handleComponentEvent"
      />
    </view>
    
    <!-- 未知组件类型的占位符 -->
    <view v-else class="unknown-component">
      <text class="unknown-text">未知组件: {{ componentType }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import HomeBasic from '../Home/HomeBasic.uvue'
import HomeMore from '../Home/HomeMore.uvue'
import HomeDetails from '../Home/HomeDetails.uvue'
import HomeNetWork from '../Home/HomeNetWork.uvue'

// ==================== 类型定义 ====================

interface ComponentProps {
  [key: string]: any
}

interface ComponentStyle {
  [key: string]: string | number
}

interface ComponentEvents {
  [key: string]: any
}

interface Props {
  /** 组件类型 */
  componentType: string
  /** 组件属性 */
  componentProps?: ComponentProps
  /** 组件样式 */
  componentStyle?: ComponentStyle
  /** 组件事件 */
  componentEvents?: ComponentEvents
}

// ==================== Props定义 ====================

const props = withDefaults(defineProps<Props>(), {
  componentProps: () => ({}),
  componentStyle: () => ({}),
  componentEvents: () => ({})
})

// ==================== 事件定义 ====================

const emit = defineEmits<{
  componentEvent: [eventData: {
    componentType: string
    elementType: string
    elementId: string
    elementData?: any
    originalEvent?: any
  }]
}>()

// ==================== 计算属性 ====================

// 合并样式
const computedStyle = computed(() => {
  const baseStyle = {
    width: '100%',
    boxSizing: 'border-box'
  }
  
  return {
    ...baseStyle,
    ...props.componentStyle
  }
})

// Text 组件样式
const textStyle = computed(() => ({
  fontSize: props.componentProps.fontSize || '28rpx',
  color: props.componentProps.color || '#333',
  lineHeight: props.componentProps.lineHeight || '1.5',
  textAlign: props.componentProps.textAlign || 'left'
}))

// Button 组件样式
const buttonClass = computed(() => {
  const type = props.componentProps.type || 'default'
  return `btn-${type}`
})

const buttonStyle = computed(() => ({
  fontSize: props.componentProps.fontSize || '28rpx',
  padding: props.componentProps.padding || '20rpx 40rpx',
  borderRadius: props.componentProps.borderRadius || '8rpx'
}))

// Image 组件样式
const imageStyle = computed(() => ({
  width: props.componentProps.width || '100%',
  height: props.componentProps.height || 'auto',
  borderRadius: props.componentProps.borderRadius || '0'
}))

// Divider 组件样式
const dividerStyle = computed(() => ({
  height: props.componentProps.height || '2rpx',
  backgroundColor: props.componentProps.color || '#f0f0f0',
  margin: props.componentProps.margin || '32rpx 0'
}))

// Space 组件样式
const spaceStyle = computed(() => ({
  height: props.componentProps.height || '32rpx',
  width: '100%'
}))

// Container 组件样式
const containerStyle = computed(() => ({
  padding: props.componentProps.padding || '0',
  margin: props.componentProps.margin || '0',
  backgroundColor: props.componentProps.backgroundColor || 'transparent',
  borderRadius: props.componentProps.borderRadius || '0',
  display: props.componentProps.display || 'block'
}))

// ==================== 事件处理 ====================

// 处理组件事件
const handleComponentEvent = (eventData: any) => {
  console.log('🎯 [DynamicComponent] 组件事件:', {
    componentType: props.componentType,
    eventData
  })
  
  // 转发事件，添加组件信息
  emit('componentEvent', {
    componentType: props.componentType,
    elementType: eventData.elementType || 'unknown',
    elementId: eventData.elementId || 'unknown',
    elementData: eventData.elementData,
    originalEvent: eventData
  })
}

// 处理按钮点击
const handleButtonClick = () => {
  const eventConfig = props.componentEvents?.click
  if (eventConfig) {
    handleComponentEvent({
      elementType: 'button',
      elementId: 'button-click',
      elementData: eventConfig
    })
  } else {
    console.log('🔘 [DynamicComponent] 按钮点击，但未配置事件')
  }
}
</script>

<style lang="scss" scoped>
.dynamic-component {
  width: 100%;
  box-sizing: border-box;
}

/* Header 组件样式 */
.header-component {
  padding: 32rpx;
  background-color: #ffffff;
  border-bottom: 2rpx solid #f0f0f0;
}

.header-content {
  text-align: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

/* Text 组件样式 */
.text-component {
  padding: 24rpx 32rpx;
}

.text-content {
  word-wrap: break-word;
  word-break: break-all;
}

/* Button 组件样式 */
.button-component {
  margin: 16rpx 32rpx;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: #1890ff;
  color: #ffffff;
}

.btn-primary:active {
  background-color: #096dd9;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
  border: 2rpx solid #d9d9d9;
}

.btn-secondary:active {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.btn-default {
  background-color: #ffffff;
  color: #333;
  border: 2rpx solid #d9d9d9;
}

.btn-default:active {
  background-color: #f5f5f5;
}

.btn-danger {
  background-color: #ff4d4f;
  color: #ffffff;
}

.btn-danger:active {
  background-color: #d9363e;
}

/* Image 组件样式 */
.image-component {
  display: block;
  margin: 0 auto;
}

/* Divider 组件样式 */
.divider-component {
  padding: 0 32rpx;
  position: relative;
  display: flex;
  align-items: center;
}

.divider-line {
  flex: 1;
}

.divider-text {
  padding: 0 24rpx;
  font-size: 24rpx;
  color: #666;
  background-color: #ffffff;
}

/* Space 组件样式 */
.space-component {
  flex-shrink: 0;
}

/* Container 组件样式 */
.container-component {
  width: 100%;
}

/* 未知组件样式 */
.unknown-component {
  padding: 32rpx;
  background-color: #fff2f0;
  border: 2rpx dashed #ffccc7;
  border-radius: 8rpx;
  margin: 16rpx 32rpx;
  text-align: center;
}

.unknown-text {
  font-size: 26rpx;
  color: #ff4d4f;
}
</style>