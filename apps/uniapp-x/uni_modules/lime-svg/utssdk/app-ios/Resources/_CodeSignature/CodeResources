<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/unimoduleLimeSvg-Swift.h</key>
		<data>
		ZAFtq3SpJcrTktt5ty6bKx5SjbE=
		</data>
		<key>Headers/unimoduleLimeSvg.h</key>
		<data>
		FH2SRnQAHIO8AbDZPa5/Bz/CcdA=
		</data>
		<key>Info.plist</key>
		<data>
		SECVeyxZywH2skYEys8lD82L9ic=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		IhA8PKd525VdS98DEH+6b9Clqhc=
		</data>
		<key>Modules/unimoduleLimeSvg.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		9Jl3lvV8eus/w+PhfpwuRB5aPWg=
		</data>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		7+rVgNvBGQmcD7+gEA3uN26z3qw=
		</data>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		xX0K3FhLkkWFdXcCvT26ocOD9VM=
		</data>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		5P+FoAZLOwzDALC1B7KcS2N9OCU=
		</data>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		xX0K3FhLkkWFdXcCvT26ocOD9VM=
		</data>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		zMybHpH5GBztWvkx8rVDF38chOM=
		</data>
		<key>config.json</key>
		<data>
		jo/qJhWkc5CQPkmEfWKkUS9c85Q=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/unimoduleLimeSvg-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Chw0MoUhe/BIGhvhiOOhZARBTWfalvswZXAeTn3tCWU=
			</data>
		</dict>
		<key>Headers/unimoduleLimeSvg.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XAsze1m7Z4PhyI2gj+RafZVS4xyqWjgvqraTawdVPMY=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			yItRRdWTDBHEqWXkBQ66ykO15PDNTn29XmtZY5MeesA=
			</data>
		</dict>
		<key>Modules/unimoduleLimeSvg.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			WpgLzGIun9mQZX3GXNQQF/wmAv0n/qzfMUe2Ac8rJno=
			</data>
		</dict>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UKJzsijDj8RUY3ZNrJvm0x8i27BkTuvAH2Uw1AlwG8Y=
			</data>
		</dict>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			WpeX9z6CL/3MC/eIryWdQPudnYw3zcAsfE5ITMzxodc=
			</data>
		</dict>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			X+5lpMhExxBrexdeBfZ+LaDMBrqAOgDKlXQ0vxQ3waI=
			</data>
		</dict>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			WpeX9z6CL/3MC/eIryWdQPudnYw3zcAsfE5ITMzxodc=
			</data>
		</dict>
		<key>Modules/unimoduleLimeSvg.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			yS8gsF8Zn4y85boJM0ctR2VPZ9Y/EEK/agE0HHQj+s0=
			</data>
		</dict>
		<key>config.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Fo/X5AmNDDUqJ4o8kFF60K9pTDiQEafR9Rzvsmrg4Mk=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
