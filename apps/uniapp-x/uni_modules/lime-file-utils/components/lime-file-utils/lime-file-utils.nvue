<template>
	<view>
		<!-- <text>测试</text> -->
		<image :src="url"></image>
		<image :src="src"></image>
		<button @click="onClick">选择图片nvue</button>
	</view>
</template>

<script>
	import { fileToDataURL, dataURLToFile, processFile, ProcessFileOptions } from '@/uni_modules/lime-file-utils'
	
	export default {
		data() {
			return {
				url: '',
				src: '',
				
			}
		},
		
		mounted() {
			const base64 = `data:image/png;base64,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`
			// processFile({
			// 	type: 'toDataURL',
			// 	path: '/static/logo.png',
			// 	// path: '/storage/emulated/0/Android/data/uni.UNI9D581D0/cache/1719285074362.png',
			// 	success: (res)=>{
			// 		this.url = res;
			// 		console.log('????????1')
			// 	},
			// 	fail(err) {
			// 		console.log('????????2')
			// 	}
			// })
			const base642 = `data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,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`
			processFile({
				type: 'toFile',
				path: base64,
				success: (res)=>{
					console.log('processFile', res)
					// src.value = res
					this.src = res
				},
				fail(err) {
					console.log('processFile',err)
				}
			})
		},
		methods:{
			onClick() {
				
			}
		}
	}
	

</script>

<style>

</style>