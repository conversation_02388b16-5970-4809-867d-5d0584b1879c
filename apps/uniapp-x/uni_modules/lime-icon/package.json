{"id": "lime-icon", "displayName": "lime-icon 图标 iconify 图标集合", "version": "0.3.6", "description": "lime-icon 图标插件可方便快捷按需的使用iconify图标，iconify超过150,000个开源矢量图标，插件内置tdesign icon。使用兼容uniapp/uniappx", "keywords": ["icon", "iconify", "图标集合", "按需加载"], "repository": "", "engines": {"HBuilderX": "^3.8.7", "uni-app": "^4.54", "uni-app-x": "^4.61"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "", "darkmode": "x", "i18n": "x", "widescreen": "x"}, "uni_modules": {"dependencies": ["lime-style", "lime-shared", "lime-svg"], "encrypt": [], "platforms": {"cloud": {"tcb": "√", "aliyun": "x", "alipay": "x"}, "client": {"uni-app": {"vue": {"vue2": "√", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "-", "android": {"extVersion": "", "minVersion": "21"}, "ios": "√", "harmony": "√"}, "mp": {"weixin": "√", "alipay": "√", "toutiao": "-", "baidu": "-", "kuaishou": "-", "jd": "-", "harmony": "-", "qq": "-", "lark": "-"}, "quickapp": {"huawei": "-", "union": "-"}}, "uni-app-x": {"web": {"safari": "√", "chrome": "√"}, "app": {"android": {"extVersion": "", "minVersion": "21"}, "ios": "√", "harmony": "√"}, "mp": {"weixin": "√"}}}}}}