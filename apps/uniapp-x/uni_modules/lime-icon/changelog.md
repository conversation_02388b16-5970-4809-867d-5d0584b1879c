## 0.3.6（2025-07-16）
- fix： 因增加依赖，没有主动下载导致报错
## 0.3.5（2025-07-15）
- chore： 更新文档
## 0.3.4（2025-07-14）
- chore: 更新文档
## 0.3.3（2025-07-14）
- feat: ios使用真的加载json的方式
## 0.3.2（2025-07-08）
- fix: 支付福小程序报错问题
## 0.3.1（2025-06-30）
- fix: 微信小程序svg不显示的问题
## 0.3.0（2025-06-26）
- chore: 更新文档
## 0.2.9（2025-05-30）
- fix: 修复uniapp自定义图标的问题
## 0.2.8（2025-05-30）
- fix: 修复自定义图标的问题
## 0.2.7（2025-05-10）
- feat: 增加组件提示
## 0.2.6（2025-04-22）
- feat: 颜色改为可选
## 0.2.5（2025-04-21）
- chore: 更新文档
## 0.2.4（2025-04-21）
- feat: 兼容鸿蒙next
## 0.2.3（2025-04-09）
- feat: 如果是字体字符则直接显示
## 0.2.2（2025-03-20）
- feat: 如果name不为字符字会报错，加强处理
## 0.2.1（2025-03-06）
- chore: 更新文档
## 0.2.0（2025-02-08）
- chore: 更新文档
## 0.1.9（2025-02-07）
- fix: 去掉app端字体默认宽度
## 0.1.8（2025-01-19）
- fix: 修复nvue加载字体文体问题
## 0.1.7（2025-01-13）
- chore: 去掉多余判断
## 0.1.6（2025-01-13）
- fix: 修复因json文件错误导致ios报错问题
## 0.1.5（2025-01-02）
- feat: hbx4.44会有css深度选择器报黄的问题？
## 0.1.4（2025-01-02）
- feat: uniapp x app 取消文本默认大小
## 0.1.3（2025-01-01）
- feat: 增加lStyle
## 0.1.2（2024-11-22）
- chore: 更新到hbx4.35
## 0.1.1（2024-10-23）
- chore: 更新到hbx4.31
## 0.1.0（2024-10-07）
- fix: ios 默认大小
## 0.0.9（2024-10-06）
- fix: name有大写的情况
## 0.0.8（2024-09-29）
- chore: 更新 非uvue app size使用css变量
## 0.0.7（2024-07-23）
- fix: 更新 vue2 使用方法
## 0.0.6（2024-07-22）
- fix: 修复vue3 h5不显示的问题
## 0.0.5（2024-07-22）
- fix: 修复vue2小程序class不显示的问题
## 0.0.4（2024-07-21）
- feat: 兼容uniappx
## 0.0.3（2023-09-05）
- chore: 更新文档
- feat: 默认使用官方API
## 0.0.2（2023-08-13）
- chore: 更新文档
## 0.0.1（2023-08-13）
- 初次上传 可能存在BUG
