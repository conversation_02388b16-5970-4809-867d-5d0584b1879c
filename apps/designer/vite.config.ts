import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      imports: ['vue', 'vue-router', 'pinia'],
      dts: true
    }),
    Components({
      resolvers: [
        AntDesignVueResolver({
          importStyle: false
        })
      ],
      dts: true
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@lowcode/aslib': resolve(__dirname, '../../packages/aslib/src'),
      '@lowcode/aslib/ui': resolve(__dirname, '../../packages/aslib/src/ui'),
      '@lowcode/aslib/core': resolve(__dirname, '../../packages/aslib/src/core'),
      '@lowcode/aslib/hooks': resolve(__dirname, '../../packages/aslib/src/hooks')
    }
  },
  server: {
    port: 3001,
    host: true
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  },
  optimizeDeps: {
    include: ['monaco-editor']
  },
  define: {
    // Monaco编辑器需要的全局变量
    global: 'globalThis'
  }
})
