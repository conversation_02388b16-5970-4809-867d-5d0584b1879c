{"name": "@lowcode/designer", "version": "0.1.0", "description": "Low-code visual designer", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@iconify/vue": "^4.1.1", "@lowcode/aslib": "workspace:*", "@vueuse/core": "^10.9.0", "ant-design-vue": "^4.2.3", "axios": "^1.7.2", "monaco-editor": "^0.47.0", "pinia": "^2.1.7", "sortablejs": "^1.15.2", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "@vue/tsconfig": "^0.5.1", "sass": "^1.77.4", "typescript": "^5.4.0", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.0", "vite": "^5.2.8", "vue-tsc": "^2.0.11"}}