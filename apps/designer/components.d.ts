/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('ant-design-vue/es')['Alert']
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    ABreadcrumb: typeof import('ant-design-vue/es')['Breadcrumb']
    ABreadcrumbItem: typeof import('ant-design-vue/es')['BreadcrumbItem']
    AButton: typeof import('ant-design-vue/es')['Button']
    AButtonGroup: typeof import('ant-design-vue/es')['ButtonGroup']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ACol: typeof import('ant-design-vue/es')['Col']
    ACollapse: typeof import('ant-design-vue/es')['Collapse']
    ACollapsePanel: typeof import('ant-design-vue/es')['CollapsePanel']
    AddEventModal: typeof import('./src/components/events/AddEventModal.vue')['default']
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions']
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADrawer: typeof import('ant-design-vue/es')['Drawer']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputGroup: typeof import('ant-design-vue/es')['InputGroup']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    AList: typeof import('ant-design-vue/es')['List']
    AListItem: typeof import('ant-design-vue/es')['ListItem']
    AListItemMeta: typeof import('ant-design-vue/es')['ListItemMeta']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuDivider: typeof import('ant-design-vue/es')['MenuDivider']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    APagination: typeof import('ant-design-vue/es')['Pagination']
    APopconfirm: typeof import('ant-design-vue/es')['Popconfirm']
    AppHeader: typeof import('./src/components/AppDetail/AppHeader.vue')['default']
    AppStats: typeof import('./src/components/AppDetail/AppStats.vue')['default']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARow: typeof import('ant-design-vue/es')['Row']
    ArrayField: typeof import('./src/components/ArrayField.vue')['default']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOptGroup: typeof import('ant-design-vue/es')['SelectOptGroup']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASlider: typeof import('ant-design-vue/es')['Slider']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    AStatistic: typeof import('ant-design-vue/es')['Statistic']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATypographyText: typeof import('ant-design-vue/es')['TypographyText']
    CanvasComponent: typeof import('./src/components/CanvasComponent.vue')['default']
    ClickableAreasEditor: typeof import('./src/components/config/ClickableAreasEditor.vue')['default']
    ClickableElementsEditor: typeof import('./src/components/config/ClickableElementsEditor.vue')['default']
    CodeEditor: typeof import('./src/components/events/CodeEditor.vue')['default']
    ColorPicker: typeof import('./src/components/ColorPicker.vue')['default']
    ComponentEventsEditor: typeof import('./src/components/ComponentEventsEditor.vue')['default']
    ComponentLibrary: typeof import('./src/components/ComponentLibrary.vue')['default']
    ComponentPropsEditor: typeof import('./src/components/ComponentPropsEditor.vue')['default']
    ComponentStyleEditor: typeof import('./src/components/ComponentStyleEditor.vue')['default']
    CustomCodeConfig: typeof import('./src/components/events/CustomCodeConfig.vue')['default']
    DesignerCanvas: typeof import('./src/components/DesignerCanvas.vue')['default']
    DesignerHeader: typeof import('./src/components/DesignerHeader.vue')['default']
    ErrorDisplay: typeof import('./src/components/ErrorDisplay.vue')['default']
    EventConfigForm: typeof import('./src/components/events/EventConfigForm.vue')['default']
    EventHelpCard: typeof import('./src/components/events/EventHelpCard.vue')['default']
    EventIdInput: typeof import('./src/components/form/EventIdInput.vue')['default']
    FormField: typeof import('./src/components/FormField.vue')['default']
    HomePageManager: typeof import('./src/components/AppDetail/HomePageManager.vue')['default']
    IconGrid: typeof import('./src/components/IconGrid.vue')['default']
    IconPicker: typeof import('./src/components/IconPicker.vue')['default']
    IconSelector: typeof import('./src/components/IconSelector.vue')['default']
    MenuItemsEditor: typeof import('./src/components/config/MenuItemsEditor.vue')['default']
    MonacoEditor: typeof import('./src/components/MonacoEditor.vue')['default']
    NavigationConfig: typeof import('./src/components/events/NavigationConfig.vue')['default']
    PageList: typeof import('./src/components/AppDetail/PageList.vue')['default']
    PagePropertiesEditor: typeof import('./src/components/PagePropertiesEditor.vue')['default']
    PageSelector: typeof import('./src/components/PageSelector.vue')['default']
    PropertiesPanel: typeof import('./src/components/PropertiesPanel.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TabBarConfig: typeof import('./src/components/TabBarConfig.vue')['default']
    TabBarItemEditor: typeof import('./src/components/TabBarItemEditor.vue')['default']
    TabBarManager: typeof import('./src/components/AppDetail/TabBarManager.vue')['default']
    TabBarModal: typeof import('./src/components/AppDetail/TabBarModal.vue')['default']
    TabBarStyleSettings: typeof import('./src/components/AppDetail/TabBarStyleSettings.vue')['default']
    TabBarTabManager: typeof import('./src/components/AppDetail/TabBarTabManager.vue')['default']
  }
}
