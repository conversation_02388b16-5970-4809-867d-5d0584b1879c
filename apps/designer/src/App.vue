<template>
  <div id="app">
    <router-view />
    <!-- 全局错误显示组件 -->
    <ErrorDisplay ref="errorDisplayRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ErrorDisplay from './components/ErrorDisplay.vue'

// 错误显示组件引用
const errorDisplayRef = ref<InstanceType<typeof ErrorDisplay>>()

// 全局错误处理
onMounted(() => {
  // 捕获未处理的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    // 这些错误会被全局错误处理器自动处理
  })

  // 捕获JavaScript运行时错误
  window.addEventListener('error', (event) => {
    console.error('JavaScript error:', event.error)
    // 这些错误会被全局错误处理器自动处理
  })
})

// 暴露错误显示方法给全局使用
if (typeof window !== 'undefined') {
  (window as any).showErrorHistory = () => {
    errorDisplayRef.value?.showErrorHistory()
  }
}
</script>

<style lang="scss">
#app {
  width: 100%;
  min-height: 100vh;
  /* 移除 overflow: hidden，允许页面滚动 */
}

// 全局样式重置
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 确保页面可以正常滚动 */
  overflow-x: hidden;
  overflow-y: auto;
}

// 设计器特定样式
.designer-layout {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.designer-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.designer-sidebar {
  width: 300px;
  background: #fafafa;
  border-right: 1px solid #e8e8e8;
  overflow-y: auto;
}

.designer-canvas {
  flex: 1;
  background: #f0f2f5;
  position: relative;
  overflow: auto;
}

.designer-properties {
  width: 320px;
  background: #fafafa;
  border-left: 1px solid #e8e8e8;
  overflow-y: auto;
}

// 拖拽相关样式
.draggable-item {
  cursor: move;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #e6f7ff;
    border-color: #1890ff;
  }
  
  &.dragging {
    opacity: 0.5;
  }
}

.drop-zone {
  min-height: 100px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  transition: all 0.2s ease;
  
  &.drag-over {
    border-color: #1890ff;
    background-color: #e6f7ff;
    color: #1890ff;
  }
}

.component-outline {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
  position: relative;
  
  &::before {
    content: attr(data-component-type);
    position: absolute;
    top: -24px;
    left: 0;
    background: #1890ff;
    color: white;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 2px;
    z-index: 10;
  }
}

.component-selected {
  outline: 2px solid #52c41a;
  outline-offset: 2px;
}
</style>
