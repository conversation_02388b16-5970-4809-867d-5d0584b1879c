/// <reference types="vite/client" />
/// <reference types="@lowcode/core/types/env" />

// PC设计器特定的环境变量扩展
interface ImportMetaEnv {
  // 设计器特定配置
  readonly VITE_DESIGNER_TITLE: string
  readonly VITE_DESIGNER_THEME: string
  readonly VITE_DESIGNER_LANGUAGE: string
  
  // 编辑器配置
  readonly VITE_MONACO_CDN: string
  readonly VITE_MONACO_THEME: string
  readonly VITE_CODE_COMPLETION: string
  
  // 预览配置
  readonly VITE_PREVIEW_MODE: string
  readonly VITE_PREVIEW_DEVICE: string
  readonly VITE_PREVIEW_SCALE: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
