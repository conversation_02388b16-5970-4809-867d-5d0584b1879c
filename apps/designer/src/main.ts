import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// 导入Ant Design Vue
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'

// 导入低代码组件库
import LowcodeUI, { setAPIClient, setDataManagerConfig } from '@lowcode/aslib/ui'

// ✅ 导入组件管理器
import { designerComponentManager, initializeComponents } from './utils/componentRegistry'

// 导入样式
import './styles/index.scss'

const app = createApp(App)

// 配置Pinia
const pinia = createPinia()

// 注册Ant Design Vue
app.use(Antd)

// 注册低代码UI组件库
app.use(LowcodeUI)

app.use(pinia)
app.use(router)

// ✅ 异步初始化组件管理器并启动应用
async function initializeApp() {
  try {
    console.log('🎨 初始化设计器...')

    // ✅ 设置数据管理器为设计器环境
    setDataManagerConfig({
      environment: 'designer'
    })

    // ✅ 配置API客户端（模拟）
    setAPIClient({
      // 套餐相关
      getPackageList: async (params) => {
        console.log('🌐 模拟获取套餐列表:', params)
        return {
          code: 200,
          data: [
            { id: 1, name: '基础套餐', packagePrice: 30, packageTotal: 5120, popular: false },
            { id: 2, name: '标准套餐', packagePrice: 60, packageTotal: 15360, popular: true },
            { id: 3, name: '高级套餐', packagePrice: 120, packageTotal: 51200, popular: false }
          ],
          msg: '获取成功'
        }
      },

      // 支付相关
      getPaymentMethods: async (params) => {
        console.log('🌐 模拟获取支付方式:', params)
        return {
          code: 200,
          data: [
            { type: 'balance', name: '余额支付' },
            { type: 'wechat', name: '微信支付' },
            { type: 'alipay', name: '支付宝' }
          ],
          msg: '获取成功'
        }
      },

      createPayment: async (params) => {
        console.log('🌐 模拟创建支付:', params)
        return {
          code: 200,
          data: { orderId: 'mock-order-123', payUrl: 'mock-pay-url' },
          msg: '创建成功'
        }
      },

      // 设备相关
      getDeviceInfo: async () => {
        console.log('🌐 模拟获取设备信息')
        return {
          code: 200,
          data: { deviceNo: 'TEST-001', balance: 100, status: 'active' },
          msg: '获取成功'
        }
      },

      getDeviceCards: async () => {
        console.log('🌐 模拟获取设备卡片')
        return {
          code: 200,
          data: [
            { id: 1, name: '设备1', status: 'online' },
            { id: 2, name: '设备2', status: 'offline' }
          ],
          msg: '获取成功'
        }
      }
    })

    // ✅ 初始化组件管理器
    await designerComponentManager.initialize()

    // ✅ 设置组件准备状态
    await initializeComponents()

    console.log('✅ 设计器初始化完成')
  } catch (error) {
    console.error('❌ 设计器初始化失败:', error)
  } finally {
    // 无论初始化是否成功都要挂载应用
    app.mount('#app')
  }
}

// 启动应用
initializeApp()
