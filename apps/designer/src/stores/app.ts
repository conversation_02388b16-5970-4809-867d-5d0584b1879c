import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { appApi, pageConfigApi, type AppConfig, type TabConfig } from '../utils/api'

export const useAppStore = defineStore('app', () => {
  // 状态
  const apps = ref<AppConfig[]>([])
  const currentApp = ref<AppConfig | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const currentAppPages = ref<any[]>([])

  // 获取应用列表
  async function getAppList(): Promise<boolean> {
    loading.value = true
    error.value = null

    try {
      console.log('🔄 获取应用列表...')
      const result = await appApi.getList()
      
      if (result.code === 1 && result.data) {
        apps.value = result.data.list
        console.log(`✅ 获取到 ${result.data.list.length} 个应用`)
        return true
      } else {
        error.value = result.msg || '获取应用列表失败'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取应用列表失败'
      console.error('❌ 获取应用列表失败:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  // 获取单个应用
  async function getApp(appId: string): Promise<boolean> {
    loading.value = true
    error.value = null

    try {
      console.log('🔄 获取应用信息:', appId)
      const result = await appApi.getById(appId)
      
      if (result.code && result.data) {
        currentApp.value = result.data
        console.log('✅ 应用信息获取成功')
        return true
      } else {
        error.value = result.msg || '获取应用信息失败'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取应用信息失败'
      console.error('❌ 获取应用信息失败:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  // 获取应用的页面列表
  async function getAppPages(appId: string): Promise<boolean> {
    loading.value = true
    error.value = null

    try {
      console.log('🔄 获取应用页面列表:', appId)
      const result = await appApi.getPages(appId)
      
      if (result.code && result.data) {
        // 确保 result.data.list 存在且是数组
        const pageList = result.data.list || result.data || []
        currentAppPages.value = Array.isArray(pageList) ? pageList : []
        console.log(`✅ 获取到 ${currentAppPages.value.length} 个页面`)
        return true
      } else {
        currentAppPages.value = []
        error.value = result.msg || '获取应用页面列表失败'
        return false
      }
    } catch (err) {
      currentAppPages.value = []
      error.value = err instanceof Error ? err.message : '获取应用页面列表失败'
      console.error('❌ 获取应用页面列表失败:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  // 创建应用
  async function createApp(app: Partial<AppConfig>): Promise<boolean> {
    loading.value = true
    error.value = null

    try {
      console.log('🔄 创建应用:', app.name)
      const result = await appApi.create(app)
      
      if (result.code && result.data) {
        apps.value.push(result.data)
        console.log('✅ 应用创建成功')
        return true
      } else {
        error.value = result.msg || '创建应用失败'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建应用失败'
      console.error('❌ 创建应用失败:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  // 更新应用
  async function updateApp(appId: string, updates: Partial<AppConfig>): Promise<boolean> {
    loading.value = true
    error.value = null

    try {
      console.log('🔄 更新应用:', appId)
      const result = await appApi.update(appId, updates)
      
      if (result.code && result.data) {
        // 更新本地数据
        const index = apps.value.findIndex(app => app.id === appId)
        if (index !== -1) {
          apps.value[index] = result.data
        }
        if (currentApp.value?.id === appId) {
          currentApp.value = result.data
        }
        console.log('✅ 应用更新成功')
        return true
      } else {
        error.value = result.msg || '更新应用失败'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新应用失败'
      console.error('❌ 更新应用失败:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  // 删除应用
  async function deleteApp(appId: string): Promise<boolean> {
    loading.value = true
    error.value = null

    try {
      console.log('🔄 删除应用:', appId)
      const result = await appApi.delete(appId)
      
      if (result.code) {
        // 更新本地数据
        apps.value = apps.value.filter(app => app.id !== appId)
        if (currentApp.value?.id === appId) {
          currentApp.value = null
        }
        console.log('✅ 应用删除成功')
        return true
      } else {
        error.value = result.msg || '删除应用失败'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除应用失败'
      console.error('❌ 删除应用失败:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  // 发布应用
  async function publishApp(appId: string): Promise<boolean> {
    loading.value = true
    error.value = null

    try {
      console.log('🔄 发布应用:', appId)
      const result = await appApi.publish(appId)

      if (result.code && result.data) {
        // 更新本地数据
        const index = apps.value.findIndex(app => app.id === appId)
        if (index !== -1) {
          apps.value[index] = result.data
        }
        if (currentApp.value?.id === appId) {
          currentApp.value = result.data
        }
        console.log('✅ 应用发布成功')
        return true
      } else {
        error.value = result.msg || '发布应用失败'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '发布应用失败'
      console.error('❌ 发布应用失败:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  // 取消发布应用
  async function unpublishApp(appId: string): Promise<boolean> {
    loading.value = true
    error.value = null

    try {
      console.log('🔄 取消发布应用:', appId)
      const result = await appApi.unpublish(appId)

      if (result.code && result.data) {
        // 更新本地数据
        const index = apps.value.findIndex(app => app.id === appId)
        if (index !== -1) {
          apps.value[index] = result.data
        }
        if (currentApp.value?.id === appId) {
          currentApp.value = result.data
        }
        console.log('✅ 应用取消发布成功')
        return true
      } else {
        error.value = result.msg || '取消发布应用失败'
        return false
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '取消发布应用失败'
      console.error('❌ 取消发布应用失败:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  // 设置当前应用
  function setCurrentApp(app: AppConfig | null) {
    currentApp.value = app
  }

  return {
    // 状态
    apps,
    currentApp,
    currentAppPages,
    loading,
    error,

    // 方法
    getAppList,
    getApp,
    getAppPages,
    createApp,
    updateApp,
    deleteApp,
    publishApp,
    unpublishApp,
    setCurrentApp
  }
})
