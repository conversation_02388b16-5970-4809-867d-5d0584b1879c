import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { ComponentConfig } from '@lowcode/aslib/core'
import { generateId } from '@lowcode/aslib/core'
import { designerComponentManager, type ComponentLibraryItem } from '../utils/componentRegistry'
import { ComponentEvents } from '@lowcode/aslib/ui'

// 本地组件库项目接口（用于store内部）
export interface LocalComponentLibraryItem {
  id: string
  name: string
  type: string
  category: string
  icon: string
  description: string
  tags?: string[]
  isContainer?: boolean
  defaultProps: Record<string, any>
  defaultStyle: Record<string, any>
  configSchema?: any
}





export const useComponentLibraryStore = defineStore('componentLibrary', () => {
  // 动态组件库数据
  const components = ref<LocalComponentLibraryItem[]>([])
  const loading = ref(false)

  // ✅ 重构后的组件加载逻辑
  async function loadRegisteredComponents() {
    if (loading.value) return

    loading.value = true
    console.log('🔄 加载组件库...')

    try {
      // ✅ 确保组件管理器已初始化
      await designerComponentManager.initialize()

      // ✅ 直接从组件管理器获取组件信息
      const componentLibrary = designerComponentManager.getComponentLibrary()

      // ✅ 转换为本地组件库格式
      components.value = componentLibrary.map(item => {
        // 获取组件的配置模式和默认配置
        const configSchema = designerComponentManager.getComponentConfigSchema(item.type)
        const defaultConfig = designerComponentManager.getComponentDefaultConfig(item.type)

        return {
          id: item.type.toLowerCase().replace(/([A-Z])/g, '-$1').substring(1),
          name: item.metadata.name,
          type: item.type,
          category: item.metadata.category,
          icon: item.metadata.icon,
          description: item.metadata.description,
          tags: item.metadata.tags,
          isContainer: item.metadata.isContainer,
          defaultProps: defaultConfig?.props || {},
          defaultStyle: defaultConfig?.style || {},
          configSchema: configSchema || {}
        }
      })

      console.log(`✅ 组件库加载完成，共 ${components.value.length} 个组件`)

    } catch (error) {
      console.error('❌ 组件库加载失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 分类数据
  const categories = ref([
    { id: 'all', name: '全部', icon: 'mdi:apps' },
    { id: 'business', name: '业务组件', icon: 'mdi:briefcase' },
    { id: 'layout', name: '布局组件', icon: 'mdi:view-dashboard' },
    { id: 'form', name: '表单组件', icon: 'mdi:form-select' },
    { id: 'display', name: '展示组件', icon: 'mdi:eye' },
    { id: 'other', name: '其他组件', icon: 'mdi:puzzle' }
  ])

  const selectedCategory = ref('all')
  const searchKeyword = ref('')

  // 过滤后的组件
  const filteredComponents = computed(() => {
    let filtered = components.value

    // 按分类过滤
    if (selectedCategory.value !== 'all') {
      filtered = filtered.filter(component => component.category === selectedCategory.value)
    }

    // 按关键词搜索
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase()
      filtered = filtered.filter(component =>
        component.name.toLowerCase().includes(keyword) ||
        component.description.toLowerCase().includes(keyword) ||
        component.type.toLowerCase().includes(keyword)
      )
    }

    return filtered
  })

  // 根据类型获取组件
  function getComponentByType(type: string): LocalComponentLibraryItem | undefined {
    return components.value.find(component => component.type === type)
  }

  // 创建默认事件配置
  function createDefaultEvents(componentType: string): Record<string, any> {
    const events: Record<string, any> = {}

    console.log('🎯 为组件创建默认事件:', componentType)

    // 获取组件的可用事件
    const componentEvents = ComponentEvents[componentType]
    if (!componentEvents) {
      console.log('⚠️ 组件没有定义事件:', componentType)
      return events
    }

    console.log('📋 组件可用事件:', Object.keys(componentEvents))

    // 为每个事件创建默认配置
    Object.keys(componentEvents).forEach(eventName => {
      events[eventName] = createDefaultEventConfig(eventName, componentType)
      console.log(`✅ 创建默认事件 ${eventName}:`, events[eventName])
    })

    console.log('🎉 默认事件创建完成:', events)
    return events
  }

  // 创建默认事件配置
  function createDefaultEventConfig(eventName: string, componentType: string) {
    // 根据事件名称定制默认行为
    switch (eventName) {
      // 页面跳转类事件 - 使用H5应用中实际存在的路由
      case 'more':
        return {
          type: 'navigate' as const,
          navigateType: 'page',
          target: '/home',  // 跳转到首页，用户可以看到更多功能
          method: 'GET',
          onSuccess: 'none',
          params: {},
          webviewTitle: '更多功能',
          webviewOptions: ['showNavBar', 'showBackButton']
        }

      case 'renew':
      case 'package':
        return {
          type: 'navigate' as const,
          navigateType: 'page',
          target: '/PackageList',  // 套餐列表页面
          method: 'GET',
          onSuccess: 'none',
          params: {},
          webviewTitle: '套餐充值',
          webviewOptions: ['showNavBar', 'showBackButton']
        }

      case 'recharge':
      case 'balance':
        return {
          type: 'navigate' as const,
          navigateType: 'page',
          target: '/BalanceList',  // 余额充值页面
          method: 'GET',
          onSuccess: 'none',
          params: {},
          webviewTitle: '余额充值',
          webviewOptions: ['showNavBar', 'showBackButton']
        }

      case 'wifi':
        return {
          type: 'navigate' as const,
          navigateType: 'page',
          target: '/EditDevice',  // 设备设置页面
          method: 'GET',
          onSuccess: 'none',
          params: {},
          webviewTitle: 'Wi-Fi设置',
          webviewOptions: ['showNavBar', 'showBackButton']
        }

      case 'history':
        return {
          type: 'navigate' as const,
          navigateType: 'page',
          target: '/BalanceDetails',  // 余额明细页面
          method: 'GET',
          onSuccess: 'none',
          params: {},
          webviewTitle: '余额明细',
          webviewOptions: ['showNavBar', 'showBackButton']
        }

      case 'password':
        return {
          type: 'navigate' as const,
          navigateType: 'page',
          target: '/EditPassword',  // 支付密码页面
          method: 'GET',
          onSuccess: 'none',
          params: {},
          webviewTitle: '支付密码',
          webviewOptions: ['showNavBar', 'showBackButton']
        }

      case 'service':
        return {
          type: 'navigate' as const,
          navigateType: 'page',
          target: '/LayoutService',  // 客服页面
          method: 'GET',
          onSuccess: 'none',
          params: {},
          webviewTitle: '联系客服',
          webviewOptions: ['showNavBar', 'showBackButton']
        }

      // 特殊操作类事件
      case 'logout':
        return {
          type: 'custom' as const,
          preset: 'showConfirm',
          confirmTitle: '退出登录',
          confirmMessage: '确定要退出登录吗？',
          confirmAction: 'navigate',
          navigateUrl: '/login',
          navigateType: 'page',
          method: 'GET',
          onSuccess: 'none',
          params: {},
          webviewTitle: '',
          webviewOptions: ['showNavBar', 'showBackButton']
        }

      case 'switch-network':
        return {
          type: 'custom' as const,
          preset: 'showMessage',
          message: '网络切换功能开发中...',
          navigateType: 'page',
          method: 'GET',
          onSuccess: 'none',
          params: {},
          webviewTitle: '',
          webviewOptions: ['showNavBar', 'showBackButton']
        }

      case 'refresh':
        return {
          type: 'custom' as const,
          preset: 'showMessage',
          message: '正在刷新设备信息...',
          navigateType: 'page',
          method: 'GET',
          onSuccess: 'none',
          params: {},
          webviewTitle: '',
          webviewOptions: ['showNavBar', 'showBackButton']
        }

      // 默认事件（简单反馈）
      default:
        return {
          type: 'custom' as const,
          preset: 'showMessage',
          message: `${eventName}事件被触发`,
          navigateType: 'page',
          method: 'GET',
          onSuccess: 'none',
          params: {},
          webviewTitle: '',
          webviewOptions: ['showNavBar', 'showBackButton']
        }
    }
  }

  // ✅ 创建组件配置
  function createComponentConfig(componentType: string): ComponentConfig {
    const libraryItem = getComponentByType(componentType)

    if (!libraryItem) {
      throw new Error(`未找到组件类型: ${componentType}`)
    }

    return {
      id: generateId('component'),
      type: componentType,
      props: { ...libraryItem.defaultProps },
      style: { ...libraryItem.defaultStyle },
      events: createDefaultEvents(componentType), // 🎉 自动创建默认事件
      dataSource: undefined,
      visible: true,
      editable: true
    }
  }

  // 设置选中的分类
  function setSelectedCategory(category: string) {
    selectedCategory.value = category
  }

  // 设置搜索关键词
  function setSearchKeyword(keyword: string) {
    searchKeyword.value = keyword
  }

  // ✅ 检查组件是否已注册
  function isComponentRegistered(type: string): boolean {
    return designerComponentManager.isComponentAvailable(type)
  }

  // 刷新组件库
  function refresh() {
    loadRegisteredComponents()
  }

  return {
    // 状态
    components,
    categories,
    selectedCategory,
    searchKeyword,
    loading,

    // 计算属性
    filteredComponents,

    // 方法
    getComponentByType,
    createComponentConfig,
    setSelectedCategory,
    setSearchKeyword,
    loadRegisteredComponents,
    isComponentRegistered,
    refresh
  }
})

