import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { PageConfig, ComponentConfig } from '@lowcode/aslib/core'
import { generateId, deepClone } from '@lowcode/aslib/core'
import { pageConfigApi } from '../utils/api'

export const useDesignerStore = defineStore('designer', () => {
  // 当前编辑的页面配置
  const currentPage = ref<PageConfig | null>(null)
  
  // 选中的组件ID
  const selectedComponentId = ref<string | null>(null)
  
  // 拖拽状态
  const isDragging = ref(false)
  const draggedComponent = ref<ComponentConfig | null>(null)
  
  // 历史记录
  const history = ref<PageConfig[]>([])
  const historyIndex = ref(-1)
  
  // 预览模式
  const isPreviewMode = ref(false)
  
  // 计算属性
  const selectedComponent = computed(() => {
    if (!currentPage.value || !selectedComponentId.value) return null
    return findComponentById(currentPage.value.components, selectedComponentId.value)
  })
  
  const canUndo = computed(() => historyIndex.value > 0)
  const canRedo = computed(() => historyIndex.value < history.value.length - 1)
  
  // 查找组件
  function findComponentById(components: ComponentConfig[], id: string): ComponentConfig | null {
    for (const component of components) {
      if (component.id === id) return component
      if (component.children) {
        const found = findComponentById(component.children, id)
        if (found) return found
      }
    }
    return null
  }
  
  // 设置当前页面
  function setCurrentPage(page: PageConfig) {
    currentPage.value = deepClone(page)
    saveToHistory()
  }
  
  // 创建新页面
  function createNewPage(name: string, path: string): PageConfig {
    const newPage: PageConfig = {
      id: generateId('page'),
      name,
      path,
      title: name,
      layout: {
        type: 'flex',
        direction: 'column',
        padding: 16,
        gap: 16
      },
      components: [],
      editable: true
    }
    
    setCurrentPage(newPage)
    return newPage
  }
  
  // 添加组件
  function addComponent(component: ComponentConfig, parentId?: string, index?: number) {
    if (!currentPage.value) return
    
    const newComponent = {
      ...deepClone(component),
      id: generateId('component')
    }
    
    if (parentId) {
      const parent = findComponentById(currentPage.value.components, parentId)
      if (parent) {
        if (!parent.children) parent.children = []
        if (typeof index === 'number') {
          parent.children.splice(index, 0, newComponent)
        } else {
          parent.children.push(newComponent)
        }
      }
    } else {
      if (typeof index === 'number') {
        currentPage.value.components.splice(index, 0, newComponent)
      } else {
        currentPage.value.components.push(newComponent)
      }
    }
    
    selectedComponentId.value = newComponent.id
    saveToHistory()
  }
  
  // 删除组件
  function removeComponent(componentId: string) {
    console.log('Store: 删除组件', componentId)
    if (!currentPage.value) return

    function removeFromArray(components: ComponentConfig[]): boolean {
      const index = components.findIndex(c => c.id === componentId)
      if (index > -1) {
        console.log('Store: 找到组件，索引:', index)
        components.splice(index, 1)
        return true
      }

      for (const component of components) {
        if (component.children && removeFromArray(component.children)) {
          return true
        }
      }
      return false
    }

    const removed = removeFromArray(currentPage.value.components)
    console.log('Store: 组件删除结果:', removed)

    if (selectedComponentId.value === componentId) {
      selectedComponentId.value = null
    }

    saveToHistory()
  }
  
  // 更新组件
  function updateComponent(componentId: string, updates: Partial<ComponentConfig>) {
    if (!currentPage.value) return

    // 特殊处理：如果是页面本身的更新
    if (componentId === currentPage.value.id) {
      // 直接更新页面对象
      if (updates.style !== undefined) {
        (currentPage.value as any).style = { ...updates.style }
      } else {
        Object.assign(currentPage.value, updates)
      }
      saveToHistory()
      console.log('🔄 页面更新完成:', { pageId: componentId, updates, newPage: currentPage.value })
      return
    }

    // 普通组件更新
    const component = findComponentById(currentPage.value.components, componentId)
    if (component) {
      console.log('🔍 更新前组件状态:', { componentId, currentEvents: component.events, updates })

      // 特殊处理样式更新，确保能删除属性
      if (updates.style !== undefined) {
        component.style = { ...updates.style }
      } else {
        Object.assign(component, updates)
      }

      console.log('🔍 更新后组件状态:', { componentId, newEvents: component.events })
      saveToHistory()
      console.log('🔄 组件更新完成:', { componentId, updates, newComponent: component })
    } else {
      console.error('❌ 找不到要更新的组件:', componentId)
    }
  }
  
  // 移动组件
  function moveComponent(componentId: string, targetParentId: string | null, targetIndex: number) {
    if (!currentPage.value) return
    
    // 先移除组件
    let movedComponent: ComponentConfig | null = null
    
    function removeFromArray(components: ComponentConfig[]): boolean {
      const index = components.findIndex(c => c.id === componentId)
      if (index > -1) {
        movedComponent = components.splice(index, 1)[0]
        return true
      }
      
      for (const component of components) {
        if (component.children && removeFromArray(component.children)) {
          return true
        }
      }
      return false
    }
    
    removeFromArray(currentPage.value.components)
    
    // 再添加到新位置
    if (movedComponent) {
      if (targetParentId) {
        const parent = findComponentById(currentPage.value.components, targetParentId)
        if (parent) {
          if (!parent.children) parent.children = []
          parent.children.splice(targetIndex, 0, movedComponent)
        }
      } else {
        currentPage.value.components.splice(targetIndex, 0, movedComponent)
      }
    }
    
    saveToHistory()
  }
  
  // 选择组件
  function selectComponent(componentId: string | null) {
    selectedComponentId.value = componentId
  }
  
  // 开始拖拽
  function startDrag(component: ComponentConfig) {
    isDragging.value = true
    draggedComponent.value = deepClone(component)
  }
  
  // 结束拖拽
  function endDrag() {
    isDragging.value = false
    draggedComponent.value = null
  }
  
  // 保存到历史记录
  function saveToHistory() {
    if (!currentPage.value) return
    
    // 移除当前位置之后的历史记录
    history.value = history.value.slice(0, historyIndex.value + 1)
    
    // 添加新的历史记录
    history.value.push(deepClone(currentPage.value))
    historyIndex.value = history.value.length - 1
    
    // 限制历史记录数量
    if (history.value.length > 50) {
      history.value.shift()
      historyIndex.value--
    }
  }
  
  // 撤销
  function undo() {
    if (canUndo.value) {
      historyIndex.value--
      currentPage.value = deepClone(history.value[historyIndex.value])
    }
  }
  
  // 重做
  function redo() {
    if (canRedo.value) {
      historyIndex.value++
      currentPage.value = deepClone(history.value[historyIndex.value])
    }
  }
  
  // 切换预览模式
  function togglePreviewMode() {
    isPreviewMode.value = !isPreviewMode.value
  }
  
  // 导出页面配置
  function exportPageConfig(): string {
    if (!currentPage.value) return '{}'
    return JSON.stringify(currentPage.value, null, 2)
  }
  
  // 导入页面配置
  function importPageConfig(configJson: string) {
    try {
      const config = JSON.parse(configJson) as PageConfig
      setCurrentPage(config)
      return true
    } catch (error) {
      console.error('Import page config failed:', error)
      return false
    }
  }

  // 保存页面配置到API
  async function savePageConfig(): Promise<boolean> {
    if (!currentPage.value) {
      console.error('没有页面配置可保存')
      return false
    }

    try {
      // 确保页面配置有正确的基本信息
      if (currentPage.value.appId) {
        currentPage.value.path = currentPage.value.path || '/home'
        currentPage.value.name = currentPage.value.name || '应用首页'
        currentPage.value.title = currentPage.value.title || '首页'
        currentPage.value.slug = currentPage.value.slug || 'home'
      }

      // 清理数据：移除后端不认识的字段
      const cleanPageData = {
        id: currentPage.value.id,
        appId: currentPage.value.appId,
        slug: currentPage.value.slug,
        name: currentPage.value.name,
        path: currentPage.value.path,
        title: currentPage.value.title,
        layout: currentPage.value.layout,
        style: currentPage.value.style,
        components: currentPage.value.components,
        dataSource: currentPage.value.dataSource,
        events: currentPage.value.events,
        published: typeof currentPage.value.published === 'boolean'
          ? (currentPage.value.published ? 1 : 0)
          : (currentPage.value.published || 0)
      }

      const identifier = currentPage.value.id || currentPage.value.slug
      console.log('🔄 保存页面配置到API:', identifier)

      // 检查页面是否已存在
      try {
        await pageConfigApi.getById(identifier)
        // 页面存在，执行更新
        const result = await pageConfigApi.update(currentPage.value.id, cleanPageData)
        if (result.code) {
          console.log('✅ 页面配置更新成功')
          return true
        } else {
          console.error('❌ 页面配置更新失败:', result.msg)
          return false
        }
      } catch (error) {
        // 页面不存在，执行创建
        const result = await pageConfigApi.create(cleanPageData)
        if (result.code) {
          console.log('✅ 页面配置创建成功')
          // 更新当前页面的ID为服务端返回的ID
          if (result.data && result.data.id) {
            currentPage.value.id = result.data.id
          }
          return true
        } else {
          console.error('❌ 页面配置创建失败:', result.msg)
          return false
        }
      }
    } catch (error) {
      console.error('❌ 保存页面配置失败:', error)
      return false
    }
  }

  // 从API加载页面配置
  async function loadPageConfig(pageId: string): Promise<boolean> {
    try {
      console.log('🔄 从API加载页面配置:', pageId)
      const result = await pageConfigApi.getById(pageId)

      if (result.code && result.data) {
        setCurrentPage(result.data)
        console.log('✅ 页面配置加载成功')
        return true
      } else {
        console.error('❌ 页面配置加载失败:', result.msg)
        return false
      }
    } catch (error) {
      console.error('❌ 加载页面配置失败:', error)
      return false
    }
  }

  // 发布页面配置
  async function publishPageConfig(pageId: string): Promise<boolean> {
    try {
      console.log('🔄 发布页面配置:', pageId)
      const result = await pageConfigApi.publish(pageId)

      if (result.code && result.data) {
        // 更新当前页面的发布状态
        if (currentPage.value && currentPage.value.id === pageId) {
          currentPage.value.published = true
          currentPage.value.publishTime = new Date().toISOString()
        }
        console.log('✅ 页面配置发布成功')
        return true
      } else {
        console.error('❌ 页面配置发布失败:', result.msg)
        return false
      }
    } catch (error) {
      console.error('❌ 发布页面配置失败:', error)
      return false
    }
  }

  // 🔧 新增：取消发布页面配置
  async function unpublishPageConfig(pageId: string): Promise<boolean> {
    try {
      console.log('🔄 取消发布页面配置:', pageId)
      const result = await pageConfigApi.unpublish(pageId)

      if (result.code && result.data) {
        // 更新当前页面的发布状态
        if (currentPage.value && currentPage.value.id === pageId) {
          currentPage.value.published = false
          currentPage.value.publishTime = undefined
        }
        console.log('✅ 页面配置取消发布成功')
        return true
      } else {
        console.error('❌ 页面配置取消发布失败:', result.msg)
        return false
      }
    } catch (error) {
      console.error('❌ 取消发布页面配置失败:', error)
      return false
    }
  }

  return {
    // 状态
    currentPage,
    selectedComponentId,
    isDragging,
    draggedComponent,
    isPreviewMode,

    // 计算属性
    selectedComponent,
    canUndo,
    canRedo,

    // 方法
    setCurrentPage,
    createNewPage,
    addComponent,
    removeComponent,
    updateComponent,
    moveComponent,
    selectComponent,
    startDrag,
    endDrag,
    undo,
    redo,
    togglePreviewMode,
    exportPageConfig,
    importPageConfig,
    savePageConfig,
    loadPageConfig,
    publishPageConfig,
    unpublishPageConfig
  }
})
