import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    redirect: '/apps'
  },
  {
    path: '/apps',
    name: 'AppManager',
    component: () => import('../views/AppManager.vue')
  },
  {
    path: '/app/:id',
    name: 'AppDetail',
    component: () => import('../views/AppDetail.vue'),
    props: true
  },
  {
    path: '/designer',
    name: 'Designer',
    component: () => import('../views/Designer.vue')
  },
  {
    path: '/designer/:pageId',
    name: 'DesignerPage',
    component: () => import('../views/Designer.vue'),
    props: true
  },
  {
    path: '/preview/:pageId',
    name: 'Preview',
    component: () => import('../views/Preview.vue'),
    props: true
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
