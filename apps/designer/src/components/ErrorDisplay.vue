<template>
  <div class="error-display">
    <!-- 错误通知 -->
    <a-notification
      v-for="error in visibleErrors"
      :key="error.timestamp"
      :type="getNotificationType(error.severity)"
      :message="error.userMessage || error.message"
      :description="error.details?.operation ? `操作: ${error.details.operation}` : undefined"
      :duration="getNotificationDuration(error.severity)"
      :closable="true"
      @close="() => dismissError(error)"
      class="error-notification"
    >
      <template #btn v-if="error.recoveryActions && error.recoveryActions.length > 0">
        <a-space>
          <a-button
            v-for="action in error.recoveryActions.slice(0, 2)"
            :key="action.label"
            :type="getActionButtonType(action.type)"
            size="small"
            @click="() => executeRecoveryAction(action, error)"
          >
            <Icon :icon="getActionIcon(action.type)" />
            {{ action.label }}
          </a-button>
        </a-space>
      </template>
    </a-notification>

    <!-- 错误历史面板 -->
    <a-drawer
      v-model:open="showErrorHistory"
      title="错误历史"
      placement="right"
      width="400"
    >
      <div class="error-history">
        <div class="error-history-header">
          <a-space>
            <a-button @click="clearErrorHistory" type="text" danger>
              <Icon icon="mdi:delete-sweep" />
              清除历史
            </a-button>
            <a-button @click="exportErrorHistory" type="text">
              <Icon icon="mdi:download" />
              导出日志
            </a-button>
          </a-space>
        </div>

        <a-list
          :data-source="errorHistory"
          :pagination="{ pageSize: 10 }"
          item-layout="vertical"
        >
          <template #renderItem="{ item: error }">
            <a-list-item>
              <a-list-item-meta>
                <template #title>
                  <a-space>
                    <a-tag :color="getSeverityColor(error.severity)">
                      {{ getSeverityLabel(error.severity) }}
                    </a-tag>
                    <span>{{ error.userMessage || error.message }}</span>
                  </a-space>
                </template>
                <template #description>
                  <div class="error-details">
                    <div>时间: {{ formatTime(error.timestamp) }}</div>
                    <div v-if="error.componentType">组件: {{ error.componentType }}</div>
                    <div v-if="error.details?.operation">操作: {{ error.details.operation }}</div>
                  </div>
                </template>
              </a-list-item-meta>
              
              <template #actions>
                <a-button
                  v-if="error.recoveryActions && error.recoveryActions.length > 0"
                  type="link"
                  size="small"
                  @click="() => showRecoveryActions(error)"
                >
                  恢复操作
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  @click="() => showErrorDetails(error)"
                >
                  详细信息
                </a-button>
              </template>
            </a-list-item>
          </template>
        </a-list>
      </div>
    </a-drawer>

    <!-- 错误详情模态框 -->
    <a-modal
      v-model:open="showErrorDetail"
      title="错误详情"
      width="600"
      :footer="null"
    >
      <div v-if="selectedError" class="error-detail">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="错误类型">
            {{ getErrorTypeLabel(selectedError.type) }}
          </a-descriptions-item>
          <a-descriptions-item label="严重级别">
            <a-tag :color="getSeverityColor(selectedError.severity)">
              {{ getSeverityLabel(selectedError.severity) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="错误消息">
            {{ selectedError.message }}
          </a-descriptions-item>
          <a-descriptions-item label="用户消息">
            {{ selectedError.userMessage }}
          </a-descriptions-item>
          <a-descriptions-item label="发生时间">
            {{ formatTime(selectedError.timestamp) }}
          </a-descriptions-item>
          <a-descriptions-item v-if="selectedError.componentType" label="组件类型">
            {{ selectedError.componentType }}
          </a-descriptions-item>
        </a-descriptions>

        <div v-if="selectedError.stack" class="error-stack">
          <h4>堆栈信息</h4>
          <pre>{{ selectedError.stack }}</pre>
        </div>

        <div v-if="selectedError.details" class="error-context">
          <h4>上下文信息</h4>
          <pre>{{ JSON.stringify(selectedError.details, null, 2) }}</pre>
        </div>
      </div>
    </a-modal>

    <!-- 恢复操作模态框 -->
    <a-modal
      v-model:open="showRecoveryModal"
      title="恢复操作"
      width="400"
      :footer="null"
    >
      <div v-if="selectedError" class="recovery-actions">
        <p>{{ selectedError.userMessage }}</p>
        <a-space direction="vertical" style="width: 100%">
          <a-button
            v-for="action in selectedError.recoveryActions"
            :key="action.label"
            :type="getActionButtonType(action.type)"
            block
            @click="() => executeRecoveryAction(action, selectedError)"
          >
            <Icon :icon="getActionIcon(action.type)" />
            {{ action.label }}
          </a-button>
        </a-space>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Icon } from '@iconify/vue'
import { 
  globalErrorHandler, 
  type ErrorInfo, 
  ErrorSeverity, 
  ErrorType,
  type RecoveryAction 
} from '@lowcode/aslib/core'

// 响应式数据
const visibleErrors = ref<ErrorInfo[]>([])
const errorHistory = ref<ErrorInfo[]>([])
const showErrorHistory = ref(false)
const showErrorDetail = ref(false)
const showRecoveryModal = ref(false)
const selectedError = ref<ErrorInfo | null>(null)

// 计算属性
const hasErrors = computed(() => visibleErrors.value.length > 0)

// 错误监听器
function onError(error: ErrorInfo) {
  // 添加到可见错误列表
  visibleErrors.value.push(error)
  
  // 更新错误历史
  errorHistory.value = globalErrorHandler.getErrorHistory()
  
  // 自动移除低严重级别的错误
  if (error.severity === ErrorSeverity.LOW) {
    setTimeout(() => {
      dismissError(error)
    }, 3000)
  }
}

// 获取通知类型
function getNotificationType(severity: ErrorSeverity): string {
  switch (severity) {
    case ErrorSeverity.CRITICAL:
    case ErrorSeverity.HIGH:
      return 'error'
    case ErrorSeverity.MEDIUM:
      return 'warning'
    case ErrorSeverity.LOW:
      return 'info'
    default:
      return 'info'
  }
}

// 获取通知持续时间
function getNotificationDuration(severity: ErrorSeverity): number {
  switch (severity) {
    case ErrorSeverity.CRITICAL:
      return 0 // 不自动关闭
    case ErrorSeverity.HIGH:
      return 10
    case ErrorSeverity.MEDIUM:
      return 6
    case ErrorSeverity.LOW:
      return 3
    default:
      return 4.5
  }
}

// 获取严重级别颜色
function getSeverityColor(severity: ErrorSeverity): string {
  switch (severity) {
    case ErrorSeverity.CRITICAL:
      return 'red'
    case ErrorSeverity.HIGH:
      return 'orange'
    case ErrorSeverity.MEDIUM:
      return 'yellow'
    case ErrorSeverity.LOW:
      return 'blue'
    default:
      return 'default'
  }
}

// 获取严重级别标签
function getSeverityLabel(severity: ErrorSeverity): string {
  switch (severity) {
    case ErrorSeverity.CRITICAL:
      return '严重'
    case ErrorSeverity.HIGH:
      return '高'
    case ErrorSeverity.MEDIUM:
      return '中'
    case ErrorSeverity.LOW:
      return '低'
    default:
      return '未知'
  }
}

// 获取错误类型标签
function getErrorTypeLabel(type: ErrorType): string {
  switch (type) {
    case ErrorType.COMPONENT_LOAD_FAILED:
      return '组件加载失败'
    case ErrorType.COMPONENT_RENDER_FAILED:
      return '组件渲染失败'
    case ErrorType.METADATA_EXTRACTION_FAILED:
      return '元数据提取失败'
    case ErrorType.CONFIG_VALIDATION_FAILED:
      return '配置验证失败'
    case ErrorType.NETWORK_ERROR:
      return '网络错误'
    case ErrorType.UNKNOWN_ERROR:
      return '未知错误'
    default:
      return '其他错误'
  }
}

// 获取操作按钮类型
function getActionButtonType(actionType: string): string {
  switch (actionType) {
    case 'retry':
      return 'primary'
    case 'fallback':
      return 'default'
    case 'ignore':
      return 'text'
    case 'reload':
      return 'danger'
    default:
      return 'default'
  }
}

// 获取操作图标
function getActionIcon(actionType: string): string {
  switch (actionType) {
    case 'retry':
      return 'mdi:refresh'
    case 'fallback':
      return 'mdi:backup-restore'
    case 'ignore':
      return 'mdi:close'
    case 'reload':
      return 'mdi:reload'
    default:
      return 'mdi:cog'
  }
}

// 格式化时间
function formatTime(timestamp: number): string {
  return new Date(timestamp).toLocaleString()
}

// 关闭错误
function dismissError(error: ErrorInfo) {
  const index = visibleErrors.value.indexOf(error)
  if (index > -1) {
    visibleErrors.value.splice(index, 1)
  }
}

// 执行恢复操作
async function executeRecoveryAction(action: RecoveryAction, error: ErrorInfo) {
  try {
    await action.action()
    dismissError(error)
    showRecoveryModal.value = false
  } catch (err) {
    console.error('Recovery action failed:', err)
  }
}

// 显示错误详情
function showErrorDetails(error: ErrorInfo) {
  selectedError.value = error
  showErrorDetail.value = true
}

// 显示恢复操作
function showRecoveryActions(error: ErrorInfo) {
  selectedError.value = error
  showRecoveryModal.value = true
}

// 清除错误历史
function clearErrorHistory() {
  globalErrorHandler.clearErrorHistory()
  errorHistory.value = []
}

// 导出错误历史
function exportErrorHistory() {
  const data = JSON.stringify(errorHistory.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `error-history-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

// 生命周期
onMounted(() => {
  globalErrorHandler.addErrorListener(onError)
  errorHistory.value = globalErrorHandler.getErrorHistory()
})

onUnmounted(() => {
  globalErrorHandler.removeErrorListener(onError)
})

// 暴露方法给父组件
defineExpose({
  showErrorHistory: () => {
    showErrorHistory.value = true
  }
})
</script>

<style scoped>
.error-display {
  position: relative;
}

.error-notification {
  margin-bottom: 8px;
}

.error-history-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.error-details {
  font-size: 12px;
  color: #666;
}

.error-detail .error-stack,
.error-detail .error-context {
  margin-top: 16px;
}

.error-detail pre {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.recovery-actions {
  text-align: center;
}
</style>
