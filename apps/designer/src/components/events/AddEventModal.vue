<template>
  <a-modal
    :open="visible"
    title="添加事件"
    @ok="handleOk"
    @cancel="() => $emit('update:visible', false)"
    width="600px"
  >
    <a-form layout="vertical">
      <a-form-item label="事件名称" required>
        <a-select
          v-model:value="eventName"
          placeholder="选择或输入事件名称"
          :options="eventOptions"
          show-search
          :filter-option="filterOption"
        />
      </a-form-item>

      <!-- 事件类型说明 -->
      <a-collapse style="margin-top: 12px;">
        <a-collapse-panel key="help" header="事件类型说明">
          <div class="event-type-help">
            <div class="help-section">
              <h4>🎯 组件事件（推荐）</h4>
              <p>组件特定的交互事件，如按钮点击、输入变化等。这些事件与组件功能紧密相关，能提供更好的用户体验。</p>
            </div>
            <div class="help-section">
              <h4>🌐 通用事件</h4>
              <p>适用于所有组件的通用事件，如鼠标悬停、获得焦点等。适合用于通用的交互逻辑。</p>
            </div>
            <div class="help-section">
              <h4>🔧 自定义事件</h4>
              <p>完全自定义的事件名称，适合特殊业务场景。需要在代码中手动触发这些事件。</p>
            </div>
          </div>
        </a-collapse-panel>
      </a-collapse>

      <!-- 显示当前组件的可用事件提示 -->
      <a-alert
        v-if="availableEvents.length > 0"
        message="当前组件支持的特定事件"
        :description="`${componentType} 组件支持 ${availableEvents.length} 个特定事件，建议优先使用以获得更好的用户体验。`"
        type="success"
        show-icon
        style="margin-top: 12px;"
      />
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ComponentEvents } from '@lowcode/aslib/ui'

const props = defineProps<{
  visible: boolean
  componentType: string
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  add: [eventName: string]
}>()

const eventName = ref('')

// 计算属性：获取当前组件的可用事件
const availableEvents = computed(() => {
  if (!props.componentType || !ComponentEvents[props.componentType]) {
    return []
  }

  const events = ComponentEvents[props.componentType]
  return Object.keys(events).map(eventName => ({
    eventName,
    info: events[eventName]
  }))
})

// 事件选项
const eventOptions = computed(() => {
  const options = []

  // 组件特定事件
  if (availableEvents.value.length > 0) {
    const componentEventOptions = availableEvents.value.map(eventInfo => ({
      label: `${eventInfo.eventName} - ${eventInfo.info.name}`,
      value: eventInfo.eventName
    }))

    if (componentEventOptions.length > 0) {
      options.push({
        label: '组件事件',
        options: componentEventOptions
      })
    }
  }

  // 添加通用事件 - 暂时只保留click事件
  const commonEvents = [
    { label: 'click - 统一点击事件', value: 'click' }
  ]

  options.push({
    label: '通用事件',
    options: commonEvents
  })

  return options
})

// 处理确认
function handleOk() {
  if (eventName.value.trim()) {
    emit('add', eventName.value.trim())
    eventName.value = ''
    emit('update:visible', false)
  }
}

// 搜索过滤
function filterOption(input: string, option: any) {
  return option.label.toLowerCase().includes(input.toLowerCase())
}
</script>

<style scoped lang="scss">
.event-type-help {
  .help-section {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    h4 {
      margin: 0 0 4px 0;
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }

    p {
      margin: 0;
      font-size: 12px;
      color: #666;
      line-height: 1.4;
    }
  }
}
</style>
