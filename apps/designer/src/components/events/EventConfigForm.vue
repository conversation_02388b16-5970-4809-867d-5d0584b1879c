<template>
  <div class="event-config-form">
    <a-card size="small">
      <template #title>
        <div class="event-title">
          <a-tag color="green">{{ eventName }}</a-tag>
          <span v-if="eventInfo">{{ eventInfo.name }}</span>
          <span v-else>{{ eventName }}</span>
          <span v-if="typeof configIndex === 'number'" class="config-index">
            #{{ configIndex + 1 }}
            <span v-if="totalConfigs && totalConfigs > 1">/{{ totalConfigs }}</span>
          </span>
        </div>
      </template>
      <template #extra>
        <a-button size="small" danger @click="$emit('remove')">
          <Icon icon="mdi:delete" />
        </a-button>
      </template>
      
      <a-form layout="vertical">
        <a-form-item label="事件类型">
          <a-select v-model:value="config.type" @change="$emit('update')" :options="EventTypeOptions" />
        </a-form-item>

        <!-- 统一事件：智能元素选择器 -->
        <a-form-item
          v-if="eventName === 'click'"
          label="绑定元素"
        >
          <!-- HomeMore组件：菜单项选择器 -->
          <a-form-item
            v-if="componentType === 'HomeMore'"
            label="选择菜单项"
            style="margin-bottom: 8px;"
          >
            <a-select
              v-model:value="config.elementId"
              placeholder="选择要配置的菜单项"
              @change="$emit('update')"
              allow-clear
            >
              <a-select-option
                v-for="item in menuItemOptions"
                :key="item.value"
                :value="item.value"
              >
                <div class="position-option">
                  <div class="position-label">{{ item.label }}</div>
                  <div class="position-desc">菜单项</div>
                  <div class="position-id">ID: {{ item.value }}</div>
                </div>
              </a-select-option>
            </a-select>
            <div class="form-help-text">
              选择要配置事件的菜单项
            </div>
          </a-form-item>

          <!-- 其他组件：位置选择器（如果有预留位置） -->
          <a-form-item
            v-else-if="availablePositions.length > 0"
            label="选择位置"
            style="margin-bottom: 8px;"
          >
            <a-select
              v-model:value="selectedPosition"
              placeholder="选择要配置的位置"
              @change="handlePositionChange"
              allow-clear
            >
              <a-select-option
                v-for="pos in availablePositions"
                :key="pos.position"
                :value="pos.position"
              >
                <div class="position-option">
                  <div class="position-label">{{ pos.label }}</div>
                  <div class="position-desc">{{ pos.description }}</div>
                  <div class="position-id">ID: {{ pos.eventId || pos.defaultId }}</div>
                </div>
              </a-select-option>
            </a-select>
            <div class="form-help-text">
              选择组件中预留的可点击位置，系统会自动填入对应的元素ID
            </div>
          </a-form-item>

          <!-- 手动配置（当组件无预留位置时） -->
          <template v-if="availablePositions.length === 0">
            <a-alert
              message="此组件暂无预留位置"
              description="请直接输入要绑定的元素ID"
              type="info"
              show-icon
              style="margin-bottom: 12px;"
            />

            <!-- 元素ID输入 -->
            <EventIdInput
              v-model="config.elementId"
              label="元素ID"
              placeholder="输入或生成元素ID"
              :element-type="'button'"
              :component-type="componentType"
              :existing-ids="getExistingElementIds()"
              @update:modelValue="$emit('update')"
            />
          </template>

          <!-- 高级配置选项（当有预留位置但想手动配置时） -->
          <template v-if="availablePositions.length > 0 && !selectedPosition">
            <a-collapse ghost>
              <a-collapse-panel key="manual" header="🔧 高级配置（手动设置）">
                <a-alert
                  message="高级用户选项"
                  description="如果预留位置不满足需求，可以手动输入元素ID"
                  type="warning"
                  show-icon
                  style="margin-bottom: 12px;"
                />

                <!-- 元素ID输入 -->
                <EventIdInput
                  v-model="config.elementId"
                  label="元素ID"
                  placeholder="输入或生成元素ID"
                  :element-type="'button'"
                  :component-type="componentType"
                  :existing-ids="getExistingElementIds()"
                  @update:modelValue="$emit('update')"
                />
              </a-collapse-panel>
            </a-collapse>
          </template>

          <!-- 已选择位置的信息显示 -->
          <div v-if="selectedPositionInfo" class="selected-position-info">
            <a-alert
              :message="`已选择: ${selectedPositionInfo.label}`"
              :description="`元素ID: ${selectedPositionInfo.eventId || selectedPositionInfo.defaultId}`"
              type="success"
              show-icon
              style="margin-top: 8px;"
            />
          </div>
        </a-form-item>

        <!-- 页面导航配置 -->
        <NavigationConfig
          v-if="config.type === 'navigate'"
          :config="config"
          @update="$emit('update')"
        />

        <!-- 自定义代码配置 -->
        <CustomCodeConfig
          v-if="config.type === 'custom'"
          :config="config"
          :event-name="eventName"
          @update="$emit('update')"
          @open-editor="$emit('open-editor')"
        />


      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { Icon } from '@iconify/vue'
import { ComponentEvents } from '@lowcode/aslib/ui'

// 事件类型选项
const EventTypeOptions = [
  { label: '页面导航', value: 'navigate' },
  { label: '自定义代码', value: 'custom' }
]
import NavigationConfig from './NavigationConfig.vue'
import CustomCodeConfig from './CustomCodeConfig.vue'
import EventIdInput from '../form/EventIdInput.vue'

const props = defineProps<{
  eventName: string
  config: any
  componentType: string
  component?: any  // 添加组件实例，用于获取菜单项配置
  configIndex?: number  // 配置索引（用于多配置场景）
  totalConfigs?: number  // 总配置数量
}>()

const emit = defineEmits<{
  update: []
  remove: []
  'open-editor': []
}>()

// 获取事件信息
const eventInfo = computed(() => {
  if (!props.componentType || !ComponentEvents[props.componentType]) {
    return null
  }
  return ComponentEvents[props.componentType][props.eventName]
})



// 获取已存在的元素ID列表（用于重复检查）
function getExistingElementIds(): string[] {
  const component = props.component
  if (!component || !component.events) {
    return []
  }

  const existingIds: string[] = []

  // 从所有事件配置中收集已使用的elementId
  Object.values(component.events).forEach((eventConfig: any) => {
    if (Array.isArray(eventConfig)) {
      eventConfig.forEach((config: any) => {
        if (config.elementId) {
          existingIds.push(config.elementId)
        }
      })
    } else if (eventConfig?.elementId) {
      existingIds.push(eventConfig.elementId)
    }
  })

  // 从组件的菜单项中收集ID（如果是HomeMore组件）
  if (props.componentType === 'HomeMore' && component.props?.config?.menuItems) {
    component.props.config.menuItems.forEach((item: any) => {
      if (item.id) {
        existingIds.push(item.id)
      }
    })
  }

  // 排除当前正在编辑的ID
  const currentId = props.config.elementId
  return existingIds.filter(id => id !== currentId)
}

// 位置选择相关
const selectedPosition = ref<string>('')

// 获取可用的位置选项
const availablePositions = computed(() => {
  const component = props.component

  // 从组件配置中获取clickableAreas
  const clickableAreas = component?.props?.config?.clickableAreas

  if (!clickableAreas || !Array.isArray(clickableAreas)) {
    return []
  }

  // 获取已经被占用的元素ID列表
  const existingElementIds = getExistingElementIds()

  return clickableAreas
    .filter((area: any) => area.enabled)
    .filter((area: any) => {
      // 检查这个位置是否已经被其他事件占用
      const areaElementId = area.eventId || area.defaultId
      return !existingElementIds.includes(areaElementId)
    })
})

// 监听配置变化，自动匹配位置
watch(() => [props.config.elementId, availablePositions.value], () => {
  if (props.config.elementId && availablePositions.value.length > 0) {
    // 根据elementId反向查找对应的position
    const matchedPosition = availablePositions.value.find((pos: any) =>
      pos.eventId === props.config.elementId || pos.defaultId === props.config.elementId
    )
    if (matchedPosition) {
      selectedPosition.value = matchedPosition.position
    } else {
      selectedPosition.value = ''
    }
  } else {
    selectedPosition.value = ''
  }
}, { immediate: true })

// 获取HomeMore组件的菜单项选项
const menuItemOptions = computed(() => {
  if (props.componentType !== 'HomeMore') {
    return []
  }

  const menuItems = props.component?.props?.config?.menuItems || []

  return menuItems.map((item: any) => ({
    label: `${item.name}`,
    value: item.id,
    disabled: !item.visible
  }))
})

// 获取选中位置的信息
const selectedPositionInfo = computed(() => {
  if (!selectedPosition.value) return null
  return availablePositions.value.find((pos: any) => pos.position === selectedPosition.value)
})

// 处理位置选择变化
function handlePositionChange(value: any) {
  const position = value as string
  if (!position) {
    selectedPosition.value = ''
    return
  }

  selectedPosition.value = position
  const positionInfo = availablePositions.value.find((pos: any) => pos.position === position)
  if (positionInfo) {
    // 自动填入位置对应的元素信息
    props.config.elementType = positionInfo.elementType
    props.config.elementId = positionInfo.eventId || positionInfo.defaultId
    emit('update')
  }
}
</script>

<style scoped lang="scss">
.event-config-form {
  margin-bottom: 16px;

  .event-title {
    display: flex;
    align-items: center;
    gap: 8px;

    .config-index {
      font-size: 11px;
      color: #999;
      font-weight: normal;
    }
  }

  :deep(.ant-form-item) {
    margin-bottom: 12px;
  }

  :deep(.ant-form-item-label) {
    padding-bottom: 4px;
    
    label {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }
  }

  :deep(.ant-card-head-title) {
    font-size: 14px;
    font-weight: 500;
  }

  .form-help-text {
    font-size: 11px;
    color: #999;
    margin-top: 4px;
    line-height: 1.4;
  }

  .position-option {
    .position-label {
      font-weight: 500;
      color: #262626;
    }

    .position-desc {
      font-size: 12px;
      color: #8c8c8c;
      margin-top: 2px;
    }

    .position-id {
      font-size: 11px;
      color: #1890ff;
      margin-top: 2px;
    }
  }

  .selected-position-info {
    margin-top: 8px;
  }
}
</style>
