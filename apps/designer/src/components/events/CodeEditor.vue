<template>
  <a-modal
    :open="visible"
    title="代码编辑器"
    width="90%"
    :style="{ top: '20px' }"
    @ok="saveCode"
    @cancel="cancelEdit"
  >
    <div class="fullscreen-code-editor">
      <div class="code-editor-toolbar">
        <a-space>
          <!-- 模板下拉菜单 -->
          <a-dropdown>
            <a-button size="small">
              <Icon icon="mdi:code-tags" />
              插入模板
              <Icon icon="mdi:chevron-down" />
            </a-button>
            <template #overlay>
              <a-menu @click="insertTemplate">
                <a-menu-item key="basic">
                  <Icon icon="mdi:code-braces" />
                  基础事件处理
                </a-menu-item>
                <a-menu-item key="api">
                  <Icon icon="mdi:api" />
                  API调用
                </a-menu-item>
                <a-menu-item key="navigation">
                  <Icon icon="mdi:navigation" />
                  页面跳转
                </a-menu-item>
                <a-menu-item key="storage">
                  <Icon icon="mdi:database" />
                  数据存储
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>

          <a-divider type="vertical" />

          <!-- 编辑工具 -->
          <a-button size="small" @click="formatCode">
            <Icon icon="mdi:code-braces" />
            格式化
          </a-button>
          <a-button size="small" @click="clearCode">
            <Icon icon="mdi:delete" />
            清空
          </a-button>
        </a-space>
      </div>

      <div class="code-editor-content">
        <a-textarea
          v-model:value="codeContent"
          placeholder="请输入JavaScript代码..."
          :rows="20"
          @keydown="handleKeydown"
        />
      </div>

      <div class="code-editor-help">
        <a-typography-text type="secondary" style="font-size: 12px;">
          💡 提示：使用 Ctrl+S (Cmd+S) 保存，Esc 取消编辑。可用变量：context（上下文）、params（参数）、eventData（事件数据）
        </a-typography-text>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Icon } from '@iconify/vue'

const props = defineProps<{
  visible: boolean
  initialCode?: string
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  save: [code: string]
  cancel: []
}>()

const codeContent = ref('')

// 监听初始代码变化
watch(() => props.initialCode, (newCode) => {
  if (newCode !== undefined) {
    codeContent.value = newCode
  }
}, { immediate: true })

// 保存代码
function saveCode() {
  emit('save', codeContent.value)
  emit('update:visible', false)
}

// 取消编辑
function cancelEdit() {
  emit('cancel')
  emit('update:visible', false)
}

// 插入模板
function insertTemplate({ key }: { key: string }) {
  const templates = {
    basic: `// 基础事件处理
console.log('事件被触发:', eventData)

// 显示消息
context.utils.showMessage('操作成功')

// 获取组件数据
const componentData = context.getComponentData()
console.log('组件数据:', componentData)`,

    api: `// API调用示例
try {
  const response = await context.axios.post('/api/action', {
    data: eventData,
    timestamp: Date.now()
  })
  
  if (response.data.success) {
    context.utils.showMessage('操作成功')
  } else {
    context.utils.showMessage('操作失败：' + response.data.message)
  }
} catch (error) {
  console.error('API调用失败:', error)
  context.utils.showMessage('网络错误，请重试')
}`,

    navigation: `// 页面跳转示例
// 跳转到指定页面
context.router.push('/target-page')

// 带参数跳转
context.router.push({
  path: '/detail',
  query: { id: eventData.id }
})

// 返回上一页
context.router.back()`,

    storage: `// 数据存储示例
// 保存到本地存储
localStorage.setItem('userData', JSON.stringify(eventData))

// 从本地存储读取
const savedData = JSON.parse(localStorage.getItem('userData') || '{}')
console.log('保存的数据:', savedData)

// 或者使用会话存储
sessionStorage.setItem('tempData', JSON.stringify(eventData))`
  }

  const template = templates[key as keyof typeof templates]
  if (template) {
    // 如果已有内容，在末尾添加
    if (codeContent.value.trim()) {
      codeContent.value += '\n\n' + template
    } else {
      codeContent.value = template
    }
  }
}

// 格式化代码
function formatCode() {
  try {
    const lines = codeContent.value.split('\n')
    const formattedLines = lines.map(line => {
      return line.trim()
    })
    codeContent.value = formattedLines.join('\n')
  } catch (error) {
    console.error('代码格式化失败:', error)
  }
}

// 清空代码
function clearCode() {
  codeContent.value = ''
}

// 键盘快捷键处理
function handleKeydown(event: KeyboardEvent) {
  // Ctrl+S 或 Cmd+S 保存
  if ((event.ctrlKey || event.metaKey) && event.key === 's') {
    event.preventDefault()
    saveCode()
  }

  // Esc 取消
  if (event.key === 'Escape') {
    event.preventDefault()
    cancelEdit()
  }
}
</script>

<style scoped lang="scss">
.fullscreen-code-editor {
  .code-editor-toolbar {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;

    :deep(.ant-dropdown-trigger) {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }

  .code-editor-content {
    :deep(.ant-input) {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      line-height: 1.5;
      border-radius: 0;
      resize: none;

      &:focus {
        box-shadow: none;
      }
    }
  }

  .code-editor-help {
    padding: 8px 16px;
    background-color: #fafafa;
    border-top: 1px solid #f0f0f0;
    flex-shrink: 0;
  }
}
</style>
