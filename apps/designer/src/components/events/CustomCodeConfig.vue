<template>
  <div class="custom-code-config">
    <a-form-item label="操作类型">
      <a-radio-group v-model:value="actionType" @change="handleActionTypeChange">
        <a-radio-button value="ui">🎨 UI操作</a-radio-button>
        <a-radio-button value="api">🔌 API操作</a-radio-button>
        <a-radio-button value="custom">🔧 自定义代码</a-radio-button>
      </a-radio-group>
    </a-form-item>

    <!-- UI操作选择 -->
    <a-form-item v-if="actionType === 'ui'" label="UI操作">
      <a-select
        v-model:value="config.preset"
        placeholder="选择UI操作"
        @change="handlePresetChange"
        :options="UIActionOptions"
      />
      <div class="form-help-text">
        纯前端操作，如弹窗、消息、复制等
      </div>
    </a-form-item>

    <!-- API操作选择 -->
    <a-form-item v-if="actionType === 'api'" label="API操作">
      <a-select
        v-model:value="config.preset"
        placeholder="选择API操作"
        @change="handlePresetChange"
        :options="APIActionOptions"
      />
      <div class="form-help-text">
        需要调用后端API的操作，如设备刷新、数据同步等
      </div>
    </a-form-item>

    <a-form-item v-if="actionType === 'custom'" label="处理函数">
      <div class="code-editor-container">
        <div class="code-templates" style="margin-bottom: 8px;">
          <!-- 第一行：主要功能 -->
          <div style="margin-bottom: 4px;">
            <a-space size="small">
              <a-button type="primary" size="small" @click="$emit('open-editor')">
                <Icon icon="mdi:fullscreen" />
                全屏编辑
              </a-button>
              
              <a-dropdown>
                <a-button size="small">
                  <Icon icon="mdi:code-tags" />
                  插入模板
                  <Icon icon="mdi:chevron-down" />
                </a-button>
                <template #overlay>
                  <a-menu @click="insertTemplate">
                    <a-menu-item key="basic">基础事件处理</a-menu-item>
                    <a-menu-item key="api">API调用</a-menu-item>
                    <a-menu-item key="navigation">页面跳转</a-menu-item>
                    <a-menu-item key="storage">数据存储</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </div>
        </div>

        <a-textarea
          v-model:value="config.handler"
          placeholder="请输入JavaScript代码..."
          :rows="6"
          @input="$emit('update')"
        />
        
        <div class="code-help">
          <a-typography-text type="secondary" style="font-size: 11px;">
            💡 可用变量：context（上下文）、params（参数）、eventData（事件数据）
          </a-typography-text>
        </div>
      </div>
    </a-form-item>

    <!-- UI操作参数配置 -->
    <template v-if="actionType === 'ui' && config.preset">
      <!-- 显示消息 -->
      <a-form-item v-if="config.preset === 'showMessage'" label="消息内容" required>
        <a-input
          v-model:value="config.message"
          placeholder="要显示的消息内容"
          @input="$emit('update')"
        />
        <div class="form-help-text">将在页面上显示一个简短的提示消息</div>
      </a-form-item>

      <!-- 确认对话框 -->
      <template v-if="config.preset === 'showConfirm'">
        <a-form-item label="对话框标题">
          <a-input
            v-model:value="config.title"
            placeholder="确认对话框标题"
            @input="$emit('update')"
          />
        </a-form-item>
        <a-form-item label="确认消息" required>
          <a-input
            v-model:value="config.message"
            placeholder="确认对话框内容"
            @input="$emit('update')"
          />
        </a-form-item>
        <a-form-item label="确认后操作">
          <a-select
            v-model:value="config.onConfirmAction"
            placeholder="用户确认后执行的操作"
            @change="$emit('update')"
          >
            <a-select-option value="none">无操作</a-select-option>
            <a-select-option value="navigate">页面跳转</a-select-option>
            <a-select-option value="refresh">刷新页面</a-select-option>
          </a-select>
        </a-form-item>
      </template>

      <!-- 复制文本 -->
      <a-form-item v-if="config.preset === 'copyText'" label="复制内容" required>
        <a-textarea
          v-model:value="config.text"
          placeholder="要复制到剪贴板的文本内容"
          @input="$emit('update')"
          :rows="3"
        />
        <div class="form-help-text">文本将被复制到用户的剪贴板</div>
      </a-form-item>

      <!-- 下载文件 -->
      <template v-if="config.preset === 'downloadFile'">
        <a-form-item label="文件URL" required>
          <a-input
            v-model:value="config.fileUrl"
            placeholder="要下载的文件URL"
            @input="$emit('update')"
          />
        </a-form-item>
        <a-form-item label="文件名">
          <a-input
            v-model:value="config.fileName"
            placeholder="下载文件名（可选）"
            @input="$emit('update')"
          />
          <div class="form-help-text">如果不指定，将使用默认文件名</div>
        </a-form-item>
      </template>

      <!-- 打开模态框 -->
      <template v-if="config.preset === 'openModal'">
        <a-form-item label="模态框标题">
          <a-input
            v-model:value="config.modalTitle"
            placeholder="模态框标题"
            @input="$emit('update')"
          />
        </a-form-item>
        <a-form-item label="模态框内容" required>
          <a-textarea
            v-model:value="config.modalContent"
            placeholder="模态框显示的内容"
            @input="$emit('update')"
            :rows="4"
          />
        </a-form-item>
      </template>

      <!-- 切换组件显示 -->
      <a-form-item v-if="config.preset === 'toggleComponent'" label="目标组件ID" required>
        <a-input
          v-model:value="config.targetId"
          placeholder="要切换显示状态的组件ID"
          @input="$emit('update')"
        />
        <div class="form-help-text">组件将在显示和隐藏之间切换</div>
      </a-form-item>
    </template>

    <!-- API操作参数配置 -->
    <template v-if="actionType === 'api' && config.preset">
      <!-- 刷新设备信息 -->
      <template v-if="config.preset === 'refreshDevice'">
        <a-alert
          message="设备信息刷新"
          description="点击后将调用后端API刷新设备信息，刷新成功后会显示提示消息"
          type="info"
          show-icon
          style="margin-bottom: 16px;"
        />

        <a-form-item label="成功后操作">
          <a-select
            v-model:value="config.onSuccessAction"
            placeholder="刷新成功后执行的操作"
            @change="$emit('update')"
          >
            <a-select-option value="none">仅显示成功消息</a-select-option>
            <a-select-option value="navigate">跳转到指定页面</a-select-option>
            <a-select-option value="refresh">刷新当前页面</a-select-option>
          </a-select>
          <div class="form-help-text">选择设备刷新成功后要执行的操作</div>
        </a-form-item>

        <a-form-item v-if="config.onSuccessAction === 'navigate'" label="跳转页面">
          <a-input
            v-model:value="config.successTarget"
            placeholder="/device/PackageList"
            @input="$emit('update')"
          />
          <div class="form-help-text">输入要跳转的页面路径</div>
        </a-form-item>
      </template>

      <!-- 同步数据 -->
      <template v-if="config.preset === 'syncData'">
        <a-form-item label="同步类型" required>
          <a-select
            v-model:value="config.syncType"
            placeholder="选择要同步的数据类型"
            @change="$emit('update')"
          >
            <a-select-option value="device">设备信息</a-select-option>
            <a-select-option value="user">用户数据</a-select-option>
            <a-select-option value="all">全部数据</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="加载提示">
          <a-input
            v-model:value="config.message"
            placeholder="同步时显示的提示信息"
            @input="$emit('update')"
          />
        </a-form-item>
      </template>

      <!-- 更新状态 -->
      <template v-if="config.preset === 'updateStatus'">
        <a-form-item label="新状态" required>
          <a-select
            v-model:value="config.newStatus"
            placeholder="选择要更新的状态"
            @change="$emit('update')"
          >
            <a-select-option value="active">激活</a-select-option>
            <a-select-option value="inactive">停用</a-select-option>
            <a-select-option value="maintenance">维护中</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="设备ID">
          <a-input
            v-model:value="config.deviceId"
            placeholder="要更新状态的设备ID（可选）"
            @input="$emit('update')"
          />
          <div class="form-help-text">如果不指定，将使用当前设备</div>
        </a-form-item>
      </template>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Icon } from '@iconify/vue'

// UI操作选项
const UIActionOptions = [
  { label: '显示消息', value: 'showMessage' },
  { label: '确认对话框', value: 'showConfirm' },
  { label: '复制文本', value: 'copyText' },
  { label: '下载文件', value: 'downloadFile' },
  { label: '打开模态框', value: 'openModal' },
  { label: '切换组件显示', value: 'toggleComponent' }
]

// API操作选项
const APIActionOptions = [
  { label: '刷新设备信息', value: 'refreshDevice' },
  { label: '同步数据', value: 'syncData' },
  { label: '更新状态', value: 'updateStatus' }
]

const props = defineProps<{
  config: any
  eventName: string
}>()

const emit = defineEmits<{
  update: []
  'open-editor': []
}>()

// 操作类型
const actionType = ref<'ui' | 'api' | 'custom'>('ui')

// 根据当前配置初始化操作类型
const initActionType = () => {
  if (props.config.preset) {
    // 检查是否是UI操作
    const isUIAction = UIActionOptions.some(option => option.value === props.config.preset)
    if (isUIAction) {
      actionType.value = 'ui'
      return
    }

    // 检查是否是API操作
    const isAPIAction = APIActionOptions.some(option => option.value === props.config.preset)
    if (isAPIAction) {
      actionType.value = 'api'
      return
    }
  }

  // 如果有自定义代码但没有预设操作，则为自定义代码
  if (props.config.handler && !props.config.preset) {
    actionType.value = 'custom'
  } else {
    actionType.value = 'ui'
  }
}

// 监听配置变化，更新操作类型
watch(() => props.config, initActionType, { immediate: true })

// 处理操作类型变化
function handleActionTypeChange() {
  // 清空当前配置
  props.config.preset = ''
  props.config.handler = ''

  // 清空所有参数
  delete props.config.message
  delete props.config.title
  delete props.config.text
  delete props.config.fileUrl
  delete props.config.fileName
  delete props.config.modalTitle
  delete props.config.modalContent
  delete props.config.targetId
  delete props.config.syncType
  delete props.config.newStatus
  delete props.config.deviceId

  emit('update')
}

// 处理预设操作变化
function handlePresetChange() {
  if (!props.config.preset) {
    props.config.handler = ''
    emit('update')
    return
  }

  // 保存当前选择的预设操作
  const selectedPreset = props.config.preset

  // 清空旧的参数（但不清空preset）
  delete props.config.message
  delete props.config.title
  delete props.config.text
  delete props.config.fileUrl
  delete props.config.fileName
  delete props.config.modalTitle
  delete props.config.modalContent
  delete props.config.targetId
  delete props.config.syncType
  delete props.config.newStatus
  delete props.config.deviceId
  delete props.config.onSuccessAction
  delete props.config.successTarget

  // 恢复预设操作
  props.config.preset = selectedPreset

  emit('update')
}

// 插入代码模板
function insertTemplate({ key }: any) {
  const templates = {
    basic: `// 基础事件处理
console.log('事件数据:', data)
utils.showMessage('操作成功')`,

    api: `// API调用示例
utils.showMessage('正在处理...')
api.getDeviceInfo().then(response => {
  console.log('设备信息:', response.data)
  utils.showMessage('获取成功')
}).catch(error => {
  console.error('获取失败:', error)
  utils.showMessage('获取失败')
})`,

    navigation: `// 页面跳转
utils.showMessage('正在跳转...')
setTimeout(() => {
  utils.navigate('/device/PackageList')
}, 1000)`,

    storage: `// 数据存储
const userData = {
  timestamp: Date.now(),
  action: 'user_action',
  data: data
}
localStorage.setItem('userAction', JSON.stringify(userData))
utils.showMessage('数据已保存')`
  }

  const template = templates[key as keyof typeof templates]
  if (template) {
    if (props.config.handler?.trim()) {
      props.config.handler += '\n\n' + template
    } else {
      props.config.handler = template
    }
    emit('update')
  }
}
</script>

<style scoped lang="scss">
.custom-code-config {
  .form-help-text {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
    line-height: 1.4;
  }

  .code-editor-container {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;

    .code-templates {
      background: #fafafa;
      padding: 8px;
      border-bottom: 1px solid #d9d9d9;
    }
  }

  :deep(.ant-radio-button-wrapper) {
    &:first-child {
      border-radius: 6px 0 0 6px;
    }

    &:last-child {
      border-radius: 0 6px 6px 0;
    }

    &.ant-radio-button-wrapper-checked {
      background: #1890ff;
      border-color: #1890ff;
      color: white;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }
  }

  .ant-form-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .ant-select,
  .ant-input,
  .ant-input-number {
    width: 100%;
  }

  .ant-textarea {
    resize: vertical;
    min-height: 80px;
  }
}
</style>

<style scoped lang="scss">
.custom-code-config {
  .code-editor-container {
    border: 1px solid #d9d9d9;
    border-radius: 6px;

    .code-help {
      padding: 8px 12px;
      background-color: #fafafa;
      border-top: 1px solid #f0f0f0;
    }
  }

  :deep(.ant-input) {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
  }
}
</style>
