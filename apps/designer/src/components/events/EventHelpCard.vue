<template>
  <div class="event-help-card">
    <a-card size="small" title="可用事件" class="events-help">
      <div class="event-help-list">
        <div v-for="eventInfo in availableEvents" :key="eventInfo.eventName" class="event-help-item">
          <div class="event-help-header">
            <a-tag color="blue">{{ eventInfo.eventName }}</a-tag>
            <span class="event-help-name">{{ eventInfo.info.name }}</span>
          </div>
          <div class="event-help-desc">{{ eventInfo.info.description }}</div>
          <div class="event-help-trigger">触发时机：{{ eventInfo.info.trigger }}</div>
          <div v-if="eventInfo.info.params" class="event-help-params">
            参数：{{ eventInfo.info.params }}
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ComponentEvents } from '@lowcode/aslib/ui'
import { UnifiedEventManager } from '@lowcode/aslib/core'

const props = defineProps<{
  componentType: string
}>()

// 计算属性：获取当前组件的可用事件
const availableEvents = computed(() => {
  if (!props.componentType) {
    return []
  }

  // ✨ 优先使用统一事件管理器
  const unifiedEvents = UnifiedEventManager.getComponentEvents(props.componentType)
  if (unifiedEvents.length > 0) {
    return unifiedEvents.map(eventInfo => ({
      eventName: eventInfo.name,
      info: eventInfo
    }))
  }

  // 兼容原有的事件定义
  if (!ComponentEvents[props.componentType]) {
    return []
  }

  const events = ComponentEvents[props.componentType]
  return Object.keys(events).map(eventName => ({
    eventName,
    info: events[eventName]
  }))
})
</script>

<style scoped lang="scss">
.event-help-card {
  margin-bottom: 16px;

  .events-help {
    border: 1px solid #e6f7ff;
    background: #f6ffed;
  }

  .event-help-list {
    .event-help-item {
      margin-bottom: 12px;
      padding: 8px;
      background: #fafafa;
      border-radius: 4px;
      border-left: 3px solid #1890ff;

      &:last-child {
        margin-bottom: 0;
      }

      .event-help-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;

        .event-help-name {
          font-weight: 500;
          color: #333;
        }
      }

      .event-help-desc {
        font-size: 12px;
        color: #666;
        margin-bottom: 2px;
      }

      .event-help-trigger {
        font-size: 11px;
        color: #999;
      }

      .event-help-params {
        font-size: 11px;
        color: #52c41a;
        margin-top: 2px;
      }
    }
  }
}
</style>
