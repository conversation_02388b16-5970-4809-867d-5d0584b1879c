<template>
  <div class="navigation-config">
    <a-form-item label="导航方式">
      <a-radio-group v-model:value="config.navigateType" @change="$emit('update')">
        <a-radio v-for="option in NavigateTypeOptions" :key="option.value" :value="option.value">
          {{ option.label }}
        </a-radio>
      </a-radio-group>
    </a-form-item>

    <a-form-item v-if="config.navigateType === 'page'" label="目标页面">
      <!-- 应用类型显示 -->
      <div class="app-type-info" v-if="currentApplicationInfo">
        <a-tag :color="currentApplicationInfo.color" class="mb-2">
          <Icon :icon="currentApplicationInfo.icon" class="mr-1" />
          {{ currentApplicationInfo.name }}
        </a-tag>
      </div>

      <!-- 页面选择器 -->
      <a-select
        v-model:value="config.target"
        placeholder="选择页面或输入路径"
        @change="handlePageChange"
        allow-clear
        show-search
        :filter-option="filterPageOption"
        :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
        option-label-prop="label"
      >
        <template #suffixIcon>
          <Icon icon="mdi:chevron-down" />
        </template>

        <a-select-opt-group
          v-for="category in filteredCategories"
          :key="category.id"
          :label="getCategoryLabel(category)"
        >
          <a-select-option
            v-for="route in getNavigatableRoutesByCategory(category.id)"
            :key="route.path"
            :value="route.path"
            :label="route.title"
            :title="route.description || route.title"
          >
            <div class="page-option">
              <div class="page-option-main">
                <Icon
                  v-if="route.icon"
                  :icon="route.icon"
                  class="page-icon"
                  :class="{ 'configurable': route.configurable }"
                />
                <div class="page-info">
                  <div class="page-title">{{ route.title }}</div>
                  <div class="page-path">{{ route.path }}</div>
                </div>
              </div>
              <div class="page-badges">
                <a-tag v-if="route.configurable" size="small" color="green">可配置</a-tag>
                <a-tag v-if="route.requiresAuth" size="small" color="orange">需登录</a-tag>
                <a-tag v-if="route.params && route.params.length > 0" size="small" color="purple">需参数</a-tag>
              </div>
            </div>
          </a-select-option>
        </a-select-opt-group>
      </a-select>

      <!-- 手动输入 -->
      <div class="manual-input-section" v-if="showManualInput">
        <a-input
          v-model:value="config.target"
          placeholder="手动输入页面路径，如 /PackageList"
          @input="handleManualInput"
          @blur="validateRoute"
        >
          <template #prefix>
            <Icon icon="mdi:keyboard" />
          </template>
        </a-input>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-button
          type="link"
          size="small"
          @click="toggleManualInput"
          class="toggle-input-btn"
        >
          <Icon :icon="showManualInput ? 'mdi:format-list-bulleted' : 'mdi:keyboard'" />
          {{ showManualInput ? '选择页面' : '手动输入' }}
        </a-button>

        <a-button
          v-if="config.target && isValidCurrentRoute"
          type="link"
          size="small"
          @click="previewPage"
          class="preview-btn"
        >
          <Icon icon="mdi:eye" />
          预览页面
        </a-button>
      </div>

      <!-- 帮助信息 -->
      <div class="form-help">
        <div class="help-row">
          <Icon icon="mdi:information" class="help-icon" />
          <span>当前应用类型: <strong>{{ currentApplicationInfo?.name || '未知' }}</strong></span>
        </div>
        <div class="help-row" v-if="!isValidCurrentRoute && config.target">
          <Icon icon="mdi:alert" class="help-icon warning" />
          <span class="warning-text">路径 "{{ config.target }}" 在当前应用类型中不存在</span>
        </div>
        <div class="help-row" v-if="selectedRouteInfo">
          <Icon icon="mdi:check-circle" class="help-icon success" />
          <span>{{ selectedRouteInfo.description || selectedRouteInfo.title }}</span>
        </div>
      </div>
    </a-form-item>

    <a-form-item v-if="config.navigateType === 'external'" label="外部链接">
      <a-input v-model:value="config.target" placeholder="https://example.com" @input="$emit('update')" />
      <div class="form-help">
        <small>🌐 将在新窗口中打开链接</small>
      </div>
    </a-form-item>

    <a-form-item v-if="config.navigateType === 'webview'" label="内嵌页面链接">
      <a-input v-model:value="config.target" placeholder="https://example.com" @input="$emit('update')" />
      <div class="form-help">
        <small>📱 将在应用内的内嵌页面中打开链接</small>
      </div>

      <!-- 内嵌页面配置 -->
      <a-collapse style="margin-top: 12px;">
        <a-collapse-panel key="webview-config" header="内嵌页面配置">
          <a-form-item label="页面标题">
            <a-input
              v-model:value="config.webviewTitle"
              placeholder="内嵌页面的标题"
              @input="$emit('update')"
            />
          </a-form-item>
          
          <a-form-item label="页面选项">
            <a-checkbox-group v-model:value="config.webviewOptions" @change="$emit('update')">
              <a-checkbox value="showNavBar">显示导航栏</a-checkbox>
              <a-checkbox value="showBackButton">显示返回按钮</a-checkbox>
              <a-checkbox value="showRefreshButton">显示刷新按钮</a-checkbox>
              <a-checkbox value="showShareButton">显示分享按钮</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </a-collapse-panel>
      </a-collapse>
    </a-form-item>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref } from 'vue'
import { Icon } from '@iconify/vue'
import {
  getPageCategories,
  getRoutesByCategory as getCoreRoutesByCategory,
  getApplicationType,
  isValidRoute
} from '@lowcode/aslib/core'

// 导航类型选项
const NavigateTypeOptions = [
  { label: '页面跳转', value: 'page' },
  { label: '外部链接', value: 'external' },
  { label: '内嵌页面', value: 'webview' }
]

// Props 和 Emits
const props = defineProps<{
  config: any
}>()

const emit = defineEmits<{
  update: []
}>()

// 从上下文获取当前应用类型
const currentAppType = inject('currentAppType', ref('device'))

// 响应式状态
const showManualInput = ref(false)

// 计算属性
const pageCategories = computed(() => {
  return getPageCategories()
})

const currentApplicationInfo = computed(() => {
  return getApplicationType(currentAppType.value)
})

const filteredCategories = computed(() => {
  return pageCategories.value.filter(category => {
    const routes = getNavigatableRoutesByCategory(category.id)
    return routes.length > 0
  })
})

const isValidCurrentRoute = computed(() => {
  if (!props.config.target) return true
  return isValidRoute(currentAppType.value, props.config.target)
})

const selectedRouteInfo = computed(() => {
  if (!props.config.target || !isValidCurrentRoute.value) return null
  const routes = getCoreRoutesByCategory(currentAppType.value, '')
  return routes.find(route => route.path === props.config.target)
})

// 方法
function getRoutesByCategory(categoryId: string) {
  return getCoreRoutesByCategory(currentAppType.value, categoryId)
}

function getNavigatableRoutesByCategory(categoryId: string) {
  const routes = getCoreRoutesByCategory(currentAppType.value, categoryId)
  // 只返回可以被跳转选择的路由
  return routes.filter(route => route.navigatable !== false)
}

function getCategoryLabel(category: any) {
  const routes = getNavigatableRoutesByCategory(category.id)
  return `${category.name} (${routes.length})`
}

function handlePageChange() {
  emit('update')
}

function handleManualInput() {
  emit('update')
}

function toggleManualInput() {
  showManualInput.value = !showManualInput.value
}

function validateRoute() {
  // 可以在这里添加路由验证逻辑
  emit('update')
}

function previewPage() {
  if (props.config.target) {
    // 🔧 修复：使用哈希路由格式，与H5端保持一致
    const previewUrl = `http://localhost:3003/#${props.config.target}`
    window.open(previewUrl, '_blank')
  }
}

function filterPageOption(input: string, option: any) {
  if (!input) return true

  const lowerInput = input.toLowerCase()
  const optionElement = option.children

  // 尝试从选项中提取文本内容
  if (optionElement && optionElement.props) {
    const title = optionElement.props.children?.[0]?.children?.[1]?.children?.[0]?.children || ''
    const path = optionElement.props.children?.[0]?.children?.[1]?.children?.[1]?.children || ''

    return title.toLowerCase().includes(lowerInput) ||
           path.toLowerCase().includes(lowerInput)
  }

  return false
}
</script>

<style scoped lang="scss">
.navigation-config {
  .form-help {
    margin-top: 4px;

    small {
      color: #999;
      font-size: 11px;
    }
  }
}

/* 应用类型信息 */
.app-type-info {
  margin-bottom: 12px;
}

/* 页面选项样式 */
.page-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.page-option-main {
  display: flex;
  align-items: center;
  flex: 1;
}

.page-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #666;

  &.configurable {
    color: #52c41a;
  }
}

.page-info {
  flex: 1;
}

.page-title {
  font-weight: 500;
  color: #262626;
  line-height: 1.4;
}

.page-path {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.2;
}

.page-badges {
  display: flex;
  gap: 4px;
}

/* 手动输入区域 */
.manual-input-section {
  margin-top: 8px;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  align-items: center;
}

.toggle-input-btn,
.preview-btn {
  padding: 0 8px;
  height: 24px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 帮助信息 */
.form-help {
  margin-top: 12px;
  padding: 8px 12px;
  background: #fafafa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.help-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.4;

  &:last-child {
    margin-bottom: 0;
  }
}

.help-icon {
  width: 14px;
  height: 14px;
  margin-right: 6px;
  color: #1890ff;

  &.warning {
    color: #faad14;
  }

  &.success {
    color: #52c41a;
  }
}

.warning-text {
  color: #faad14;
  font-weight: 500;
}

/* 修复Select组件的样式问题 */
:deep(.ant-select-selector) {
  padding-left: 11px !important;
}

:deep(.ant-select-selection-item) {
  padding-left: 0 !important;
  margin-left: 0 !important;
}

/* 选择器下拉样式优化 */
:deep(.ant-select-dropdown) {
  border-radius: 8px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

:deep(.ant-select-item-group) {
  font-weight: 600;
  color: #262626;
  background: #f5f5f5;
}

:deep(.ant-select-item-option) {
  padding: 8px 12px;
}

:deep(.ant-select-item-option:hover) {
  background: #f0f9ff;
}

:deep(.ant-select-item-option-selected) {
  background: #e6f7ff;
  font-weight: 500;
}

:deep(.ant-select-item-option-content) {
  padding: 0;
}

/* 标签样式 */
:deep(.ant-tag) {
  border-radius: 4px;
  font-size: 11px;
  line-height: 1.2;
  padding: 2px 6px;
}
</style>
