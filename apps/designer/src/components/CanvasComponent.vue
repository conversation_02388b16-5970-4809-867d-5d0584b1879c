<template>
  <div
    class="canvas-component"
    :class="{
      'component-selected': isSelected,
      'component-hover': isHovered
    }"
    @click.stop="handleSelect"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
    :data-component-type="component.type"
  >
    <!-- 组件渲染 -->
    <div
      v-if="isComponentsReady"
      class="component-wrapper"
      :style="componentStyle"
    >
      <ComponentRenderer
        :config="component"
        :context="mockContext"
        :data-manager="dataManager"
        :event-manager="eventManager"
      />
    </div>

    <!-- 组件加载中状态 -->
    <div v-else class="component-loading">
      <div class="loading-content">
        <Icon icon="mdi:loading" class="loading-icon" />
        <span>{{ component.type }}</span>
      </div>
    </div>
    
    <!-- 选中状态的操作按钮 -->
    <div v-if="isSelected" class="component-actions">
      <a-button-group size="small">
        <a-button @click.stop="handleDelete" danger>
          <Icon icon="mdi:delete" />
        </a-button>
        <a-button @click.stop="handleDuplicate">
          <Icon icon="mdi:content-copy" />
        </a-button>
        <a-button @click.stop="handleMoveUp" :disabled="index === 0">
          <Icon icon="mdi:arrow-up" />
        </a-button>
        <a-button @click.stop="handleMoveDown">
          <Icon icon="mdi:arrow-down" />
        </a-button>
      </a-button-group>
    </div>
    
    <!-- 组件标签 -->
    <div v-if="isSelected || isHovered" class="component-label">
      {{ getComponentDisplayName(component.type) }}
    </div>
    
    <!-- 子组件渲染 -->
    <template v-if="component.children?.length">
      <CanvasComponent
        v-for="(child, childIndex) in component.children"
        :key="child.id"
        :component="child"
        :index="childIndex"
        :parent-id="component.id"
        @select="$emit('select', $event)"
        @delete="$emit('delete', $event)"
        @move="(...args) => $emit('move', ...args)"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { ComponentRenderer, DataManager, EventManager } from '@lowcode/aslib/core'
import type { ComponentConfig } from '@lowcode/aslib/core'
import { useDesignerStore } from '../stores/designer'
import { useComponentLibraryStore } from '../stores/componentLibrary'
import { isComponentsReady } from '../utils/componentRegistry'

const props = defineProps<{
  component: ComponentConfig
  index: number
  parentId: string | null
}>()

const emit = defineEmits<{
  select: [componentId: string]
  delete: [componentId: string]
  move: [componentId: string, targetParentId: string | null, targetIndex: number]
}>()

const designerStore = useDesignerStore()
const componentLibraryStore = useComponentLibraryStore()

// 响应式数据
const isHovered = ref(false)

// 计算属性
const isSelected = computed(() => designerStore.selectedComponentId === props.component.id)

// 组件样式
const componentStyle = computed(() => {
  const style = props.component.style || {}

  // 调试背景图片样式
  if (style.backgroundImage) {
    console.log('🖼️ 组件背景图片样式:', {
      componentId: props.component.id,
      componentType: props.component.type,
      style: style,
      backgroundImage: style.backgroundImage,
      backgroundSize: style.backgroundSize,
      backgroundRepeat: style.backgroundRepeat,
      backgroundPosition: style.backgroundPosition
    })
  }

  return style
})

// Mock数据管理器和事件管理器
const mockContext = {
  device: {
    details: {
      id: 1,
      deviceNo: 'TEST001',
      packageName: '基础套餐 10GB',
      vTotalFlow: 10240,
      vUseFlow: 3072,
      vResidueFlow: 7168,
      balance: 25.50,
      status: 3,
      nameStatus: 2
    }
  }
}

const dataManager = new DataManager(mockContext)
const eventManager = new EventManager(mockContext)

// 获取组件显示名称
function getComponentDisplayName(type: string): string {
  const libraryItem = componentLibraryStore.getComponentByType(type)
  return libraryItem?.name || type
}

// 事件处理
function handleSelect() {
  emit('select', props.component.id)
}

function handleDelete() {
  console.log('删除组件:', props.component.id)
  emit('delete', props.component.id)
}

function handleDuplicate() {
  // 复制组件
  const duplicatedComponent = {
    ...props.component,
    id: `${props.component.id}_copy_${Date.now()}`
  }
  
  designerStore.addComponent(duplicatedComponent, props.parentId || undefined, props.index + 1)
}

function handleMoveUp() {
  console.log('上移组件:', props.component.id, '当前索引:', props.index)
  if (props.index > 0) {
    emit('move', props.component.id, props.parentId, props.index - 1)
  }
}

function handleMoveDown() {
  console.log('下移组件:', props.component.id, '当前索引:', props.index)
  emit('move', props.component.id, props.parentId, props.index + 1)
}
</script>

<style scoped lang="scss">
.canvas-component {
  position: relative;
  transition: all 0.2s ease;

  &.component-hover {
    outline: 2px dashed #1890ff;
    outline-offset: 2px;
  }

  &.component-selected {
    outline: 2px solid #52c41a;
    outline-offset: 2px;
  }
}

.component-wrapper {
  // 确保样式能正确应用到组件
  width: 100%;
  // 确保背景图片能正确显示
  // min-height: 100px;
  min-height: 0;
}

.component-actions {
  position: absolute;
  top: -40px;
  right: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.component-label {
  position: absolute;
  top: -24px;
  left: 0;
  background: #1890ff;
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 2px;
  z-index: 10;
  white-space: nowrap;
}

// 组件加载状态
.component-loading {
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: 2px dashed #d9d9d9;
  border-radius: 4px;
  color: #999;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .loading-icon {
      font-size: 20px;
      animation: spin 1s linear infinite;
    }

    span {
      font-size: 12px;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// 确保组件内容不被选中框影响
:deep(.lowcode-component) {
  position: relative;
  z-index: 1;
}
</style>
