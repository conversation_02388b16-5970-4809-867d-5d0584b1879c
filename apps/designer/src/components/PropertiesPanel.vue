<template>
  <div class="properties-panel" :class="{ 'collapsed': collapsed }">
    <div class="panel-header" v-show="!collapsed">
      <div class="header-content">
        <h3 class="header-title">属性面板</h3>
        <p class="header-description">配置选中组件的属性</p>
      </div>
    </div>

    <!-- 折叠状态下的快捷操作 -->
    <div v-if="collapsed" class="collapsed-actions">
      <a-tooltip title="页面属性" placement="left">
        <div class="collapsed-action-item" @click="$emit('expand')">
          <Icon icon="mdi:file-cog" />
        </div>
      </a-tooltip>
      <a-tooltip title="组件属性" placement="left" v-if="selectedComponent">
        <div class="collapsed-action-item" @click="$emit('expand')">
          <Icon icon="mdi:cog" />
        </div>
      </a-tooltip>
    </div>

    <div class="panel-content" v-show="!collapsed">
      <!-- 页面属性 -->
      <div v-if="!selectedComponent && currentPage" class="property-section">
        <h4>页面属性</h4>

        <a-tabs v-model:activeKey="pageActiveTab">
          <a-tab-pane key="basic" tab="基础设置">
            <a-form layout="vertical" :model="pageForm">
              <a-row :gutter="8">
                <a-col :span="24">
                  <a-form-item label="页面名称">
                    <a-input v-model:value="pageForm.name" @blur="updatePageProperty('name', pageForm.name)" />
                  </a-form-item>
                </a-col>
              </a-row>
              
              <a-row :gutter="8">
                <a-col :span="12">
                  <a-form-item label="页面路径">
                    <a-input v-model:value="pageForm.path" @blur="updatePageProperty('path', pageForm.path)" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="页面标题">
                    <a-input v-model:value="pageForm.title" @blur="updatePageProperty('title', pageForm.title)" />
                  </a-form-item>
                </a-col>
              </a-row>
              
              <a-divider orientation="left" plain>布局配置</a-divider>
              
              <a-row :gutter="8">
                <a-col :span="12">
                  <a-form-item label="布局类型">
                    <a-select v-model:value="pageForm.layout.type" @change="updatePageLayout">
                      <a-select-option value="flex">弹性布局</a-select-option>
                      <a-select-option value="grid">网格布局</a-select-option>
                      <a-select-option value="absolute">绝对定位</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12" v-if="pageForm.layout.type === 'flex'">
                  <a-form-item label="排列方向">
                    <a-select v-model:value="pageForm.layout.direction" @change="updatePageLayout">
                      <a-select-option value="row">水平</a-select-option>
                      <a-select-option value="column">垂直</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              
              <template v-if="pageForm.layout.type === 'flex'">
                <a-row :gutter="8">
                  <a-col :span="12">
                    <a-form-item label="主轴对齐">
                      <a-select v-model:value="pageForm.layout.justify" @change="updatePageLayout">
                        <a-select-option value="start">开始</a-select-option>
                        <a-select-option value="center">居中</a-select-option>
                        <a-select-option value="end">结束</a-select-option>
                        <a-select-option value="space-between">两端对齐</a-select-option>
                        <a-select-option value="space-around">环绕对齐</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="交叉轴对齐">
                      <a-select v-model:value="pageForm.layout.align" @change="updatePageLayout">
                        <a-select-option value="start">开始</a-select-option>
                        <a-select-option value="center">居中</a-select-option>
                        <a-select-option value="end">结束</a-select-option>
                        <a-select-option value="stretch">拉伸</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
              </template>
              
              <a-row :gutter="8">
                <a-col :span="12">
                  <a-form-item label="内边距">
                    <a-input-number 
                      v-model:value="pageForm.layout.padding" 
                      :min="0" 
                      addon-after="px"
                      @change="updatePageLayout"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="间距">
                    <a-input-number 
                      v-model:value="pageForm.layout.gap" 
                      :min="0" 
                      addon-after="px"
                      @change="updatePageLayout"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-tab-pane>

          <a-tab-pane key="style" tab="样式设计">
            <div class="page-style-editor">
              <a-form layout="vertical">
                <a-divider orientation="left" plain>背景设置</a-divider>
                
                <a-row :gutter="8">
                  <a-col :span="16">
                    <a-form-item label="背景颜色">
                      <a-input
                        v-model:value="pageStyleForm.backgroundColor"
                        type="color"
                        @change="updatePageStyle"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label=" ">
                      <a-button @click="clearPageBackgroundColor" size="small" block>
                        清空
                      </a-button>
                    </a-form-item>
                  </a-col>
                </a-row>

                <a-form-item label="背景图片">
                  <a-input
                    v-model:value="pageStyleForm.backgroundImage"
                    placeholder="直接输入图片链接，如：https://example.com/image.jpg"
                    @blur="updatePageStyle"
                  />
                  <div style="margin-top: 4px;">
                    <small style="color: #999;">💡 直接粘贴图片链接即可，无需添加url()</small>
                  </div>
                </a-form-item>

                <a-row :gutter="8">
                  <a-col :span="12">
                    <a-form-item label="背景大小">
                      <a-select
                        v-model:value="pageStyleForm.backgroundSize"
                        @change="updatePageStyle"
                        allow-clear
                        placeholder="选择背景大小"
                      >
                        <a-select-option value="cover">覆盖（推荐）</a-select-option>
                        <a-select-option value="contain">包含</a-select-option>
                        <a-select-option value="100% 100%">拉伸</a-select-option>
                        <a-select-option value="auto">自动</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="背景重复">
                      <a-select
                        v-model:value="pageStyleForm.backgroundRepeat"
                        @change="updatePageStyle"
                        allow-clear
                        placeholder="选择重复方式"
                      >
                        <a-select-option value="no-repeat">不重复（推荐）</a-select-option>
                        <a-select-option value="repeat">重复</a-select-option>
                        <a-select-option value="repeat-x">水平重复</a-select-option>
                        <a-select-option value="repeat-y">垂直重复</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>

                <a-row :gutter="8">
                  <a-col :span="12">
                    <a-form-item label="背景位置">
                      <a-select
                        v-model:value="pageStyleForm.backgroundPosition"
                        @change="updatePageStyle"
                        allow-clear
                        placeholder="选择背景位置"
                      >
                        <a-select-option value="center">居中（推荐）</a-select-option>
                        <a-select-option value="top">顶部</a-select-option>
                        <a-select-option value="bottom">底部</a-select-option>
                        <a-select-option value="left">左侧</a-select-option>
                        <a-select-option value="right">右侧</a-select-option>
                        <a-select-option value="top left">左上</a-select-option>
                        <a-select-option value="top right">右上</a-select-option>
                        <a-select-option value="bottom left">左下</a-select-option>
                        <a-select-option value="bottom right">右下</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="最小高度">
                      <a-input
                        v-model:value="pageStyleForm.minHeight"
                        placeholder="100vh"
                        @blur="updatePageStyle"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>

                <a-divider orientation="left" plain>高级设置</a-divider>
                
                <a-collapse>
                  <a-collapse-panel key="css" header="自定义CSS">
                    <a-textarea
                      v-model:value="pageCssValue"
                      placeholder="输入自定义CSS样式..."
                      :rows="6"
                      @blur="updatePageFromCss"
                    />
                    <div class="css-help">
                      <small>💡 支持所有CSS属性，如：padding: 20px; margin: 10px;</small>
                    </div>
                  </a-collapse-panel>
                </a-collapse>
              </a-form>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
      
      <!-- 组件属性 -->
      <div v-else-if="selectedComponent" class="property-section">
        <h4>{{ getComponentDisplayName(selectedComponent.type) }} 属性</h4>
        
        <a-tabs v-model:activeKey="activeTab">
          <a-tab-pane key="props" tab="属性配置">
            <ComponentPropsEditor 
              :component="selectedComponent"
              @update="updateComponentProps"
            />
          </a-tab-pane>
          
          <a-tab-pane key="style" tab="样式设计">
            <ComponentStyleEditor 
              :component="selectedComponent"
              @update="updateComponentStyle"
            />
          </a-tab-pane>
          
          <a-tab-pane key="events" tab="事件绑定">
            <ComponentEventsEditor 
              :component="selectedComponent"
              @update="updateComponentEvents"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-state">
        <Icon icon="mdi:cursor-pointer" class="empty-icon" />
        <div class="empty-text">选择组件或页面查看属性</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import ComponentPropsEditor from './ComponentPropsEditor.vue'
import ComponentStyleEditor from './ComponentStyleEditor.vue'
import ComponentEventsEditor from './ComponentEventsEditor.vue'

interface Props {
  collapsed?: boolean
}

interface Emits {
  (e: 'expand'): void
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false
})

const emit = defineEmits<Emits>()

import { useDesignerStore } from '../stores/designer'
import { useComponentLibraryStore } from '../stores/componentLibrary'
import { ref, computed, watch, onMounted } from 'vue'

const designerStore = useDesignerStore()
const componentLibraryStore = useComponentLibraryStore()

// 响应式数据
const activeTab = ref('props')
const pageActiveTab = ref('basic')
const pageForm = ref({
  name: '',
  path: '',
  title: '',
  layout: {
    type: 'flex',
    direction: 'column',
    justify: 'start',
    align: 'stretch',
    padding: 16,
    gap: 16
  }
})

const pageStyleForm = ref({
  backgroundColor: '',
  backgroundImage: '',
  backgroundSize: '',
  backgroundRepeat: '',
  backgroundPosition: '',
  minHeight: '100vh'
})

const pageCssValue = ref('')

// 计算属性
const { currentPage, selectedComponent } = storeToRefs(designerStore)

// 监听当前页面变化
watch(currentPage, (newPage) => {
  if (newPage) {
    pageForm.value = {
      name: newPage.name,
      path: newPage.path,
      title: newPage.title || '',
      layout: { ...newPage.layout }
    }

    // 同步页面样式
    if (newPage.style) {
      // 智能处理背景图片显示格式
      let displayBackgroundImage = newPage.style.backgroundImage || ''
      if (displayBackgroundImage.startsWith('url(') && displayBackgroundImage.endsWith(')')) {
        // 提取纯URL显示给用户
        displayBackgroundImage = displayBackgroundImage.slice(4, -1).replace(/^["']|["']$/g, '')
      }

      pageStyleForm.value = {
        backgroundColor: newPage.style.backgroundColor || '',
        backgroundImage: displayBackgroundImage,
        backgroundSize: newPage.style.backgroundSize || '',
        backgroundRepeat: newPage.style.backgroundRepeat || '',
        backgroundPosition: newPage.style.backgroundPosition || '',
        minHeight: newPage.style.minHeight || '100vh'
      }
      pageCssValue.value = styleToCss(newPage.style)
    } else {
      pageStyleForm.value = {
        backgroundColor: '',
        backgroundImage: '',
        backgroundSize: '',
        backgroundRepeat: '',
        backgroundPosition: '',
        minHeight: '100vh'
      }
      pageCssValue.value = ''
    }
  }
}, { immediate: true, deep: true })

// 获取组件显示名称
function getComponentDisplayName(type: string): string {
  const libraryItem = componentLibraryStore.getComponentByType(type)
  return libraryItem?.name || type
}

// 更新页面属性
async function updatePageProperty(key: string, value: any) {
  if (currentPage.value) {
    designerStore.updateComponent(currentPage.value.id, {
      [key]: value
    })
    // 保存到API
    await designerStore.savePageConfig()
    console.log(`✅ 页面属性 ${key} 已保存:`, value)
  }
}

// 更新页面布局
async function updatePageLayout() {
  if (currentPage.value) {
    designerStore.updateComponent(currentPage.value.id, {
      layout: { ...pageForm.value.layout }
    })
    // 保存到API
    await designerStore.savePageConfig()
    console.log('✅ 页面布局已保存:', pageForm.value.layout)
  }
}

// 更新页面样式
async function updatePageStyle() {
  if (currentPage.value) {
    // 智能处理背景图片格式
    const processedStyle = { ...pageStyleForm.value }
    if (processedStyle.backgroundImage && processedStyle.backgroundImage.trim()) {
      const inputValue = processedStyle.backgroundImage.trim()

      if (inputValue.startsWith('linear-gradient') || inputValue.startsWith('radial-gradient')) {
        // CSS渐变保持原样
        processedStyle.backgroundImage = inputValue
      } else if (inputValue.startsWith('url(')) {
        // 已经是完整CSS格式
        processedStyle.backgroundImage = inputValue
      } else if (inputValue.startsWith('http') || inputValue.startsWith('//') || inputValue.startsWith('data:')) {
        // 普通图片URL - 自动添加url()包装
        processedStyle.backgroundImage = `url("${inputValue}")`

        // 为图片设置默认的背景属性，确保图片能正确显示
        if (!processedStyle.backgroundSize) {
          processedStyle.backgroundSize = 'cover'
        }
        if (!processedStyle.backgroundRepeat) {
          processedStyle.backgroundRepeat = 'no-repeat'
        }
        if (!processedStyle.backgroundPosition) {
          processedStyle.backgroundPosition = 'center'
        }
      } else {
        // 其他格式也添加url()包装
        processedStyle.backgroundImage = `url("${inputValue}")`
      }
    }

    const cleanedStyle = Object.fromEntries(
      Object.entries(processedStyle).filter(([_, value]) =>
        value !== '' && value != null && value !== undefined
      )
    )

    console.log('🎨 更新页面样式:', {
      原有样式: currentPage.value.style,
      表单样式: pageStyleForm.value,
      处理后样式: processedStyle,
      清理后样式: cleanedStyle
    })

    // 更新页面配置中的样式（直接替换，不合并）
    const updatedPage = {
      ...currentPage.value,
      style: cleanedStyle
    }

    designerStore.setCurrentPage(updatedPage)

    // 同步到CSS编辑器
    pageCssValue.value = styleToCss(cleanedStyle)

    // 保存到API
    await designerStore.savePageConfig()
    console.log('✅ 页面样式已保存:', cleanedStyle)
  }
}

// 从CSS更新页面样式
async function updatePageFromCss() {
  if (currentPage.value) {
    try {
      const parsed = cssToStyle(pageCssValue.value)
      pageStyleForm.value = { ...pageStyleForm.value, ...parsed }

      designerStore.setCurrentPage({
        ...currentPage.value,
        style: { ...currentPage.value.style, ...parsed }
      })

      // 保存到API
      await designerStore.savePageConfig()
      console.log('✅ 页面CSS样式已保存')
    } catch (error) {
      console.error('Invalid CSS:', error)
    }
  }
}

// 样式对象转CSS字符串
function styleToCss(style: any): string {
  return Object.entries(style)
    .map(([key, value]) => {
      const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase()
      return `${cssKey}: ${value};`
    })
    .join('\n')
}

// CSS字符串转样式对象
function cssToStyle(css: string): any {
  const style: any = {}
  const rules = css.split(';').filter(rule => rule.trim())

  rules.forEach(rule => {
    // 找到第一个冒号的位置，避免URL中的冒号被误认为是属性分隔符
    const colonIndex = rule.indexOf(':')
    if (colonIndex === -1) return

    const property = rule.substring(0, colonIndex).trim()
    const value = rule.substring(colonIndex + 1).trim()

    if (property && value) {
      const camelKey = property.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
      style[camelKey] = value
    }
  })

  return style
}

// 更新组件属性
function updateComponentProps(props: any) {
  if (selectedComponent.value) {
    designerStore.updateComponent(selectedComponent.value.id, {
      props: { ...selectedComponent.value.props, ...props }
    })
  }
}

// 更新组件样式
function updateComponentStyle(style: any) {
  if (selectedComponent.value) {
    console.log('🎨 更新组件样式:', {
      componentId: selectedComponent.value.id,
      原有样式: selectedComponent.value.style,
      新样式: style
    })

    // 直接使用新样式，不合并原有样式，这样可以删除属性
    designerStore.updateComponent(selectedComponent.value.id, {
      style: style
    })
  }
}

// 更新组件事件
function updateComponentEvents(events: any) {
  if (selectedComponent.value) {
    console.log('🔄 更新组件事件:', events)
    designerStore.updateComponent(selectedComponent.value.id, {
      events: events  // 直接使用新的events对象，不要合并
    })
    console.log('✅ 事件更新完成')
  }
}



// 清空页面背景颜色
function clearPageBackgroundColor() {
  pageStyleForm.value.backgroundColor = ''
  updatePageStyle()
}
</script>

<style scoped lang="scss">
.properties-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
}

.panel-header {
  flex-shrink: 0;
  padding: 16px 16px;
  border-bottom: 1px solid #e4e4e7;
  background: #ffffff;

  .header-content {
    .header-title {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
      color: #09090b;
      line-height: 1.2;
    }

    .header-description {
      margin: 0;
      font-size: 12px;
      color: #71717a;
      line-height: 1.4;
    }
  }
}

.panel-content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.property-section {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  padding: 16px;

  h4 {
    flex-shrink: 0;
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #09090b;
    border-bottom: 1px solid #e4e4e7;
    padding-bottom: 6px;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #71717a;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
  color: #a1a1aa;
  opacity: 0.6;
}

.empty-text {
  font-size: 14px;
  color: #71717a;
}

// 全局表单样式优化
:deep(.ant-form-item) {
  margin-bottom: 12px;

  .ant-form-item-label {
    padding-bottom: 4px;

    label {
      font-size: 12px;
      font-weight: 500;
      color: #374151;

      &::after {
        display: none;
      }
    }
  }

  .ant-form-item-control-input {
    .ant-input,
    .ant-input-number,
    .ant-select-selector,
    .ant-textarea {
      border: 1px solid #e4e4e7;
      border-radius: 6px;
      font-size: 13px;

      &:focus {
        border-color: #18181b;
        box-shadow: 0 0 0 2px rgba(24, 24, 27, 0.1);
      }
    }
  }
}

:deep(.ant-tabs) {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  
  .ant-tabs-nav {
    flex-shrink: 0;
    margin-bottom: 8px;
  }
  
  .ant-tabs-content-holder {
    flex: 1;
    min-height: 0;
    
    .ant-tabs-content {
      height: 100%;
      
      .ant-tabs-tabpane {
        height: 100%;
        overflow-y: auto;
        padding-right: 4px;
        
        &::-webkit-scrollbar {
          width: 4px;
        }
        
        &::-webkit-scrollbar-track {
          background: transparent;
        }
        
        &::-webkit-scrollbar-thumb {
          background: #d4d4d8;
          border-radius: 2px;
          
          &:hover {
            background: #a1a1aa;
          }
        }
      }
    }
  }
}

:deep(.ant-collapse) {
  border: none;
  background: transparent;
  
  .ant-collapse-item {
    border: 1px solid #e4e4e7;
    border-radius: 6px;
    margin-bottom: 8px;
    
    .ant-collapse-header {
      padding: 8px 12px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .ant-collapse-content {
      border-top: 1px solid #e4e4e7;
      
      .ant-collapse-content-box {
        padding: 12px;
      }
    }
  }
}

:deep(.ant-divider) {
  margin: 12px 0 8px 0;
  font-size: 11px;
  font-weight: 500;
  color: #71717a;
  
  &.ant-divider-with-text {
    .ant-divider-inner-text {
      padding: 0 8px;
      background: #ffffff;
    }
  }
}

:deep(.ant-row) {
  .ant-col {
    &:not(:last-child) {
      padding-right: 4px;
    }
    &:not(:first-child) {
      padding-left: 4px;
    }
  }
}

.page-style-editor {
  .css-help {
    margin-top: 6px;

    small {
      color: #999;
      font-size: 11px;
    }
  }
}

// 折叠状态样式
.properties-panel.collapsed {
  .collapsed-actions {
    padding: 16px 8px;

    .collapsed-action-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      margin-bottom: 12px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      color: #666;

      &:hover {
        background: #f0f0f0;
        color: #1890ff;
      }

      .iconify {
        font-size: 20px;
      }
    }
  }
}
</style>
