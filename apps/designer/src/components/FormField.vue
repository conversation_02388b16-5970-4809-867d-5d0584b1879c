<template>
  <a-form-item :label="field.title || fieldKey" :help="field.description">
    <!-- 数组类型 -->
    <ArrayField
      v-if="field.type === 'array'"
      :field="field"
      :model-value="modelValue"
      @update:model-value="handleUpdate"
    />

    <!-- 菜单项配置类型 -->
    <MenuItemsEditor
      v-else-if="field.type === 'menuItems'"
      :modelValue="modelValue"
      :eventConfigs="getEventConfigs()"
      @update:modelValue="handleUpdate"
      @update:eventConfigs="handleEventConfigsUpdate"
    />

    <!-- 可点击区域配置类型 -->
    <ClickableAreasEditor
      v-else-if="field.type === 'clickableAreas'"
      :modelValue="modelValue"
      @update:modelValue="handleUpdate"
    />

    <!-- 图标选择类型 -->
    <IconPicker
      v-else-if="isIconField"
      :modelValue="modelValue"
      :placeholder="field.description"
      @update:modelValue="handleUpdate"
    />

    <!-- 字符串类型 -->
    <a-input
      v-else-if="field.type === 'string' && !field.enum"
      :value="modelValue"
      :placeholder="field.description"
      @input="handleInput"
    />
    
    <!-- 数字类型 -->
    <a-input-number 
      v-else-if="field.type === 'number'"
      :value="modelValue" 
      :min="field.minimum"
      :max="field.maximum"
      :step="field.multipleOf || 1"
      @change="handleUpdate"
      style="width: 100%"
    />
    
    <!-- 布尔类型 -->
    <a-switch 
      v-else-if="field.type === 'boolean'"
      :checked="modelValue" 
      @change="handleUpdate"
    />
    
    <!-- 枚举选择类型 -->
    <a-select 
      v-else-if="field.enum"
      :value="modelValue" 
      :placeholder="field.description"
      @change="handleUpdate"
    >
      <a-select-option 
        v-for="option in field.enum" 
        :key="option" 
        :value="option"
      >
        {{ option }}
      </a-select-option>
    </a-select>
    
    <!-- 对象类型 -->
    <div v-else-if="field.type === 'object' && field.properties">
      <a-collapse>
        <a-collapse-panel :key="fieldKey" :header="field.title || fieldKey">
          <template v-for="(subField, subKey) in field.properties" :key="subKey">
            <FormField
              :field="subField"
              :fieldKey="String(subKey)"
              :modelValue="(modelValue || {})[subKey]"
              @update:modelValue="(value) => updateSubField(String(subKey), value)"
            />
          </template>
        </a-collapse-panel>
      </a-collapse>
    </div>
    
    <!-- 数组类型 -->
    <div v-else-if="field.type === 'array'">
      <a-button @click="addArrayItem" type="dashed" block style="margin-bottom: 8px">
        <Icon icon="mdi:plus" />
        添加项目
      </a-button>
      <div v-for="(item, index) in (modelValue || [])" :key="index" style="margin-bottom: 8px;">
        <a-space style="width: 100%">
          <FormField 
            v-if="field.items"
            :field="field.items"
            :fieldKey="`${fieldKey}[${index}]`"
            :modelValue="item"
            @update:modelValue="(value) => updateArrayItem(index, value)"
            style="flex: 1"
          />
          <a-button @click="removeArrayItem(index)" type="text" danger>
            <Icon icon="mdi:delete" />
          </a-button>
        </a-space>
      </div>
    </div>
    
    <!-- 未知类型，显示JSON编辑器 -->
    <a-textarea
      v-else
      :value="JSON.stringify(modelValue, null, 2)"
      :placeholder="`${field.type} 类型，请输入JSON`"
      @input="handleJsonChange"
      :rows="3"
    />
  </a-form-item>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'
import IconPicker from './IconPicker.vue'
import ArrayField from './ArrayField.vue'
import MenuItemsEditor from './config/MenuItemsEditor.vue'
import ClickableAreasEditor from './config/ClickableAreasEditor.vue'

const props = defineProps<{
  field: any
  fieldKey: string
  modelValue: any
}>()

const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()

// 计算属性
const isIconField = computed(() => {
  // 数字类型不应该被识别为图标字段
  if (props.field.type === 'number') {
    return false
  }

  // 检查字段名称或描述是否包含图标相关关键词
  const fieldName = props.fieldKey.toLowerCase()
  const fieldTitle = props.field.title?.toLowerCase() || ''
  const fieldDescription = props.field.description?.toLowerCase() || ''

  const iconKeywords = ['icon', '图标', 'iconify']

  return iconKeywords.some(keyword =>
    fieldName.includes(keyword) ||
    fieldTitle.includes(keyword) ||
    fieldDescription.includes(keyword)
  )
})

// 处理输入
function handleInput(e: Event) {
  const target = e.target as HTMLInputElement
  emit('update:modelValue', target.value)
}

// 处理更新
function handleUpdate(value: any) {
  emit('update:modelValue', value)
}

// 处理JSON变化
function handleJsonChange(e: Event) {
  const target = e.target as HTMLTextAreaElement
  const value = target.value

  // 如果是空值，直接更新
  if (!value.trim()) {
    emit('update:modelValue', null)
    return
  }

  try {
    const parsed = JSON.parse(value)
    emit('update:modelValue', parsed)
  } catch (error) {
    // JSON解析错误时，暂时不更新值，等待用户完成输入
    console.warn('JSON parse error:', error)
  }
}

// 更新子字段
function updateSubField(subKey: string, value: any) {
  const newValue = { ...(props.modelValue || {}), [subKey]: value }
  emit('update:modelValue', newValue)
}

// 添加数组项
function addArrayItem() {
  const currentArray = props.modelValue || []
  const defaultValue = props.field.items?.default || null
  emit('update:modelValue', [...currentArray, defaultValue])
}

// 更新数组项
function updateArrayItem(index: number, value: any) {
  const currentArray = [...(props.modelValue || [])]
  currentArray[index] = value
  emit('update:modelValue', currentArray)
}

// 删除数组项
function removeArrayItem(index: number) {
  const currentArray = [...(props.modelValue || [])]
  currentArray.splice(index, 1)
  emit('update:modelValue', currentArray)
}

// 获取事件配置（菜单项编辑器需要）
function getEventConfigs() {
  // 这里应该从父组件获取事件配置，暂时返回空对象
  return {}
}

// 处理事件配置更新（菜单项编辑器需要）
function handleEventConfigsUpdate(eventConfigs: any) {
  // 这里应该通知父组件更新事件配置
  console.log('事件配置更新:', eventConfigs)
  // TODO: 实现事件配置的保存逻辑
}
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}
</style>
