<template>
  <a-card title="应用统计">
    <div class="stats-grid">
      <a-statistic
        title="可配置页面"
        :value="totalPages"
        :value-style="{ color: '#1890ff' }"
      >
        <template #suffix>个</template>
      </a-statistic>
      
      <a-statistic
        title="已发布页面"
        :value="publishedPages"
        :value-style="{ color: '#52c41a' }"
      >
        <template #suffix>个</template>
      </a-statistic>
      
      <a-statistic
        title="底部导航"
        :value="tabBarStatus"
        :value-style="{ color: tabBarEnabled ? '#52c41a' : '#faad14' }"
      />
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  totalPages: number
  publishedPages: number
  tabBarEnabled: boolean
}

const props = defineProps<Props>()

const tabBarStatus = computed(() => {
  return props.tabBarEnabled ? '已启用' : '未启用'
})
</script>

<style scoped lang="scss">
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;

  :deep(.ant-statistic) {
    text-align: center;
    
    .ant-statistic-title {
      font-size: 14px;
      color: #8c8c8c;
      margin-bottom: 8px;
    }
    
    .ant-statistic-content {
      font-size: 24px;
      font-weight: 600;
    }
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
