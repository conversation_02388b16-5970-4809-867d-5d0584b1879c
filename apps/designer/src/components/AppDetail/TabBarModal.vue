<template>
  <a-modal
    v-model:open="visible"
    title="TabBar设置"
    width="900px"
    :body-style="{ padding: '24px' }"
    @ok="handleSave"
    @cancel="handleCancel"
  >
    <div class="tabbar-modal-content">
      <!-- 基础设置 -->
      <a-card title="基础设置" size="small" class="setting-card">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="启用TabBar">
              <a-switch v-model:checked="formData.enabled" />
            </a-form-item>
          </a-col>
          <a-col :span="16">
            <a-form-item label="TabBar类型">
              <a-radio-group v-model:value="formData.type">
                <a-radio value="default">默认样式</a-radio>
                <a-radio value="rounded">圆角样式</a-radio>
                <a-radio value="minimal">极简样式</a-radio>
                <a-radio value="floating">悬浮样式</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-alert
          :message="getTypeDescription(formData.type)"
          type="info"
          show-icon
          style="margin-top: 12px;"
        />
      </a-card>

      <!-- 样式设置 -->
      <StyleSettings
        v-if="formData.enabled"
        :style="formData.style"
        @update:style="(newStyle) => formData.style = newStyle"
      />

      <!-- 标签管理 -->
      <TabManager
        v-if="formData.enabled"
        :tabs="formData.tabs"
        @update:tabs="(newTabs) => formData.tabs = newTabs"
        :available-routes="availableRoutes"
      />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import StyleSettings from './TabBarStyleSettings.vue'
import TabManager from './TabBarTabManager.vue'

interface Props {
  open: boolean
  tabBarData: any
  availableRoutes: any[]
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'save', data: any): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const formData = ref({
  enabled: false,
  type: 'default',
  tabs: [] as any[],
  style: {
    activeColor: '#1890ff',
    inactiveColor: '#8c8c8c',
    backgroundColor: '#ffffff',
    height: 50,
    iconTextGap: 2,
    borderRadius: 0,
    showText: true
  }
})

// 监听props变化
watch(() => props.open, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 每次打开弹窗时重新初始化数据
    initFormData()
  }
})

// 初始化表单数据
function initFormData() {
  const tabBarData = props.tabBarData || {}
  formData.value = {
    enabled: tabBarData.enabled || false,
    type: tabBarData.type || 'default',
    tabs: [...(tabBarData.tabs || [])],
    style: {
      activeColor: tabBarData.style?.activeColor || '#1890ff',
      inactiveColor: tabBarData.style?.inactiveColor || '#8c8c8c',
      backgroundColor: tabBarData.style?.backgroundColor || '#ffffff',
      height: tabBarData.style?.height || 50,
      iconTextGap: tabBarData.style?.iconTextGap || 2,
      borderRadius: tabBarData.style?.borderRadius || 0,
      showText: tabBarData.style?.showText !== false
    }
  }
}

watch(visible, (newVal) => {
  emit('update:open', newVal)
})

function getTypeDescription(type: string) {
  const descriptions: Record<string, string> = {
    default: '经典底部导航栏，适合大多数应用场景',
    rounded: '顶部圆角设计，更加现代化的视觉效果',
    minimal: '极简风格，透明背景，适合内容为主的应用',
    floating: '悬浮式设计，距离底部有间距，更具层次感'
  }
  return descriptions[type] || '未知类型'
}

function handleSave() {
  emit('save', formData.value)
  visible.value = false
}

function handleCancel() {
  emit('cancel')
  visible.value = false
}
</script>

<style scoped lang="scss">
.tabbar-modal-content {
  .setting-card {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
