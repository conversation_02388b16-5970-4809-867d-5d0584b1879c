<template>
  <a-card title="首页管理">
    <template #extra>
      <a-tag :color="homePageExists ? 'success' : 'warning'">
        {{ homePageExists ? '已配置' : '未配置' }}
      </a-tag>
    </template>

    <div class="homepage-content">
      <!-- 首页状态 -->
      <div v-if="homePageExists" class="homepage-status">
        <a-descriptions :column="1" size="small">
          <a-descriptions-item label="发布状态">
            <a-tag :color="homePagePublished ? 'success' : 'warning'">
              {{ homePagePublished ? '已发布' : '草稿' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="最后更新">
            {{ formatTime(homePageUpdateTime) }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 空状态 -->
      <div v-else class="homepage-empty">
        <a-empty description="尚未配置首页">
          <template #image>
            <Icon icon="mdi:home-outline" style="font-size: 48px; color: #d9d9d9;" />
          </template>
          <p>创建首页来展示应用的主要内容</p>
        </a-empty>
      </div>

      <!-- 操作按钮 -->
      <div class="homepage-actions">
        <a-space>
          <a-button 
            @click="$emit('edit-home')" 
            type="primary" 
            :disabled="!app"
          >
            <Icon icon="mdi:home-edit" />
            {{ homePageExists ? '编辑首页' : '创建首页' }}
          </a-button>

          <a-button 
            @click="$emit('preview-home')" 
            :disabled="!homePageExists"
          >
            <Icon icon="mdi:eye" />
            预览
          </a-button>

          <a-button
            v-if="!homePagePublished"
            @click="$emit('publish-home')"
            :disabled="!homePageExists"
            :loading="publishing"
            type="primary"
            ghost
          >
            <Icon icon="mdi:publish" />
            发布
          </a-button>

          <a-button
            v-else
            @click="$emit('unpublish-home')"
            :disabled="!homePageExists"
            :loading="publishing"
            danger
          >
            <Icon icon="mdi:publish-off" />
            取消发布
          </a-button>
        </a-space>
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'

interface Props {
  app: any
  homePageConfig: any
  publishing: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'edit-home': []
  'preview-home': []
  'publish-home': []
  'unpublish-home': []
}>()

const homePageExists = computed(() => !!props.homePageConfig)
const homePagePublished = computed(() => {
  if (!props.homePageConfig) return false
  const published = props.homePageConfig.published
  return published === 1 || published === true
})
const homePageUpdateTime = computed(() => props.homePageConfig?.updateTime)

function formatTime(time: string | Date | undefined): string {
  if (!time) return '未知'
  
  try {
    const date = new Date(time)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return '格式错误'
  }
}
</script>

<style scoped lang="scss">
.homepage-content {
  .homepage-status {
    margin-bottom: 24px;
  }

  .homepage-empty {
    margin-bottom: 24px;
    
    p {
      margin-top: 8px;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .homepage-actions {
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
