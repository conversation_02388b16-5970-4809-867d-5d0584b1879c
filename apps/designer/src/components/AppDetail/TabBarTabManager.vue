<template>
  <a-card title="标签管理" size="small" class="setting-card">
    <template #extra>
      <a-button @click="addTab" type="primary" size="small">
        <Icon icon="mdi:plus" />
        添加标签
      </a-button>
    </template>

    <div class="tab-manager-content">
      <!-- 标签列表 -->
      <div v-if="localTabs.length === 0" class="empty-tabs">
        <a-empty description="暂无标签">
          <a-button @click="addTab" type="primary">添加第一个标签</a-button>
        </a-empty>
      </div>

      <div v-else class="tabs-list">
        <a-card
          v-for="(tab, index) in localTabs"
          :key="tab.id || index"
          size="small"
          class="tab-item-card"
        >
          <template #title>
            <div class="tab-card-title">
              <Icon :icon="tab.icon || 'mdi:home'" />
              <span>{{ tab.label || '未命名标签' }}</span>
              <a-tag v-if="tab.pageId" color="blue" size="small">
                已配置
              </a-tag>
            </div>
          </template>

          <template #extra>
            <a-popconfirm
              title="确定删除这个标签吗？"
              @confirm="removeTab(index)"
            >
              <a-button danger size="small" type="text">
                <Icon icon="mdi:delete" />
              </a-button>
            </a-popconfirm>
          </template>

          <div class="tab-form">
            <a-row :gutter="12">
              <a-col :span="6">
                <a-form-item label="标签名称" :label-col="{ span: 24 }">
                  <a-input
                    v-model:value="tab.label"
                    placeholder="首页"
                    size="small"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="6">
                <a-form-item label="图标" :label-col="{ span: 24 }">
                  <div class="icon-input-group">
                    <IconPicker
                      v-model:model-value="tab.icon"
                      placeholder="选择图标"
                      size="small"
                    />
                    <a-button
                      @click="resetTabIcon(tab)"
                      size="small"
                      title="重置图标"
                    >
                      <Icon icon="mdi:refresh" />
                    </a-button>
                  </div>
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item label="关联页面" :label-col="{ span: 24 }">
                  <a-select
                    v-model:value="tab.pageId"
                    placeholder="选择页面"
                    @change="(value) => handlePageSelect(tab, value)"
                    allow-clear
                    size="small"
                    show-search
                    :filter-option="filterPageOption"
                    :dropdown-match-select-width="false"
                    :dropdown-style="{ minWidth: '300px' }"
                    :not-found-content="availableRoutes.length === 0 ? '暂无可用页面' : '未找到匹配页面'"
                  >
                    <a-select-option
                      v-for="route in availableRoutes"
                      :key="route.path"
                      :value="route.path"
                      :title="route.description || route.title"
                    >
                      <div class="page-option">
                        <Icon :icon="route.icon || 'mdi:file-document'" />
                        <span>{{ route.title }}</span>
                        <a-tag size="small">{{ route.path }}</a-tag>
                      </div>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>

              <a-col :span="4">
                <a-form-item label="排序" :label-col="{ span: 24 }">
                  <a-input-number
                    v-model:value="tab.order"
                    :min="1"
                    size="small"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 路径显示 -->
            <div v-if="tab.pageId" class="tab-path-info">
              <a-descriptions :column="2" size="small">
                <a-descriptions-item label="页面路径">
                  <a-typography-text code>{{ tab.path || tab.pageId }}</a-typography-text>
                </a-descriptions-item>
                <a-descriptions-item label="页面类型">
                  {{ getPageType(tab.pageId) }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 操作提示 -->
      <a-alert
        message="标签配置说明"
        description="每个标签代表TabBar中的一个导航项。建议配置3-5个标签，确保用户能够快速访问主要功能页面。标签会按照排序字段从小到大排列。"
        type="info"
        show-icon
        style="margin-top: 16px;"
      />
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'
import { message } from 'ant-design-vue'
import IconPicker from '../IconPicker.vue'

interface Props {
  tabs: any[]
  availableRoutes: any[]
}

interface Emits {
  (e: 'update:tabs', value: any[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 直接使用props，不需要本地拷贝
const localTabs = computed({
  get: () => props.tabs || [],
  set: (newVal) => {
    emit('update:tabs', [...newVal])
  }
})

function addTab() {
  const currentTabs = [...localTabs.value]
  const newOrder = currentTabs.length + 1
  const newTab = {
    id: `tab-${Date.now()}`,
    label: `标签${newOrder}`,
    icon: 'mdi:home',
    path: '',
    pageId: '',
    order: newOrder
  }
  currentTabs.push(newTab)
  localTabs.value = currentTabs
}

function removeTab(index: number) {
  const currentTabs = [...localTabs.value]
  currentTabs.splice(index, 1)
  // 重新排序
  currentTabs.forEach((tab, idx) => {
    tab.order = idx + 1
  })
  localTabs.value = currentTabs
}

function handlePageSelect(tab: any, pagePath: string) {
  if (!pagePath) {
    tab.path = ''
    tab.pageId = ''
    return
  }

  // 从可用路由中查找选中的页面
  const selectedPage = props.availableRoutes.find(route => route.path === pagePath)
  
  if (selectedPage) {
    tab.path = selectedPage.path
    tab.pageId = selectedPage.path
    
    // 自动填充标签名称（如果当前是默认名称）
    if (!tab.label || tab.label.startsWith('标签')) {
      tab.label = selectedPage.title
    }
    
    // 设置默认图标
    if (!tab.icon || tab.icon === 'mdi:home') {
      tab.icon = selectedPage.icon || 'mdi:file-document'
    }
  }
}

function resetTabIcon(tab: any) {
  if (tab.icon && tab.icon.includes('|')) {
    // 如果图标包含颜色，移除颜色部分
    const iconName = tab.icon.split('|')[0]
    tab.icon = iconName
    message.success('图标颜色已重置')
  } else {
    message.info('当前图标没有自定义颜色')
  }
}

function getPageType(pageId: string) {
  const route = props.availableRoutes.find(r => r.path === pageId)
  return route?.category || '未知'
}

function filterPageOption(input: string, option: any) {
  const route = props.availableRoutes.find(r => r.path === option.value)
  if (!route) return false
  
  const searchText = input.toLowerCase()
  return (
    route.title.toLowerCase().includes(searchText) ||
    route.path.toLowerCase().includes(searchText)
  )
}
</script>

<style scoped lang="scss">
.setting-card {
  .tab-manager-content {
    .empty-tabs {
      text-align: center;
      padding: 40px 0;
    }

    .tabs-list {
      .tab-item-card {
        margin-bottom: 16px;
        border: 1px solid #e8e8e8;
        
        &:last-child {
          margin-bottom: 0;
        }

        .tab-card-title {
          display: flex;
          align-items: center;
          gap: 8px;
          
          span {
            font-weight: 500;
          }
        }

        .tab-form {
          .icon-input-group {
            display: flex;
            gap: 4px;
            
            :deep(.icon-picker) {
              flex: 1;
            }
          }

          .tab-path-info {
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #f0f0f0;
          }
        }

        .page-option {
          display: flex;
          align-items: center;
          gap: 8px;
          
          span {
            flex: 1;
          }
        }
      }
    }
  }
}
</style>
