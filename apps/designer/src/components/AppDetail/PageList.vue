<template>
  <a-card title="可配置页面">
    <template #extra>
      <a-button @click="$emit('refresh')" size="small">
        <Icon icon="mdi:refresh" />
        刷新
      </a-button>
    </template>

    <div class="page-list-content">
      <!-- 空状态 -->
      <a-empty v-if="pages.length === 0" description="暂无可配置页面" />

      <!-- 页面列表 -->
      <div v-else class="pages-grid">
        <a-card
          v-for="page in pages"
          :key="page.path"
          size="small"
          :class="['page-card', { 'published': page.isPublished }]"
          hoverable
          @click="$emit('edit-page', page)"
        >
          <template #title>
            <div class="page-title">
              <Icon :icon="page.icon || 'mdi:file-document'" class="page-icon" />
              <span>{{ page.title }}</span>
              <a-tag 
                :color="page.isPublished ? 'success' : 'warning'" 
                size="small"
              >
                {{ page.isPublished ? '已发布' : '未发布' }}
              </a-tag>
            </div>
          </template>

          <template #extra>
            <a-dropdown :trigger="['click']" @click.stop>
              <a-button type="text" size="small">
                <Icon icon="mdi:dots-vertical" />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="$emit('edit-page', page)">
                    <Icon icon="mdi:pencil" />
                    设计页面
                  </a-menu-item>
                  <a-menu-item 
                    v-if="!page.isPublished"
                    @click="$emit('publish-page', page)"
                  >
                    <Icon icon="mdi:publish" />
                    发布页面
                  </a-menu-item>
                  <a-menu-item 
                    v-else
                    @click="$emit('unpublish-page', page)"
                    danger
                  >
                    <Icon icon="mdi:publish-off" />
                    取消发布
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>

          <div class="page-info">
            <a-descriptions :column="1" size="small">
              <a-descriptions-item label="路径">
                <a-typography-text code>{{ page.path }}</a-typography-text>
              </a-descriptions-item>
              <a-descriptions-item label="分类">
                {{ getCategoryName(page.category) }}
              </a-descriptions-item>
              <a-descriptions-item 
                v-if="page.updateTime" 
                label="更新时间"
              >
                {{ formatTime(page.updateTime) }}
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </a-card>
      </div>

      <!-- 说明信息 -->
      <a-alert
        message="业务页面自定义"
        description="这些是除首页外的其他业务页面，支持低代码自定义。您可以重新设计页面布局、添加自定义组件、配置交互逻辑等。首页请使用上方的专用编辑入口。"
        type="info"
        show-icon
        class="page-description"
      />
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'

interface Props {
  pages: any[]
}

defineProps<Props>()

defineEmits<{
  'refresh': []
  'edit-page': [page: any]
  'publish-page': [page: any]
  'unpublish-page': [page: any]
}>()

function getCategoryName(categoryId: string) {
  // 这里可以从props传入categories或者使用store
  const categoryMap: Record<string, string> = {
    'basic': '基础页面',
    'business': '业务页面',
    'system': '系统页面'
  }
  return categoryMap[categoryId] || categoryId
}

function formatTime(time: string | Date | undefined): string {
  if (!time) return '未知'
  
  try {
    const date = new Date(time)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return '格式错误'
  }
}
</script>

<style scoped lang="scss">
.page-list-content {
  .pages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }

  .page-card {
    cursor: pointer;
    transition: all 0.2s;

    &.published {
      border-color: #52c41a;
      
      &:hover {
        border-color: #52c41a;
        box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
      }
    }

    &:hover {
      border-color: #1890ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
    }

    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;

      .page-icon {
        color: #1890ff;
        font-size: 16px;
      }

      span {
        flex: 1;
        font-weight: 500;
      }
    }

    .page-info {
      :deep(.ant-descriptions-item-label) {
        font-size: 12px;
        color: #8c8c8c;
      }

      :deep(.ant-descriptions-item-content) {
        font-size: 12px;
      }
    }
  }

  .page-description {
    margin: 0;
  }
}

@media (max-width: 768px) {
  .pages-grid {
    grid-template-columns: 1fr;
  }
}
</style>
