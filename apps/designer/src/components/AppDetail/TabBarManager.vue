<template>
  <a-card title="TabBar配置" :loading="loading">
    <div class="tabbar-content">
      <!-- TabBar状态信息 -->
      <a-descriptions :column="1" size="small" class="tabbar-info">
        <a-descriptions-item label="TabBar状态">
          <a-tag :color="app?.tabBar?.enabled ? 'success' : 'default'">
            {{ app?.tabBar?.enabled ? '已启用' : '未启用' }}
          </a-tag>
        </a-descriptions-item>
        
        <a-descriptions-item 
          v-if="app?.tabBar?.enabled" 
          label="标签数量"
        >
          {{ app?.tabBar?.tabs?.length || 0 }} 个
        </a-descriptions-item>
        
        <a-descriptions-item 
          v-if="app?.tabBar?.enabled" 
          label="样式主题"
        >
          <div class="theme-preview">
            <div 
              class="color-dot" 
              :style="{ backgroundColor: app?.tabBar?.style?.activeColor || '#1890ff' }"
            ></div>
            {{ app?.tabBar?.style?.activeColor || '#1890ff' }}
          </div>
        </a-descriptions-item>
      </a-descriptions>

      <!-- 操作按钮 -->
      <div class="tabbar-actions">
        <a-space>
          <a-button 
            @click="$emit('edit-tabbar')" 
            type="primary" 
            :disabled="!app"
          >
            <Icon icon="mdi:tab" />
            {{ app?.tabBar?.enabled ? '编辑TabBar' : '配置TabBar' }}
          </a-button>

          <a-button 
            @click="$emit('preview-tabbar')" 
            :disabled="!app?.tabBar?.enabled"
          >
            <Icon icon="mdi:eye" />
            预览TabBar
          </a-button>

          <a-button
            @click="$emit('toggle-tabbar')"
            :disabled="!app"
            :type="app?.tabBar?.enabled ? 'default' : 'primary'"
            ghost
          >
            <Icon :icon="app?.tabBar?.enabled ? 'mdi:tab-remove' : 'mdi:tab-plus'" />
            {{ app?.tabBar?.enabled ? '禁用TabBar' : '启用TabBar' }}
          </a-button>
        </a-space>
      </div>

      <!-- 说明信息 -->
      <a-alert
        message="TabBar导航配置"
        description="TabBar是应用底部的导航栏，可以配置多个标签页，支持自定义图标、颜色和跳转页面。启用后用户可以快速在不同页面间切换。"
        type="info"
        show-icon
        class="tabbar-description"
      />
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'

interface Props {
  app: any
  loading: boolean
}

defineProps<Props>()

defineEmits<{
  'edit-tabbar': []
  'preview-tabbar': []
  'toggle-tabbar': []
}>()
</script>

<style scoped lang="scss">
.tabbar-content {
  .tabbar-info {
    margin-bottom: 24px;
  }

  .theme-preview {
    display: flex;
    align-items: center;
    gap: 8px;

    .color-dot {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 1px solid #d9d9d9;
    }
  }

  .tabbar-actions {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .tabbar-description {
    margin: 0;
  }
}
</style>
