<template>
  <a-card title="样式设置" size="small" class="setting-card">
    <!-- 颜色设置 -->
    <div class="style-section">
      <h4>颜色配置</h4>
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="激活颜色">
            <div class="color-input-group">
              <input
                :value="localStyle.activeColor.value"
                @input="(e) => localStyle.activeColor.value = (e.target as HTMLInputElement).value"
                type="color"
                class="color-picker"
              />
              <a-input
                v-model:value="localStyle.activeColor.value"
                placeholder="#1890ff"
                class="color-text"
              />
              <a-button
                size="small"
                @click="resetColor('activeColor', '#1890ff')"
              >
                重置
              </a-button>
            </div>
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="非激活颜色">
            <div class="color-input-group">
              <input
                :value="localStyle.inactiveColor.value"
                @input="(e) => localStyle.inactiveColor.value = (e.target as HTMLInputElement).value"
                type="color"
                class="color-picker"
              />
              <a-input
                v-model:value="localStyle.inactiveColor.value"
                placeholder="#8c8c8c"
                class="color-text"
              />
              <a-button
                size="small"
                @click="resetColor('inactiveColor', '#8c8c8c')"
              >
                重置
              </a-button>
            </div>
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="背景颜色">
            <div class="color-input-group">
              <input
                :value="localStyle.backgroundColor.value"
                @input="(e) => localStyle.backgroundColor.value = (e.target as HTMLInputElement).value"
                type="color"
                class="color-picker"
              />
              <a-input
                v-model:value="localStyle.backgroundColor.value"
                placeholder="#ffffff"
                class="color-text"
              />
              <a-button
                size="small"
                @click="resetColor('backgroundColor', '#ffffff')"
              >
                重置
              </a-button>
            </div>
          </a-form-item>
        </a-col>
      </a-row>
    </div>

    <!-- 尺寸设置 -->
    <a-divider />
    <div class="style-section">
      <h4>尺寸配置</h4>
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="TabBar高度">
            <a-input-number
              v-model:value="localStyle.height.value"
              :min="40"
              :max="100"
              addon-after="px"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="6">
          <a-form-item label="图标文字间距">
            <a-input-number
              v-model:value="localStyle.iconTextGap.value"
              :min="0"
              :max="20"
              addon-after="px"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="6">
          <a-form-item label="圆角大小">
            <a-input-number
              v-model:value="localStyle.borderRadius.value"
              :min="0"
              :max="50"
              addon-after="px"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="6">
          <a-form-item label="显示文字">
            <a-switch v-model:checked="localStyle.showText.value" />
          </a-form-item>
        </a-col>
      </a-row>
    </div>

    <!-- 预览区域 -->
    <a-divider />
    <div class="style-section">
      <h4>样式预览</h4>
      <div class="preview-container">
        <div
          class="tabbar-preview"
          :style="{
            backgroundColor: localStyle.backgroundColor.value,
            height: localStyle.height.value + 'px',
            borderRadius: localStyle.borderRadius.value + 'px'
          }"
        >
          <div class="preview-tab active">
            <Icon
              icon="mdi:home"
              class="tab-icon"
              :style="{ color: localStyle.activeColor.value }"
            />
            <div
              v-if="localStyle.showText.value"
              class="tab-text"
              :style="{
                color: localStyle.activeColor.value,
                marginTop: localStyle.iconTextGap.value + 'px'
              }"
            >
              首页
            </div>
          </div>
          <div class="preview-tab">
            <Icon
              icon="mdi:format-list-bulleted"
              class="tab-icon"
              :style="{ color: localStyle.inactiveColor.value }"
            />
            <div
              v-if="localStyle.showText.value"
              class="tab-text"
              :style="{
                color: localStyle.inactiveColor.value,
                marginTop: localStyle.iconTextGap.value + 'px'
              }"
            >
              列表
            </div>
          </div>
          <div class="preview-tab">
            <Icon
              icon="mdi:account"
              class="tab-icon"
              :style="{ color: localStyle.inactiveColor.value }"
            />
            <div
              v-if="localStyle.showText.value"
              class="tab-text"
              :style="{
                color: localStyle.inactiveColor.value,
                marginTop: localStyle.iconTextGap.value + 'px'
              }"
            >
              我的
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'

interface Props {
  style: any
}

interface Emits {
  (e: 'update:style', value: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 创建响应式的样式对象，每个属性都有独立的computed
const localStyle = {
  activeColor: computed({
    get: () => props.style?.activeColor || '#1890ff',
    set: (val) => emit('update:style', { ...props.style, activeColor: val })
  }),
  inactiveColor: computed({
    get: () => props.style?.inactiveColor || '#8c8c8c',
    set: (val) => emit('update:style', { ...props.style, inactiveColor: val })
  }),
  backgroundColor: computed({
    get: () => props.style?.backgroundColor || '#ffffff',
    set: (val) => emit('update:style', { ...props.style, backgroundColor: val })
  }),
  height: computed({
    get: () => props.style?.height || 50,
    set: (val) => emit('update:style', { ...props.style, height: val })
  }),
  iconTextGap: computed({
    get: () => props.style?.iconTextGap || 2,
    set: (val) => emit('update:style', { ...props.style, iconTextGap: val })
  }),
  borderRadius: computed({
    get: () => props.style?.borderRadius || 0,
    set: (val) => emit('update:style', { ...props.style, borderRadius: val })
  }),
  showText: computed({
    get: () => props.style?.showText !== false,
    set: (val) => emit('update:style', { ...props.style, showText: val })
  })
}

function resetColor(key: string, defaultValue: string) {
  if (key === 'activeColor') {
    localStyle.activeColor.value = defaultValue
  } else if (key === 'inactiveColor') {
    localStyle.inactiveColor.value = defaultValue
  } else if (key === 'backgroundColor') {
    localStyle.backgroundColor.value = defaultValue
  }
}
</script>

<style scoped lang="scss">
.setting-card {
  .style-section {
    h4 {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #262626;
    }
  }

  .color-input-group {
    display: flex;
    gap: 8px;
    align-items: center;

    .color-picker {
      width: 32px;
      height: 32px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      background: none;
      
      &::-webkit-color-swatch-wrapper {
        padding: 0;
      }
      
      &::-webkit-color-swatch {
        border: none;
        border-radius: 4px;
      }
    }

    .color-text {
      flex: 1;
    }
  }

  .preview-container {
    display: flex;
    justify-content: center;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 8px;

    .tabbar-preview {
      display: flex;
      justify-content: space-around;
      align-items: center;
      width: 300px;
      border: 1px solid #e8e8e8;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .preview-tab {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px;
        cursor: pointer;

        .tab-icon {
          font-size: 20px;
          width: 20px;
          height: 20px;
        }

        .tab-text {
          font-size: 12px;
          font-weight: 500;
        }

        &.active {
          .tab-icon,
          .tab-text {
            font-weight: 600;
          }
        }
      }
    }
  }
}
</style>
