<template>
  <div class="app-header">
    <!-- 导航栏 -->
    <div class="header-nav">
      <a-button @click="handleGoBack" type="text" class="back-btn">
        <Icon icon="mdi:arrow-left" />
        {{ backButtonText }}
      </a-button>
      
      <a-breadcrumb>
        <a-breadcrumb-item>应用管理</a-breadcrumb-item>
        <a-breadcrumb-item>{{ app?.name || '应用详情' }}</a-breadcrumb-item>
      </a-breadcrumb>
    </div>

    <!-- 应用信息卡片 -->
    <a-card class="app-info-card">
      <div class="app-info-content">
        <div class="app-icon-section">
          <a-avatar :size="80" class="app-avatar">
            <Icon :icon="app?.icon || 'mdi:application'" />
          </a-avatar>
          <a-tag :color="app?.published ? 'success' : 'warning'">
            {{ app?.published ? '已发布' : '草稿' }}
          </a-tag>
        </div>

        <div class="app-meta">
          <h1 class="app-title">{{ app?.name }}</h1>
          <p class="app-description">{{ app?.description }}</p>
          
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="应用ID">
              <a-typography-text code>{{ app?.id }}</a-typography-text>
            </a-descriptions-item>
            <a-descriptions-item label="应用类型">
              {{ getAppTypeName(app?.appType) }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatTime(app?.createTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="更新时间">
              {{ formatTime(app?.updateTime) }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <div class="app-actions">
          <a-space direction="vertical" size="small">
            <a-button @click="$emit('edit-home')" type="primary" block>
              <Icon icon="mdi:home-edit" />
              编辑首页
            </a-button>
            <a-button @click="$emit('preview-app')" block>
              <Icon icon="mdi:eye" />
              预览应用
            </a-button>
            <a-button @click="$emit('copy-link')" block>
              <Icon icon="mdi:link" />
              复制链接
            </a-button>
          </a-space>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'

interface Props {
  app: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'edit-home': []
  'preview-app': []
  'copy-link': []
}>()

const route = useRoute()
const router = useRouter()

const backButtonText = computed(() => {
  const fromDesigner = route.query.from === 'designer'
  return fromDesigner ? '返回设计器' : '返回应用列表'
})

function handleGoBack() {
  const fromDesigner = route.query.from === 'designer'
  
  if (fromDesigner) {
    const designerQuery = {
      appId: route.params.id,
      pageId: route.query.pageId || `${route.params.id}_home`
    }
    router.push({ path: '/designer', query: designerQuery })
  } else {
    router.push({ path: '/apps' })
  }
}

function getAppTypeName(appType?: string) {
  const typeMap: Record<string, string> = {
    device: '设备应用',
    mall: '商城应用',
    crm: 'CRM系统',
    web: 'Web应用'
  }
  return typeMap[appType || 'device'] || '未知类型'
}

function formatTime(time: string | Date | undefined): string {
  if (!time) return '未知'
  
  try {
    const date = new Date(time)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return '格式错误'
  }
}
</script>

<style scoped lang="scss">
.app-header {
  margin-bottom: 24px;
}

.header-nav {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;

  .back-btn {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

.app-info-card {
  .app-info-content {
    display: flex;
    gap: 24px;
    align-items: flex-start;
  }

  .app-icon-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    .app-avatar {
      background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 100%);
      color: #1890ff;
      font-size: 32px;
    }
  }

  .app-meta {
    flex: 1;

    .app-title {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #262626;
    }

    .app-description {
      margin: 0 0 16px 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .app-actions {
    width: 140px;
  }
}

@media (max-width: 768px) {
  .app-info-content {
    flex-direction: column;
    text-align: center;
  }

  .app-actions {
    width: 100%;
  }
}
</style>
