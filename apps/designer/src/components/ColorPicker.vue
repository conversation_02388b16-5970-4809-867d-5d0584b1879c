<template>
  <div class="color-picker-enhanced">
    <!-- 模式选择 -->
    <div class="picker-mode">
      <a-radio-group v-model:value="currentMode" size="small" @change="handleModeChange">
        <a-radio-button value="solid">纯色</a-radio-button>
        <a-radio-button value="gradient">渐变</a-radio-button>
      </a-radio-group>
    </div>

    <!-- 纯色模式 -->
    <div v-if="currentMode === 'solid'" class="solid-color-section">
      <div class="color-input-group">
        <!-- 颜色选择器 -->
        <div class="color-picker-wrapper">
          <input
            :value="hexColor"
            @input="handleColorChange"
            type="color"
            class="color-picker"
          />
          <div 
            class="color-preview"
            :style="{ background: displayColor }"
            @click="handlePreviewClick"
          ></div>
        </div>

        <!-- 颜色值输入框 -->
        <a-input
          :value="displayValue"
          @input="handleInputChange"
          @blur="handleInputBlur"
          placeholder="如: #ff0000 或 rgba(255,0,0,0.5) 或 transparent"
          class="color-input"
        />

        <!-- 清空按钮 -->
        <a-button size="small" @click="clearColor">
          清空
        </a-button>
      </div>

      <!-- 透明度控制 -->
      <div class="alpha-section">
        <label class="alpha-label">透明度: {{ Math.round(alpha * 100) }}%</label>
        <a-slider
          v-model:value="alpha"
          :min="0"
          :max="1"
          :step="0.01"
          @change="handleAlphaChange"
          class="alpha-slider"
        />
      </div>

      <!-- 预设颜色 -->
      <div class="preset-colors">
        <div class="preset-title">常用颜色:</div>
        <div class="preset-grid">
          <div
            v-for="preset in presetColors"
            :key="preset"
            class="preset-color"
            :style="{ backgroundColor: preset }"
            :title="preset"
            @click="selectPresetColor(preset)"
          ></div>
        </div>
      </div>
    </div>

    <!-- 渐变模式 -->
    <div v-if="currentMode === 'gradient'" class="gradient-section">
      <div class="gradient-type">
        <a-radio-group v-model:value="gradientType" size="small" @change="updateGradient">
          <a-radio-button value="linear">线性渐变</a-radio-button>
          <a-radio-button value="radial">径向渐变</a-radio-button>
        </a-radio-group>
      </div>

      <!-- 渐变方向/位置 -->
      <div v-if="gradientType === 'linear'" class="gradient-direction">
        <label>方向:</label>
        <a-select v-model:value="gradientDirection" @change="updateGradient" style="width: 120px;">
          <a-select-option value="to right">向右</a-select-option>
          <a-select-option value="to left">向左</a-select-option>
          <a-select-option value="to bottom">向下</a-select-option>
          <a-select-option value="to top">向上</a-select-option>
          <a-select-option value="45deg">45度</a-select-option>
          <a-select-option value="135deg">135度</a-select-option>
        </a-select>
      </div>

      <!-- 渐变颜色点 -->
      <div class="gradient-stops">
        <div class="stops-header">
          <span>渐变颜色:</span>
          <a-button size="small" @click="addGradientStop">添加颜色</a-button>
        </div>
        
        <div v-for="(stop, index) in gradientStops" :key="index" class="gradient-stop">
          <input
            :value="stop.color"
            @input="(e) => updateGradientStop(index, 'color', e.target.value)"
            type="color"
            class="stop-color-picker"
          />
          <a-input
            :value="stop.color"
            @input="(e) => updateGradientStop(index, 'color', e.target.value)"
            style="width: 100px;"
          />
          <a-input-number
            :value="stop.position"
            @change="(value) => updateGradientStop(index, 'position', value)"
            :min="0"
            :max="100"
            addon-after="%"
            style="width: 80px;"
          />
          <a-button 
            v-if="gradientStops.length > 2"
            size="small" 
            danger 
            @click="removeGradientStop(index)"
          >
            删除
          </a-button>
        </div>
      </div>

      <!-- 渐变预览 -->
      <div class="gradient-preview">
        <div 
          class="gradient-preview-box"
          :style="{ background: currentGradient }"
        ></div>
        <div class="gradient-code">
          <small>{{ currentGradient }}</small>
        </div>
      </div>

      <!-- 预设渐变 -->
      <div class="preset-gradients">
        <div class="preset-title">预设渐变:</div>
        <div class="preset-gradient-grid">
          <div
            v-for="(preset, index) in presetGradients"
            :key="index"
            class="preset-gradient"
            :style="{ background: preset }"
            :title="preset"
            @click="selectPresetGradient(preset)"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'

interface Props {
  modelValue?: string
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 模式：纯色或渐变
const currentMode = ref<'solid' | 'gradient'>('solid')

// 纯色相关
const hexColor = ref('#000000')
const alpha = ref(1)
const inputValue = ref('')

// 渐变相关
const gradientType = ref<'linear' | 'radial'>('linear')
const gradientDirection = ref('to right')
const gradientStops = ref([
  { color: '#ff0000', position: 0 },
  { color: '#0000ff', position: 100 }
])

// 预设颜色
const presetColors = [
  '#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff',
  '#000000', '#ffffff', '#808080', '#ff8000', '#8000ff', '#0080ff',
  'rgba(255,0,0,0.5)', 'rgba(0,255,0,0.5)', 'rgba(0,0,255,0.5)', 'transparent'
]

// 预设渐变
const presetGradients = [
  'linear-gradient(to right, #ff0000, #0000ff)',
  'linear-gradient(to right, #00ff00, #ffff00)',
  'linear-gradient(45deg, #ff00ff, #00ffff)',
  'linear-gradient(to bottom, #ff8000, #ff0080)',
  'radial-gradient(circle, #ff0000, #0000ff)',
  'linear-gradient(to right, rgba(255,0,0,0.5), rgba(0,0,255,0.5))'
]

// 计算属性
const displayColor = computed(() => {
  if (alpha.value === 0) {
    return 'transparent'
  } else if (alpha.value === 1) {
    return hexColor.value
  } else {
    const r = parseInt(hexColor.value.slice(1, 3), 16)
    const g = parseInt(hexColor.value.slice(3, 5), 16)
    const b = parseInt(hexColor.value.slice(5, 7), 16)
    return `rgba(${r}, ${g}, ${b}, ${alpha.value})`
  }
})

const displayValue = computed(() => {
  if (currentMode.value === 'solid') {
    return inputValue.value || displayColor.value
  } else {
    return currentGradient.value
  }
})

const currentGradient = computed(() => {
  const stops = gradientStops.value
    .sort((a, b) => a.position - b.position)
    .map(stop => `${stop.color} ${stop.position}%`)
    .join(', ')
  
  if (gradientType.value === 'linear') {
    return `linear-gradient(${gradientDirection.value}, ${stops})`
  } else {
    return `radial-gradient(circle, ${stops})`
  }
})

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== undefined) {
    parseValue(newValue)
  }
}, { immediate: true })

// 解析输入值
function parseValue(value: string) {
  console.log('🔍 解析颜色值:', value)
  
  if (!value || value === '') {
    currentMode.value = 'solid'
    hexColor.value = '#000000'
    alpha.value = 1
    inputValue.value = ''
    return
  }

  if (value.includes('gradient')) {
    currentMode.value = 'gradient'
    parseGradientValue(value)
  } else {
    currentMode.value = 'solid'
    parseSolidColorValue(value)
  }
}

// 解析纯色值
function parseSolidColorValue(value: string) {
  inputValue.value = value
  
  if (value === 'transparent') {
    hexColor.value = '#000000'
    alpha.value = 0
  } else if (value.startsWith('rgba')) {
    const match = value.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)/)
    if (match) {
      const [, r, g, b, a] = match
      hexColor.value = `#${[r, g, b].map(x => parseInt(x).toString(16).padStart(2, '0')).join('')}`
      alpha.value = a ? parseFloat(a) : 1
    }
  } else if (value.startsWith('#')) {
    hexColor.value = value.length === 7 ? value : '#000000'
    alpha.value = 1
  }
}

// 解析渐变值
function parseGradientValue(value: string) {
  if (value.includes('linear-gradient')) {
    gradientType.value = 'linear'
    
    // 提取方向
    const directionMatch = value.match(/linear-gradient\(([^,]+),/)
    if (directionMatch) {
      gradientDirection.value = directionMatch[1].trim()
    }
  } else if (value.includes('radial-gradient')) {
    gradientType.value = 'radial'
  }

  // 简化的颜色提取
  const colorMatches = value.match(/#[0-9a-fA-F]{6}|rgba?\([^)]+\)/g)
  if (colorMatches && colorMatches.length >= 2) {
    gradientStops.value = colorMatches.map((color, index) => ({
      color,
      position: index === 0 ? 0 : index === colorMatches.length - 1 ? 100 : (100 / (colorMatches.length - 1)) * index
    }))
  }
}

// 事件处理
function handleModeChange() {
  emitValue()
}

function handleColorChange(e: Event) {
  const target = e.target as HTMLInputElement
  hexColor.value = target.value
  updateSolidColor()
}

function handleInputChange(e: Event) {
  const target = e.target as HTMLInputElement
  inputValue.value = target.value
}

function handleInputBlur() {
  if (currentMode.value === 'solid') {
    parseSolidColorValue(inputValue.value)
    emitValue()
  }
}

function handlePreviewClick() {
  // 可以点击预览框来快速设置透明
  if (alpha.value > 0) {
    alpha.value = 0
  } else {
    alpha.value = 1
  }
  updateSolidColor()
}

function handleAlphaChange() {
  updateSolidColor()
}

function updateSolidColor() {
  inputValue.value = displayColor.value
  emitValue()
}

function clearColor() {
  inputValue.value = ''
  hexColor.value = '#000000'
  alpha.value = 1
  emitValue()
}

function selectPresetColor(color: string) {
  parseSolidColorValue(color)
  emitValue()
}

function updateGradient() {
  emitValue()
}

function addGradientStop() {
  const positions = gradientStops.value.map(s => s.position).sort((a, b) => a - b)
  let newPosition = 50
  
  // 找一个合适的位置插入新的颜色点
  for (let i = 0; i < positions.length - 1; i++) {
    const gap = positions[i + 1] - positions[i]
    if (gap > 20) {
      newPosition = positions[i] + gap / 2
      break
    }
  }
  
  gradientStops.value.push({
    color: '#808080',
    position: newPosition
  })
  updateGradient()
}

function removeGradientStop(index: number) {
  if (gradientStops.value.length > 2) {
    gradientStops.value.splice(index, 1)
    updateGradient()
  }
}

function updateGradientStop(index: number, key: 'color' | 'position', value: any) {
  if (gradientStops.value[index]) {
    gradientStops.value[index][key] = value
    updateGradient()
  }
}

function selectPresetGradient(gradient: string) {
  parseGradientValue(gradient)
  emitValue()
}

function emitValue() {
  let value = ''
  
  if (currentMode.value === 'solid') {
    value = inputValue.value || displayColor.value
  } else {
    value = currentGradient.value
  }
  
  console.log('🚀 发出颜色值:', value)
  emit('update:modelValue', value)
  emit('change', value)
}
</script>

<style scoped lang="scss">
.color-picker-enhanced {
  .picker-mode {
    margin-bottom: 12px;
  }

  .solid-color-section {
    .color-input-group {
      display: flex;
      gap: 8px;
      align-items: center;
      margin-bottom: 12px;

      .color-picker-wrapper {
        position: relative;
        width: 32px;
        height: 32px;

        .color-picker {
          width: 100%;
          height: 100%;
          border: 1px solid #d9d9d9;
          border-radius: 6px;
          cursor: pointer;
          opacity: 0;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 2;

          &::-webkit-color-swatch-wrapper {
            padding: 0;
          }
          
          &::-webkit-color-swatch {
            border: none;
            border-radius: 4px;
          }
        }

        .color-preview {
          width: 100%;
          height: 100%;
          border: 1px solid #d9d9d9;
          border-radius: 6px;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
          cursor: pointer;
          background-image: 
            linear-gradient(45deg, #ccc 25%, transparent 25%), 
            linear-gradient(-45deg, #ccc 25%, transparent 25%), 
            linear-gradient(45deg, transparent 75%, #ccc 75%), 
            linear-gradient(-45deg, transparent 75%, #ccc 75%);
          background-size: 8px 8px;
          background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
        }
      }

      .color-input {
        flex: 1;
      }
    }

    .alpha-section {
      margin-bottom: 16px;

      .alpha-label {
        display: block;
        margin-bottom: 8px;
        font-size: 12px;
        color: #666;
      }

      .alpha-slider {
        margin-bottom: 0;
      }
    }

    .preset-colors {
      .preset-title {
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;
      }

      .preset-grid {
        display: grid;
        grid-template-columns: repeat(8, 1fr);
        gap: 4px;

        .preset-color {
          width: 24px;
          height: 24px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          cursor: pointer;
          transition: transform 0.2s;
          background-image: 
            linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
            linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
            linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
            linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
          background-size: 6px 6px;
          background-position: 0 0, 0 3px, 3px -3px, -3px 0px;

          &:hover {
            transform: scale(1.1);
          }
        }
      }
    }
  }

  .gradient-section {
    .gradient-type {
      margin-bottom: 12px;
    }

    .gradient-direction {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;

      label {
        font-size: 12px;
        color: #666;
      }
    }

    .gradient-stops {
      margin-bottom: 16px;

      .stops-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        span {
          font-size: 12px;
          color: #666;
        }
      }

      .gradient-stop {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        .stop-color-picker {
          width: 24px;
          height: 24px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          cursor: pointer;

          &::-webkit-color-swatch-wrapper {
            padding: 0;
          }
          
          &::-webkit-color-swatch {
            border: none;
            border-radius: 3px;
          }
        }
      }
    }

    .gradient-preview {
      margin-bottom: 16px;

      .gradient-preview-box {
        width: 100%;
        height: 40px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        margin-bottom: 4px;
      }

      .gradient-code {
        font-family: monospace;
        font-size: 10px;
        color: #666;
        word-break: break-all;
        background: #f5f5f5;
        padding: 4px;
        border-radius: 3px;
      }
    }

    .preset-gradients {
      .preset-title {
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;
      }

      .preset-gradient-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;

        .preset-gradient {
          width: 100%;
          height: 30px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          cursor: pointer;
          transition: transform 0.2s;

          &:hover {
            transform: scale(1.05);
          }
        }
      }
    }
  }
}
</style>