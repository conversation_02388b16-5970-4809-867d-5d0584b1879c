<template>
  <div class="component-props-editor">
    <!-- 检查是否有配置模式 -->
    <div v-if="!hasConfigSchema" class="no-schema-warning">
      <a-alert
        message="该组件暂未配置属性模式"
        description="请使用JSON编辑器进行高级编辑"
        type="warning"
        show-icon
        style="margin-bottom: 16px"
      />
    </div>

    <!-- 基于Schema的表单 -->
    <div v-else>
      <!-- 数据配置 -->
      <a-collapse v-if="dataFields && Object.keys(dataFields).length > 0">
        <a-collapse-panel key="data" header="数据配置">
          <a-form layout="vertical" :model="dataForm">
            <template v-for="(field, key) in dataFields" :key="key">
              <FormField
                :field="field"
                :fieldKey="key"
                :modelValue="dataForm[key]"
                @update:modelValue="(value) => updateDataField(String(key), value)"
              />
            </template>
          </a-form>
        </a-collapse-panel>
      </a-collapse>

      <!-- 组件配置 -->
      <a-collapse v-if="configFields && Object.keys(configFields).length > 0" style="margin-top: 8px">
        <a-collapse-panel key="config" header="组件配置">
          <a-form layout="vertical" :model="configForm">
            <template v-for="(field, key) in configFields" :key="key">
              <!-- 菜单项配置使用特殊布局 -->
              <div v-if="key === 'menuItems'" class="menu-config-section">
                <a-form-item :label="field.title || key" :help="field.description">
                  <div class="menu-config-compact">
                    <div class="menu-summary">
                      <span class="count">{{ (configForm[key] || []).length }} 个菜单项</span>
                      <a-button size="small" type="primary" @click="showMenuEditor = true">
                        <Icon icon="mdi:pencil" />
                        编辑菜单
                      </a-button>
                    </div>
                    
                    <!-- 菜单项预览（最多显示4个，按order排序） -->
                    <div class="menu-preview">
                      <div 
                        v-for="(item, index) in getSortedPreviewItems(configForm[key] || [])" 
                        :key="item.id || index"
                        class="menu-preview-item"
                      >
                        <Icon :icon="parseIcon(item.icon).icon" :style="{ color: parseIcon(item.icon).color }" />
                        <span>{{ item.name }}</span>
                      </div>
                      <div v-if="(configForm[key] || []).length > 4" class="menu-preview-more">
                        +{{ (configForm[key] || []).length - 4 }} 更多
                      </div>
                    </div>
                  </div>
                </a-form-item>
              </div>
              
              <!-- 其他普通字段 -->
              <FormField
                v-else
                :field="field"
                :fieldKey="key"
                :modelValue="configForm[key]"
                @update:modelValue="(value) => updateConfigField(String(key), value)"
              />
            </template>
          </a-form>
        </a-collapse-panel>
      </a-collapse>
      
      <!-- 菜单编辑器模态框 -->
      <a-modal
        v-model:open="showMenuEditor"
        title="菜单项配置"
        width="800px"
        @ok="handleMenuEditorOk"
        @cancel="handleMenuEditorCancel"
      >
        <MenuItemsEditor
          v-if="showMenuEditor"
          :modelValue="tempMenuItems"
          @update:modelValue="handleMenuItemsUpdate"
        />
      </a-modal>
    </div>

    <!-- JSON编辑器 -->
    <a-form-item label="高级编辑" style="margin-top: 16px">
      <a-button @click="showJsonEditor = true" block>
        <Icon icon="mdi:code-json" />
        JSON编辑器
      </a-button>
    </a-form-item>
    
    <!-- JSON编辑模态框 -->
    <a-modal
      v-model:open="showJsonEditor"
      title="属性JSON编辑"
      width="800px"
      @ok="updateFromJson"
    >
      <a-textarea
        v-model:value="jsonValue"
        :rows="20"
        placeholder="请输入有效的JSON"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import type { ComponentConfig } from '@lowcode/aslib/core'
import { useComponentLibraryStore } from '../stores/componentLibrary'
import FormField from './FormField.vue'
import MenuItemsEditor from './config/MenuItemsEditor.vue'

const props = defineProps<{
  component: ComponentConfig
}>()

const emit = defineEmits<{
  update: [props: any]
}>()

const componentLibraryStore = useComponentLibraryStore()

// 响应式数据
const dataForm = ref<any>({})
const configForm = ref<any>({})
const showJsonEditor = ref(false)
const jsonValue = ref('')
const showMenuEditor = ref(false)
const tempMenuItems = ref<any[]>([])

// 计算属性
const hasConfigSchema = computed(() => {
  const libraryItem = componentLibraryStore.getComponentByType(props.component.type)
  return !!(libraryItem?.configSchema?.properties)
})

const dataFields = computed(() => {
  const libraryItem = componentLibraryStore.getComponentByType(props.component.type)
  return libraryItem?.configSchema?.properties?.data?.properties || {}
})

const configFields = computed(() => {
  const libraryItem = componentLibraryStore.getComponentByType(props.component.type)
  return libraryItem?.configSchema?.properties?.config?.properties || {}
})

// 监听组件变化
watch(() => props.component, (newComponent) => {
  if (newComponent) {
    // 初始化数据表单，使用默认值填充空字段
    const dataDefaults = getDefaultValues(dataFields.value)
    dataForm.value = { ...dataDefaults, ...newComponent.props?.data || {} }

    // 初始化配置表单，使用默认值填充空字段
    const configDefaults = getDefaultValues(configFields.value)
    configForm.value = { ...configDefaults, ...newComponent.props?.config || {} }

    jsonValue.value = JSON.stringify(newComponent.props, null, 2)
  }
}, { immediate: true, deep: true })

// 从字段定义中提取默认值
function getDefaultValues(fields: any): any {
  const defaults: any = {}

  for (const [key, field] of Object.entries(fields)) {
    const fieldDef = field as any
    if (fieldDef.default !== undefined) {
      defaults[key] = fieldDef.default
    }
  }

  return defaults
}

// 更新数据字段
function updateDataField(key: string, value: any) {
  dataForm.value[key] = value
  emitUpdate()
}

// 更新配置字段
function updateConfigField(key: string, value: any) {
  configForm.value[key] = value
  emitUpdate()
}

// 发送更新事件
function emitUpdate() {
  const updatedProps = {
    ...props.component.props,
    data: { ...dataForm.value },
    config: { ...configForm.value }
  }
  emit('update', updatedProps)
}

// 从JSON更新
function updateFromJson() {
  try {
    const parsed = JSON.parse(jsonValue.value)
    if (parsed.data) {
      dataForm.value = parsed.data
    }
    if (parsed.config) {
      configForm.value = parsed.config
    }
    emit('update', parsed)
    showJsonEditor.value = false
  } catch (error) {
    console.error('Invalid JSON:', error)
  }
}

// 获取排序后的预览项（最多4个）
function getSortedPreviewItems(items: any[]) {
  return items
    .filter(item => item.visible !== false)
    .sort((a, b) => (a.order || 0) - (b.order || 0))
    .slice(0, 4)
}

// 解析图标配置（支持 "icon|color" 格式）
function parseIcon(iconValue: string) {
  if (!iconValue) return { icon: 'mdi:help', color: '' }
  const parts = iconValue.split('|')
  return {
    icon: parts[0] || 'mdi:help',
    color: parts[1] || ''
  }
}

// 处理菜单编辑器确认
function handleMenuEditorOk() {
  configForm.value.menuItems = [...tempMenuItems.value]
  emitUpdate()
  showMenuEditor.value = false
}

// 处理菜单编辑器取消
function handleMenuEditorCancel() {
  // 重置临时数据
  tempMenuItems.value = [...(configForm.value.menuItems || [])]
  showMenuEditor.value = false
}

// 处理菜单项更新
function handleMenuItemsUpdate(newItems: any[]) {
  tempMenuItems.value = [...newItems]
}

// 监听showMenuEditor变化，初始化临时数据
watch(showMenuEditor, (newValue) => {
  if (newValue) {
    tempMenuItems.value = [...(configForm.value.menuItems || [])]
  }
})
</script>

<style scoped lang="scss">
.component-props-editor {
  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }
  
  :deep(.ant-form-item-label) {
    padding-bottom: 4px;
    
    label {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }
  }
  
  :deep(.ant-collapse) {
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }
  
  :deep(.ant-collapse-header) {
    font-size: 12px;
    padding: 8px 12px;
  }
  
  :deep(.ant-collapse-content-box) {
    padding: 12px;
  }
  
  // 菜单配置特殊样式
  .menu-config-section {
    .menu-config-compact {
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      padding: 12px;
      background: #fafafa;
      
      .menu-summary {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        .count {
          font-size: 13px;
          color: #666;
          font-weight: 500;
        }
      }
      
      .menu-preview {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        
        .menu-preview-item {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 4px 8px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #e8e8e8;
          font-size: 11px;
          color: #666;
          
          .iconify {
            font-size: 14px;
          }
          
          span {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        
        .menu-preview-more {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 4px 8px;
          background: #f0f0f0;
          border-radius: 4px;
          border: 1px dashed #ccc;
          font-size: 11px;
          color: #999;
        }
      }
    }
  }
}
</style>
