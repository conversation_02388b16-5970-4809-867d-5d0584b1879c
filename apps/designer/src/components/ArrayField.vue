<template>
  <div class="array-field">
    <div class="array-field-header">
      <span class="array-field-title">{{ field.title }}</span>
      <a-button type="primary" size="small" @click="addItem">
        <Icon icon="mdi:plus" style="margin-right: 4px;" />
        添加项目
      </a-button>
    </div>
    
    <div class="array-field-items">
      <div 
        v-for="(item, index) in items" 
        :key="index"
        class="array-field-item"
      >
        <div class="array-field-item-header">
          <span class="array-field-item-title">{{ getItemTitle(item, index) }}</span>
          <div class="array-field-item-actions">
            <a-button size="small" @click="moveUp(index)" :disabled="index === 0" title="上移">
              <Icon icon="mdi:arrow-up" />
            </a-button>
            <a-button size="small" @click="moveDown(index)" :disabled="index === items.length - 1" title="下移">
              <Icon icon="mdi:arrow-down" />
            </a-button>
            <a-button size="small" danger @click="removeItem(index)" title="删除">
              <Icon icon="mdi:delete" />
            </a-button>
          </div>
        </div>
        
        <div class="array-field-item-content">
          <FormField
            v-for="(propSchema, propKey) in field.items.properties"
            :key="propKey"
            :field-key="propKey"
            :field="propSchema"
            :model-value="item[propKey]"
            @update:model-value="updateItemProperty(index, propKey, $event)"
          />
        </div>
      </div>
    </div>
    
    <div v-if="items.length === 0" class="array-field-empty">
      <a-empty description="暂无项目" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Icon } from '@iconify/vue'
import FormField from './FormField.vue'

interface Props {
  field: any
  modelValue: any[]
}

interface Emits {
  (e: 'update:modelValue', value: any[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 内部状态
const items = ref<any[]>([])

// 初始化
watch(() => props.modelValue, (newValue) => {
  if (Array.isArray(newValue)) {
    items.value = [...newValue]
  } else {
    items.value = []
  }
}, { immediate: true })

// 获取项目标题
function getItemTitle(item: any, index: number): string {
  if (item.name) return `${index + 1}. ${item.name}`
  if (item.title) return `${index + 1}. ${item.title}`
  if (item.label) return `${index + 1}. ${item.label}`
  return `项目 ${index + 1}`
}

// 添加项目
function addItem() {
  const newItem: any = {}
  
  // 根据 schema 初始化默认值
  if (props.field.items?.properties) {
    Object.keys(props.field.items.properties).forEach(key => {
      const propSchema = props.field.items.properties[key]
      newItem[key] = propSchema.default || getDefaultValue(propSchema.type)
    })
  }
  
  items.value.push(newItem)
  emitUpdate()
}

// 删除项目
function removeItem(index: number) {
  items.value.splice(index, 1)
  emitUpdate()
}

// 上移
function moveUp(index: number) {
  if (index > 0) {
    const item = items.value.splice(index, 1)[0]
    items.value.splice(index - 1, 0, item)
    emitUpdate()
  }
}

// 下移
function moveDown(index: number) {
  if (index < items.value.length - 1) {
    const item = items.value.splice(index, 1)[0]
    items.value.splice(index + 1, 0, item)
    emitUpdate()
  }
}

// 更新项目属性
function updateItemProperty(index: number, key: string, value: any) {
  if (items.value[index]) {
    items.value[index][key] = value
    emitUpdate()
  }
}

// 发出更新事件
function emitUpdate() {
  emit('update:modelValue', [...items.value])
}

// 获取默认值
function getDefaultValue(type: string) {
  switch (type) {
    case 'string': return ''
    case 'number': return 0
    case 'boolean': return false
    case 'array': return []
    case 'object': return {}
    default: return null
  }
}
</script>

<style lang="scss" scoped>
.array-field {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  &-title {
    font-weight: 500;
    font-size: 14px;
  }

  &-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &-item {
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 12px;

    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
    }

    &-title {
      font-weight: 500;
      color: #262626;
    }

    &-actions {
      display: flex;
      gap: 4px;
    }

    &-content {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
  }

  &-empty {
    text-align: center;
    padding: 32px;
  }
}
</style>
