<template>
  <div class="icon-picker">
    <!-- 图标选择按钮 -->
    <a-input-group compact>
      <a-input
        :value="parsedIcon.icon"
        :placeholder="placeholder"
        @input="handleIconInput"
        style="width: calc(100% - 80px)"
      />
      <a-input
        v-model:value="iconColor"
        type="color"
        @change="handleColorChange"
        style="width: 40px; padding: 4px;"
        title="选择图标颜色"
      />
      <a-button @click="showModal = true" style="width: 40px">
        <Icon :icon="parsedIcon.icon || 'mdi:palette'" :style="{ color: iconColor }" />
      </a-button>
    </a-input-group>

    <!-- 图标选择模态框 -->
    <a-modal
      v-model:open="showModal"
      title="选择图标"
      width="90vw"
      :style="{ maxWidth: '800px' }"
      :footer="null"
      class="icon-picker-modal"
      :z-index="2000"
      :bodyStyle="{
        maxHeight: '70vh',
        overflow: 'hidden',
        padding: '16px'
      }"
    >
      <div class="icon-picker-content">
        <!-- 搜索框 -->
        <div class="search-section fixed-section">
          <a-input
            v-model:value="searchKeyword"
            placeholder="搜索图标... (支持在线搜索)"
            @input="handleSearch"
            @pressEnter="searchOnline"
            style="margin-bottom: 8px"
          >
            <template #prefix>
              <Icon icon="mdi:magnify" />
            </template>
            <template #suffix>
              <a-button
                type="link"
                size="small"
                @click="searchOnline"
                :loading="onlineSearchLoading"
                title="在线搜索更多图标"
              >
                <Icon icon="mdi:cloud-search" />
              </a-button>
            </template>
          </a-input>

          <!-- 搜索提示 -->
          <div class="search-tips">
            <a-alert
              message="💡 搜索提示"
              :description="searchTipsText"
              type="info"
              show-icon
              closable
              style="margin-bottom: 12px"
              v-if="showSearchTips"
              @close="showSearchTips = false"
            />
          </div>
        </div>

        <!-- 搜索状态和分类标签 -->
        <div class="category-section fixed-section">
          <!-- 搜索状态指示 -->
          <div v-if="isOnlineSearch" class="search-status">
            <a-tag color="green">
              <Icon icon="mdi:cloud-check" />
              在线搜索结果 ({{ onlineIcons.length }} 个图标)
            </a-tag>
            <a-button type="link" size="small" @click="clearOnlineSearch">
              <Icon icon="mdi:close" />
              返回本地
            </a-button>
          </div>

          <!-- 分类标签 (仅在本地搜索时显示) -->
          <a-space wrap v-if="!isOnlineSearch">
            <a-tag
              v-for="category in categories"
              :key="category.key"
              :color="selectedCategory === category.key ? 'blue' : 'default'"
              @click="selectCategory(category.key)"
              style="cursor: pointer"
            >
              {{ category.label }}
            </a-tag>
          </a-space>
        </div>

        <!-- 图标网格容器 -->
        <div class="icons-container">
          <!-- 图标网格 -->
          <div class="icons-grid" v-if="filteredIcons.length > 0">
          <div
            v-for="icon in filteredIcons"
            :key="icon"
            class="icon-item"
            :class="{
              'icon-selected': modelValue === icon,
              'icon-online': isOnlineSearch
            }"
            @click="selectIcon(icon)"
          >
            <Icon :icon="icon" class="icon-preview" />
            <div class="icon-name">{{ icon.split(':')[1] || icon }}</div>
            <div v-if="isOnlineSearch" class="icon-source">
              <Icon icon="mdi:cloud" />
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <Icon icon="mdi:image-search" class="empty-icon" />
          <div class="empty-text">
            <template v-if="isOnlineSearch">
              未找到相关图标，请尝试其他关键词
            </template>
            <template v-else-if="searchKeyword">
              本地未找到相关图标，试试在线搜索？
            </template>
            <template v-else>
              选择分类或搜索图标
            </template>
          </div>
          <a-button
            v-if="!isOnlineSearch && searchKeyword"
            type="primary"
            size="small"
            @click="searchOnline"
            :loading="onlineSearchLoading"
          >
            <Icon icon="mdi:cloud-search" />
            在线搜索
          </a-button>
        </div>

        <!-- 分页 -->
        <div class="pagination-section" v-if="totalIcons > pageSize">
          <a-pagination
            v-model:current="currentPage"
            :total="totalIcons"
            :page-size="pageSize"
            :show-size-changer="false"
            size="small"
            @change="handlePageChange"
          />
        </div>
        </div> <!-- 关闭 icons-container -->
      </div> <!-- 关闭 icon-picker-content -->
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Icon } from '@iconify/vue'

const props = defineProps<{
  modelValue?: string
  placeholder?: string
  color?: string
}>()

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'update:color': [color: string]
}>()

// 响应式数据
const showModal = ref(false)
const searchKeyword = ref('')
const selectedCategory = ref('all')
const currentPage = ref(1)
const pageSize = 48
const onlineSearchLoading = ref(false)
const onlineIcons = ref<string[]>([])
const showSearchTips = ref(true)
const isOnlineSearch = ref(false)

// 解析图标值（支持 "icon|color" 格式）
const parsedIcon = computed(() => {
  if (!props.modelValue || typeof props.modelValue !== 'string') {
    return { icon: '', color: '#666666' }
  }

  const parts = props.modelValue.split('|')
  return {
    icon: parts[0] || '',
    color: parts[1] || props.color || '#666666'
  }
})

const iconColor = ref(parsedIcon.value.color || '#666666')

// 图标分类
const categories = [
  { key: 'all', label: '全部' },
  { key: 'mdi', label: 'Material Design' },
  { key: 'lucide', label: 'Lucide' },
  { key: 'heroicons', label: 'Heroicons' },
  { key: 'tabler', label: 'Tabler' },
  { key: 'carbon', label: 'Carbon' },
  { key: 'ant-design', label: 'Ant Design' }
]

// 常用图标列表
const commonIcons = [
  // Material Design Icons
  'mdi:home', 'mdi:user', 'mdi:settings', 'mdi:heart', 'mdi:star',
  'mdi:search', 'mdi:plus', 'mdi:minus', 'mdi:close', 'mdi:check',
  'mdi:arrow-left', 'mdi:arrow-right', 'mdi:arrow-up', 'mdi:arrow-down',
  'mdi:edit', 'mdi:delete', 'mdi:copy', 'mdi:share', 'mdi:download',
  'mdi:upload', 'mdi:file', 'mdi:folder', 'mdi:image', 'mdi:video',
  'mdi:music', 'mdi:phone', 'mdi:email', 'mdi:calendar', 'mdi:clock',
  'mdi:location', 'mdi:map', 'mdi:camera', 'mdi:microphone', 'mdi:volume-high',
  'mdi:wifi', 'mdi:bluetooth', 'mdi:battery', 'mdi:signal', 'mdi:refresh',
  'mdi:sync', 'mdi:cloud', 'mdi:database', 'mdi:server', 'mdi:monitor',
  'mdi:laptop', 'mdi:tablet', 'mdi:cellphone', 'mdi:gamepad', 'mdi:headphones',
  'mdi:shopping', 'mdi:cart', 'mdi:credit-card', 'mdi:currency-usd', 'mdi:gift',
  'mdi:bookmark', 'mdi:tag', 'mdi:flag', 'mdi:bell', 'mdi:message',
  'mdi:chat', 'mdi:comment', 'mdi:thumb-up', 'mdi:thumb-down', 'mdi:eye',
  'mdi:eye-off', 'mdi:lock', 'mdi:unlock', 'mdi:key', 'mdi:shield',
  'mdi:account', 'mdi:account-group', 'mdi:account-plus', 'mdi:account-minus',
  'mdi:weather-sunny', 'mdi:weather-cloudy', 'mdi:weather-rainy', 'mdi:weather-snowy',
  'mdi:car', 'mdi:bus', 'mdi:train', 'mdi:airplane', 'mdi:ship',
  'mdi:package', 'mdi:package-variant', 'mdi:application', 'mdi:web', 'mdi:code-tags',
  
  // Lucide Icons
  'lucide:home', 'lucide:user', 'lucide:settings', 'lucide:heart', 'lucide:star',
  'lucide:search', 'lucide:plus', 'lucide:minus', 'lucide:x', 'lucide:check',
  'lucide:arrow-left', 'lucide:arrow-right', 'lucide:arrow-up', 'lucide:arrow-down',
  'lucide:edit', 'lucide:trash', 'lucide:copy', 'lucide:share', 'lucide:download',
  'lucide:upload', 'lucide:file', 'lucide:folder', 'lucide:image', 'lucide:video',
  
  // Heroicons
  'heroicons:home', 'heroicons:user', 'heroicons:cog-6-tooth', 'heroicons:heart', 'heroicons:star',
  'heroicons:magnifying-glass', 'heroicons:plus', 'heroicons:minus', 'heroicons:x-mark', 'heroicons:check',
  
  // Tabler Icons
  'tabler:home', 'tabler:user', 'tabler:settings', 'tabler:heart', 'tabler:star',
  'tabler:search', 'tabler:plus', 'tabler:minus', 'tabler:x', 'tabler:check',
  
  // Carbon Icons
  'carbon:home', 'carbon:user', 'carbon:settings', 'carbon:favorite', 'carbon:star',
  'carbon:search', 'carbon:add', 'carbon:subtract', 'carbon:close', 'carbon:checkmark',
  
  // Ant Design Icons
  'ant-design:home-outlined', 'ant-design:user-outlined', 'ant-design:setting-outlined',
  'ant-design:heart-outlined', 'ant-design:star-outlined', 'ant-design:search-outlined',
  'ant-design:plus-outlined', 'ant-design:minus-outlined', 'ant-design:close-outlined',
  'ant-design:check-outlined'
]

// 计算属性
const searchTipsText = computed(() => {
  return `• 输入关键词搜索本地图标库\n• 按回车或点击云图标进行在线搜索\n• 支持中英文搜索，如："home"、"首页"、"用户"\n• 在线搜索可找到更多 Iconify 图标`
})

const allIcons = computed(() => {
  // 如果是在线搜索结果，优先显示在线图标
  if (isOnlineSearch.value && onlineIcons.value.length > 0) {
    return onlineIcons.value
  }
  return commonIcons
})

const filteredIcons = computed(() => {
  let icons = allIcons.value

  // 按分类过滤（仅对本地图标生效）
  if (selectedCategory.value !== 'all' && !isOnlineSearch.value) {
    icons = icons.filter(icon => icon.startsWith(selectedCategory.value + ':'))
  }

  // 按搜索关键词过滤
  if (searchKeyword.value && !isOnlineSearch.value) {
    const keyword = searchKeyword.value.toLowerCase()
    icons = icons.filter(icon =>
      icon.toLowerCase().includes(keyword) ||
      icon.split(':')[1]?.toLowerCase().includes(keyword)
    )
  }

  // 分页
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return icons.slice(start, end)
})

const totalIcons = computed(() => {
  let icons = allIcons.value

  if (selectedCategory.value !== 'all' && !isOnlineSearch.value) {
    icons = icons.filter(icon => icon.startsWith(selectedCategory.value + ':'))
  }

  if (searchKeyword.value && !isOnlineSearch.value) {
    const keyword = searchKeyword.value.toLowerCase()
    icons = icons.filter(icon =>
      icon.toLowerCase().includes(keyword) ||
      icon.split(':')[1]?.toLowerCase().includes(keyword)
    )
  }

  return icons.length
})

// 方法
function handleIconInput(e: Event) {
  const target = e.target as HTMLInputElement
  const iconName = target.value || ''
  const newValue = iconName && iconColor.value !== '#666666'
    ? `${iconName}|${iconColor.value}`
    : iconName
  emit('update:modelValue', newValue)
}

function selectIcon(icon: string) {
  const iconName = icon || ''
  const newValue = iconName && iconColor.value !== '#666666'
    ? `${iconName}|${iconColor.value}`
    : iconName
  emit('update:modelValue', newValue)
  showModal.value = false
}

function selectCategory(category: string) {
  selectedCategory.value = category
  currentPage.value = 1
  // 切换分类时回到本地搜索
  isOnlineSearch.value = false
  onlineIcons.value = []
}

function handleSearch() {
  currentPage.value = 1
  // 本地搜索时重置在线搜索状态
  isOnlineSearch.value = false
  onlineIcons.value = []
}

function handlePageChange(page: number) {
  currentPage.value = page
}

// 在线搜索图标
async function searchOnline() {
  if (!searchKeyword.value.trim()) {
    return
  }

  onlineSearchLoading.value = true

  try {
    // 使用 Iconify API 搜索图标
    const response = await fetch(`https://api.iconify.design/search?query=${encodeURIComponent(searchKeyword.value)}&limit=200`)

    if (!response.ok) {
      throw new Error('搜索失败')
    }

    const data = await response.json()

    if (data.icons && Array.isArray(data.icons)) {
      // 转换为完整的图标名称
      const foundIcons = data.icons.map((iconData: any) => {
        // iconData 可能是字符串或对象
        if (typeof iconData === 'string') {
          return iconData
        } else if (iconData.icon) {
          return iconData.icon
        }
        return null
      }).filter(Boolean)

      onlineIcons.value = foundIcons
      isOnlineSearch.value = true
      currentPage.value = 1
      selectedCategory.value = 'all' // 重置分类

      console.log(`🔍 在线搜索到 ${foundIcons.length} 个图标`)
    } else {
      onlineIcons.value = []
      isOnlineSearch.value = false
      console.log('🔍 未找到相关图标')
    }
  } catch (error) {
    console.error('在线搜索失败:', error)
    onlineIcons.value = []
    isOnlineSearch.value = false
    // 可以添加错误提示
  } finally {
    onlineSearchLoading.value = false
  }
}

// 清除在线搜索结果
function clearOnlineSearch() {
  isOnlineSearch.value = false
  onlineIcons.value = []
  searchKeyword.value = ''
  currentPage.value = 1
}

// 处理颜色变化
function handleColorChange() {
  const iconName = parsedIcon.value.icon || ''
  const newValue = iconName && iconColor.value !== '#666666'
    ? `${iconName}|${iconColor.value}`
    : iconName
  emit('update:modelValue', newValue)
  emit('update:color', iconColor.value)
}

// 监听颜色 prop 变化
watch(() => props.color, (newColor) => {
  if (newColor && typeof newColor === 'string') {
    iconColor.value = newColor
  }
})

// 监听 modelValue 变化，更新颜色
watch(() => props.modelValue, () => {
  const parsed = parsedIcon.value
  if (parsed.color) {
    iconColor.value = parsed.color
  }
})

// 监听搜索关键词变化
watch(searchKeyword, () => {
  currentPage.value = 1
})
</script>

<style scoped lang="scss">
.icon-picker-modal {
  :deep(.ant-modal-body) {
    padding: 16px;
  }
}

.icon-picker-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  max-height: 70vh;
}

.fixed-section {
  flex-shrink: 0;
}

.icons-container {
  flex: 1;
  // overflow-y: auto;
  min-height: 0;
  padding-right: 4px;
}

.search-section {
  margin-bottom: 16px;
}

.search-tips {
  .ant-alert {
    font-size: 12px;

    :deep(.ant-alert-description) {
      white-space: pre-line;
      line-height: 1.4;
    }
  }
}

.category-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.search-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;

  .ant-tag {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

.icons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    border-color: #1890ff;
    background: #f6ffed;
  }

  &.icon-selected {
    border-color: #1890ff;
    background: #e6f7ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &.icon-online {
    border-color: #52c41a;
    background: #f6ffed;

    &:hover {
      border-color: #389e0d;
      background: #f0f9e8;
    }

    &.icon-selected {
      border-color: #389e0d;
      background: #d9f7be;
      box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
    }
  }
}

.icon-preview {
  font-size: 24px;
  margin-bottom: 4px;
  color: #666;
}

.icon-name {
  font-size: 10px;
  color: #999;
  text-align: center;
  word-break: break-all;
  line-height: 1.2;
}

.icon-source {
  position: absolute;
  top: 4px;
  right: 4px;
  font-size: 10px;
  color: #52c41a;
  opacity: 0.7;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #999;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-text {
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 1.5;
  }
}

.pagination-section {
  display: flex;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

// 响应式设计
@media (max-width: 768px) {
  .icons-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  }
  
  .icon-item {
    padding: 8px 4px;
  }
  
  .icon-preview {
    font-size: 20px;
  }
  
  .icon-name {
    font-size: 9px;
  }
}
</style>
