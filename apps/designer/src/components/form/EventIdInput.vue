<template>
  <div class="event-id-input">
    <a-form-item :label="label" :help="helpText">
      <a-input-group compact>
        <a-input
          v-model:value="localValue"
          :placeholder="placeholder"
          @change="handleChange"
          @blur="handleBlur"
          style="width: calc(100% - 80px)"
        />
        <a-button 
          @click="generateRandomId"
          :title="'随机生成ID'"
          style="width: 40px"
        >
          🎲
        </a-button>
        <a-dropdown :trigger="['click']" placement="bottomRight">
          <a-button 
            :title="'智能建议'"
            style="width: 40px"
          >
            💡
          </a-button>
          <template #overlay>
            <a-menu @click="handleSuggestionClick">
              <a-menu-item 
                v-for="suggestion in suggestions" 
                :key="suggestion.value"
              >
                <div class="suggestion-item">
                  <div class="suggestion-label">{{ suggestion.label }}</div>
                  <div class="suggestion-desc">{{ suggestion.description }}</div>
                </div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </a-input-group>
      
      <!-- 验证提示 -->
      <div v-if="validationMessage" class="validation-message" :class="validationClass">
        {{ validationMessage }}
      </div>
    </a-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Suggestion {
  label: string
  value: string
  description: string
}

const props = defineProps<{
  modelValue: string
  label?: string
  placeholder?: string
  elementType?: string
  componentType?: string
  existingIds?: string[]
}>()

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const localValue = ref(props.modelValue || '')

// 帮助文本
const helpText = computed(() => {
  return '用于在事件配置中识别此元素，建议使用语义化名称（如：more-button、submit-btn）'
})

// 智能建议
const suggestions = computed((): Suggestion[] => {
  const { elementType, componentType } = props
  
  const baseSuggestions: Suggestion[] = [
    { label: 'more-button', value: 'more-button', description: '更多按钮' },
    { label: 'submit-btn', value: 'submit-btn', description: '提交按钮' },
    { label: 'cancel-btn', value: 'cancel-btn', description: '取消按钮' },
    { label: 'confirm-action', value: 'confirm-action', description: '确认操作' },
    { label: 'nav-link', value: 'nav-link', description: '导航链接' }
  ]
  
  // 根据元素类型和组件类型提供更精准的建议
  if (elementType === 'button') {
    return [
      { label: 'primary-btn', value: 'primary-btn', description: '主要按钮' },
      { label: 'secondary-btn', value: 'secondary-btn', description: '次要按钮' },
      { label: 'action-btn', value: 'action-btn', description: '操作按钮' },
      ...baseSuggestions.slice(0, 3)
    ]
  }
  
  if (elementType === 'menu') {
    return [
      { label: 'menu-item', value: 'menu-item', description: '菜单项' },
      { label: 'nav-item', value: 'nav-item', description: '导航项' },
      { label: 'action-item', value: 'action-item', description: '操作项' },
      ...baseSuggestions.slice(0, 2)
    ]
  }
  
  if (elementType === 'action') {
    return [
      { label: 'quick-action', value: 'quick-action', description: '快捷操作' },
      { label: 'main-action', value: 'main-action', description: '主要操作' },
      { label: 'toggle-action', value: 'toggle-action', description: '切换操作' },
      ...baseSuggestions.slice(0, 2)
    ]
  }
  
  return baseSuggestions
})

// 验证逻辑
const validationResult = computed(() => {
  const value = localValue.value.trim()
  
  if (!value) {
    return { valid: false, message: '事件ID不能为空', type: 'error' }
  }
  
  // 检查格式
  if (!/^[a-zA-Z][a-zA-Z0-9_-]*$/.test(value)) {
    return { 
      valid: false, 
      message: 'ID格式不正确，应以字母开头，只能包含字母、数字、下划线和连字符', 
      type: 'error' 
    }
  }
  
  // 检查重复
  if (props.existingIds && props.existingIds.includes(value)) {
    return { valid: false, message: '此ID已存在，请使用其他ID', type: 'error' }
  }
  
  // 检查长度
  if (value.length > 50) {
    return { valid: false, message: 'ID长度不能超过50个字符', type: 'error' }
  }
  
  // 建议优化
  if (value.length < 3) {
    return { valid: true, message: '建议使用更具描述性的ID', type: 'warning' }
  }
  
  if (!/[a-z]/.test(value) || !/[-_]/.test(value)) {
    return { valid: true, message: '建议使用kebab-case格式（如：my-button）', type: 'info' }
  }
  
  return { valid: true, message: '✓ ID格式正确', type: 'success' }
})

const validationMessage = computed(() => validationResult.value.message)
const validationClass = computed(() => `validation-${validationResult.value.type}`)

// 生成随机ID
function generateRandomId() {
  const prefixes = ['btn', 'action', 'link', 'item', 'element']
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
  const suffix = Math.random().toString(36).substring(2, 8)
  localValue.value = `${prefix}-${suffix}`
  handleChange()
}

// 处理建议点击
function handleSuggestionClick({ key }: { key: string }) {
  localValue.value = key
  handleChange()
}

// 处理值变化
function handleChange() {
  emit('update:modelValue', localValue.value)
}

function handleBlur() {
  // 自动格式化：转换为kebab-case
  const formatted = localValue.value
    .trim()
    .toLowerCase()
    .replace(/[^a-zA-Z0-9_-]/g, '-')
    .replace(/[-_]+/g, '-')
    .replace(/^-+|-+$/g, '')
  
  if (formatted !== localValue.value) {
    localValue.value = formatted
    handleChange()
  }
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  localValue.value = newValue || ''
}, { immediate: true })
</script>

<style scoped lang="scss">
.event-id-input {
  .validation-message {
    margin-top: 4px;
    font-size: 12px;
    
    &.validation-error {
      color: #ff4d4f;
    }
    
    &.validation-warning {
      color: #faad14;
    }
    
    &.validation-info {
      color: #1890ff;
    }
    
    &.validation-success {
      color: #52c41a;
    }
  }
  
  .suggestion-item {
    .suggestion-label {
      font-weight: 500;
      color: #262626;
    }
    
    .suggestion-desc {
      font-size: 12px;
      color: #8c8c8c;
      margin-top: 2px;
    }
  }
}
</style>
