<template>
  <div class="designer-canvas">
    <!-- 画布工具栏 -->
    <div class="canvas-toolbar">
      <a-space>
        <a-select v-model:value="canvasScale" style="width: 120px">
          <a-select-option value="0.5">50%</a-select-option>
          <a-select-option value="0.75">75%</a-select-option>
          <a-select-option value="1">100%</a-select-option>
          <a-select-option value="1.25">125%</a-select-option>
          <a-select-option value="1.5">150%</a-select-option>
        </a-select>
        
        <a-divider type="vertical" />
        
        <a-button size="small" @click="centerCanvas">
          <Icon icon="mdi:center-focus-strong" />
          居中
        </a-button>
        
        <a-button size="small" @click="fitCanvas">
          <Icon icon="mdi:fit-to-screen" />
          适应
        </a-button>

        <a-divider type="vertical" />

        <a-button
          size="small"
          @click="clearSelection"
          :disabled="!designerStore.selectedComponentId"
          type="text"
        >
          <Icon icon="mdi:cursor-default-click" />
          取消选择
        </a-button>

        <a-divider type="vertical" />

        <a-select v-model:value="deviceMode" style="width: 120px" @change="handleDeviceModeChange">
          <a-select-option value="normal">普通模式</a-select-option>
          <a-select-option value="wechat">微信内置</a-select-option>
        </a-select>
      </a-space>
    </div>
    
    <!-- 画布容器 -->
    <div class="canvas-container" ref="canvasContainer">
      <div 
        class="canvas-viewport"
        :style="{ transform: `scale(${canvasScale})` }"
      >
        <!-- 手机框架 -->
        <div class="phone-frame">
          <!-- 微信标题栏 -->
          <div v-if="deviceMode === 'wechat'" class="wechat-header">
            <div class="wechat-header-content">
              <div class="wechat-back">
                <Icon icon="mdi:arrow-left" />
              </div>
              <div class="wechat-title">{{ currentPage?.title || '页面标题' }}</div>
              <div class="wechat-more">
                <Icon icon="mdi:dots-horizontal" />
              </div>
            </div>
          </div>

          <div class="phone-screen" :class="{ 'with-wechat-header': deviceMode === 'wechat' }">
            <!-- 页面渲染区域 -->
            <div
              v-if="currentPage && !isPreviewMode"
              class="page-editor"
              @drop="onDrop"
              @dragover="onDragOver"
              @dragleave="onDragLeave"
              @click="handleCanvasClick"
              :class="{ 'drag-over': isDragOver }"
              :style="pageEditorStyle"
            >
              <CanvasComponent
                v-for="(component, index) in currentPage.components"
                :key="component.id"
                :component="component"
                :index="index"
                :parent-id="null"
                @select="selectComponent"
                @delete="removeComponent"
                @move="moveComponent"
              />
              
              <!-- 拖拽提示 -->
              <div v-if="isDragOver" class="drop-indicator">
                <Icon icon="mdi:plus-circle" class="drop-icon" />
                <div class="drop-text">释放以添加组件</div>
              </div>

              <!-- 空状态 -->
              <div v-if="currentPage.components.length === 0 && !isDragOver" class="empty-canvas">
                <Icon icon="mdi:drag" class="empty-icon" />
                <div class="empty-text">拖拽组件到这里开始设计</div>
              </div>
            </div>
            
            <!-- 预览模式 -->
            <div v-else-if="currentPage && isPreviewMode" class="page-preview">
              <PageRenderer
                :config="currentPage"
                :initial-context="mockContext"
                :designer-mode="true"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { storeToRefs } from 'pinia'
import { Icon } from '@iconify/vue'
import { PageRenderer } from '@lowcode/aslib/core'
import CanvasComponent from './CanvasComponent.vue'
import { useDesignerStore } from '../stores/designer'
import { useComponentLibraryStore } from '../stores/componentLibrary'

const designerStore = useDesignerStore()
const componentLibraryStore = useComponentLibraryStore()

// 响应式数据
const canvasContainer = ref<HTMLElement>()
const canvasScale = ref(1)
const isDragOver = ref(false)
const deviceMode = ref('normal')

// 计算属性
const { currentPage, isPreviewMode } = storeToRefs(designerStore)

// 页面编辑器样式
const pageEditorStyle = computed(() => {
  if (!currentPage.value) return {}

  const pageStyle = currentPage.value.style || {}
  const layout = currentPage.value.layout || {}

  const computedStyle = {
    // 页面样式
    ...pageStyle,
    // 布局样式
    display: layout.type === 'flex' ? 'flex' : 'block',
    flexDirection: layout.direction || 'column',
    padding: layout.padding ? `${layout.padding}px` : '16px',
    gap: layout.gap ? `${layout.gap}px` : '16px',
    // 确保最小高度
    minHeight: pageStyle.minHeight || '100%'
  }

  console.log('🎨 设计器页面样式:', {
    页面样式: pageStyle,
    布局配置: layout,
    最终样式: computedStyle
  })

  return computedStyle
})

// Mock上下文数据
const mockContext = {
  device: {
    details: {
      id: 1,
      deviceNo: 'TEST001',
      packageName: '基础套餐 10GB',
      vTotalFlow: 10240,
      vUseFlow: 3072,
      vResidueFlow: 7168,
      balance: 25.50,
      status: 3,
      nameStatus: 2
    }
  }
}

// 拖拽处理
function onDragOver(event: DragEvent) {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'copy'
  isDragOver.value = true
}

function onDragLeave(event: DragEvent) {
  event.preventDefault()
  isDragOver.value = false
}

function onDrop(event: DragEvent) {
  event.preventDefault()
  isDragOver.value = false
  
  try {
    const componentData = JSON.parse(event.dataTransfer!.getData('application/json'))
    console.log('拖拽的组件数据:', componentData)
    const componentConfig = componentLibraryStore.createComponentConfig(componentData.type)
    designerStore.addComponent(componentConfig)
  } catch (error) {
    console.error('Drop component failed:', error)
  }
}

// 组件操作
function selectComponent(componentId: string) {
  designerStore.selectComponent(componentId)
}

// 画布点击处理 - 取消选择组件
function handleCanvasClick(event: MouseEvent) {
  // 只有点击的是画布本身（不是组件）时才取消选择
  if (event.target === event.currentTarget) {
    designerStore.selectComponent(null)
    console.log('🎯 取消选择组件，回到页面编辑模式')
  }
}

// 取消选择组件
function clearSelection() {
  designerStore.selectComponent(null)
  console.log('🎯 手动取消选择组件')
}

// 设备模式切换
function handleDeviceModeChange(mode: string) {
  console.log('📱 切换设备模式:', mode)
}

// 键盘事件处理
function handleKeyDown(event: KeyboardEvent) {
  if (event.key === 'Escape') {
    clearSelection()
  }
}

// 生命周期
onMounted(() => {
  // 添加全局键盘事件监听
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  // 清理键盘事件监听
  document.removeEventListener('keydown', handleKeyDown)
})

function removeComponent(componentId: string) {
  console.log('画布接收到删除事件:', componentId)
  designerStore.removeComponent(componentId)
}

function moveComponent(componentId: string, targetParentId: string | null, targetIndex: number) {
  console.log('画布接收到移动事件:', componentId, targetParentId, targetIndex)
  designerStore.moveComponent(componentId, targetParentId, targetIndex)
}

// 画布操作
function centerCanvas() {
  if (canvasContainer.value) {
    const container = canvasContainer.value
    container.scrollLeft = (container.scrollWidth - container.clientWidth) / 2
    container.scrollTop = (container.scrollHeight - container.clientHeight) / 2
  }
}

function fitCanvas() {
  const container = canvasContainer.value
  if (!container) return
  
  const containerWidth = container.clientWidth - 40 // 减去padding
  const phoneWidth = 375 // iPhone尺寸
  const scale = Math.min(containerWidth / phoneWidth, 1)
  canvasScale.value = Math.max(scale, 0.5)
}

// 初始化
onMounted(() => {
  nextTick(() => {
    fitCanvas()
    centerCanvas()
  })
})
</script>

<style scoped lang="scss">
.designer-canvas {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.canvas-toolbar {
  height: 48px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.canvas-container {
  flex: 1;
  overflow: auto;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.canvas-viewport {
  transform-origin: top center;
  transition: transform 0.2s ease;
}

.phone-frame {
  width: 375px;
  height: 812px;
  background: #000;
  border-radius: 40px;
  padding: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  position: relative;
  display: flex;
  flex-direction: column;

  &::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 134px;
    height: 6px;
    background: #000;
    border-radius: 3px;
    z-index: 10;
  }
}

.phone-screen {
  width: 100%;
  flex: 1;
  background: #fff;
  border-radius: 32px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  display: flex;
  flex-direction: column;

  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none;
  }

  /* Firefox 隐藏滚动条 */
  scrollbar-width: none;

  &.with-wechat-header {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}

// 微信标题栏样式
.wechat-header {
  background: #f7f7f7;
  border-bottom: 1px solid #e5e5e5;
  border-top-left-radius: 32px;
  border-top-right-radius: 32px;

  .wechat-header-content {
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    position: relative;

    .wechat-back {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
      font-size: 18px;
    }

    .wechat-title {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      font-size: 16px;
      font-weight: 500;
      color: #333;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .wechat-more {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
      font-size: 18px;
    }
  }
}

.page-editor {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  position: relative;
  cursor: pointer; // 提示可以点击

  &.drag-over {
    background: rgba(24, 144, 255, 0.1);

    &::after {
      content: '释放以添加组件';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #1890ff;
      font-size: 16px;
      font-weight: 500;
      z-index: 100;
    }
  }

  // 当有组件被选中时，显示提示
  &:hover::before {
    content: '点击空白区域取消选择组件 (ESC)';
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    pointer-events: none;
    opacity: 0.8;
  }
}

.page-preview {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  /* 确保滚动区域可以正常工作 */
  -webkit-overflow-scrolling: touch; /* iOS 平滑滚动 */
}

.drop-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #1890ff;
  z-index: 100;
  pointer-events: none;
}

.drop-icon {
  font-size: 48px;
  margin-bottom: 12px;
  animation: bounce 1s infinite;
}

.drop-text {
  font-size: 16px;
  font-weight: 500;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.empty-canvas {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #999;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
}
</style>
