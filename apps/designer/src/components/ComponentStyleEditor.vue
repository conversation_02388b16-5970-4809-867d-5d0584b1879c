<template>
  <div class="component-style-editor">
    <a-form layout="vertical" :model="styleForm">
      <!-- 布局样式 -->
      <a-collapse>
        <a-collapse-panel key="layout" header="布局与尺寸">
          <a-row :gutter="8">
            <a-col :span="12">
              <a-form-item label="显示类型">
                <a-select v-model:value="styleForm.display" @change="handleUpdate">
                  <a-select-option value="">默认</a-select-option>
                  <a-select-option value="block">块级</a-select-option>
                  <a-select-option value="inline">行内</a-select-option>
                  <a-select-option value="inline-block">行内块</a-select-option>
                  <a-select-option value="flex">弹性</a-select-option>
                  <a-select-option value="none">隐藏</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="宽度">
                <a-input v-model:value="styleForm.width" placeholder="auto, 100px, 100%" @blur="handleUpdate" />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item label="高度">
            <a-input v-model:value="styleForm.height" placeholder="auto, 100px, 100%" @blur="handleUpdate" />
          </a-form-item>
        </a-collapse-panel>
        
        <!-- 间距样式 -->
        <a-collapse-panel key="spacing" header="间距设置">
          <a-row :gutter="8">
            <a-col :span="12">
              <a-form-item label="外边距">
                <a-input v-model:value="styleForm.margin" placeholder="16px" @blur="handleUpdate" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="内边距">
                <a-input v-model:value="styleForm.padding" placeholder="16px" @blur="handleUpdate" />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="4">
            <a-col :span="6">
              <a-form-item label="上">
                <a-input v-model:value="styleForm.marginTop" placeholder="0" @blur="handleUpdate" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="右">
                <a-input v-model:value="styleForm.marginRight" placeholder="0" @blur="handleUpdate" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="下">
                <a-input v-model:value="styleForm.marginBottom" placeholder="0" @blur="handleUpdate" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="左">
                <a-input v-model:value="styleForm.marginLeft" placeholder="0" @blur="handleUpdate" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-collapse-panel>
        
        <!-- 背景样式 -->
        <a-collapse-panel key="background" header="背景设置">
          <a-row :gutter="8">
            <a-col :span="16">
              <a-form-item label="背景颜色">
                <a-input
                  v-model:value="styleForm.backgroundColor"
                  type="color"
                  @change="handleUpdate"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label=" ">
                <a-button
                  @click="clearBackgroundColor"
                  size="small"
                  block
                >
                  清空
                </a-button>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item label="背景图片">
            <a-input
              v-model:value="backgroundImageUrl"
              placeholder="直接输入图片链接，如：https://example.com/image.jpg"
              @blur="updateBackgroundImage"
            />
            <div style="margin-top: 6px; display: flex; gap: 6px; align-items: center;">
              <a-button size="small" @click="testBackgroundImage" type="primary">
                测试图片
              </a-button>
              <a-button size="small" @click="clearBackgroundImage">
                清空
              </a-button>
              <small style="color: #999;">直接粘贴图片链接即可</small>
            </div>
          </a-form-item>
          
          <a-row :gutter="8">
            <a-col :span="8">
              <a-form-item label="背景大小">
                <a-select v-model:value="styleForm.backgroundSize" @change="handleUpdate" allow-clear>
                  <a-select-option value="cover">覆盖</a-select-option>
                  <a-select-option value="contain">包含</a-select-option>
                  <a-select-option value="100% 100%">拉伸</a-select-option>
                  <a-select-option value="auto">自动</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="背景重复">
                <a-select v-model:value="styleForm.backgroundRepeat" @change="handleUpdate" allow-clear>
                  <a-select-option value="no-repeat">不重复</a-select-option>
                  <a-select-option value="repeat">重复</a-select-option>
                  <a-select-option value="repeat-x">水平重复</a-select-option>
                  <a-select-option value="repeat-y">垂直重复</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="背景位置">
                <a-select v-model:value="styleForm.backgroundPosition" @change="handleUpdate" allow-clear>
                  <a-select-option value="center">居中</a-select-option>
                  <a-select-option value="top">顶部</a-select-option>
                  <a-select-option value="bottom">底部</a-select-option>
                  <a-select-option value="left">左侧</a-select-option>
                  <a-select-option value="right">右侧</a-select-option>
                  <a-select-option value="top left">左上</a-select-option>
                  <a-select-option value="top right">右上</a-select-option>
                  <a-select-option value="bottom left">左下</a-select-option>
                  <a-select-option value="bottom right">右下</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-collapse-panel>
        
        <!-- 边框与文字样式 -->
        <a-collapse-panel key="border-text" header="边框与文字">
          <a-row :gutter="8">
            <a-col :span="12">
              <a-form-item label="边框">
                <a-input v-model:value="styleForm.border" placeholder="1px solid #ccc" @blur="handleUpdate" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="圆角">
                <a-input v-model:value="styleForm.borderRadius" placeholder="4px" @blur="handleUpdate" />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="8">
            <a-col :span="8">
              <a-form-item label="字体大小">
                <a-input v-model:value="styleForm.fontSize" placeholder="14px" @blur="handleUpdate" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="字体颜色">
                <a-input v-model:value="styleForm.color" type="color" @change="handleUpdate" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="字体粗细">
                <a-select v-model:value="styleForm.fontWeight" @change="handleUpdate">
                  <a-select-option value="">默认</a-select-option>
                  <a-select-option value="normal">正常</a-select-option>
                  <a-select-option value="bold">粗体</a-select-option>
                  <a-select-option value="lighter">细体</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="8">
            <a-col :span="12">
              <a-form-item label="文字对齐">
                <a-select v-model:value="styleForm.textAlign" @change="handleUpdate">
                  <a-select-option value="">默认</a-select-option>
                  <a-select-option value="left">左对齐</a-select-option>
                  <a-select-option value="center">居中</a-select-option>
                  <a-select-option value="right">右对齐</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="盒子阴影">
                <a-input v-model:value="styleForm.boxShadow" placeholder="0 2px 8px rgba(0,0,0,0.1)" @blur="handleUpdate" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-collapse-panel>
      </a-collapse>
      
      <!-- CSS编辑器 -->
      <a-form-item label="自定义CSS" style="margin-top: 12px;">
        <a-button @click="showCssEditor = true" block>
          <Icon icon="mdi:language-css3" />
          CSS编辑器
        </a-button>
      </a-form-item>
    </a-form>
    
    <!-- CSS编辑模态框 -->
    <a-modal
      v-model:open="showCssEditor"
      title="CSS样式编辑"
      width="800px"
      @ok="updateFromCss"
    >
      <a-textarea
        v-model:value="cssValue"
        :rows="20"
        placeholder="请输入CSS样式，如：color: red; font-size: 16px;"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import type { ComponentConfig } from '@lowcode/aslib/core'

const props = defineProps<{
  component: ComponentConfig
}>()

const emit = defineEmits<{
  update: [style: any]
}>()

// 响应式数据
const styleForm = ref<any>({})
const showCssEditor = ref(false)
const cssValue = ref('')
const backgroundImageUrl = ref('')

// 监听组件变化
watch(() => props.component, (newComponent) => {
  if (newComponent) {
    styleForm.value = { ...newComponent.style }
    cssValue.value = styleToCss(newComponent.style || {})

    // 同步背景图片数据
    if (newComponent.style?.backgroundImage) {
      const bgImage = newComponent.style.backgroundImage
      if (bgImage.startsWith('url(') && bgImage.endsWith(')')) {
        // 提取纯URL，移除url()包装和引号，显示用户友好的格式
        backgroundImageUrl.value = bgImage.slice(4, -1).replace(/^["']|["']$/g, '')
        console.log('🔄 同步背景图片URL:', backgroundImageUrl.value)
      } else if (bgImage.startsWith('linear-gradient') || bgImage.startsWith('radial-gradient')) {
        // CSS渐变保持原样
        backgroundImageUrl.value = bgImage
        console.log('🔄 同步CSS渐变:', backgroundImageUrl.value)
      } else {
        // 其他格式保持原样
        backgroundImageUrl.value = bgImage
        console.log('🔄 同步其他格式:', backgroundImageUrl.value)
      }
    } else {
      backgroundImageUrl.value = ''
    }

    console.log('🔄 组件样式同步:', {
      componentId: newComponent.id,
      style: newComponent.style,
      backgroundImageUrl: backgroundImageUrl.value
    })
  }
}, { immediate: true, deep: true })

// 处理更新
function handleUpdate() {
  // 清理空值和undefined
  const cleanedStyle = Object.fromEntries(
    Object.entries(styleForm.value).filter(([_, value]) =>
      value !== '' && value != null && value !== undefined
    )
  )

  console.log('🔄 样式更新:', {
    原始样式: styleForm.value,
    清理后样式: cleanedStyle,
    背景相关: {
      backgroundImage: cleanedStyle.backgroundImage,
      backgroundSize: cleanedStyle.backgroundSize,
      backgroundRepeat: cleanedStyle.backgroundRepeat,
      backgroundPosition: cleanedStyle.backgroundPosition
    }
  })

  emit('update', cleanedStyle)
}

// 样式对象转CSS字符串
function styleToCss(style: any): string {
  return Object.entries(style)
    .map(([key, value]) => `${kebabCase(key)}: ${value};`)
    .join('\n')
}

// CSS字符串转样式对象
function cssToStyle(css: string): any {
  const style: any = {}
  const rules = css.split(';').filter(rule => rule.trim())

  for (const rule of rules) {
    // 找到第一个冒号的位置，避免URL中的冒号被误认为是属性分隔符
    const colonIndex = rule.indexOf(':')
    if (colonIndex === -1) continue

    const property = rule.substring(0, colonIndex).trim()
    const value = rule.substring(colonIndex + 1).trim()

    if (property && value) {
      style[camelCase(property)] = value
    }
  }

  return style
}

// 驼峰转短横线
function kebabCase(str: string): string {
  return str.replace(/([A-Z])/g, '-$1').toLowerCase()
}

// 短横线转驼峰
function camelCase(str: string): string {
  return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
}

// 从CSS更新
function updateFromCss() {
  try {
    console.log('🔧 解析CSS:', cssValue.value)
    const parsed = cssToStyle(cssValue.value)
    console.log('✅ CSS解析结果:', parsed)

    styleForm.value = { ...styleForm.value, ...parsed }
    emit('update', styleForm.value)
    showCssEditor.value = false
  } catch (error) {
    console.error('❌ CSS解析失败:', error)
  }
}

// 背景图片相关方法
function updateBackgroundImage() {
  console.log('🖼️ 更新背景图片:', backgroundImageUrl.value)

  if (backgroundImageUrl.value && backgroundImageUrl.value.trim()) {
    let inputValue = backgroundImageUrl.value.trim()

    // 智能检测用户输入的格式
    if (inputValue.startsWith('linear-gradient') || inputValue.startsWith('radial-gradient')) {
      // CSS渐变
      styleForm.value.backgroundImage = inputValue
      console.log('🎨 检测到CSS渐变')
    } else if (inputValue.startsWith('url(')) {
      // 用户已经输入了完整的CSS格式
      styleForm.value.backgroundImage = inputValue
      console.log('🔧 检测到完整CSS格式')
    } else if (inputValue.startsWith('http') || inputValue.startsWith('//') || inputValue.startsWith('data:')) {
      // 普通图片URL - 自动添加url()包装
      styleForm.value.backgroundImage = `url("${inputValue}")`
      console.log('🖼️ 检测到图片链接，自动添加url()包装')

      // 为图片设置默认的背景属性，确保图片能正确显示
      if (!styleForm.value.backgroundSize) {
        styleForm.value.backgroundSize = 'cover'
      }
      if (!styleForm.value.backgroundRepeat) {
        styleForm.value.backgroundRepeat = 'no-repeat'
      }
      if (!styleForm.value.backgroundPosition) {
        styleForm.value.backgroundPosition = 'center'
      }
    } else {
      // 可能是相对路径或其他格式，也添加url()包装
      styleForm.value.backgroundImage = `url("${inputValue}")`
      console.log('📁 检测到其他格式，添加url()包装')
    }

    console.log('✅ 设置背景图片:', {
      原始输入: inputValue,
      最终CSS: styleForm.value.backgroundImage,
      backgroundSize: styleForm.value.backgroundSize,
      backgroundRepeat: styleForm.value.backgroundRepeat,
      backgroundPosition: styleForm.value.backgroundPosition
    })
  } else {
    // 明确删除背景图片相关属性
    delete styleForm.value.backgroundImage
    delete styleForm.value.backgroundSize
    delete styleForm.value.backgroundRepeat
    delete styleForm.value.backgroundPosition
    console.log('🗑️ 删除背景图片相关属性')
  }

  handleUpdate()
}

// 清空背景颜色
function clearBackgroundColor() {
  delete styleForm.value.backgroundColor
  console.log('🗑️ 清空背景颜色')
  handleUpdate()
}

// 测试背景图片
function testBackgroundImage() {
  backgroundImageUrl.value = 'https://picsum.photos/400/300'
  updateBackgroundImage()
  console.log('🧪 测试背景图片已设置')
}

// 清空背景图片
function clearBackgroundImage() {
  backgroundImageUrl.value = ''
  updateBackgroundImage()
  console.log('🗑️ 背景图片已清空')
}
</script>

<style scoped lang="scss">
.component-style-editor {
  :deep(.ant-form-item) {
    margin-bottom: 12px;
  }
  
  :deep(.ant-form-item-label) {
    padding-bottom: 4px;
    
    label {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }
  }
  
  :deep(.ant-collapse) {
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    margin-bottom: 8px;
  }
  
  :deep(.ant-collapse-header) {
    font-size: 12px;
    padding: 8px 12px;
  }
  
  :deep(.ant-collapse-content-box) {
    padding: 12px;
  }

  .form-help {
    margin-top: 4px;

    small {
      color: #999;
      font-size: 11px;
    }
  }
}
</style>
