<template>
  <div class="designer-header">
    <div class="header-left">
      <div class="logo">
        <Icon icon="mdi:code-braces" class="logo-icon" />
        <span class="logo-text">低代码设计器</span>
      </div>

      <!-- 面包屑导航 -->
      <div class="breadcrumb" v-if="currentPage">
        <a-breadcrumb>
          <a-breadcrumb-item>
            <a @click="goToAppList">
              <Icon icon="mdi:apps" />
              应用管理
            </a>
          </a-breadcrumb-item>
          <a-breadcrumb-item v-if="currentApp">
            <a @click="goToAppDetail">
              <Icon :icon="currentApp.icon" />
              {{ currentApp.name }}
            </a>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <Icon icon="mdi:file-document" />
            {{ currentPage.name }}
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>

      <div class="page-info" v-if="currentPage">
        <a-input
          v-model:value="pageTitle"
          class="page-title-input"
          @blur="updatePageTitle"
          @pressEnter="updatePageTitle"
        />
        <span class="page-path">{{ currentPage.path }}</span>
      </div>
    </div>
    
    <div class="header-center">
      <a-space>
        <!-- 快速返回按钮 -->
        <a-button @click="goToAppDetail" v-if="currentApp" size="small" type="default">
          <Icon icon="mdi:arrow-left" />
          返回应用
        </a-button>
        <a-divider type="vertical" v-if="currentApp" />

        <a-button
          :disabled="!canUndo"
          @click="designerStore.undo()"
          title="撤销 (Ctrl+Z)"
        >
          <Icon icon="mdi:undo" />
        </a-button>

        <a-button
          :disabled="!canRedo"
          @click="designerStore.redo()"
          title="重做 (Ctrl+Shift+Z)"
        >
          <Icon icon="mdi:redo" />
        </a-button>

        <a-divider type="vertical" />

        <a-button @click="togglePreview">
          <Icon :icon="isPreviewMode ? 'mdi:pencil' : 'mdi:eye'" />
          {{ isPreviewMode ? '编辑' : '预览' }}
        </a-button>
      </a-space>
    </div>
    
    <div class="header-right">
      <a-space>
        <a-button @click="showImportModal = true">
          <Icon icon="mdi:import" />
          导入
        </a-button>
        
        <a-button @click="showExportModal = true">
          <Icon icon="mdi:export" />
          导出
        </a-button>
        
        <a-button type="primary" @click="savePage">
          <Icon icon="mdi:content-save" />
          保存
        </a-button>
        
        <a-button @click="openPreview" title="在新窗口预览">
          <Icon icon="mdi:open-in-new" />
        </a-button>
      </a-space>
    </div>
    
    <!-- 导出模态框 -->
    <a-modal
      v-model:open="showExportModal"
      title="导出页面配置"
      width="800px"
      @ok="copyToClipboard"
    >
      <a-textarea
        v-model:value="exportedConfig"
        :rows="20"
        readonly
        placeholder="页面配置JSON"
      />
    </a-modal>
    
    <!-- 导入模态框 -->
    <a-modal
      v-model:open="showImportModal"
      title="导入页面配置"
      width="800px"
      @ok="importConfig"
    >
      <a-textarea
        v-model:value="importedConfig"
        :rows="20"
        placeholder="请粘贴页面配置JSON"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { Icon } from '@iconify/vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { useDesignerStore } from '../stores/designer'
import { useAppStore } from '../stores/app'

const router = useRouter()
const designerStore = useDesignerStore()
const appStore = useAppStore()

// 响应式数据
const pageTitle = ref('')
const showExportModal = ref(false)
const showImportModal = ref(false)
const exportedConfig = ref('')
const importedConfig = ref('')

// 计算属性
const { currentPage, isPreviewMode, canUndo, canRedo } = storeToRefs(designerStore)
const { currentApp } = storeToRefs(appStore)

// 监听当前页面变化
watch(currentPage, (newPage) => {
  if (newPage) {
    pageTitle.value = newPage.name
  }
}, { immediate: true })

// 监听导出模态框显示
watch(showExportModal, (show) => {
  if (show) {
    exportedConfig.value = designerStore.exportPageConfig()
  }
})

// 更新页面标题
function updatePageTitle() {
  if (currentPage.value && pageTitle.value.trim()) {
    // 直接更新页面配置的名称
    currentPage.value.name = pageTitle.value.trim()
  }
}

// 切换预览模式
function togglePreview() {
  designerStore.togglePreviewMode()
}

// 保存页面
async function savePage() {
  if (!currentPage.value) {
    message.error('没有页面可保存')
    return
  }

  try {
    console.log('🔄 开始保存页面配置:', currentPage.value.id)
    const success = await designerStore.savePageConfig()

    if (success) {
      message.success('页面保存成功！')
    } else {
      message.error('页面保存失败，请检查网络连接')
    }
  } catch (error) {
    console.error('保存页面失败:', error)
    message.error('页面保存失败')
  }
}

// 在新窗口打开预览
function openPreview() {
  if (!currentPage.value || !currentApp.value) return

  // 🔧 修复：使用哈希路由格式，与H5端保持一致
  const url = `http://localhost:3003/#/app/${currentApp.value.id}/home`
  window.open(url, '_blank')
}

// 复制到剪贴板
async function copyToClipboard() {
  try {
    await navigator.clipboard.writeText(exportedConfig.value)
    message.success('配置已复制到剪贴板')
    showExportModal.value = false
  } catch (error) {
    message.error('复制失败')
  }
}

// 导入配置
function importConfig() {
  if (!importedConfig.value.trim()) {
    message.error('请输入配置内容')
    return
  }

  const success = designerStore.importPageConfig(importedConfig.value)
  if (success) {
    message.success('配置导入成功')
    showImportModal.value = false
    importedConfig.value = ''
  } else {
    message.error('配置格式错误')
  }
}

// 导航方法
function goToAppList() {
  router.push('/apps')
}

function goToAppDetail() {
  if (currentApp.value) {
    // 🔧 修复：返回应用详情时带上来源信息
    const query = {
      from: 'designer',
      pageId: currentPage.value?.id
    }
    router.push({
      path: `/app/${currentApp.value.id}`,
      query
    })
  }
}
</script>

<style scoped lang="scss">
.designer-header {
  height: 60px;
  background: #ffffff;
  border-bottom: 1px solid #e4e4e7;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
}

.breadcrumb {
  margin-left: 24px;

  :deep(.ant-breadcrumb-link) {
    color: #71717a;
    font-size: 14px;
    font-weight: 500;

    &:hover {
      color: #18181b;
    }
  }

  :deep(.ant-breadcrumb-separator) {
    color: #d4d4d8;
  }
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  font-weight: 600;
  color: #18181b;
}

.logo-icon {
  font-size: 24px;
  color: #52525b;
}

.page-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title-input {
  width: 200px;
  border: 1px solid transparent;
  border-radius: 6px;
  background: #f9fafb;
  font-size: 14px;
  font-weight: 500;

  &:focus {
    border-color: #18181b;
    box-shadow: 0 0 0 2px rgba(24, 24, 27, 0.1);
    background: #ffffff;
  }
}

.page-path {
  color: #71717a;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f4f4f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-right {
  display: flex;
  align-items: center;
}
</style>
