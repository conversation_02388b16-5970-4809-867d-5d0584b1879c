<template>
  <div class="menu-items-editor">
    <div class="editor-header">
      <span class="title">菜单项配置 ({{ menuItems.length }}项)</span>
      <a-button type="primary" size="small" @click="addMenuItem">
        <Icon icon="mdi:plus" />
        添加
      </a-button>
    </div>

    <!-- 紧凑的列表视图 -->
    <div class="menu-items-compact-list">
      <div 
        v-for="(item, index) in sortedMenuItems" 
        :key="item.id || index"
        class="menu-item-row"
      >
        <div class="item-info">
          <div class="item-icon">
            <Icon :icon="parseIcon(item.icon).icon" :style="{ color: parseIcon(item.icon).color }" />
          </div>
          <div class="item-text">
            <div class="item-name">{{ item.name || '未命名' }}</div>
            <div class="item-id">ID: {{ item.id }} (排序: {{ item.order }})</div>
          </div>
          <div class="item-status">
            <a-tag v-if="!item.visible" color="default" size="small">隐藏</a-tag>
            <a-tag v-if="item.disabled" color="orange" size="small">禁用</a-tag>
            <a-tag v-if="item.visible && !item.disabled" color="green" size="small">启用</a-tag>
          </div>
        </div>
        
        <div class="item-actions">
          <a-tooltip title="编辑">
            <a-button size="small" @click="toggleEdit(getOriginalIndex(item))" :type="editingIndex === getOriginalIndex(item) ? 'primary' : 'default'">
              <Icon icon="mdi:pencil" />
            </a-button>
          </a-tooltip>
          <a-tooltip title="上移">
            <a-button size="small" @click="moveUp(getOriginalIndex(item))" :disabled="index === 0">
              <Icon icon="mdi:arrow-up" />
            </a-button>
          </a-tooltip>
          <a-tooltip title="下移">
            <a-button size="small" @click="moveDown(getOriginalIndex(item))" :disabled="index === sortedMenuItems.length - 1">
              <Icon icon="mdi:arrow-down" />
            </a-button>
          </a-tooltip>
          <a-tooltip title="删除">
            <a-button size="small" danger @click="removeMenuItem(getOriginalIndex(item))">
              <Icon icon="mdi:delete" />
            </a-button>
          </a-tooltip>
        </div>

        <!-- 展开的编辑区域 -->
        <div v-if="editingIndex === getOriginalIndex(item)" class="item-edit-panel">
          <a-form layout="vertical" size="small">
            <a-row :gutter="8">
              <a-col :span="8">
                <a-form-item label="ID" style="margin-bottom: 8px;">
                  <a-input 
                    v-model:value="item.id" 
                    size="small"
                    @change="updateMenuItem(getOriginalIndex(item), 'id', item.id)"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="名称" style="margin-bottom: 8px;">
                  <a-input 
                    v-model:value="item.name" 
                    size="small"
                    @change="updateMenuItem(getOriginalIndex(item), 'name', item.name)"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="图标" style="margin-bottom: 8px;">
                  <IconPicker
                    :modelValue="item.icon"
                    size="small"
                    @update:modelValue="updateMenuItem(getOriginalIndex(item), 'icon', $event)"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="8">
              <a-col :span="8">
                <a-form-item label="显示" style="margin-bottom: 8px;">
                  <a-switch 
                    v-model:checked="item.visible" 
                    size="small"
                    @change="updateMenuItem(getOriginalIndex(item), 'visible', item.visible)"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="启用" style="margin-bottom: 8px;">
                  <a-switch 
                    :checked="!item.disabled" 
                    size="small"
                    @change="updateMenuItem(getOriginalIndex(item), 'disabled', !$event)"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="排序" style="margin-bottom: 8px;">
                  <a-input-number 
                    v-model:value="item.order"
                    size="small"
                    :min="1"
                    style="width: 100%"
                    @change="updateMenuItem(getOriginalIndex(item), 'order', item.order)"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item label="描述" style="margin-bottom: 8px;">
              <a-textarea 
                v-model:value="item.description" 
                placeholder="菜单项描述（可选）"
                size="small"
                :rows="2"
                @change="updateMenuItem(getOriginalIndex(item), 'description', item.description)"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>

    <div v-if="menuItems.length === 0" class="empty-state">
      <a-empty description="暂无菜单项" :image="null">
        <a-button type="primary" size="small" @click="addMenuItem">添加第一个菜单项</a-button>
      </a-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Icon } from '@iconify/vue'
import IconPicker from '../IconPicker.vue'

interface MenuItem {
  id: string
  name: string
  icon: string
  visible?: boolean
  disabled?: boolean
  description?: string
  order?: number
}

interface Props {
  modelValue: MenuItem[]
}

interface Emits {
  (e: 'update:modelValue', value: MenuItem[]): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => []
})

const emit = defineEmits<Emits>()

const menuItems = ref<MenuItem[]>([...props.modelValue])
const editingIndex = ref<number>(-1)

// 计算属性：按order排序显示菜单项
const sortedMenuItems = computed(() => {
  return [...menuItems.value].sort((a, b) => (a.order || 0) - (b.order || 0))
})

// 切换编辑状态
function toggleEdit(index: number) {
  editingIndex.value = editingIndex.value === index ? -1 : index
}

// 获取原始数组中的索引
function getOriginalIndex(item: MenuItem): number {
  return menuItems.value.findIndex(originalItem => originalItem.id === item.id)
}

// 解析图标配置
function parseIcon(iconValue: string) {
  if (!iconValue) return { icon: 'mdi:help', color: '' }
  const parts = iconValue.split('|')
  return {
    icon: parts[0] || 'mdi:help',
    color: parts[1] || ''
  }
}

// 添加菜单项
function addMenuItem() {
  const newItem: MenuItem = {
    id: `menu_${Date.now()}`,
    name: '新菜单项',
    icon: 'mdi:star',
    visible: true,
    disabled: false,
    description: '',
    order: menuItems.value.length + 1
  }
  
  menuItems.value.push(newItem)
  
  // 自动展开编辑
  editingIndex.value = menuItems.value.length - 1
  
  emitChanges()
}

// 删除菜单项
function removeMenuItem(index: number) {
  menuItems.value.splice(index, 1)
  emitChanges()
}

// 上移菜单项 - 基于排序后的显示位置
function moveUp(originalIndex: number) {
  const sortedList = sortedMenuItems.value
  const item = menuItems.value[originalIndex]
  const sortedIndex = sortedList.findIndex(sortedItem => sortedItem.id === item.id)
  
  if (sortedIndex > 0) {
    const currentItem = sortedList[sortedIndex]
    const prevItem = sortedList[sortedIndex - 1]
    
    // 交换order值
    const tempOrder = currentItem.order || 0
    currentItem.order = prevItem.order || 0
    prevItem.order = tempOrder
    
    emitChanges()
  }
}

// 下移菜单项 - 基于排序后的显示位置
function moveDown(originalIndex: number) {
  const sortedList = sortedMenuItems.value
  const item = menuItems.value[originalIndex]
  const sortedIndex = sortedList.findIndex(sortedItem => sortedItem.id === item.id)
  
  if (sortedIndex < sortedList.length - 1) {
    const currentItem = sortedList[sortedIndex]
    const nextItem = sortedList[sortedIndex + 1]
    
    // 交换order值
    const tempOrder = currentItem.order || 0
    currentItem.order = nextItem.order || 0
    nextItem.order = tempOrder
    
    emitChanges()
  }
}

// 更新菜单项
function updateMenuItem(index: number, field: keyof MenuItem, value: any) {
  console.log('🔧 MenuItemsEditor: 更新菜单项', {
    index,
    field,
    value,
    oldValue: menuItems.value[index][field]
  })
  
  menuItems.value[index][field] = value
  emitChanges()
}

// 发射变更事件
function emitChanges() {
  emit('update:modelValue', [...menuItems.value])
}

// 监听外部变化
watch(() => props.modelValue, (newValue) => {
  menuItems.value = [...newValue]
}, { deep: true })
</script>

<style scoped lang="scss">
.menu-items-editor {
  .editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .title {
      font-weight: 500;
      font-size: 13px;
      color: #666;
    }
  }
  
  .menu-items-compact-list {
    .menu-item-row {
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      margin-bottom: 8px;
      background: #fff;
      
      .item-info {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        gap: 8px;
        
        .item-icon {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f8f9fa;
          border-radius: 4px;
          font-size: 14px;
        }
        
        .item-text {
          flex: 1;
          min-width: 0;
          
          .item-name {
            font-size: 13px;
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
          }
          
          .item-id {
            font-size: 11px;
            color: #999;
          }
        }
        
        .item-status {
          display: flex;
          gap: 4px;
        }
      }
      
      .item-actions {
        display: flex;
        gap: 4px;
        padding: 0 12px 8px 12px;
        border-top: 1px solid #f0f0f0;
        padding-top: 8px;
        justify-content: flex-end;
      }
      
      .item-edit-panel {
        padding: 12px;
        border-top: 1px solid #f0f0f0;
        background: #fafafa;
        border-radius: 0 0 6px 6px;
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 24px 16px;
  }
}

// 全局样式覆盖，减少表单项间距
:deep(.item-edit-panel) {
  .ant-form-item {
    margin-bottom: 8px !important;
  }
  
  .ant-form-item-label {
    padding-bottom: 2px !important;
    
    label {
      font-size: 11px !important;
      color: #666 !important;
    }
  }
  
  .ant-input,
  .ant-input-number,
  .ant-select-selector,
  .ant-textarea {
    font-size: 12px !important;
  }
}
</style>
