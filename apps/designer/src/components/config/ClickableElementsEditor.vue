<template>
  <div class="clickable-elements-editor">
    <div class="elements-list">
      <div 
        v-for="(element, index) in localElements" 
        :key="element.id || index"
        class="element-item"
      >
        <a-card size="small">
          <template #title>
            <div class="element-title">
              <a-tag :color="getElementTypeColor(element.type)">
                {{ getElementTypeLabel(element.type) }}
              </a-tag>
              <span>{{ element.label || element.id }}</span>
            </div>
          </template>
          
          <template #extra>
            <div class="element-actions">
              <a-button 
                size="small" 
                @click="moveUp(index)"
                :disabled="index === 0"
                title="上移"
              >
                ↑
              </a-button>
              <a-button 
                size="small" 
                @click="moveDown(index)"
                :disabled="index === localElements.length - 1"
                title="下移"
              >
                ↓
              </a-button>
              <a-button 
                size="small" 
                danger 
                @click="removeElement(index)"
                title="删除"
              >
                ×
              </a-button>
            </div>
          </template>

          <a-form layout="vertical" size="small">
            <!-- 元素ID -->
            <a-form-item label="元素ID" style="margin-bottom: 8px;">
              <EventIdInput
                v-model="element.id"
                placeholder="输入或生成元素ID"
                :element-type="element.type"
                :existing-ids="getExistingIds(index)"
                @update:modelValue="updateElement"
              />
            </a-form-item>

            <!-- 元素类型 -->
            <a-form-item label="类型" style="margin-bottom: 8px;">
              <a-select 
                v-model:value="element.type" 
                @change="updateElement"
                size="small"
              >
                <a-select-option value="button">按钮</a-select-option>
                <a-select-option value="action">操作</a-select-option>
                <a-select-option value="link">链接</a-select-option>
              </a-select>
            </a-form-item>

            <!-- 显示标签 -->
            <a-form-item label="显示标签" style="margin-bottom: 8px;">
              <a-input 
                v-model:value="element.label" 
                placeholder="按钮显示的文字"
                @change="updateElement"
                size="small"
              />
            </a-form-item>

            <!-- 位置标识 -->
            <a-form-item label="位置标识" style="margin-bottom: 8px;">
              <a-input 
                v-model:value="element.position" 
                placeholder="如：top-right, flow-area"
                @change="updateElement"
                size="small"
              />
            </a-form-item>

            <!-- 描述 -->
            <a-form-item label="描述" style="margin-bottom: 8px;">
              <a-textarea 
                v-model:value="element.description" 
                placeholder="元素的功能描述"
                @change="updateElement"
                :rows="2"
                size="small"
              />
            </a-form-item>

            <!-- 可见性 -->
            <a-form-item style="margin-bottom: 0;">
              <a-checkbox 
                v-model:checked="element.visible" 
                @change="updateElement"
              >
                显示此元素
              </a-checkbox>
            </a-form-item>
          </a-form>
        </a-card>
      </div>
    </div>

    <!-- 添加按钮 -->
    <a-button 
      @click="addElement" 
      type="dashed" 
      block 
      style="margin-top: 16px;"
    >
      <Icon icon="mdi:plus" />
      添加可点击元素
    </a-button>

    <!-- 预设模板 -->
    <a-dropdown :trigger="['click']" placement="topCenter">
      <a-button 
        type="link" 
        size="small" 
        style="margin-top: 8px; width: 100%;"
      >
        <Icon icon="mdi:magic-staff" />
        使用预设模板
      </a-button>
      <template #overlay>
        <a-menu @click="applyTemplate">
          <a-menu-item key="basic-buttons">
            <div>
              <div>基础按钮组</div>
              <div style="font-size: 11px; color: #999;">更多、续费、充值</div>
            </div>
          </a-menu-item>
          <a-menu-item key="action-buttons">
            <div>
              <div>操作按钮组</div>
              <div style="font-size: 11px; color: #999;">确认、取消、重置</div>
            </div>
          </a-menu-item>
          <a-menu-item key="nav-links">
            <div>
              <div>导航链接组</div>
              <div style="font-size: 11px; color: #999;">首页、设置、帮助</div>
            </div>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Icon } from '@iconify/vue'
import EventIdInput from '../form/EventIdInput.vue'

interface ClickableElement {
  id: string
  type: 'button' | 'action' | 'link'
  label: string
  position?: string
  visible?: boolean
  description?: string
}

const props = defineProps<{
  modelValue: ClickableElement[]
}>()

const emit = defineEmits<{
  'update:modelValue': [value: ClickableElement[]]
}>()

const localElements = ref<ClickableElement[]>([])

// 初始化
watch(() => props.modelValue, (newValue) => {
  localElements.value = newValue ? [...newValue] : []
}, { immediate: true, deep: true })

// 更新元素
function updateElement() {
  emit('update:modelValue', [...localElements.value])
}

// 添加元素
function addElement() {
  const newElement: ClickableElement = {
    id: `element-${Date.now()}`,
    type: 'button',
    label: '新按钮',
    position: '',
    visible: true,
    description: ''
  }
  localElements.value.push(newElement)
  updateElement()
}

// 删除元素
function removeElement(index: number) {
  localElements.value.splice(index, 1)
  updateElement()
}

// 上移
function moveUp(index: number) {
  if (index > 0) {
    const temp = localElements.value[index]
    localElements.value[index] = localElements.value[index - 1]
    localElements.value[index - 1] = temp
    updateElement()
  }
}

// 下移
function moveDown(index: number) {
  if (index < localElements.value.length - 1) {
    const temp = localElements.value[index]
    localElements.value[index] = localElements.value[index + 1]
    localElements.value[index + 1] = temp
    updateElement()
  }
}

// 获取已存在的ID
function getExistingIds(currentIndex: number): string[] {
  return localElements.value
    .map((el, index) => index !== currentIndex ? el.id : null)
    .filter(Boolean) as string[]
}

// 获取元素类型颜色
function getElementTypeColor(type: string): string {
  const colors = {
    button: 'blue',
    action: 'green', 
    link: 'orange'
  }
  return colors[type as keyof typeof colors] || 'default'
}

// 获取元素类型标签
function getElementTypeLabel(type: string): string {
  const labels = {
    button: '按钮',
    action: '操作',
    link: '链接'
  }
  return labels[type as keyof typeof labels] || type
}

// 应用预设模板
function applyTemplate({ key }: { key: string }) {
  const templates: Record<string, ClickableElement[]> = {
    'basic-buttons': [
      { id: 'more-button', type: 'button', label: '更多', position: 'top-right', visible: true, description: '查看详细信息' },
      { id: 'renew-button', type: 'button', label: '续费', position: 'center', visible: true, description: '续费服务' },
      { id: 'recharge-button', type: 'button', label: '充值', position: 'bottom', visible: true, description: '账户充值' }
    ],
    'action-buttons': [
      { id: 'confirm-action', type: 'action', label: '确认', position: 'right', visible: true, description: '确认操作' },
      { id: 'cancel-action', type: 'action', label: '取消', position: 'left', visible: true, description: '取消操作' },
      { id: 'reset-action', type: 'action', label: '重置', position: 'center', visible: true, description: '重置设置' }
    ],
    'nav-links': [
      { id: 'home-link', type: 'link', label: '首页', position: 'nav', visible: true, description: '返回首页' },
      { id: 'settings-link', type: 'link', label: '设置', position: 'nav', visible: true, description: '系统设置' },
      { id: 'help-link', type: 'link', label: '帮助', position: 'nav', visible: true, description: '帮助文档' }
    ]
  }

  if (templates[key]) {
    localElements.value = [...templates[key]]
    updateElement()
  }
}
</script>

<style scoped lang="scss">
.clickable-elements-editor {
  .elements-list {
    .element-item {
      margin-bottom: 16px;
      
      .element-title {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .element-actions {
        display: flex;
        gap: 4px;
      }
    }
  }
}
</style>
