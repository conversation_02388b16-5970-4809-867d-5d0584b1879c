<template>
  <div class="clickable-areas-editor">
    <div class="areas-list">
      <div 
        v-for="(area, index) in localAreas" 
        :key="area.position"
        class="area-item"
      >
        <a-card size="small">
          <template #title>
            <div class="area-title">
              <a-tag :color="area.enabled ? 'green' : 'default'">
                {{ area.label }}
              </a-tag>
              <span class="position-tag">{{ area.position }}</span>
            </div>
          </template>
          
          <template #extra>
            <a-switch 
              v-model:checked="area.enabled"
              @change="updateAreas"
              size="small"
            />
          </template>

          <div class="area-content">
            <!-- 区域描述 -->
            <div class="area-description">
              <Icon :icon="getElementTypeIcon(area.elementType)" />
              {{ area.description }}
            </div>

            <!-- 事件ID配置 -->
            <div v-if="area.enabled" class="area-config">
              <a-form layout="vertical" size="small">
                <a-form-item label="事件ID" style="margin-bottom: 8px;">
                  <EventIdInput
                    v-model="area.eventId"
                    :placeholder="`建议: ${area.defaultId}`"
                    :element-type="area.elementType"
                    :existing-ids="getExistingIds(index)"
                    @update:modelValue="updateAreas"
                  />
                  <div class="id-hint">
                    💡 此ID将用于事件配置中的元素绑定
                  </div>
                </a-form-item>
              </a-form>
            </div>

            <!-- 禁用状态提示 -->
            <div v-else class="disabled-hint">
              <Icon icon="mdi:information-outline" />
              此区域已禁用，不会响应点击事件
            </div>
          </div>
        </a-card>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <a-button @click="resetToDefaults" size="small" style="margin-right: 8px;">
        <Icon icon="mdi:restore" />
        重置为默认
      </a-button>
      
      <a-button @click="generateAllIds" type="primary" size="small">
        <Icon icon="mdi:auto-fix" />
        自动生成所有ID
      </a-button>
    </div>

    <!-- 预览说明 -->
    <div class="preview-hint">
      <a-alert
        message="配置说明"
        description="每个可点击区域对应组件中的一个按钮位置。启用区域并设置事件ID后，可在事件配置中为该ID绑定具体的跳转或操作行为。"
        type="info"
        show-icon
        style="margin-top: 16px;"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Icon } from '@iconify/vue'
import EventIdInput from '../form/EventIdInput.vue'

interface ClickableArea {
  position: string          // 位置标识
  label: string            // 显示标签
  description: string      // 功能描述
  elementType: string      // 元素类型
  defaultId: string        // 默认ID建议
  eventId?: string         // 用户配置的事件ID
  enabled: boolean         // 是否启用
}

const props = defineProps<{
  modelValue: ClickableArea[]
}>()

const emit = defineEmits<{
  'update:modelValue': [value: ClickableArea[]]
}>()

const localAreas = ref<ClickableArea[]>([])

// 初始化
watch(() => props.modelValue, (newValue) => {
  localAreas.value = newValue ? newValue.map(area => ({
    ...area,
    eventId: area.eventId || area.defaultId  // 如果没有设置eventId，使用defaultId
  })) : []
}, { immediate: true, deep: true })

// 更新区域配置
function updateAreas() {
  emit('update:modelValue', [...localAreas.value])
}

// 获取已存在的ID
function getExistingIds(currentIndex: number): string[] {
  return localAreas.value
    .map((area, index) => index !== currentIndex && area.enabled ? area.eventId : null)
    .filter(Boolean) as string[]
}

// 获取元素类型图标
function getElementTypeIcon(type: string): string {
  const icons = {
    button: 'mdi:gesture-tap-button',
    action: 'mdi:lightning-bolt',
    link: 'mdi:link'
  }
  return icons[type as keyof typeof icons] || 'mdi:cursor-default-click'
}

// 重置为默认配置
function resetToDefaults() {
  localAreas.value.forEach(area => {
    area.eventId = area.defaultId
    area.enabled = true
  })
  updateAreas()
}

// 自动生成所有ID
function generateAllIds() {
  localAreas.value.forEach(area => {
    if (area.enabled) {
      // 基于位置生成语义化ID
      const positionMap: Record<string, string> = {
        'top-right': 'more-btn',
        'flow-area': 'renew-btn', 
        'balance-area': 'recharge-btn',
        'network-area': 'network-btn',
        'action-area': 'action-btn'
      }
      
      area.eventId = positionMap[area.position] || `${area.position}-btn`
    }
  })
  updateAreas()
}
</script>

<style scoped lang="scss">
.clickable-areas-editor {
  .areas-list {
    .area-item {
      margin-bottom: 16px;
      
      .area-title {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .position-tag {
          font-size: 11px;
          color: #999;
          background: #f5f5f5;
          padding: 2px 6px;
          border-radius: 4px;
        }
      }
      
      .area-content {
        .area-description {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #666;
          font-size: 13px;
          margin-bottom: 12px;
          padding: 8px;
          background: #fafafa;
          border-radius: 4px;
        }
        
        .area-config {
          .id-hint {
            font-size: 11px;
            color: #1890ff;
            margin-top: 4px;
          }
        }
        
        .disabled-hint {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #999;
          font-size: 13px;
          padding: 8px;
          background: #f5f5f5;
          border-radius: 4px;
        }
      }
    }
  }
  
  .actions {
    margin-top: 16px;
    text-align: center;
  }
  
  .preview-hint {
    margin-top: 16px;
  }
}
</style>
