<template>
  <div class="component-events-editor">
    <!-- 可用事件说明 -->
    <EventHelpCard
      v-if="hasAvailableEvents"
      :component-type="props.component?.type || ''"
    />

    <!-- 事件配置列表 -->
    <div class="events-list">
      <template v-for="(eventConfigs, eventName) in eventsForm" :key="String(eventName)">
        <!-- 如果是数组（支持多个配置） -->
        <template v-if="Array.isArray(eventConfigs)">
          <EventConfigForm
            v-for="(config, index) in eventConfigs"
            :key="`${eventName}-${index}`"
            :event-name="String(eventName)"
            :config="config"
            :component-type="props.component?.type || ''"
            :component="props.component"
            :config-index="index"
            :total-configs="eventConfigs.length"
            @update="handleUpdate"
            @remove="removeEventConfig(String(eventName), index)"
            @open-editor="openCodeEditor(String(eventName), index)"
          />
        </template>
        <!-- 如果是单个配置（向后兼容） -->
        <template v-else>
          <EventConfigForm
            :key="String(eventName)"
            :event-name="String(eventName)"
            :config="eventConfigs"
            :component-type="props.component?.type || ''"
            :component="props.component"
            @update="handleUpdate"
            @remove="removeEvent(String(eventName))"
            @open-editor="openCodeEditor(String(eventName))"
          />
        </template>
      </template>
    </div>

    <!-- 添加事件按钮 -->
    <a-button @click="showAddEventModal = true" type="dashed" block style="margin-top: 16px;">
      <Icon icon="mdi:plus" />
      添加事件
    </a-button>

    <!-- 添加事件对话框 -->
    <AddEventModal
      v-model:visible="showAddEventModal"
      :component-type="props.component?.type || ''"
      @add="addEvent"
    />

    <!-- 代码编辑器 -->
    <CodeEditor
      v-model:visible="showCodeEditor"
      :initial-code="tempCodeContent"
      @save="saveCode"
      @cancel="cancelCodeEdit"
    />
  </div>
</template>


<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { Icon } from '@iconify/vue'
import type { ComponentConfig } from '@lowcode/aslib/core'
import EventHelpCard from './events/EventHelpCard.vue'
import EventConfigForm from './events/EventConfigForm.vue'
import AddEventModal from './events/AddEventModal.vue'
import CodeEditor from './events/CodeEditor.vue'
import {
  ComponentEvents
} from '@lowcode/aslib/ui'
import { UnifiedEventManager } from '@lowcode/aslib/core'

const props = defineProps<{
  component: ComponentConfig
}>()

const emit = defineEmits<{
  update: [events: any]
}>()

// 响应式数据
const eventsForm = ref<any>({})
const showAddEventModal = ref(false)
const showCodeEditor = ref(false)
const tempCodeContent = ref('')
const currentEditingEvent = ref('')

// 计算属性：是否有可用事件
const hasAvailableEvents = computed(() => {
  const componentType = props.component?.type
  if (!componentType) return false

  // ✨ 优先使用统一事件管理器
  const unifiedEvents = UnifiedEventManager.getComponentEvents(componentType)
  if (unifiedEvents.length > 0) return true

  // 兼容原有的事件定义
  if (!ComponentEvents[componentType]) {
    return false
  }
  return Object.keys(ComponentEvents[componentType]).length > 0
})

// 创建默认事件配置
function createDefaultEventConfig(eventName: string, componentType: string) {
  return {
    type: 'navigate',
    navigateType: 'page',
    target: '',
    method: 'GET',
    onSuccess: 'none',
    params: {},
    handler: '',
    webviewTitle: '',
    webviewOptions: ['showNavBar', 'showBackButton']
  }
}

// 监听组件变化，初始化事件配置
watch(() => props.component, (newComponent) => {
  if (newComponent?.events) {
    eventsForm.value = { ...newComponent.events }
  } else {
    eventsForm.value = {}
  }
}, { immediate: true })

// 更新事件配置
function handleUpdate() {
  emit('update', eventsForm.value)
}

// 添加事件
function addEvent(eventName: string) {
  if (!eventName) return

  const defaultConfig = createDefaultEventConfig(eventName, props.component?.type || '')

  // 对于支持多配置的事件，使用数组结构
  if ((eventName === 'menuClick' && props.component?.type === 'HomeMore') ||
      (eventName === 'click')) {
    if (!eventsForm.value[eventName]) {
      eventsForm.value[eventName] = []
    }
    if (Array.isArray(eventsForm.value[eventName])) {
      eventsForm.value[eventName].push(defaultConfig)
    } else {
      // 转换为数组格式
      eventsForm.value[eventName] = [eventsForm.value[eventName], defaultConfig]
    }
  } else {
    // 其他事件使用单个配置
    if (!eventsForm.value[eventName]) {
      eventsForm.value[eventName] = defaultConfig
    }
  }

  handleUpdate()
}

// 删除事件
function removeEvent(eventName: string) {
  if (eventsForm.value[eventName]) {
    delete eventsForm.value[eventName]
    handleUpdate()
  }
}

// 删除特定的事件配置（用于数组格式）
function removeEventConfig(eventName: string, index: number) {
  const eventConfigs = eventsForm.value[eventName]
  if (Array.isArray(eventConfigs) && eventConfigs.length > index) {
    eventConfigs.splice(index, 1)

    // 如果只剩一个配置，可以选择保持数组格式或转换为单个配置
    if (eventConfigs.length === 0) {
      delete eventsForm.value[eventName]
    }

    handleUpdate()
  }
}

// 打开代码编辑器
function openCodeEditor(eventName: string, index?: number) {
  currentEditingEvent.value = eventName

  let config
  if (typeof index === 'number' && Array.isArray(eventsForm.value[eventName])) {
    config = eventsForm.value[eventName][index]
  } else {
    config = eventsForm.value[eventName]
  }

  tempCodeContent.value = config?.handler || ''
  showCodeEditor.value = true
}

// 保存代码
function saveCode(code: string) {
  if (currentEditingEvent.value && eventsForm.value[currentEditingEvent.value]) {
    eventsForm.value[currentEditingEvent.value].handler = code
    handleUpdate()
  }
}

// 取消代码编辑
function cancelCodeEdit() {
  tempCodeContent.value = ''
  currentEditingEvent.value = ''
}
</script>

<style scoped lang="scss">
.component-events-editor {
  .events-list {
    .event-item {
      margin-bottom: 16px;
    }
  }
}
</style>