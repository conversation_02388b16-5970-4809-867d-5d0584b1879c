<template>
  <div ref="editorContainer" class="monaco-editor-container" :style="{ height: `${height}px` }"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as monaco from 'monaco-editor'

interface Props {
  value?: string
  language?: string
  height?: number
  options?: monaco.editor.IStandaloneEditorConstructionOptions
}

interface Emits {
  (e: 'update:value', value: string): void
  (e: 'change', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  language: 'javascript',
  height: 200,
  options: () => ({})
})

const emit = defineEmits<Emits>()

const editorContainer = ref<HTMLElement>()
let editor: monaco.editor.IStandaloneCodeEditor | null = null

// 默认编辑器选项
const defaultOptions: monaco.editor.IStandaloneEditorConstructionOptions = {
  theme: 'vs',
  fontSize: 14,
  tabSize: 2,
  insertSpaces: true,
  automaticLayout: true,
  scrollBeyondLastLine: false,
  minimap: { enabled: false },
  wordWrap: 'on',
  lineNumbers: 'on',
  glyphMargin: false,
  folding: false,
  lineDecorationsWidth: 0,
  lineNumbersMinChars: 3,
  renderLineHighlight: 'none'
}

onMounted(async () => {
  await nextTick()
  
  if (!editorContainer.value) return

  // 合并选项
  const editorOptions = {
    ...defaultOptions,
    ...props.options,
    value: props.value,
    language: props.language
  }

  // 创建编辑器
  editor = monaco.editor.create(editorContainer.value, editorOptions)

  // 监听内容变化
  editor.onDidChangeModelContent(() => {
    const value = editor?.getValue() || ''
    emit('update:value', value)
    emit('change', value)
  })

  // 设置JavaScript语言的智能提示
  if (props.language === 'javascript') {
    setupJavaScriptIntelliSense()
  }
})

onUnmounted(() => {
  if (editor) {
    editor.dispose()
    editor = null
  }
})

// 监听value变化
watch(() => props.value, (newValue) => {
  if (editor && editor.getValue() !== newValue) {
    editor.setValue(newValue || '')
  }
})

// 监听语言变化
watch(() => props.language, (newLanguage) => {
  if (editor) {
    const model = editor.getModel()
    if (model) {
      monaco.editor.setModelLanguage(model, newLanguage)
    }
  }
})

// 设置JavaScript智能提示
function setupJavaScriptIntelliSense() {
  // 添加自定义类型定义
  const contextTypes = `
// Axios类型定义
declare interface AxiosResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: any;
  config: any;
}

declare interface AxiosRequestConfig {
  url?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';
  baseURL?: string;
  headers?: any;
  params?: any;
  data?: any;
  timeout?: number;
  withCredentials?: boolean;
}

declare interface AxiosInstance {
  (config: AxiosRequestConfig): Promise<AxiosResponse>;
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>>;
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>>;
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>>;
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>>;
  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>>;
  head<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>>;
  options<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>>;
}

// 上下文类型定义
declare interface Context {
  router: {
    push: (path: string | { path: string, query?: any, params?: any }) => void;
    back: () => void;
    replace: (path: string | { path: string, query?: any }) => void;
    go: (delta: number) => void;
    forward: () => void;
  };
  route: {
    path: string;
    name?: string;
    params: Record<string, any>;
    query: Record<string, any>;
    hash: string;
    fullPath: string;
    meta: Record<string, any>;
  };
  pageConfig: {
    id: string;
    name: string;
    components: any[];
    style?: any;
    meta?: any;
  };
  component: any;
  console: Console;
  alert: (message: string) => void;
  axios: AxiosInstance;
  utils: {
    /** 显示Toast消息 */
    showMessage: (msg: string) => void;
    /** 复制文本到剪贴板 */
    copyText: (text: string) => void;
    /** 页面跳转 */
    navigate: (path: string) => void;
    /** 返回上一页 */
    goBack: () => void;
    /** 刷新页面 */
    reload: () => void;
  };
}

// 全局变量声明
declare const context: Context;
declare const params: any;
declare const eventData: any;

// 常用API示例
/**
 * 发送GET请求
 * @example
 * context.axios.get('/api/users').then(response => {
 *   console.log(response.data);
 * });
 */

/**
 * 发送POST请求
 * @example
 * context.axios.post('/api/users', { name: 'John' }).then(response => {
 *   context.utils.showMessage('创建成功');
 * });
 */

/**
 * 第三方API调用
 * @example
 * context.axios({
 *   method: 'POST',
 *   url: 'https://api.example.com/webhook',
 *   headers: { 'Authorization': 'Bearer token' },
 *   data: eventData
 * }).then(response => {
 *   console.log('第三方API响应:', response.data);
 * });
 */
`

  // 添加类型定义
  monaco.languages.typescript.javascriptDefaults.addExtraLib(
    contextTypes,
    'context.d.ts'
  )

  // 配置编译选项
  monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
    target: monaco.languages.typescript.ScriptTarget.ES2020,
    allowNonTsExtensions: true,
    moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
    module: monaco.languages.typescript.ModuleKind.CommonJS,
    noEmit: true,
    esModuleInterop: true,
    jsx: monaco.languages.typescript.JsxEmit.React,
    reactNamespace: 'React',
    allowJs: true,
    typeRoots: ['node_modules/@types']
  })

  // 禁用一些不需要的诊断
  monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
    noSemanticValidation: false,
    noSyntaxValidation: false,
    noSuggestionDiagnostics: true
  })
}
</script>

<style scoped>
.monaco-editor-container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}
</style>
