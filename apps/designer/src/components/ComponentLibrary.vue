<template>
  <div class="component-library" :class="{ 'collapsed': collapsed }">
    <div class="library-header" v-show="!collapsed">
      <div class="header-content">
        <h3 class="header-title">组件库</h3>
        <p class="header-description">拖拽组件到画布开始设计</p>
      </div>
      <div class="search-container">
        <a-input-search
          v-model:value="searchKeyword"
          placeholder="搜索组件..."
          @search="handleSearch"
          @input="handleInput"
          class="search-input"
        />
      </div>
    </div>
    
    <!-- 折叠状态下的简化分类 -->
    <div v-if="collapsed" class="collapsed-categories">
      <a-tooltip
        v-for="category in categories"
        :key="category.id"
        :title="category.name"
        placement="right"
      >
        <div
          class="collapsed-category-item"
          :class="{ 'active': selectedCategory === category.id }"
          @click="selectedCategory = category.id"
        >
          <Icon :icon="category.icon" />
        </div>
      </a-tooltip>
    </div>

    <!-- 正常状态下的分类标签 -->
    <div v-else class="library-categories">
      <a-tabs v-model:activeKey="selectedCategory" @change="handleCategoryChange">
        <a-tab-pane
          v-for="category in categories"
          :key="category.id"
          :tab="category.name"
        >
          <template #tab>
            <Icon :icon="category.icon" />
            {{ category.name }}
          </template>
        </a-tab-pane>
      </a-tabs>
    </div>
    
    <div class="library-components">
      <!-- ✅ 加载状态 -->
      <div v-if="loading" class="loading-state">
        <a-spin size="large" />
        <div class="loading-text">正在加载组件库...</div>
      </div>

      <!-- ✅ 组件列表 -->
      <template v-else>
        <div
          v-for="component in filteredComponents"
          :key="component.id"
          class="component-item"
          :class="{ 'collapsed': collapsed }"
          :draggable="true"
          @dragstart="onDragStart(component, $event)"
          @dragend="onDragEnd"
          @click="addComponentToCanvas(component)"
        >
          <div class="component-icon">
            <Icon :icon="component.icon" />
          </div>
          <div class="component-content">
            <div class="component-name">{{ component.name }}</div>
            <div class="component-description">{{ component.description }}</div>
            <div v-if="component.tags && component.tags.length > 0" class="component-tags">
              <span v-for="tag in component.tags" :key="tag" class="component-tag">
                {{ tag }}
              </span>
            </div>
          </div>
        </div>

        <div v-if="!loading && filteredComponents.length === 0" class="empty-state">
          <Icon icon="mdi:package-variant-closed" class="empty-icon" />
          <div class="empty-text">暂无组件</div>
          <div class="empty-hint">请检查组件是否正确配置元数据</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { Icon } from '@iconify/vue'
import { useComponentLibraryStore } from '../stores/componentLibrary'
import { useDesignerStore } from '../stores/designer'

interface Props {
  collapsed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false
})

const componentLibraryStore = useComponentLibraryStore()
const designerStore = useDesignerStore()

// 响应式数据
const {
  categories,
  selectedCategory,
  searchKeyword,
  filteredComponents,
  loading
} = storeToRefs(componentLibraryStore)

const {
  setSelectedCategory,
  setSearchKeyword,
  createComponentConfig
} = componentLibraryStore

// 处理搜索
function handleSearch(value: string) {
  setSearchKeyword(value)
}

function handleInput(e: Event) {
  const target = e.target as HTMLInputElement
  setSearchKeyword(target.value)
}

// 处理分类变更
function handleCategoryChange(activeKey: string | number) {
  setSelectedCategory(String(activeKey))
}

// 拖拽开始
function onDragStart(component: any, event: DragEvent) {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify(component))
    event.dataTransfer.effectAllowed = 'copy'
  }
  
  designerStore.startDrag(createComponentConfig(component.type))
}

// 拖拽结束
function onDragEnd() {
  designerStore.endDrag()
}

// 添加组件到画布
function addComponentToCanvas(libraryItem: any) {
  const componentConfig = createComponentConfig(libraryItem.type)
  designerStore.addComponent(componentConfig)
}

// ✅ 组件挂载时加载组件库
onMounted(async () => {
  console.log('🎨 ComponentLibrary 组件挂载，开始加载组件库...')
  await componentLibraryStore.loadRegisteredComponents()
})
</script>

<style scoped lang="scss">
.component-library {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
}

.library-header {
  padding: 24px 20px;
  border-bottom: 1px solid #e4e4e7;
  background: #ffffff;

  .header-content {
    margin-bottom: 16px;
  }

  .header-title {
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: 600;
    color: #09090b;
    line-height: 1.2;
  }

  .header-description {
    margin: 0;
    font-size: 14px;
    color: #71717a;
    line-height: 1.4;
  }

  .search-container {
    .search-input {
      :deep(.ant-input) {
        border: 1px solid #e4e4e7;
        border-radius: 6px;
        background: #ffffff;
        font-size: 14px;

        &:focus {
          border-color: #18181b;
          box-shadow: 0 0 0 2px rgba(24, 24, 27, 0.1);
        }

        &::placeholder {
          color: #a1a1aa;
        }
      }

      :deep(.ant-input-search-button) {
        border: 1px solid #e4e4e7;
        border-left: none;
        border-radius: 0 6px 6px 0;
        background: #f4f4f5;
        color: #71717a;

        &:hover {
          background: #e4e4e7;
          color: #18181b;
        }
      }
    }
  }
}

.library-categories {
  border-bottom: 1px solid #e4e4e7;
  background: #ffffff;

  :deep(.ant-tabs-nav) {
    margin: 0;
    padding: 0 20px;
    background: transparent;
  }

  :deep(.ant-tabs-tab) {
    padding: 12px 16px;
    color: #71717a;
    font-weight: 500;
    border: none;

    .anticon {
      margin-right: 6px;
      font-size: 14px;
    }

    &:hover {
      color: #18181b;
    }
  }

  :deep(.ant-tabs-tab-active) {
    color: #18181b;
    background: transparent;
  }

  :deep(.ant-tabs-ink-bar) {
    background: #18181b;
    height: 2px;
  }

  :deep(.ant-tabs-nav-wrap) {
    &::before {
      border: none;
    }
  }
}

.library-components {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.component-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  margin: 0 16px 8px;
  background: #ffffff;
  border: 1px solid #e4e4e7;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    border-color: #d4d4d8;
    background: #f9fafb;
  }

  &:active {
    transform: scale(0.98);
  }
}

.component-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f4f4f5;
  border-radius: 6px;
  margin-right: 12px;
  margin-top: 2px;
  font-size: 18px;
  color: #52525b;
  flex-shrink: 0;
}

.component-content {
  flex: 1;
  min-width: 0;
}

.component-name {
  font-size: 14px;
  font-weight: 500;
  color: #09090b;
  line-height: 1.4;
  margin-bottom: 2px;
}

.component-description {
  font-size: 12px;
  color: #71717a;
  line-height: 1.4;
  margin-bottom: 8px;
}

.component-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.component-tag {
  font-size: 11px;
  color: #52525b;
  background: #f4f4f5;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #e4e4e7;
  font-weight: 500;
}

.component-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  color: #52c41a;

  .badge-icon {
    font-size: 12px;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
}

.empty-hint {
  font-size: 12px;
  color: #ccc;
  margin-top: 4px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.loading-text {
  margin-top: 12px;
  font-size: 14px;
}

// 折叠状态样式
.component-library.collapsed {
  .collapsed-categories {
    padding: 8px 4px;

    .collapsed-category-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      margin-bottom: 8px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      color: #666;

      &:hover {
        background: #f0f0f0;
        color: #1890ff;
      }

      &.active {
        background: #e6f7ff;
        color: #1890ff;
      }

      .iconify {
        font-size: 20px;
      }
    }
  }

  .component-item.collapsed {
    width: 48px;
    height: 48px;
    margin: 4px;
    padding: 8px;

    .component-icon {
      font-size: 24px;
    }

    .component-name {
      display: none;
    }

    .component-description {
      display: none;
    }
  }
}
</style>
