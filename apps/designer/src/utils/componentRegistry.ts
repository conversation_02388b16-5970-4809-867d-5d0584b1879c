// 设计器组件管理器 - 重构后的正确实现

import { ref } from 'vue'
import * as UIComponents from '@lowcode/aslib/ui'
import { registerComponents, hasComponent } from '@lowcode/aslib/core'

// 组件库项目接口
export interface ComponentLibraryItem {
  name: string
  type: string
  component: any
  metadata: any
  category: string
}

export class DesignerComponentManager {
  private static instance: DesignerComponentManager
  private initialized = false

  static getInstance(): DesignerComponentManager {
    if (!this.instance) {
      this.instance = new DesignerComponentManager()
    }
    return this.instance
  }

  // ✅ 正确的初始化逻辑：将业务组件注册到渲染引擎用于预览
  async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      console.log('🔄 初始化设计器组件管理器...')

      // ✅ 将业务组件注册到渲染引擎（用于ComponentRenderer预览）
      registerComponents(UIComponents)

      this.initialized = true
      console.log('✅ 设计器组件管理器初始化完成')
      console.log('📦 可用组件:', Object.keys(UIComponents))

    } catch (error) {
      console.error('❌ 设计器组件管理器初始化失败:', error)
      throw error
    }
  }

  // ✅ 判断是否为Vue组件
  private isVueComponent(component: any): boolean {
    // 检查是否为Vue组件的特征
    return component && (
      // Vue 3 组件特征
      component.__vccOpts ||
      component.render ||
      component.setup ||
      // 有元数据的组件
      component.__lowcodeMetadata ||
      component.__componentMetadata ||
      // 组件名称
      (typeof component === 'object' && component.name && !component.install)
    )
  }

  // ✅ 获取组件库信息（用于组件面板）
  getComponentLibrary(): ComponentLibraryItem[] {
    const components: ComponentLibraryItem[] = []

    Object.entries(UIComponents).forEach(([name, component]) => {
      // 过滤掉非组件的导出（工具函数、版本号等）
      if (!this.isVueComponent(component)) {
        console.log(`⏭️ 跳过非组件导出: ${name}`)
        return
      }

      console.log(`🔍 检查组件 ${name}:`, component)
      const metadata = this.extractMetadata(component)
      console.log(`📋 组件 ${name} 的元数据:`, metadata)

      if (metadata) {
        components.push({
          name,
          type: name,
          component,
          metadata,
          category: metadata.category || 'other'
        })
        console.log(`✅ 添加组件 ${name} 到组件库`)
      } else {
        console.log(`⚠️ 组件 ${name} 没有有效的元数据`)
      }
    })

    console.log(`📦 组件库总计: ${components.length} 个组件`)
    return components
  }

  // ✅ 检查组件是否可用
  isComponentAvailable(type: string): boolean {
    return type in UIComponents && hasComponent(type)
  }

  // ✅ 获取组件元数据
  getComponentMetadata(type: string): any {
    const component = UIComponents[type as keyof typeof UIComponents]
    return component ? this.extractMetadata(component) : null
  }

  // ✅ 获取组件配置模式
  getComponentConfigSchema(type: string): any {
    const component = UIComponents[type as keyof typeof UIComponents] as any
    if (!component) return null

    return component.__configSchema ||
           component.__vccOpts?.__configSchema ||
           component.default?.__configSchema ||
           component.default?.__vccOpts?.__configSchema
  }

  // ✅ 获取组件默认配置
  getComponentDefaultConfig(type: string): any {
    const component = UIComponents[type as keyof typeof UIComponents] as any
    if (!component) return null

    return component.__defaultConfig ||
           component.__vccOpts?.__defaultConfig ||
           component.default?.__defaultConfig ||
           component.default?.__vccOpts?.__defaultConfig
  }

  private extractMetadata(component: any): any {
    console.log('🔍 提取元数据，组件结构:', {
      hasLowcodeMetadata: !!component.__lowcodeMetadata,
      hasComponentMetadata: !!component.__componentMetadata,
      hasVccOpts: !!component.__vccOpts,
      hasVccOptsLowcode: !!component.__vccOpts?.__lowcodeMetadata,
      hasVccOptsComponent: !!component.__vccOpts?.__componentMetadata,
      hasDefault: !!component.default,
      hasDefaultLowcode: !!component.default?.__lowcodeMetadata,
      hasDefaultComponent: !!component.default?.__componentMetadata,
      componentKeys: Object.keys(component),
      componentType: typeof component,
      isFunction: typeof component === 'function'
    })

    // ✅ 尝试多种路径提取元数据（优先使用 __lowcodeMetadata）
    let metadata = null

    // 1. 直接从组件获取 lowcode 元数据
    if (component.__lowcodeMetadata) {
      metadata = component.__lowcodeMetadata
      console.log('📋 从 component.__lowcodeMetadata 获取元数据')
    }
    // 2. 从 __vccOpts 获取 lowcode 元数据（Vue 3 编译后的结构）
    else if (component.__vccOpts?.__lowcodeMetadata) {
      metadata = component.__vccOpts.__lowcodeMetadata
      console.log('📋 从 component.__vccOpts.__lowcodeMetadata 获取元数据')
    }
    // 3. 从 default 获取 lowcode 元数据（ES模块导出）
    else if (component.default?.__lowcodeMetadata) {
      metadata = component.default.__lowcodeMetadata
      console.log('📋 从 component.default.__lowcodeMetadata 获取元数据')
    }
    // 4. 从 default.__vccOpts 获取 lowcode 元数据
    else if (component.default?.__vccOpts?.__lowcodeMetadata) {
      metadata = component.default.__vccOpts.__lowcodeMetadata
      console.log('📋 从 component.default.__vccOpts.__lowcodeMetadata 获取元数据')
    }
    // 5. 兼容旧的 __componentMetadata
    else if (component.__componentMetadata) {
      metadata = component.__componentMetadata
      console.log('📋 从 component.__componentMetadata 获取元数据（兼容模式）')
    }
    // 6. 从 __vccOpts 获取旧元数据
    else if (component.__vccOpts?.__componentMetadata) {
      metadata = component.__vccOpts.__componentMetadata
      console.log('📋 从 component.__vccOpts.__componentMetadata 获取元数据（兼容模式）')
    }

    console.log('📋 最终提取到的元数据:', metadata)
    return metadata
  }
}

// 导出单例实例
export const designerComponentManager = DesignerComponentManager.getInstance()

// ✅ 兼容性函数（保持向后兼容）
export const isComponentsReady = ref(false)

export async function initializeComponents() {
  try {
    await designerComponentManager.initialize()
    isComponentsReady.value = true
  } catch (error) {
    console.error('组件初始化失败:', error)
  }
}

export function checkComponentRegistration(componentType: string): boolean {
  return designerComponentManager.isComponentAvailable(componentType)
}
