// 设计器全局样式

// 变量定义
:root {
  --designer-primary: #1890ff;
  --designer-success: #52c41a;
  --designer-warning: #faad14;
  --designer-error: #ff4d4f;
  --designer-bg: #f0f2f5;
  --designer-border: #e8e8e8;
  --designer-text: #333;
  --designer-text-secondary: #666;
  --designer-text-light: #999;
  --designer-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

// 全局重置
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--designer-bg);
  color: var(--designer-text);
}

// 设计器布局样式
.designer-layout {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--designer-bg);
}

.designer-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.designer-sidebar {
  width: 300px;
  background: #fafafa;
  border-right: 1px solid var(--designer-border);
  overflow-y: auto;
}

.designer-canvas {
  flex: 1;
  background: var(--designer-bg);
  position: relative;
  overflow: auto;
}

.designer-properties {
  width: 320px;
  background: #fafafa;
  border-left: 1px solid var(--designer-border);
  overflow-y: auto;
}

// 拖拽相关样式
.draggable-item {
  cursor: move;
  transition: all 0.2s ease;
  user-select: none;
  
  &:hover {
    background-color: #e6f7ff;
    border-color: var(--designer-primary);
    transform: translateY(-1px);
    box-shadow: var(--designer-shadow);
  }
  
  &.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
  }
}

.drop-zone {
  min-height: 100px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.5);
  
  &.drag-over {
    border-color: var(--designer-primary);
    background-color: rgba(24, 144, 255, 0.1);
    color: var(--designer-primary);
  }
}

// 组件选中和悬停样式
.component-outline {
  outline: 2px dashed var(--designer-primary);
  outline-offset: 2px;
  position: relative;
  
  &::before {
    content: attr(data-component-type);
    position: absolute;
    top: -24px;
    left: 0;
    background: var(--designer-primary);
    color: white;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 2px;
    z-index: 10;
    white-space: nowrap;
  }
}

.component-selected {
  outline: 2px solid var(--designer-success);
  outline-offset: 2px;
  
  &::before {
    background: var(--designer-success);
  }
}

.component-hover {
  outline: 2px dashed var(--designer-primary);
  outline-offset: 2px;
}

// 工具栏样式
.toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #fff;
  border-bottom: 1px solid var(--designer-border);
  
  .toolbar-group {
    display: flex;
    align-items: center;
    gap: 4px;
    
    &:not(:last-child)::after {
      content: '';
      width: 1px;
      height: 20px;
      background: var(--designer-border);
      margin-left: 8px;
    }
  }
}

// 面板样式
.panel {
  background: #fff;
  border-radius: 6px;
  box-shadow: var(--designer-shadow);
  overflow: hidden;
  
  .panel-header {
    padding: 12px 16px;
    background: #fafafa;
    border-bottom: 1px solid var(--designer-border);
    font-weight: 600;
    font-size: 14px;
  }
  
  .panel-content {
    padding: 16px;
  }
}

// 表单样式增强
.form-section {
  margin-bottom: 24px;
  
  .section-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--designer-text);
    border-bottom: 1px solid var(--designer-border);
    padding-bottom: 8px;
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--designer-text-light);
  text-align: center;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  .empty-title {
    font-size: 16px;
    margin-bottom: 8px;
    color: var(--designer-text-secondary);
  }
  
  .empty-description {
    font-size: 14px;
    line-height: 1.5;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .designer-sidebar {
    width: 280px;
  }
  
  .designer-properties {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .designer-content {
    flex-direction: column;
  }
  
  .designer-sidebar,
  .designer-properties {
    width: 100%;
    height: 200px;
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// Ant Design 样式覆盖
.ant-btn {
  border-radius: 4px;
  
  &.ant-btn-sm {
    font-size: 12px;
  }
}

.ant-input,
.ant-select-selector {
  border-radius: 4px;
}

.ant-card {
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.ant-modal {
  .ant-modal-content {
    border-radius: 8px;
  }
}

.ant-tabs {
  .ant-tabs-tab {
    font-size: 12px;
  }
}

.ant-collapse {
  .ant-collapse-header {
    font-size: 13px;
    padding: 8px 12px;
  }
}
