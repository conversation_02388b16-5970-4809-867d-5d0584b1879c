<template>
  <div class="app-detail">
    <!-- 应用头部信息 -->
    <AppHeader
      :app="currentApp"
      @edit-home="editHomePage"
      @preview-app="previewApp"
      @copy-link="copyAppLink"
    />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-row :gutter="24">
        <!-- 左侧：首页管理 -->
        <a-col :span="12">
          <HomePageManager
            :app="currentApp"
            :home-page-config="homePageConfig"
            :publishing="publishing"
            @edit-home="editHomePage"
            @preview-home="previewHomePage"
            @publish-home="publishHomePage"
            @unpublish-home="unpublishHomePage"
          />
        </a-col>

        <!-- 右侧：应用统计 -->
        <a-col :span="12">
          <AppStats
            :total-pages="totalPagesCount"
            :published-pages="publishedPagesCount"
            :tab-bar-enabled="tabBarEnabled"
          />
        </a-col>
      </a-row>
    </div>

    <!-- TabBar配置 -->
    <TabBarManager
      :app="currentApp"
      :loading="loading"
      @edit-tabbar="showTabBarModal = true"
      @preview-tabbar="previewTabBar"
      @toggle-tabbar="toggleTabBar"
    />

    <!-- 可配置页面列表 -->
    <PageList
      :pages="configurablePages"
      @refresh="refreshConfigurablePages"
      @edit-page="editPage"
      @publish-page="publishPage"
      @unpublish-page="unpublishPage"
    />

    <!-- TabBar设置弹窗 -->
    <TabBarModal
      v-model:open="showTabBarModal"
      :tab-bar-data="currentApp?.tabBar"
      :available-routes="allNavigatableRoutes"
      @save="handleSaveTabBar"
      @cancel="handleCancelTabBar"
    />
  </div>
</template>




<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { Icon } from '@iconify/vue'
import { message } from 'ant-design-vue'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue'
import IconPicker from '../components/IconPicker.vue'
import { useAppStore } from '../stores/app'
import { useDesignerStore } from '../stores/designer'
import { getConfigurableRoutes, getPageCategories, getRoutesByCategory as getCoreRoutesByCategory } from '@lowcode/aslib/core'

// 导入子组件
import AppHeader from '../components/AppDetail/AppHeader.vue'
import HomePageManager from '../components/AppDetail/HomePageManager.vue'
import AppStats from '../components/AppDetail/AppStats.vue'
import TabBarManager from '../components/AppDetail/TabBarManager.vue'
import PageList from '../components/AppDetail/PageList.vue'
import TabBarModal from '../components/AppDetail/TabBarModal.vue'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const designerStore = useDesignerStore()

// 响应式数据
const showTabBarModal = ref(false)

// 可配置页面相关
const configurablePages = ref<any[]>([])
const allConfigurablePagesForStats = ref<any[]>([]) // 🔧 新增：包含首页的统计数据
const pageCategories = ref<any[]>([])

// 计算属性
const { currentApp, currentAppPages, loading } = storeToRefs(appStore)
const appId = computed(() => route.params.id as string)

// 获取当前应用类型
const currentAppType = computed(() => currentApp.value?.appType || 'device')

// 计算已发布页面数量（包含首页）
const publishedPagesCount = computed(() => {
  let count = 0
  
  // 统计可配置页面的发布状态
  count += allConfigurablePagesForStats.value.filter(page => page.isPublished).length
  
  // 🔧 修复：单独统计首页的发布状态
  if (homePagePublished.value) {
    // 检查首页是否已经在统计数据中（避免重复计算）
    const homePageInStats = allConfigurablePagesForStats.value.some(page => 
      page.name === 'home' || page.name === 'Home' || page.path === '/home'
    )
    if (!homePageInStats) {
      count += 1
    }
  }
  
  return count
})

// 🔧 新增：总页面数量（包含首页）
const totalPagesCount = computed(() => {
  let count = allConfigurablePagesForStats.value.length
  
  // 🔧 修复：检查首页是否已在统计中，如果没有则添加
  const homePageInStats = allConfigurablePagesForStats.value.some(page => 
    page.name === 'home' || page.name === 'Home' || page.path === '/home'
  )
  
  // 如果首页存在但不在统计数据中，添加到总数
  if (homePageExists.value && !homePageInStats) {
    count += 1
  }
  
  return count
})

// 计算TabBar是否启用
const tabBarEnabled = computed(() => {
  return currentApp.value?.tabBar?.enabled || false
})

// 删除重复的函数，已移到组件中

// TabBar类型描述已移到TabBarModal组件中

// 返回按钮逻辑已移到AppHeader组件中

// 获取所有可导航的页面（简化版，不分组）
const allNavigatableRoutes = computed(() => {
  const allRoutes: any[] = []

  // 直接获取页面分类，不依赖响应式数据
  const categories = getPageCategories()

  // 遍历所有分类，收集所有可导航的页面
  for (const category of categories) {
    const routes = getNavigatableRoutesByCategory(category.id)
    allRoutes.push(...routes)
  }

  // 去重（基于路径）
  const uniqueRoutes = allRoutes.filter((route, index, self) =>
    index === self.findIndex(r => r.path === route.path)
  )

  return uniqueRoutes
})

// 生命周期
onMounted(async () => {
  if (appId.value) {
    await loadAppDetail()
    // 加载可配置页面列表
    loadConfigurablePages()
  }
})

// 方法
async function loadAppDetail() {
  await appStore.getApp(appId.value)

  // 获取应用页面列表（用于TabBar页面选择）
  await appStore.getAppPages(appId.value)

  // 检查应用首页是否存在
  await checkHomePage()

  // TabBar表单初始化已移到TabBarModal组件中
}

// 检查应用首页
async function checkHomePage() {
  if (!appId.value || !currentApp.value) return

  try {
    // 🔧 修复：使用应用特定的首页slug格式查找首页
    const expectedHomeSlug = `${appId.value}_home`
    const homePage = currentApp.value.pageConfigs?.find((page: any) =>
      page.slug === expectedHomeSlug || page.path === '/home'
    )

    console.log('🔍 [AppDetail] 查找首页，期望slug:', expectedHomeSlug)
    console.log('🔍 [AppDetail] 应用页面配置:', currentApp.value.pageConfigs?.map((p: any) => ({ id: p.id, slug: p.slug, path: p.path })))

    if (homePage) {
      homePageConfig.value = homePage
      console.log('✅ 应用首页存在:', homePage)
    } else {
      homePageConfig.value = null
      console.log('ℹ️ 应用首页不存在')
    }
  } catch (error) {
    homePageConfig.value = null
    console.log('ℹ️ 应用首页检查失败:', error)
  }
}

// 首页相关的响应式数据
const homePageConfig = ref<any>(null)
const homePageExists = computed(() => !!homePageConfig.value)
// 🔧 修复：正确处理发布状态（支持数字和布尔值）
const homePagePublished = computed(() => {
  if (!homePageConfig.value) return false
  const published = homePageConfig.value.published
  return published === 1 || published === true
})
const homePageUpdateTime = computed(() => homePageConfig.value?.updateTime)
const publishing = ref(false)

// 编辑首页
function editHomePage() {
  if (!currentApp.value) return

  console.log('🔍 [AppDetail] 编辑首页，应用信息:', {
    appId: appId.value,
    currentAppId: currentApp.value?.id,
    routeParamsId: route.params.id
  })

  // 跳转到设计器，传递应用ID和首页标识，带上来源信息
  const query = {
    appId: appId.value,
    pageType: 'home',
    from: 'app-detail'  // 🔧 新增：标记来源页面
  }
  console.log('🔍 [AppDetail] 跳转到设计器，query:', query)
  router.push({ path: '/designer', query })
}

// 预览应用
function previewApp() {
  // 🎯 新版路由：简化URL，通过参数传递appId
  const url = `http://localhost:3000/#/home?appId=${appId.value}`
  window.open(url, '_blank')
}

// 预览首页
function previewHomePage() {
  if (!homePageExists.value) return
  // 🎯 新版路由：简化URL，通过参数传递appId
  const url = `http://localhost:3000/#/home?appId=${appId.value}`
  window.open(url, '_blank')
}

// 加载可配置页面列表
async function loadConfigurablePages() {
  if (!currentApp.value) {
    console.warn('⚠️ 当前应用不存在，跳过加载可配置页面')
    return
  }

  try {
    console.log('🔄 开始加载可配置页面，应用:', currentApp.value.name, '类型:', currentApp.value.appType)

    // 获取应用类型的可配置页面
    const routes = getConfigurableRoutes(currentApp.value?.appType || 'device')
    console.log('📋 获取到可配置路由:', routes.length, '个')

    // 🔧 修复：不过滤掉首页，让首页也参与统计和管理
    // 过滤掉首页，因为首页有专门的编辑入口
    const filteredRoutes = routes.filter(route =>
      route.name !== 'Home' && route.name !== 'home' && route.path !== '/home'
    )
    console.log('📋 过滤后的路由:', filteredRoutes.length, '个')

    // 🔧 修复：为统计目的，需要包含首页数据
    const allRoutesForStats = routes // 包含首页的完整路由列表
    console.log('📊 统计用路由:', allRoutesForStats.length, '个（含首页）')

    // 🔧 优化：使用已有的应用页面配置，避免额外请求
    const pagesWithStatus = await loadPagesStatusFromApp(filteredRoutes)
    
    // 🔧 修复：为统计创建包含首页的完整数据
    const allPagesWithStatus = await loadPagesStatusFromApp(allRoutesForStats)

    configurablePages.value = pagesWithStatus
    console.log('✅ 可配置页面加载完成:', configurablePages.value.length, '个页面')
    
    // 🔧 修复：为统计保存包含首页的完整数据
    allConfigurablePagesForStats.value = allPagesWithStatus
    console.log('📊 统计页面数据:', allConfigurablePagesForStats.value.length, '个页面（含首页）')

    // 加载页面分类（TabBar页面选择需要）
    pageCategories.value = getPageCategories()
    console.log('📂 页面分类加载完成:', pageCategories.value.length, '个分类')
  } catch (error) {
    console.error('❌ 加载可配置页面失败:', error)
    configurablePages.value = []
    pageCategories.value = []
  }
}

// 🔧 新增：从已加载的应用数据中获取页面状态，避免额外请求
function loadPagesStatusFromApp(routes: any[]) {
  console.log('🔄 从应用数据中获取页面状态')

  // 获取应用已有的页面配置
  const existingPages = currentApp.value?.pageConfigs || []
  console.log('📋 应用现有页面配置:', existingPages.length, '个')

  // 创建页面ID到配置的映射
  const pageConfigMap = new Map()
  existingPages.forEach((page: any) => {
    pageConfigMap.set(page.id, page)
  })

  // 为每个路由匹配对应的页面配置
  const pagesWithStatus = routes.map((route) => {
    const pageId = `${appId.value}_${route.name}`
    const pageConfig = pageConfigMap.get(pageId)

    if (pageConfig) {
      const publishedValue = Number(pageConfig.published || 0)
      return {
        ...route,
        pageId,
        published: publishedValue,
        isPublished: publishedValue > 0,
        updateTime: pageConfig.updateTime
      }
    } else {
      // 页面不存在，未发布
      return {
        ...route,
        pageId,
        published: 0,
        isPublished: false,
        updateTime: null
      }
    }
  })

  console.log('✅ 页面状态匹配完成:', pagesWithStatus.map(p => ({
    name: p.name,
    pageId: p.pageId,
    isPublished: p.isPublished
  })))

  return Promise.resolve(pagesWithStatus)
}



// 刷新可配置页面
function refreshConfigurablePages() {
  loadConfigurablePages()
  message.success('页面列表已刷新')
}

// 编辑页面
function editPage(page: any) {
  // 生成页面ID：appId_pageName
  const pageId = `${appId.value}_${page.name}`

  // 跳转到设计器，带上来源信息
  const query = {
    appId: appId.value,
    pageType: page.name,
    pagePath: page.path,
    from: 'app-detail'  // 🔧 新增：标记来源页面
  }
  router.push({ path: '/designer', query })
}

// 发布页面
async function publishPage(page: any) {
  const pageId = `${appId.value}_${page.name}`

  try {
    // 调用设计器store的发布方法
    const success = await designerStore.publishPageConfig(pageId)

    if (success) {
      message.success(`${page.title} 发布成功`)
      // 🔧 修复：重新加载应用详情和可配置页面列表以获取最新状态
      await loadAppDetail()
      await loadConfigurablePages()
    } else {
      message.error(`${page.title} 发布失败`)
    }
  } catch (error) {
    console.error('发布页面失败:', error)
    message.error(`${page.title} 发布失败`)
  }
}

// 取消发布页面
async function unpublishPage(page: any) {
  try {
    // 🔧 修复：使用专门的取消发布API
    const success = await designerStore.unpublishPageConfig(page.pageId)
    if (success) {
      message.success(`已取消发布: ${page.title}`)
      // 🔧 修复：重新加载应用详情和可配置页面列表以获取最新状态
      await loadAppDetail()
      await loadConfigurablePages()
    } else {
      message.error(`取消发布失败: ${page.title}`)
    }
  } catch (error) {
    console.error('取消发布失败:', error)
    message.error(`取消发布失败: ${page.title}`)
  }
}



// 获取分类名称
function getCategoryName(categoryId: string) {
  const categories = getPageCategories()
  const category = categories.find(c => c.id === categoryId)
  return category?.name || categoryId
}

// 发布首页
async function publishHomePage() {
  if (!homePageConfig.value || !appId.value) return

  try {
    publishing.value = true

    // 构建首页ID
    const homePageId = `${appId.value}_home`
    console.log('🔍 [AppDetail] 发布首页，页面ID:', homePageId)
    console.log('🔍 [AppDetail] 应用信息:', {
      appId: appId.value,
      currentAppId: currentApp.value?.id,
      routeParamsId: route.params.id
    })

    // 调用设计器store的发布方法
    const success = await designerStore.publishPageConfig(homePageId)

    if (success) {
      homePageConfig.value.published = true
      homePageConfig.value.publishTime = new Date().toISOString()
      message.success('首页发布成功')

      // 🔧 修复：重新加载应用详情和可配置页面列表以更新页面状态
      await loadAppDetail()
      await loadConfigurablePages()
    } else {
      message.error('首页发布失败')
    }
  } catch (error) {
    message.error('首页发布失败')
    console.error('发布失败:', error)
  } finally {
    publishing.value = false
  }
}

// 🔧 新增：取消发布首页
async function unpublishHomePage() {
  if (!homePageConfig.value || !appId.value) return

  try {
    publishing.value = true

    // 构建首页ID
    const homePageId = `${appId.value}_home`
    console.log('🔍 [AppDetail] 取消发布首页，页面ID:', homePageId)

    // 🔧 修复：调用设计器store的取消发布方法
    const success = await designerStore.unpublishPageConfig(homePageId)

    if (success) {
      // 更新本地状态
      homePageConfig.value.published = 0  // 使用数字格式保持一致
      homePageConfig.value.publishTime = null
      message.success('首页已取消发布')

      // 🔧 修复：重新加载应用详情和可配置页面列表以更新页面状态
      await loadAppDetail()
      await loadConfigurablePages()
    } else {
      message.error('取消发布首页失败')
    }
  } catch (error) {
    message.error('取消发布首页失败')
    console.error('取消发布失败:', error)
  } finally {
    publishing.value = false
  }
}

// 格式化时间
function formatTime(time: string | Date | undefined): string {
  if (!time) return '未知'

  try {
    const date = new Date(time)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return '格式错误'
  }
}

// 移除旧的页面创建方法

// TabBar标签管理已移到TabBarTabManager组件中

// TabBar页面选择相关方法（从NavigationConfig复制）
function getNavigatableRoutesByCategory(categoryId: string) {
  const routes = getCoreRoutesByCategory(currentAppType.value, categoryId)
  // 只返回可以被跳转选择的路由
  return routes.filter(route => route.navigatable !== false)
}

function getCategoryLabel(category: any) {
  const routes = getNavigatableRoutesByCategory(category.id)
  return `${category.name} (${routes.length})`
}

// 页面选择和图标重置已移到TabBarTabManager组件中

async function handleSaveTabBar(tabBarData: any) {
  try {
    const updates = {
      tabBar: tabBarData
    }

    const success = await appStore.updateApp(appId.value, updates)
    if (success) {
      message.success('TabBar设置保存成功')
      showTabBarModal.value = false
      
      // 🔧 修复：重新加载应用详情以获取最新状态（包括发布状态）
      await loadAppDetail()
      await loadConfigurablePages()
    } else {
      message.error('TabBar设置保存失败')
    }
  } catch (error) {
    message.error('保存失败')
  }
}

function handleCancelTabBar() {
  // 取消操作，表单重置已在TabBarModal组件中处理
  showTabBarModal.value = false
}

// TabBar预览
function previewTabBar() {
  if (!currentApp.value) return

  // 🎯 新版路由：简化URL，通过参数传递appId
  const previewUrl = `http://localhost:3000/#/home?appId=${currentApp.value.id}`
  window.open(previewUrl, '_blank')
  message.info('已在新窗口打开TabBar预览')
}

// 快速启用/禁用TabBar
async function toggleTabBar() {
  if (!currentApp.value) return

  try {
    const newEnabled = !currentApp.value.tabBar?.enabled

    const updates = {
      tabBar: {
        enabled: newEnabled,
        tabs: currentApp.value.tabBar?.tabs || [
          {
            id: 'home-tab',
            name: 'home',
            label: '首页',
            icon: 'mdi:home',
            path: '/home',
            pageId: 'home',
            order: 1
          }
        ],
        style: currentApp.value.tabBar?.style || {
          activeColor: '#1989fa',
          inactiveColor: '#7d7e80',
          backgroundColor: '#ffffff',
          height: '50'
        }
      }
    }

    const success = await appStore.updateApp(appId.value, updates)
    if (success) {
      message.success(`TabBar已${newEnabled ? '启用' : '禁用'}`)
      
      // 🔧 修复：重新加载应用详情以获取最新状态（包括发布状态）
      await loadAppDetail()
      await loadConfigurablePages()
    } else {
      message.error(`TabBar${newEnabled ? '启用' : '禁用'}失败`)
    }
  } catch (error) {
    message.error('操作失败')
  }
}

// 复制应用链接
async function copyAppLink() {
  if (!currentApp.value) return

  // 🎯 新版路由：简化URL，通过参数传递appId
  const url = `http://localhost:3000/#/home?appId=${currentApp.value.id}`
  try {
    await navigator.clipboard.writeText(url)
    message.success('应用链接已复制到剪贴板')
  } catch (error) {
    // 降级方案：创建临时输入框复制
    const textArea = document.createElement('textarea')
    textArea.value = url
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    message.success('应用链接已复制到剪贴板')
  }
}

// 移除页面链接复制功能
</script>

<style scoped lang="scss">
.app-detail {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

// 主要内容区域
.main-content {
  margin-bottom: 24px;
}

// 主要内容区域
.main-content {
  margin-bottom: 24px;
}

// TabBar配置表单样式（仅保留模态框内的样式）
.tab-list {
  max-height: 400px;
  overflow-y: auto;

  .tab-item {
    .ant-card {
      margin-bottom: 8px;

      .ant-card-head {
        padding: 8px 12px;
        min-height: auto;
      }

      .ant-card-body {
        padding: 12px;
      }

      .ant-form-item {
        margin-bottom: 0;

        .ant-form-item-label {
          padding-bottom: 2px;

          label {
            font-size: 11px;
            height: auto;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .app-detail {
    padding: 16px;
  }
}


</style>
