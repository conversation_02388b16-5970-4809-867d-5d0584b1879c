<template>
  <div class="preview-page">
    <!-- 预览工具栏 -->
    <div class="preview-toolbar">
      <a-space>
        <a-button @click="$router.back()">
          <Icon icon="mdi:arrow-left" />
          返回设计器
        </a-button>
        
        <a-divider type="vertical" />
        
        <a-select v-model:value="deviceType" style="width: 120px;">
          <a-select-option value="mobile">手机</a-select-option>
          <a-select-option value="tablet">平板</a-select-option>
          <a-select-option value="desktop">桌面</a-select-option>
        </a-select>
        
        <a-select v-model:value="orientation" style="width: 100px;">
          <a-select-option value="portrait">竖屏</a-select-option>
          <a-select-option value="landscape">横屏</a-select-option>
        </a-select>
        
        <a-button @click="refreshPreview">
          <Icon icon="mdi:refresh" />
          刷新
        </a-button>
      </a-space>
    </div>
    
    <!-- 预览容器 -->
    <div class="preview-container">
      <div 
        class="preview-frame"
        :class="[`device-${deviceType}`, `orientation-${orientation}`]"
      >
        <div class="preview-screen">
          <PageRenderer
            v-if="pageConfig"
            :config="pageConfig"
            :initial-context="mockContext"
            @loaded="onPageLoaded"
            @error="onPageError"
          />
          
          <div v-else-if="loading" class="preview-loading">
            <a-spin size="large" />
            <div style="margin-top: 16px;">加载页面配置中...</div>
          </div>
          
          <div v-else class="preview-error">
            <Icon icon="mdi:alert-circle" style="font-size: 48px; color: #ff4d4f;" />
            <div style="margin-top: 16px;">页面配置加载失败</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { PageRenderer } from '@lowcode/aslib/core'
import type { PageConfig } from '@lowcode/aslib/core'

const props = defineProps<{
  pageId: string
}>()

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const pageConfig = ref<PageConfig | null>(null)
const deviceType = ref('mobile')
const orientation = ref('portrait')

// Mock上下文数据
const mockContext = {
  device: {
    details: {
      id: 1,
      deviceNo: 'TEST001',
      packageName: '基础套餐 10GB',
      vTotalFlow: 10240,
      vUseFlow: 3072,
      vResidueFlow: 7168,
      balance: 25.50,
      status: 3,
      nameStatus: 2,
      becomedueDatetime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      wifiName: 'TestWiFi',
      wifiPwd: '12345678'
    }
  },
  route: {
    path: route.path,
    query: route.query,
    params: route.params
  }
}

// 加载页面配置
async function loadPageConfig() {
  loading.value = true
  
  try {
    // TODO: 从API加载页面配置
    // 现在使用示例配置
    const exampleConfig: PageConfig = {
      id: props.pageId,
      name: '预览页面',
      path: `/preview/${props.pageId}`,
      title: '预览页面',
      layout: {
        type: 'flex',
        direction: 'column',
        padding: 16,
        gap: 16
      },
      components: [
        {
          id: 'device_info_preview',
          type: 'DeviceInfo',
          props: {
            config: {
              networkTitle: '网络连接',
              renewText: '立即续费',
              balanceLabel: '账户余额',
              rechargeText: '充值'
            }
          },
          dataSource: {
            type: 'static',
            params: mockContext.device.details
          },
          visible: true,
          editable: false
        }
      ],
      editable: false
    }
    
    pageConfig.value = exampleConfig
  } catch (error) {
    console.error('Load page config failed:', error)
  } finally {
    loading.value = false
  }
}

// 刷新预览
function refreshPreview() {
  loadPageConfig()
}

// 事件处理
function onPageLoaded(data: any) {
  console.log('Preview page loaded:', data)
}

function onPageError(error: any) {
  console.error('Preview page error:', error)
}

// 初始化
onMounted(() => {
  loadPageConfig()
})

// 监听pageId变化
watch(() => props.pageId, () => {
  loadPageConfig()
})
</script>

<style scoped lang="scss">
.preview-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

.preview-toolbar {
  height: 56px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  overflow: auto;
}

.preview-frame {
  background: #000;
  border-radius: 20px;
  padding: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  
  &.device-mobile {
    &.orientation-portrait {
      width: 375px;
      height: 812px;
    }
    
    &.orientation-landscape {
      width: 812px;
      height: 375px;
    }
  }
  
  &.device-tablet {
    &.orientation-portrait {
      width: 768px;
      height: 1024px;
    }
    
    &.orientation-landscape {
      width: 1024px;
      height: 768px;
    }
  }
  
  &.device-desktop {
    width: 1200px;
    height: 800px;
    border-radius: 8px;
  }
}

.preview-screen {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.preview-loading,
.preview-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #666;
}
</style>
