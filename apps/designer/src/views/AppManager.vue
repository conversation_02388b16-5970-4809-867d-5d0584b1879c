<template>
  <div class="app-manager">
    <div class="app-manager-header">
      <h2>应用管理</h2>
      <div class="header-actions">
        <a-space>
          <div class="debug-info">
            <span>状态: {{ loading ? '加载中' : '已加载' }}</span>
            <span>应用: {{ apps.length }}个</span>
            <a-button @click="testApi" size="small">测试API</a-button>
            <a-button @click="loadApps" size="small">重新加载</a-button>
          </div>
          <a-button type="primary" @click="showCreateModal = true">
            <Icon icon="mdi:plus" />
            创建应用
          </a-button>
        </a-space>
      </div>
    </div>

    <div class="app-list">
      <a-spin :spinning="loading">
        <div class="app-grid">
          <div
            v-for="app in apps"
            :key="app.id"
            class="app-card"
            @click="openApp(app)"
          >
            <!-- 应用图标和状态 -->
            <div class="app-card-header">
              <div class="app-icon-wrapper">
                <Icon :icon="app.icon" class="app-icon" />
              </div>
              <div class="app-status">
                <span
                  class="status-dot"
                  :class="{ 'status-published': app.published, 'status-draft': !app.published }"
                ></span>
                <span class="status-text">
                  {{ app.published ? '已发布' : '草稿' }}
                </span>
              </div>
            </div>

            <!-- 应用信息 -->
            <div class="app-card-content">
              <h3 class="app-name">{{ app.name }}</h3>
              <p class="app-description">{{ app.description }}</p>
            </div>

            <!-- 操作按钮 -->
            <div class="app-card-actions">
              <a-button
                type="text"
                size="small"
                @click.stop="editApp(app)"
                title="编辑应用"
              >
                <Icon icon="mdi:pencil" />
              </a-button>
              <a-button
                type="text"
                size="small"
                @click.stop="previewApp(app)"
                title="预览应用"
              >
                <Icon icon="mdi:eye" />
              </a-button>
              <a-button
                type="text"
                size="small"
                @click.stop="copyAppLink(app)"
                title="复制链接"
              >
                <Icon icon="mdi:link" />
              </a-button>
              <a-dropdown trigger="click" @click.stop>
                <a-button type="text" size="small" title="更多操作">
                  <Icon icon="mdi:dots-horizontal" />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="toggleAppPublish(app)">
                      <Icon icon="mdi:publish" />
                      {{ app.published ? '取消发布' : '发布应用' }}
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="deleteApp(app)" class="danger-item">
                      <Icon icon="mdi:delete" />
                      删除应用
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </div>

          <!-- 创建新应用卡片 -->
          <div class="app-card app-card-create" @click="showCreateModal = true">
            <div class="create-content">
              <div class="create-icon">
                <Icon icon="mdi:plus" />
              </div>
              <div class="create-text">创建新应用</div>
            </div>
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 创建/编辑应用模态框 -->
    <a-modal
      v-model:open="showCreateModal"
      :title="editingApp ? '编辑应用' : '创建应用'"
      width="600px"
      @ok="handleSaveApp"
      @cancel="handleCancelEdit"
    >
      <a-form
        ref="formRef"
        :model="appForm"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="应用类型" name="appType" v-if="!editingApp">
          <a-select
            v-model:value="appForm.appType"
            placeholder="请选择应用类型"
            @change="handleAppTypeChange"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            :show-search="false"
            option-label-prop="label"
          >
            <!-- 选项列表 -->
            <a-select-option
              v-for="type in enabledApplicationTypes"
              :key="type.id"
              :value="type.id"
              :label="type.name"
            >
              <div class="app-type-option">
                <Icon :icon="type.icon" :style="{ color: type.color }" class="option-icon" />
                <div class="option-content">
                  <div class="option-name">{{ type.name }}</div>
                  <div class="option-desc">{{ type.description }}</div>
                </div>
              </div>
            </a-select-option>
          </a-select>

          <!-- 显示当前选中的应用类型详细信息 -->
          <div v-if="selectedApplicationType" class="selected-app-type">
            <div class="selected-info">
              <a-tag :color="selectedApplicationType.color" class="selected-type-tag">
                <Icon :icon="selectedApplicationType.icon" class="mr-1" />
                {{ selectedApplicationType.name }}
              </a-tag>
              <span class="selected-desc">{{ selectedApplicationType.description }}</span>
            </div>
            <div class="selected-features" v-if="selectedApplicationType.features && selectedApplicationType.features.length > 0">
              <span class="features-label">支持功能：</span>
              <a-tag
                v-for="feature in selectedApplicationType.features"
                :key="feature"
                size="small"
                class="feature-tag"
              >
                {{ getFeatureDisplayName(feature) }}
              </a-tag>
            </div>
          </div>
        </a-form-item>

        <a-form-item label="应用ID" name="id" v-if="!editingApp">
          <a-input
            v-model:value="appForm.id"
            placeholder="请输入应用ID（唯一标识）"
          >
            <template #suffix>
              <a-tooltip title="点击生成UUID">
                <a-button
                  type="text"
                  size="small"
                  @click="generateAppId"
                  style="padding: 0 4px;"
                >
                  <Icon icon="mdi:dice-6" />
                </a-button>
              </a-tooltip>
            </template>
          </a-input>

          <!-- 应用ID帮助信息 -->
          <div class="app-id-help mt-2">
            <div class="help-row">
              <Icon icon="mdi:information" class="help-icon" />
              <span class="text-sm text-gray-600">
                应用ID用于唯一标识应用，建议格式：<code>device-an</code>、<code>mall-shop</code>、<code>crm-system</code>
              </span>
            </div>
            <div class="help-row mt-1">
              <Icon icon="mdi:check-circle" class="help-icon success" />
              <span class="text-xs text-gray-500">
                支持字母、数字、连字符，建议使用小写字母和连字符分隔
              </span>
            </div>
          </div>
        </a-form-item>

        <a-form-item label="应用名称" name="name">
          <a-input v-model:value="appForm.name" placeholder="请输入应用名称" />
        </a-form-item>

        <a-form-item label="应用描述" name="description">
          <a-textarea v-model:value="appForm.description" placeholder="请输入应用描述" :rows="3" />
        </a-form-item>
        
        <a-form-item label="应用图标" name="icon">
          <IconPicker v-model="appForm.icon" placeholder="选择应用图标" />
        </a-form-item>
        
        <a-form-item label="默认首页" name="defaultHomePage">
          <a-input v-model:value="appForm.defaultHomePage" placeholder="请输入默认首页页面ID" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { Icon } from '@iconify/vue'
import { message } from 'ant-design-vue'
import IconPicker from '../components/IconPicker.vue'
import { useAppStore } from '../stores/app'
import type { AppConfig } from '../utils/api'
import { appApi } from '../utils/api'
import { getEnabledApplicationTypes, getApplicationType } from '@lowcode/aslib/core'

const router = useRouter()
const appStore = useAppStore()

// 响应式数据
const showCreateModal = ref(false)
const editingApp = ref<AppConfig | null>(null)
const formRef = ref()

const appForm = ref({
  appType: '',
  id: '',
  name: '',
  description: '',
  icon: 'mdi:application',
  defaultHomePage: 'home'
})

const formRules = {
  appType: [{ required: true, message: '请选择应用类型' }],
  id: [{ required: true, message: '请输入应用ID' }],
  name: [{ required: true, message: '请输入应用名称' }],
  description: [{ required: true, message: '请输入应用描述' }],
  icon: [{ required: true, message: '请输入应用图标' }],
  defaultHomePage: [{ required: true, message: '请输入默认首页' }]
}

// 计算属性
const { apps, loading } = storeToRefs(appStore)

// 获取启用的应用类型
const enabledApplicationTypes = computed(() => {
  return getEnabledApplicationTypes()
})

// 获取当前选中的应用类型信息
const selectedApplicationType = computed(() => {
  if (!appForm.value.appType) return null
  return getApplicationType(appForm.value.appType)
})

// 生命周期
onMounted(() => {
  loadApps()
})

// 方法
async function loadApps() {
  console.log('🔄 AppManager: 开始加载应用列表')
  const success = await appStore.getAppList()
  console.log('📊 AppManager: 加载结果:', success, '应用数量:', apps.value.length)
}

// 处理应用类型变化
function handleAppTypeChange(value: any) {
  const appType = typeof value === 'string' ? value : String(value)

  const applicationTypeConfig = getApplicationType(appType)
  if (applicationTypeConfig) {
    // 自动填充应用信息
    appForm.value.name = applicationTypeConfig.name
    appForm.value.description = applicationTypeConfig.description
    appForm.value.icon = applicationTypeConfig.icon
    appForm.value.defaultHomePage = applicationTypeConfig.defaultPage

    // 自动生成建议的应用ID
    generateAppId()
  }
}

// 生成应用ID建议
function generateAppId() {
  // 🎯 使用UUID生成唯一ID
  const uuid = crypto.randomUUID()

  // 生成短UUID（取前8位）+ 应用类型前缀
  const shortUuid = uuid.split('-')[0]
  const appType = appForm.value.appType

  if (appType) {
    appForm.value.id = `${appType}-${shortUuid}`
  } else {
    appForm.value.id = `app-${shortUuid}`
  }

  console.log('🎯 生成新的应用ID:', appForm.value.id)
}

// 获取功能特性的显示名称
function getFeatureDisplayName(feature: string): string {
  const featureNames: Record<string, string> = {
    'device-management': '设备管理',
    'package-purchase': '套餐购买',
    'balance-recharge': '余额充值',
    'real-name-auth': '实名认证',
    'product-catalog': '商品目录',
    'shopping-cart': '购物车',
    'order-management': '订单管理',
    'payment': '支付功能',
    'customer-management': '客户管理',
    'sales-tracking': '销售跟踪'
  }

  return featureNames[feature] || feature
}

async function testApi() {
  console.log('🧪 测试API调用')
  try {
    // 使用配置好的API实例，会自动携带token
    const result = await appApi.getList()
    console.log('✅ API调用成功:', result)
  } catch (error) {
    console.error('❌ API调用失败:', error)
  }
}

function openApp(app: AppConfig) {
  router.push(`/app/${app.id}`)
}

function editApp(app: AppConfig) {
  editingApp.value = app
  appForm.value = {
    appType: app.appType || '',
    id: app.id,
    name: app.name,
    description: app.description,
    icon: app.icon,
    defaultHomePage: app.defaultHomePage
  }
  showCreateModal.value = true
}

function previewApp(app: AppConfig) {
  // 🎯 新版路由：简化URL，通过参数传递appId
  const url = `http://localhost:3000/#/home?appId=${app.id}`
  window.open(url, '_blank')
}

async function copyAppLink(app: AppConfig) {
  // 🎯 新版路由：简化URL，通过参数传递appId
  const url = `http://localhost:3000/#/home?appId=${app.id}`
  try {
    await navigator.clipboard.writeText(url)
    message.success('应用链接已复制到剪贴板')
  } catch (error) {
    // 降级方案：创建临时输入框复制
    const textArea = document.createElement('textarea')
    textArea.value = url
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    try {
      const successful = document.execCommand('copy')
      if (successful) {
        message.success('应用链接已复制到剪贴板')
      } else {
        message.error('复制失败，请手动复制')
      }
    } catch (err) {
      console.error('复制失败:', err)
      message.error('复制失败，请手动复制')
    } finally {
      document.body.removeChild(textArea)
    }
  }
}

// 🔧 修复：智能的发布/取消发布切换方法
async function toggleAppPublish(app: AppConfig) {
  let success = false

  // 🎯 关键修复：使用实时的应用状态，而不是传入的app参数
  const currentAppState = appStore.apps.find(a => a.id === app.id)
  const isCurrentlyPublished = currentAppState ? currentAppState.published : app.published

  console.log('🔍 应用发布状态检查:', {
    appId: app.id,
    paramPublished: app.published,
    storePublished: currentAppState?.published,
    finalState: isCurrentlyPublished
  })

  if (isCurrentlyPublished) {
    // 当前已发布，执行取消发布
    console.log('🔄 取消发布应用:', app.id)
    success = await appStore.unpublishApp(app.id)
    if (success) {
      message.success('应用取消发布成功')
      // 🎯 强制更新本地状态，确保UI立即响应
      app.published = false
    } else {
      message.error('应用取消发布失败')
    }
  } else {
    // 当前未发布，执行发布
    console.log('🔄 发布应用:', app.id)
    success = await appStore.publishApp(app.id)
    if (success) {
      message.success('应用发布成功')
      // 🎯 强制更新本地状态，确保UI立即响应
      app.published = true
    } else {
      message.error('应用发布失败')
    }
  }

  // 🎯 额外保障：操作成功后重新加载应用列表
  if (success) {
    console.log('✅ 发布状态切换成功，重新加载应用列表')
    await appStore.getAppList()
  }
}

async function deleteApp(app: AppConfig) {
  const success = await appStore.deleteApp(app.id)
  if (success) {
    message.success('应用删除成功')
  } else {
    message.error('应用删除失败')
  }
}

async function handleSaveApp() {
  try {
    await formRef.value.validate()
    
    let success = false
    if (editingApp.value) {
      // 编辑应用 - 🔧 修复：过滤掉id字段，避免服务端验证错误
      const { id, createTime, updateTime, ...updateData } = appForm.value
      success = await appStore.updateApp(editingApp.value.id, updateData)
    } else {
      // 创建应用
      success = await appStore.createApp(appForm.value)
    }
    
    if (success) {
      message.success(editingApp.value ? '应用更新成功' : '应用创建成功')
      showCreateModal.value = false
      handleCancelEdit()
    } else {
      message.error(editingApp.value ? '应用更新失败' : '应用创建失败')
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

function handleCancelEdit() {
  editingApp.value = null
  appForm.value = {
    appType: '',
    id: '',
    name: '',
    description: '',
    icon: 'mdi:application',
    defaultHomePage: 'home'
  }
  formRef.value?.resetFields()
}
</script>

<style scoped lang="scss">
.app-manager {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.app-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;

  h2 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: #1a202c;
  }
}

.debug-info {
  padding: 8px 12px;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 12px;
  color: #4a5568;

  span {
    margin-right: 12px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.app-list {
  min-height: 400px;
}

.app-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.app-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  padding: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
    border-color: #cbd5e0;
  }

  &.app-card-create {
    border: 2px dashed #cbd5e0;
    background: #f7fafc;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;

    &:hover {
      border-color: #4299e1;
      background: #ebf8ff;
      transform: translateY(-2px);
    }
  }
}

.app-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.app-icon-wrapper {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 16px;
    background: linear-gradient(135deg, #667eea20, #764ba220);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .app-card:hover &::before {
    opacity: 1;
  }
}

.app-icon {
  font-size: 32px;
  color: #4a5568;
  z-index: 1;
  position: relative;
}

.app-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;

  &.status-published {
    background: #48bb78;
  }

  &.status-draft {
    background: #ed8936;
  }
}

.status-text {
  font-size: 12px;
  color: #718096;
  font-weight: 500;
}

.app-card-content {
  margin-bottom: 20px;
}

.app-name {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.app-description {
  font-size: 14px;
  color: #718096;
  margin: 0;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 应用类型选择相关样式 */
.app-type-option {
  display: flex;
  align-items: center;
  padding: 8px 0;
  min-height: 48px;
}

.option-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  flex-shrink: 0;
}

.option-content {
  flex: 1;
  min-width: 0; /* 防止内容溢出 */
}

.option-name {
  font-weight: 500;
  color: #262626;
  line-height: 1.4;
  margin-bottom: 2px;
}

.option-desc {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.3;
}

/* 修复Select组件的样式问题 */
:deep(.ant-select-selector) {
  padding-left: 11px !important;
}

:deep(.ant-select-selection-item) {
  padding-left: 0 !important;
  margin-left: 0 !important;
}

/* 下拉选项样式优化 */
:deep(.ant-select-item-option-content) {
  padding: 0;
}

:deep(.ant-select-item-option) {
  padding: 4px 12px;
}

:deep(.ant-select-item-option:hover) {
  background-color: #f5f5f5;
}

:deep(.ant-select-item-option-selected) {
  background-color: #e6f7ff;
  font-weight: 500;
}

.selected-app-type {
  margin-top: 12px;
  padding: 12px;
  background: #f6f8fa;
  border-radius: 8px;
  border: 1px solid #e1e4e8;
}

.selected-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.selected-type-tag {
  margin: 0;
  margin-right: 12px;
  display: flex;
  align-items: center;
  font-weight: 500;
}

.selected-desc {
  color: #586069;
  font-size: 14px;
}

.selected-features {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
}

.features-label {
  font-size: 12px;
  color: #6a737d;
  margin-right: 4px;
}

.feature-tag {
  margin: 0;
  font-size: 11px;
}

/* 应用ID帮助信息样式 */
.app-id-help {
  padding: 8px 12px;
  background: #f6f8fa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.help-row {
  display: flex;
  align-items: center;
  gap: 6px;
}

.help-icon {
  width: 14px;
  height: 14px;
  color: #1890ff;

  &.success {
    color: #52c41a;
  }
}

code {
  background: #f1f3f4;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #d73a49;
}

.app-card-actions {
  display: flex;
  gap: 4px;
  padding-top: 16px;
  border-top: 1px solid #f1f5f9;

  .ant-btn {
    color: #718096;

    &:hover {
      color: #4299e1;
      background: #ebf8ff;
    }
  }
}

.create-content {
  text-align: center;
  color: #718096;
}

.create-icon {
  font-size: 48px;
  margin-bottom: 12px;
  color: #cbd5e0;
  transition: color 0.2s ease;

  .app-card-create:hover & {
    color: #4299e1;
  }
}

.create-text {
  font-size: 16px;
  font-weight: 500;
  transition: color 0.2s ease;

  .app-card-create:hover & {
    color: #4299e1;
  }
}

// 下拉菜单样式
:deep(.ant-dropdown-menu) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  .ant-dropdown-menu-item {
    padding: 8px 12px;

    &.danger-item {
      color: #e53e3e;

      &:hover {
        background: #fed7d7;
        color: #c53030;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-grid {
    grid-template-columns: 1fr;
  }

  .app-manager {
    padding: 16px;
  }

  .app-manager-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    h2 {
      font-size: 24px;
    }
  }
}
</style>
