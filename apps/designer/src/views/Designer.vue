<template>
  <div class="designer-layout">
    <!-- 顶部工具栏 -->
    <DesignerHeader />

    <!-- 主要内容区域 -->
    <div class="designer-content">
      <!-- 左侧组件库 -->
      <div
        class="designer-sidebar"
        :class="{ 'collapsed': sidebarCollapsed }"
        v-show="!isMobile || showSidebar"
      >
        <div class="sidebar-header">
          <h3 v-show="!sidebarCollapsed">组件库</h3>
          <a-button
            type="text"
            size="small"
            @click="toggleSidebar"
            class="collapse-btn"
          >
            <Icon :icon="sidebarCollapsed ? 'mdi:chevron-right' : 'mdi:chevron-left'" />
          </a-button>
        </div>
        <ComponentLibrary :collapsed="sidebarCollapsed" />
      </div>

      <!-- 中间画布区域 -->
      <div class="designer-canvas">
        <!-- 移动端工具栏 -->
        <div v-if="isMobile" class="mobile-toolbar">
          <a-button @click="showSidebar = !showSidebar" size="small">
            <Icon icon="mdi:view-list" />
            组件库
          </a-button>
          <a-button @click="showProperties = !showProperties" size="small">
            <Icon icon="mdi:cog" />
            属性
          </a-button>
        </div>

        <DesignerCanvas />
      </div>

      <!-- 右侧属性面板 -->
      <div
        class="designer-properties"
        :class="{ 'collapsed': propertiesCollapsed }"
        v-show="!isMobile || showProperties"
      >
        <div class="properties-header">
          <h3 v-show="!propertiesCollapsed">属性设置</h3>
          <a-button
            type="text"
            size="small"
            @click="toggleProperties"
            class="collapse-btn"
          >
            <Icon :icon="propertiesCollapsed ? 'mdi:chevron-left' : 'mdi:chevron-right'" />
          </a-button>
        </div>
        <PropertiesPanel :collapsed="propertiesCollapsed" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, provide, computed } from 'vue'
import { Icon } from '@iconify/vue'
import DesignerHeader from '../components/DesignerHeader.vue'
import ComponentLibrary from '../components/ComponentLibrary.vue'
import DesignerCanvas from '../components/DesignerCanvas.vue'
import PropertiesPanel from '../components/PropertiesPanel.vue'
import { useDesignerStore } from '../stores/designer'
import { useAppStore } from '../stores/app'

const props = defineProps<{
  pageId?: string
}>()

const designerStore = useDesignerStore()
const appStore = useAppStore()

// 响应式状态
const sidebarCollapsed = ref(false)
const propertiesCollapsed = ref(false)
const isMobile = ref(false)
const showSidebar = ref(false)
const showProperties = ref(false)

// 计算当前应用类型
const currentAppType = computed(() => {
  // 从当前应用信息中获取应用类型，默认为 device
  return appStore.currentApp?.appType || 'device'
})

// 提供应用类型给子组件
provide('currentAppType', currentAppType)

// 响应式布局检测
function checkScreenSize() {
  const width = window.innerWidth
  isMobile.value = width < 768

  // 在小屏幕上自动折叠侧边栏
  if (width < 1200) {
    sidebarCollapsed.value = true
    propertiesCollapsed.value = true
  } else {
    sidebarCollapsed.value = false
    propertiesCollapsed.value = false
  }
}

// 切换侧边栏
function toggleSidebar() {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 切换属性面板
function toggleProperties() {
  propertiesCollapsed.value = !propertiesCollapsed.value
}

// 初始化
onMounted(() => {
  // 检查屏幕尺寸
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)

  // 从 URL 参数获取 appId 和 pageType
  const urlParams = new URLSearchParams(window.location.search)
  const appId = urlParams.get('appId')
  const pageType = urlParams.get('pageType')

  console.log('🔍 [Designer] URL参数:', { appId, pageType })
  console.log('🔍 [Designer] 完整URL:', window.location.href)

  if (appId && pageType) {
    // 应用页面模式 - 使用应用特定的页面ID格式
    if (pageType === 'home') {
      // 应用首页模式 - 使用 appId_home 格式
      const homePageId = `${appId}_home`
      loadOrCreateHomePage(homePageId, appId)
    } else {
      // 其他可配置页面模式 - 使用 appId_pageType 格式
      const pageId = `${appId}_${pageType}`
      loadOrCreateConfigurablePage(pageId, appId, pageType)
    }
  } else if (props.pageId) {
    // 加载指定页面
    loadPage(props.pageId)
  } else {
    // 创建新页面
    designerStore.createNewPage('新页面', '/new-page')
  }
})

// 加载或创建应用首页
async function loadOrCreateHomePage(homePageId: string, appId: string) {
  console.log('🔄 设计器加载应用首页:', homePageId, appId)

  try {
    const success = await designerStore.loadPageConfig(homePageId)

    if (success && designerStore.currentPage) {
      console.log('✅ 应用首页加载成功')
      // 加载应用信息
      await appStore.getApp(appId)
    } else {
      console.warn('⚠️ 应用首页不存在，创建新的首页')
      // 创建新的应用首页
      const newHomePage = {
        appId: appId,
        slug: homePageId, // 现在homePageId已经是 appId_home 格式
        name: '应用首页',
        path: '/home',
        title: '首页',
        layout: {
          type: 'flex' as const,
          direction: 'column' as const,
          padding: 16,
          gap: 16
        },
        style: {
          // 🔧 修复：新应用首页默认无背景色
        },
        components: [
          // 🔧 修复：新应用首页默认无组件，让用户自由设计
        ],
        published: 0
      }

      designerStore.setCurrentPage(newHomePage)
      // 加载应用信息
      await appStore.getApp(appId)
    }
  } catch (error) {
    console.error('❌ 加载应用首页失败:', error)
  }
}

// 加载或创建可配置页面
async function loadOrCreateConfigurablePage(pageId: string, appId: string, pageType: string) {
  console.log('🔄 设计器加载可配置页面:', pageId, appId, pageType)

  try {
    const success = await designerStore.loadPageConfig(pageId)

    if (success && designerStore.currentPage) {
      console.log('✅ 可配置页面加载成功')
      // 加载应用信息
      await appStore.getApp(appId)
    } else {
      console.warn('⚠️ 可配置页面不存在，创建新页面')
      // 创建新的可配置页面
      const newPage = {
        id: pageId,
        name: `${pageType}页面`,
        path: `/${pageType}`,
        title: `${pageType}页面`,
        appId: appId,
        slug: pageId, // 现在pageId已经是 appId_pageType 格式
        pageType: pageType,
        layout: {
          type: 'flex' as const,
          direction: 'column' as const,
          padding: 16,
          gap: 16
        },
        components: [],
        published: 0
      }

      designerStore.setCurrentPage(newPage)
      // 加载应用信息
      await appStore.getApp(appId)
    }
  } catch (error) {
    console.error('❌ 加载可配置页面失败:', error)
  }

// 加载页面
async function loadPage(pageId: string) {
  console.log('🔄 设计器加载页面:', pageId)

  try {
    const success = await designerStore.loadPageConfig(pageId)

    if (success && designerStore.currentPage) {
      // 如果页面加载成功且有appId，加载应用信息
      const appId = (designerStore.currentPage as any).appId
      if (appId) {
        console.log('🔄 加载页面所属应用:', appId)
        await appStore.getApp(appId)
      }
    } else {
      console.warn('⚠️ 页面不存在，创建新页面')
      // 如果页面不存在，创建一个新页面
      const newPage = {
        id: pageId,
        name: `页面 ${pageId}`,
        path: `/${pageId}`,
        title: `页面 ${pageId}`,
        layout: {
          type: 'flex' as const,
          direction: 'column' as const,
          padding: 16,
          gap: 16
        },
        components: [],
        editable: true
      }
      designerStore.setCurrentPage(newPage)
    }
  } catch (error) {
    console.error('❌ 加载页面失败:', error)
  }
}
}

// 键盘快捷键
onMounted(() => {
  const handleKeydown = (e: KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'z':
          e.preventDefault()
          if (e.shiftKey) {
            designerStore.redo()
          } else {
            designerStore.undo()
          }
          break
        case 's':
          e.preventDefault()
          // 保存页面配置
          handleSave()
          break
      }
    }
    
    if (e.key === 'Delete' && designerStore.selectedComponentId) {
      designerStore.removeComponent(designerStore.selectedComponentId)
    }
  }

  // 保存页面配置
  async function handleSave() {
    try {
      const success = await designerStore.savePageConfig()
      if (success) {
        console.log('✅ 页面保存成功')
        // 可以添加成功提示
      } else {
        console.error('❌ 页面保存失败')
        // 可以添加失败提示
      }
    } catch (error) {
      console.error('❌ 保存过程中出错:', error)
    }
  }

  document.addEventListener('keydown', handleKeydown)
})

// 清理事件监听器
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  window.removeEventListener('resize', checkScreenSize)
})
</script>

<style scoped lang="scss">
.designer-layout {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fafafa;
}

.designer-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.designer-sidebar {
  width: 320px;
  background: #ffffff;
  border-right: 1px solid #e4e4e7;
  overflow-y: auto;
  transition: width 0.3s ease;
  position: relative;

  &.collapsed {
    width: 60px;
  }

  .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;

    h3 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #262626;
    }

    .collapse-btn {
      padding: 4px;

      &:hover {
        background: #f0f0f0;
      }
    }
  }

  // 简洁的滚动条
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #d4d4d8;
    border-radius: 2px;

    &:hover {
      background: #a1a1aa;
    }
  }
}

.designer-canvas {
  flex: 1;
  background: #fafafa;
  position: relative;
  overflow: auto;
  display: flex;
  flex-direction: column;

  .mobile-toolbar {
    display: none;
    padding: 8px 16px;
    background: #ffffff;
    border-bottom: 1px solid #e4e4e7;
    gap: 8px;

    @media (max-width: 768px) {
      display: flex;
    }

    .ant-btn {
      border-radius: 6px;
      font-size: 12px;
    }
  }
}

.designer-properties {
  width: 400px;
  background: #ffffff;
  border-left: 1px solid #e4e4e7;
  overflow-y: auto;
  transition: width 0.3s ease;
  position: relative;

  &.collapsed {
    width: 60px;
  }

  .properties-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;

    h3 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #262626;
    }

    .collapse-btn {
      padding: 4px;

      &:hover {
        background: #f0f0f0;
      }
    }
  }

  // 简洁的滚动条
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #d4d4d8;
    border-radius: 2px;

    &:hover {
      background: #a1a1aa;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .designer-sidebar {
    width: 280px;

    &.collapsed {
      width: 50px;
    }
  }

  .designer-properties {
    width: 350px;

    &.collapsed {
      width: 50px;
    }
  }
}

@media (max-width: 768px) {
  .designer-content {
    position: relative;
  }

  .designer-sidebar,
  .designer-properties {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 100;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);

    &.collapsed {
      width: 0;
      overflow: hidden;
    }
  }

  .designer-sidebar {
    left: 0;
    width: 280px;
  }

  .designer-properties {
    right: 0;
    width: 320px;
  }

  .designer-canvas {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .designer-sidebar,
  .designer-properties {
    width: 100vw;
  }
}
</style>
