# PC设计器环境变量配置示例

# ==================== API配置 ====================

# 低代码平台API地址
VITE_LOWCODE_API_URL=http://localhost:3002

# Device-An API地址（用于预览）
VITE_DEVICE_AN_API_URL=https://api.device-an.com

# ==================== 安全配置 ====================

# 认证密钥（生产环境必须设置）
VITE_AUTH_SECRET=your-secret-key-here

# ==================== 应用配置 ====================

# 应用标题
VITE_APP_TITLE=低代码设计器

# 应用版本
VITE_APP_VERSION=1.0.0

# 应用环境
VITE_APP_ENV=development

# ==================== 设计器配置 ====================

# 默认画布宽度
VITE_CANVAS_WIDTH=375

# 默认画布高度
VITE_CANVAS_HEIGHT=667

# 是否启用网格
VITE_ENABLE_GRID=true

# 是否启用标尺
VITE_ENABLE_RULER=true

# ==================== 功能开关 ====================

# 是否启用调试模式
VITE_DEBUG_MODE=true

# 是否启用组件热重载
VITE_ENABLE_HOT_RELOAD=true

# 是否启用代码编辑器
VITE_ENABLE_CODE_EDITOR=true

# ==================== 开发配置 ====================

# 开发服务器端口
VITE_DEV_PORT=3001

# 是否启用HTTPS
VITE_HTTPS=false

# 代理配置
VITE_PROXY_TARGET=http://localhost:3002
