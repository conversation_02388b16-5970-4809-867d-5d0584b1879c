#!/usr/bin/env node

/**
 * 一次性迁移脚本
 * 将项目中所有的 @lowcode/core、@lowcode/ui、@lowcode/hooks 引用
 * 替换为 @lowcode/aslib 的对应引用
 */

const fs = require('fs')
const path = require('path')

// 需要替换的包映射
const packageMappings = {
  '@lowcode/aslib/core': '@lowcode/aslib/core',
  '@lowcode/aslib/ui': '@lowcode/aslib/ui',
  '@lowcode/aslib/hooks': '@lowcode/aslib/hooks'
}

// 需要处理的文件扩展名
const fileExtensions = ['.ts', '.vue', '.js', '.tsx', '.jsx']

// 需要排除的目录
const excludeDirs = ['node_modules', 'dist', '.git', 'packages/core', 'packages/ui', 'packages/hooks']

/**
 * 递归查找所有需要处理的文件
 */
function findFiles(dir, files = []) {
  const items = fs.readdirSync(dir)
  
  for (const item of items) {
    const fullPath = path.join(dir, item)
    const stat = fs.statSync(fullPath)
    
    if (stat.isDirectory()) {
      // 跳过排除的目录
      if (!excludeDirs.some(excludeDir => fullPath.includes(excludeDir))) {
        findFiles(fullPath, files)
      }
    } else if (stat.isFile()) {
      // 检查文件扩展名
      const ext = path.extname(fullPath)
      if (fileExtensions.includes(ext)) {
        files.push(fullPath)
      }
    }
  }
  
  return files
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8')
    let hasChanges = false
    
    // 替换导入语句
    for (const [oldPackage, newPackage] of Object.entries(packageMappings)) {
      const regex = new RegExp(`(['"\`])${oldPackage.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(['"\`])`, 'g')
      if (regex.test(content)) {
        content = content.replace(regex, `$1${newPackage}$2`)
        hasChanges = true
      }
    }
    
    // 如果有变更，写回文件
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8')
      console.log(`✅ 已更新: ${filePath}`)
      return true
    }
    
    return false
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message)
    return false
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始一次性迁移到 @lowcode/aslib...\n')
  
  const startTime = Date.now()
  const projectRoot = path.resolve(__dirname, '..')
  
  // 查找所有需要处理的文件
  console.log('📁 扫描文件...')
  const files = findFiles(projectRoot)
  console.log(`找到 ${files.length} 个文件需要检查\n`)
  
  // 处理文件
  let updatedCount = 0
  let totalChecked = 0
  
  for (const file of files) {
    totalChecked++
    if (processFile(file)) {
      updatedCount++
    }
  }
  
  const endTime = Date.now()
  const duration = ((endTime - startTime) / 1000).toFixed(2)
  
  console.log('\n📊 迁移完成统计:')
  console.log(`- 检查文件数: ${totalChecked}`)
  console.log(`- 更新文件数: ${updatedCount}`)
  console.log(`- 耗时: ${duration}s`)
  
  if (updatedCount > 0) {
    console.log('\n🎉 迁移完成！请运行以下命令验证:')
    console.log('pnpm install')
    console.log('pnpm build:packages')
    console.log('pnpm dev:h5')
  } else {
    console.log('\n✨ 没有找到需要更新的文件')
  }
}

// 运行脚本
if (require.main === module) {
  main()
}

module.exports = { processFile, findFiles, packageMappings }
