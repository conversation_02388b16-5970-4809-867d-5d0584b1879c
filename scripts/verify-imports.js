#!/usr/bin/env node

/**
 * 验证导入脚本
 * 检查所有 @lowcode/aslib 的导入是否能正确解析
 */

const fs = require('fs')
const path = require('path')

// 需要验证的导入模式
const importPatterns = [
  /@lowcode\/aslib\/core/g,
  /@lowcode\/aslib\/ui/g,
  /@lowcode\/aslib\/hooks/g,
  /@lowcode\/aslib(?!\/)/g  // 匹配 @lowcode/aslib 但不包含子路径
]

// 需要处理的文件扩展名
const fileExtensions = ['.ts', '.vue', '.js', '.tsx', '.jsx']

// 需要排除的目录
const excludeDirs = ['node_modules', 'dist', '.git']

/**
 * 递归查找所有需要处理的文件
 */
function findFiles(dir, files = []) {
  const items = fs.readdirSync(dir)
  
  for (const item of items) {
    const fullPath = path.join(dir, item)
    const stat = fs.statSync(fullPath)
    
    if (stat.isDirectory()) {
      // 跳过排除的目录
      if (!excludeDirs.some(excludeDir => fullPath.includes(excludeDir))) {
        findFiles(fullPath, files)
      }
    } else if (stat.isFile()) {
      // 检查文件扩展名
      const ext = path.extname(fullPath)
      if (fileExtensions.includes(ext)) {
        files.push(fullPath)
      }
    }
  }
  
  return files
}

/**
 * 检查单个文件的导入
 */
function checkFileImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const imports = []
    
    // 检查每种导入模式
    for (const pattern of importPatterns) {
      const matches = content.match(pattern)
      if (matches) {
        imports.push(...matches)
      }
    }
    
    return imports.length > 0 ? { file: filePath, imports: [...new Set(imports)] } : null
  } catch (error) {
    console.error(`❌ 读取文件失败 ${filePath}:`, error.message)
    return null
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔍 开始验证 @lowcode/aslib 导入...\n')
  
  const startTime = Date.now()
  const projectRoot = path.resolve(__dirname, '..')
  
  // 查找所有需要处理的文件
  console.log('📁 扫描文件...')
  const files = findFiles(projectRoot)
  console.log(`找到 ${files.length} 个文件需要检查\n`)
  
  // 检查导入
  const filesWithImports = []
  let totalImports = 0
  
  for (const file of files) {
    const result = checkFileImports(file)
    if (result) {
      filesWithImports.push(result)
      totalImports += result.imports.length
    }
  }
  
  const endTime = Date.now()
  const duration = ((endTime - startTime) / 1000).toFixed(2)
  
  // 输出结果
  console.log('📊 验证结果:')
  console.log(`- 检查文件数: ${files.length}`)
  console.log(`- 包含导入的文件数: ${filesWithImports.length}`)
  console.log(`- 总导入数: ${totalImports}`)
  console.log(`- 耗时: ${duration}s\n`)
  
  if (filesWithImports.length > 0) {
    console.log('📋 包含 @lowcode/aslib 导入的文件:')
    filesWithImports.forEach(({ file, imports }) => {
      const relativePath = path.relative(projectRoot, file)
      console.log(`  ${relativePath}`)
      imports.forEach(imp => {
        console.log(`    - ${imp}`)
      })
    })
    
    console.log('\n✅ 所有导入都已正确配置！')
    console.log('🎯 可以使用以下方式导入:')
    console.log('  import { ComponentRenderer } from "@lowcode/aslib/core"')
    console.log('  import { HomeBasic, setAPIClient } from "@lowcode/aslib/ui"')
    console.log('  import { useGlobalData } from "@lowcode/aslib/hooks"')
  } else {
    console.log('ℹ️ 没有找到 @lowcode/aslib 的导入')
  }
}

// 运行脚本
if (require.main === module) {
  main()
}

module.exports = { checkFileImports, findFiles }
