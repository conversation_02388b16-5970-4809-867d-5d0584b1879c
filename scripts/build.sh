#!/bin/bash

# 低代码平台构建脚本

echo "🔨 构建低代码平台..."

# 构建组件库
echo "📦 构建组件库..."
cd packages/ui && pnpm build && cd ../..

# 构建核心包
echo "⚙️ 构建核心包..."
cd packages/core && pnpm build && cd ../..

# 构建H5应用
echo "📱 构建H5应用..."
cd apps/h5 && pnpm build && cd ../..

# 构建设计器
echo "🎨 构建设计器..."
cd apps/designer && pnpm build && cd ../..

# 构建API服务
echo "🔌 构建API服务..."
cd apps/api && pnpm build && cd ../..

echo "✅ 构建完成！"
echo ""
echo "📁 构建产物："
echo "  - packages/ui/dist - 组件库"
echo "  - packages/core/dist - 核心包"
echo "  - apps/h5/dist - H5应用"
echo "  - apps/designer/dist - 设计器"
echo "  - apps/api/dist - API服务"
