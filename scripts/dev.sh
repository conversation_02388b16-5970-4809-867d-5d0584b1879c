#!/bin/bash

# 低代码平台开发启动脚本

echo "🚀 启动低代码平台开发环境..."

# 检查pnpm是否安装
if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm 未安装，请先安装 pnpm"
    echo "npm install -g pnpm"
    exit 1
fi

# 安装依赖
echo "📦 安装依赖..."
pnpm install

# 构建组件库
echo "🔨 构建组件库..."
cd packages/ui && pnpm build && cd ../..
cd packages/core && pnpm build && cd ../..

# 启动服务
echo "🌟 启动所有服务..."

# 使用 concurrently 同时启动多个服务
npx concurrently \
  --names "API,H5,Designer" \
  --prefix-colors "blue,green,yellow" \
  "cd apps/api && pnpm dev" \
  "cd apps/h5 && pnpm dev" \
  "cd apps/designer && pnpm dev"

echo "✅ 所有服务已启动！"
echo ""
echo "📱 H5移动端: http://localhost:3000"
echo "🎨 可视化设计器: http://localhost:3001" 
echo "🔌 API服务: http://localhost:3002"
echo ""
echo "按 Ctrl+C 停止所有服务"
