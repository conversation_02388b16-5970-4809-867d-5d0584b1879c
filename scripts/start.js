#!/usr/bin/env node

const { spawn } = require('child_process')
const readline = require('readline')

// 检测包管理器
function getPackageManager() {
  try {
    require.resolve('pnpm/package.json')
    return 'pnpm'
  } catch {
    return 'npm'
  }
}

const pm = getPackageManager()

const apps = [
  {
    name: '@lowcode/api - API服务',
    command: `${pm} run dev:api`,
    description: '启动API服务 (端口3002)'
  },
  {
    name: '@lowcode/h5 - H5移动端',
    command: `${pm} run dev:h5`,
    description: '启动H5移动端应用 (端口3000)'
  },
  {
    name: '@lowcode/designer - 可视化设计器',
    command: `${pm} run dev:designer`,
    description: '启动桌面端设计器 (端口3001)'
  },
  {
    name: '全部应用',
    command: `${pm} run dev:all`,
    description: '同时启动所有应用'
  }
]

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

console.log('\n🚀 低代码平台启动器\n')
console.log('请选择要启动的应用:\n')

apps.forEach((app, index) => {
  console.log(`${index + 1}. ${app.name}`)
  console.log(`   ${app.description}\n`)
})

rl.question('请输入选项 (1-4): ', (answer) => {
  const choice = parseInt(answer) - 1
  
  if (choice >= 0 && choice < apps.length) {
    const selectedApp = apps[choice]
    console.log(`\n启动: ${selectedApp.name}`)
    console.log(`命令: ${selectedApp.command}\n`)
    
    const [cmd, ...args] = selectedApp.command.split(' ')
    const child = spawn(cmd, args, {
      stdio: 'inherit',
      shell: true
    })
    
    child.on('error', (error) => {
      console.error('启动失败:', error)
      process.exit(1)
    })
    
    child.on('exit', (code) => {
      process.exit(code)
    })
    
    // 处理Ctrl+C
    process.on('SIGINT', () => {
      child.kill('SIGINT')
      process.exit(0)
    })
    
  } else {
    console.log('无效选择，请重新运行脚本')
    process.exit(1)
  }
  
  rl.close()
})
