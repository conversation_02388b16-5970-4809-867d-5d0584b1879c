{"name": "lowcode-platform", "version": "1.2.0", "private": true, "description": "Modern low-code platform with intelligent routing and modular services", "scripts": {"start": "node scripts/start.js", "dev": "node scripts/start.js", "dev:api": "turbo run dev --filter=@lowcode/api", "dev:server": "turbo run dev --filter=@lowcode/server", "dev:h5": "turbo run dev --filter=@lowcode/h5", "dev:designer": "turbo run dev --filter=@lowcode/designer", "dev:all": "concurrently \"npm run dev:server\" \"npm run dev:h5\" \"npm run dev:designer\"", "build": "turbo run build", "build:packages": "turbo run build --filter=@lowcode/aslib", "lint": "turbo run lint", "clean": "turbo run clean", "type-check": "turbo run type-check", "check": "node scripts/check-config.js"}, "devDependencies": {"@changesets/cli": "^2.27.1", "concurrently": "^8.2.2", "turbo": "^1.13.2", "typescript": "^5.4.0"}, "packageManager": "pnpm@8.15.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}