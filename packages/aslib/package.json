{"name": "@lowcode/aslib", "version": "1.2.0", "description": "安生低代码平台统一库 - 包含core、ui、hooks三个模块", "type": "module", "main": "dist/index.umd.js", "module": "dist/index.es.js", "types": "src/index.ts", "exports": {".": {"development": "./src/index.ts", "import": "./dist/index.es.js", "require": "./dist/index.umd.js", "types": "./src/index.ts"}, "./core": {"development": "./src/core/index.ts", "import": "./src/core/index.ts", "require": "./src/core/index.ts", "types": "./src/core/index.ts"}, "./ui": {"development": "./src/ui/index.ts", "import": "./src/ui/index.ts", "require": "./src/ui/index.ts", "types": "./src/ui/index.ts"}, "./hooks": {"development": "./src/hooks/index.ts", "import": "./src/hooks/index.ts", "require": "./src/hooks/index.ts", "types": "./src/hooks/index.ts"}}, "files": ["dist", "src", "README.md"], "scripts": {"dev": "vite build --watch", "build": "vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["lowcode", "vue", "typescript", "ui-components", "rendering-engine", "hooks", "composables"], "author": "安生低代码平台开发团队", "license": "MIT", "peerDependencies": {"vue": "^3.4.0"}, "dependencies": {"vue": "^3.4.21", "vant": "^4.9.16", "@iconify/vue": "^4.1.1", "@iconify/json": "^2.2.170", "axios": "^1.10.0", "dayjs": "^1.11.13", "js-md5": "^0.8.3", "qrcode": "^1.5.4"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "@types/node": "^22.10.7", "typescript": "^5.7.3", "vite": "^5.2.8", "vue-tsc": "^2.0.11", "sass": "^1.77.4", "terser": "^5.0.0", "rimraf": "^5.0.0"}}