import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'AsLib',
      formats: ['es', 'umd'],
      fileName: (format) => `index.${format}.js`
    },
    
    rollupOptions: {
      external: ['vue', 'vant'],
      output: {
        globals: {
          vue: 'Vue',
          vant: 'Vant'
        }
      }
    },
    
    sourcemap: true,
    emptyOutDir: true,
    target: 'es2015',
    
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@core': resolve(__dirname, 'src/core'),
      '@ui': resolve(__dirname, 'src/ui'),
      '@hooks': resolve(__dirname, 'src/hooks'),
      // 添加自引用别名，解决开发环境导入问题
      '@lowcode/aslib': resolve(__dirname, 'src'),
      '@lowcode/aslib/core': resolve(__dirname, 'src/core'),
      '@lowcode/aslib/ui': resolve(__dirname, 'src/ui'),
      '@lowcode/aslib/hooks': resolve(__dirname, 'src/hooks')
    }
  },
  
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/ui/styles/variables.scss";`
      }
    }
  }
})
