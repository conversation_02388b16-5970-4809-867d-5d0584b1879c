/**
 * 组件管理器
 * 统一管理组件的注册、发现和配置
 */

import type { App } from 'vue'

// 简单的类型定义，替代不存在的 ComponentRegistry
export interface LowcodeComponent {
  name: string
  type: string
  config?: any
  [key: string]: any
}

/**
 * 组件注册信息
 */
export interface ComponentRegistration {
  /** 组件实例 */
  component: any
  /** 组件配置 */
  config: LowcodeComponent
  /** 注册时间 */
  registeredAt: Date
  /** 是否已安装到Vue应用 */
  installed: boolean
}

/**
 * 组件管理器类
 */
export class ComponentManager {
  private static instance: ComponentManager
  private registrations = new Map<string, ComponentRegistration>()
  private vueApp: App | null = null

  static getInstance(): ComponentManager {
    if (!ComponentManager.instance) {
      ComponentManager.instance = new ComponentManager()
    }
    return ComponentManager.instance
  }

  /**
   * 设置Vue应用实例
   */
  setVueApp(app: App): void {
    this.vueApp = app
    console.log('📱 Vue应用实例已设置到组件管理器')
  }

  /**
   * 注册单个组件
   */
  registerComponent(
    component: any, 
    config: LowcodeComponent,
    options: { autoInstall?: boolean } = {}
  ): void {
    const { autoInstall = true } = options
    
    // 注册到低代码组件注册表 (暂时注释掉，因为 ComponentRegistry 不存在)
    // registerLowcodeComponent({
    //   component,
    //   metadata: config
    // })

    // 记录注册信息
    const registration: ComponentRegistration = {
      component,
      config,
      registeredAt: new Date(),
      installed: false
    }

    this.registrations.set(config.name, registration)

    // 自动安装到Vue应用
    if (autoInstall && this.vueApp) {
      this.installComponent(config.name)
    }

    console.log(`✅ 组件 ${config.name} 注册成功`)
  }

  /**
   * 批量注册组件
   */
  registerComponents(
    components: Array<{
      component: any
      config: LowcodeComponent
    }>,
    options: { autoInstall?: boolean } = {}
  ): void {
    console.log(`📦 开始批量注册 ${components.length} 个组件...`)
    
    components.forEach(({ component, config }) => {
      this.registerComponent(component, config, { autoInstall: false })
    })

    // 批量安装
    if (options.autoInstall !== false && this.vueApp) {
      this.installAllComponents()
    }

    console.log(`✅ 批量注册完成，共注册 ${components.length} 个组件`)
  }

  /**
   * 安装组件到Vue应用
   */
  installComponent(componentName: string): boolean {
    if (!this.vueApp) {
      console.warn('⚠️ Vue应用实例未设置，无法安装组件')
      return false
    }

    const registration = this.registrations.get(componentName)
    if (!registration) {
      console.error(`❌ 组件 ${componentName} 未注册`)
      return false
    }

    if (registration.installed) {
      console.log(`ℹ️ 组件 ${componentName} 已安装`)
      return true
    }

    try {
      this.vueApp.component(componentName, registration.component)
      registration.installed = true
      console.log(`🔧 组件 ${componentName} 安装成功`)
      return true
    } catch (error) {
      console.error(`❌ 组件 ${componentName} 安装失败:`, error)
      return false
    }
  }

  /**
   * 安装所有已注册的组件
   */
  installAllComponents(): void {
    if (!this.vueApp) {
      console.warn('⚠️ Vue应用实例未设置，无法安装组件')
      return
    }

    let installedCount = 0
    for (const [name, registration] of this.registrations) {
      if (!registration.installed) {
        if (this.installComponent(name)) {
          installedCount++
        }
      }
    }

    console.log(`🔧 批量安装完成，共安装 ${installedCount} 个组件`)
  }

  /**
   * 获取组件注册信息
   */
  getComponentRegistration(componentName: string): ComponentRegistration | undefined {
    return this.registrations.get(componentName)
  }

  /**
   * 获取所有注册的组件
   */
  getAllRegistrations(): Map<string, ComponentRegistration> {
    return new Map(this.registrations)
  }

  /**
   * 按分类获取组件
   */
  getComponentsByCategory(_category: string): ComponentRegistration[] {
    // 暂时返回空数组，因为 getComponentsByCategory 函数不存在
    // const components = getComponentsByCategory(category)
    // return components.map(comp => this.registrations.get(comp.name)).filter(Boolean) as ComponentRegistration[]
    return []
  }

  /**
   * 搜索组件
   */
  searchComponents(query: string): ComponentRegistration[] {
    const results: ComponentRegistration[] = []
    const lowerQuery = query.toLowerCase()

    for (const registration of this.registrations.values()) {
      const config = registration.config
      const searchText = [
        config.name,
        config.displayName,
        config.description,
        ...(config.tags || [])
      ].join(' ').toLowerCase()

      if (searchText.includes(lowerQuery)) {
        results.push(registration)
      }
    }

    return results
  }

  /**
   * 获取组件统计信息
   */
  getStats(): {
    total: number
    installed: number
    byCategory: Record<string, number>
  } {
    const stats = {
      total: this.registrations.size,
      installed: 0,
      byCategory: {} as Record<string, number>
    }

    for (const registration of this.registrations.values()) {
      if (registration.installed) {
        stats.installed++
      }

      const category = registration.config.category
      stats.byCategory[category] = (stats.byCategory[category] || 0) + 1
    }

    return stats
  }

  /**
   * 验证组件配置
   */
  validateComponent(config: LowcodeComponent): {
    valid: boolean
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []

    // 必需字段检查
    if (!config.name) errors.push('组件名称不能为空')
    if (!config.displayName) errors.push('组件显示名称不能为空')
    if (!config.category) errors.push('组件分类不能为空')

    // 命名规范检查
    if (config.name && !/^[A-Z][a-zA-Z0-9]*$/.test(config.name)) {
      errors.push('组件名称必须以大写字母开头，只能包含字母和数字')
    }

    // 版本格式检查
    if (config.version && !/^\d+\.\d+\.\d+$/.test(config.version)) {
      warnings.push('建议使用语义化版本号格式 (x.y.z)')
    }

    // 图标检查
    if (!config.icon) {
      warnings.push('建议设置组件图标')
    }

    // 描述检查
    if (!config.description) {
      warnings.push('建议添加组件描述')
    }

    // 事件定义检查
    if (!config.events || config.events.length === 0) {
      warnings.push('建议定义组件事件')
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * 重置管理器（主要用于测试）
   */
  reset(): void {
    this.registrations.clear()
    this.vueApp = null
    console.log('🔄 组件管理器已重置')
  }
}

// ==================== 导出 ====================

/**
 * 全局组件管理器实例
 */
export const componentManager = ComponentManager.getInstance()

/**
 * 便捷函数：注册单个组件
 */
export function registerComponent(
  component: any, 
  config: LowcodeComponent,
  options?: { autoInstall?: boolean }
): void {
  componentManager.registerComponent(component, config, options)
}

/**
 * 便捷函数：批量注册组件
 */
export function registerComponents(
  components: Array<{
    component: any
    config: LowcodeComponent
  }>,
  options?: { autoInstall?: boolean }
): void {
  componentManager.registerComponents(components, options)
}

/**
 * 便捷函数：设置Vue应用
 */
export function setVueApp(app: App): void {
  componentManager.setVueApp(app)
}

/**
 * 便捷函数：获取组件统计
 */
export function getComponentStats(): ReturnType<ComponentManager['getStats']> {
  return componentManager.getStats()
}

/**
 * 便捷函数：搜索组件
 */
export function searchComponents(query: string): ComponentRegistration[] {
  return componentManager.searchComponents(query)
}
