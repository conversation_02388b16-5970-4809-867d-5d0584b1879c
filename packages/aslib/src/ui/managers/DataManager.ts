// 数据管理器 - 支持环境区分

import { getAPIClient } from '../api'

// 环境类型
export type Environment = 'designer' | 'runtime'

// 数据管理器配置
interface DataManagerConfig {
  environment: Environment
  mockData?: Record<string, any>
}

// 全局配置
let globalConfig: DataManagerConfig = {
  environment: 'runtime'
}

// 设置环境配置
export function setDataManagerConfig(config: DataManagerConfig) {
  globalConfig = { ...globalConfig, ...config }
  console.log(`📊 数据管理器环境设置为: ${config.environment}`)
}

// 获取当前环境
export function getCurrentEnvironment(): Environment {
  return globalConfig.environment
}

// 数据管理器类
export class DataManager {
  private environment: Environment

  constructor(config?: Partial<DataManagerConfig>) {
    this.environment = config?.environment || globalConfig.environment
  }

  // 获取套餐列表
  async getPackageList(_params?: any): Promise<any[]> {
    if (this.environment === 'designer') {
      // PC设计器环境：返回模拟数据
      console.log('🎨 [设计器] 使用模拟套餐数据')
      return [
        {
          id: 1,
          name: '基础套餐',
          packagePrice: 30,
          packageTotal: 5120, // 5GB
          popular: false,
          validityDays: 30,
          packageIntroduce: '适合轻度使用用户，包含基础流量和通话'
        },
        {
          id: 2,
          name: '标准套餐',
          packagePrice: 60,
          packageTotal: 15360, // 15GB
          popular: true,
          validityDays: 30,
          packageIntroduce: '最受欢迎的套餐，流量充足，性价比高'
        },
        {
          id: 3,
          name: '高级套餐',
          packagePrice: 120,
          packageTotal: 51200, // 50GB
          popular: false,
          validityDays: 30,
          packageIntroduce: '大流量套餐，适合重度用户和商务人士'
        },
        {
          id: 4,
          name: '企业套餐',
          packagePrice: 300,
          packageTotal: 102400, // 100GB
          popular: false,
          validityDays: 30,
          packageIntroduce: '企业级大流量套餐，支持多设备共享'
        }
      ]
    } else {
      // H5运行时环境：调用API获取套餐数据
      console.log('📱 [运行时] 调用API获取套餐数据')
      try {
        const apiClient = getAPIClient()
        if (apiClient && typeof apiClient.getPackageList === 'function') {
          const response = await apiClient.getPackageList(_params)

          // 检查响应格式并正确解析数据
          if (response && response.code && response.data) {
            return Array.isArray(response.data) ? response.data : []
          } else if (response && Array.isArray(response)) {
            return response
          } else {
            console.warn('⚠️ 套餐数据API返回格式异常:', response)
            return []
          }
        } else {
          console.warn('⚠️ API客户端未配置或不支持getPackageList方法')
          return []
        }
      } catch (error) {
        console.error('❌ 获取套餐数据失败:', error)
        return []
      }
    }
  }

  // 获取支付方式列表 - 简化实现
  async getPaymentMethods(): Promise<any[]> {
    if (this.environment === 'designer') {
      // PC设计器环境：返回模拟数据
      console.log('🎨 [设计器] 使用模拟支付方式数据')
      return [
        { type: 'balance', name: '余额支付', available: true, balance: 150.50 },
        { type: 'wechat', name: '微信支付', available: true },
        { type: 'alipay', name: '支付宝', available: true },
        { type: 'unionpay', name: '银联支付', available: false }
      ]
    } else {
      // H5运行时环境：暂时返回空数组，由具体业务实现
      console.log('📱 [运行时] 支付方式获取功能待实现')
      return []
    }
  }

  // 获取设备信息 - 使用新API架构
  async getDeviceInfo(): Promise<any> {
    if (this.environment === 'designer') {
      // PC设计器环境：返回模拟数据
      console.log('🎨 [设计器] 使用模拟设备数据')
      return {
        deviceNo: 'DEMO-DEVICE-001',
        balance: 88.51,
        status: 1, // 1:在线
        vResidueFlow: 8500, // 剩余流量 MB
        vTotalFlow: 15360, // 总流量 MB (15GB)
        vUseFlow: 6860, // 已用流量 MB
        currentNetwork: 3, // 当前网络 3:移动（与新的映射一致）
        packageName: '标准套餐',
        currentSignal: '4',
        networkStatus: 'connected',
        signalStrength: 4
      }
    } else {
      // H5运行时环境：调用新的API架构
      console.log('📱 [运行时] 调用API获取设备信息')
      try {
        // 调用API获取设备信息
        const apiClient = getAPIClient()
        if (apiClient && typeof apiClient.getDeviceInfo === 'function') {
          const response = await apiClient.getDeviceInfo()

          // 检查响应格式并正确解析数据
          if (response && response.code && response.data) {
            return response.data
          } else if (response && typeof response === 'object') {
            return response
          } else {
            console.warn('⚠️ 设备信息API返回格式异常:', response)
            return {}
          }
        } else {
          console.warn('⚠️ API客户端未配置或不支持getDeviceInfo方法')
          return {}
        }
      } catch (error) {
        console.error('❌ 获取设备信息失败:', error)
        return {}
      }
    }
  }

  // 获取设备卡片信息
  async getDeviceCards(): Promise<any[]> {
    if (this.environment === 'designer') {
      // PC设计器环境：返回模拟数据（模拟实际API返回的2张卡）
      console.log('🎨 [设计器] 使用模拟设备卡片数据')
      return [
        {
          operator: 3,
          iccid: '89860000000000000001',
          msisdn: '13800000001',
          name: '中国移动',
          status: 'active'
        },
        {
          operator: 1,
          iccid: '89860000000000000002',
          msisdn: '13800000002',
          name: '中国电信',
          status: 'inactive'
        }
      ]
    } else {
      // H5运行时环境：调用API获取设备卡片
      console.log('📱 [运行时] 调用API获取设备卡片')
      try {
        const apiClient = getAPIClient()
        if (apiClient && typeof apiClient.getDeviceCards === 'function') {
          const response = await apiClient.getDeviceCards()
          console.log('✅ 设备卡片获取成功:', response)
          return response.data || response || []
        } else {
          console.warn('⚠️ API客户端未配置或不支持getDeviceCards方法')
          return []
        }
      } catch (error) {
        console.error('❌ 获取设备卡片失败:', error)
        return []
      }
    }
  }

  // 创建支付订单
  async createPayment(params: any): Promise<any> {
    if (this.environment === 'designer') {
      // PC设计器环境：返回模拟结果
      console.log('🎨 [设计器] 模拟创建支付订单')
      return {
        orderId: `DEMO-ORDER-${Date.now()}`,
        payUrl: 'https://demo-pay-url.com',
        status: 'pending'
      }
    } else {
      // H5运行时环境：调用真实API
      console.log('📱 [运行时] 调用真实API创建支付')
      try {
        const apiClient = getAPIClient()
        const response = await apiClient.createPayment(params)
        return response.data || {}
      } catch (error) {
        console.error('❌ 创建支付失败:', error)
        throw error
      }
    }
  }
}

// 创建全局数据管理器实例
export const dataManager = new DataManager()

// 便捷函数
export function createDataManager(environment: Environment): DataManager {
  return new DataManager({ environment })
}
