<!-- ✅ 完全复制device-an的dxTag组件 -->
<script setup lang="ts">
const props = defineProps({
  class: {
    type: String,
    required: true
  },
  txt: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    required: false
  },
  round: {
    type: Boolean,
    required: false,
    default: true
  }
})

import { Icon } from '@iconify/vue'
</script>

<template>
  <div class="dxTag" :class="{ ['dxTag-' + props.class]: true, 'dxTag-round': props.round }">
    <div class="dxTag-box">
      <Icon v-if="props.icon" :icon="`mdi:${props.icon}`" style="margin-right: 0.3rem" />{{ props.txt }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* ✅ 完全复制device-an的dxTag样式 */
@import '../../../styles/variables.scss';

.dxTag {
  display: inline-block;
  height: 1rem;
  line-height: 1rem;
  padding: 0 0.5rem;
  font-size: 0.6rem;
  border-radius: 0.2rem;

  &-box {
    display: flex;
    justify-content: start;
    align-items: center;
  }
}

.dxTag-round {
  border-radius: 0.5rem;
}

.dxTag-success {
  background-color: rgba($color: $success, $alpha: 0.1);
  color: $success;
}

.dxTag-warning {
  background-color: rgba($color: $warning, $alpha: 0.1);
  color: $warning;
}

.dxTag-primary {
  background-color: rgba($color: $primary, $alpha: 0.1);
  color: $primary;
}

.dxTag-error {
  background-color: rgba($color: $error, $alpha: 0.1);
  color: $error;
}

.dxTag-info {
  background-color: rgba(127, 127, 213, 0.1);
  color: rgb(127, 127, 213);
}

.dxTag-wait {
  background-color: rgba(100, 116, 139, 0.1);
  color: #999;
}

.dxTag + .dxTag {
  margin-left: 0.12rem;
}
</style>
