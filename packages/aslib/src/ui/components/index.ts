/**
 * 组件库统一导出
 */

// ==================== Home组件 ====================
export { default as HomeBasic, HomeBasicMetadata, HomeBasicDefaultConfig, HomeBasicConfigSchema } from './Home/HomeBasic'
export { default as HomeMore, HomeMoreMetadata, HomeMoreDefaultConfig, HomeMoreConfigSchema } from './Home/HomeMore'
export { default as HomeDetails, HomeDetailsMetadata, HomeDetailsDefaultConfig, HomeDetailsConfigSchema } from './Home/HomeDetails'
export { default as HomeNetWork, HomeNetWorkMetadata, HomeNetWorkDefaultConfig, HomeNetWorkConfigSchema } from './Home/HomeNetWork'
export { default as HomeRealName, HomeRealNameMetadata, HomeRealNameDefaultConfig, HomeRealNameConfigSchema } from './Home/HomeRealName'

// ==================== Business组件 ====================

// ==================== Common组件 ====================
export { default as DxTag } from './Common/DxTag'
export { default as SvgIcon } from './Common/SvgIcon'



// ==================== 类型导出 ====================
// 暂时注释掉不存在的类型导出
// export type { HomeBasicConfig, HomeBasicData, HomeBasicEvents } from './Home/HomeBasic'
// export type { HomeMoreConfig, HomeMoreData, HomeMoreEvents } from './Home/HomeMore'

// ==================== 组件安装函数 ====================
import type { App } from 'vue'

// 导入所有组件
import HomeBasic from './Home/HomeBasic'
import HomeMore from './Home/HomeMore'
import HomeDetails from './Home/HomeDetails'
import HomeNetWork from './Home/HomeNetWork'
import HomeRealName from './Home/HomeRealName'
import DxTag from './Common/DxTag'
import SvgIcon from './Common/SvgIcon'

// 组件列表
const components = [
  HomeBasic,
  HomeMore,
  HomeDetails,
  HomeNetWork,
  HomeRealName,
  DxTag,
  SvgIcon
]

// 安装函数
export function install(app: App) {
  components.forEach(component => {
    if (component.name) {
      app.component(component.name, component)
    }
  })
}

// 默认导出
export default {
  install
}
