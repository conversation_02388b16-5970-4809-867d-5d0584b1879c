<!--
  HomeNetWork 组件
  显示网络状态、卡片信息和信号强度
-->
<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { ref, computed, watch } from 'vue'
import { useGlobalData } from '../../../../hooks'
import { toGB } from '../../../utils/formatters'
import { HomeNetWorkDefaultConfig } from './HomeNetWork.config'
import type {
  HomeNetWorkConfig,
  HomeNetWorkData,
  DeviceCard
} from './HomeNetWork.types'
import DxTag from '../../Common/DxTag/DxTag.vue'

// ==================== Props定义 ====================
interface Props {
  /** 组件配置 */
  config?: HomeNetWorkConfig
  /** 外部数据（主要用于设计器预览） */
  data?: HomeNetWorkData
  /** 是否为设计器模式 */
  designMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  designMode: false
})

// ==================== 配置管理 ====================
// 合并默认配置和传入配置
const config = computed(() => ({
  ...HomeNetWorkDefaultConfig.config,
  ...props.config
}))

// ==================== 事件定义 ====================
// ✨ 统一事件架构：所有交互都通过click事件，完全数据驱动
const emit = defineEmits<{
  click: [eventData: {
    elementType: 'action' | 'button'
    elementId: string
    elementData?: any
    componentType: string
  }]
}>()

// ==================== 状态管理 ====================
// 使用全局数据钩子
const { deviceData } = useGlobalData()

// 🎯 简化运营商配置
const DeviceNetWork = [
  { value: 0, label: '未知', class: 'wait' },
  { value: 1, label: '中国电信', class: 'DX' },
  { value: 2, label: '中国联通', class: 'LT' },
  { value: 3, label: '中国移动', class: 'YD' },
  { value: 4, label: '中国广电', class: 'GD' }
]

// 设备卡片数据 - 使用全局数据
const deviceCards = computed(() => {
  // 设计器模式：使用演示数据
  if (props.designMode) {
    return props.data?.deviceCards || HomeNetWorkDefaultConfig.data.deviceCards
  }

  // H5端：优先使用全局数据
  if (deviceData.value.cards && deviceData.value.cards.length > 0) {
    return deviceData.value.cards
  }

  // 兜底：使用外部传入数据或默认数据
  return props.data?.deviceCards || HomeNetWorkDefaultConfig.data.deviceCards
})

// 设备详情数据 - 使用全局数据
const deviceDetails = computed(() => {
  // 设计器模式：使用演示数据
  if (props.designMode) {
    return props.data?.deviceDetails || HomeNetWorkDefaultConfig.data.deviceDetails
  }

  // H5端：优先使用全局数据
  if (deviceData.value.details && Object.keys(deviceData.value.details).length > 0) {
    return deviceData.value.details
  }

  // 兜底：使用外部传入数据或默认数据
  return props.data?.deviceDetails || HomeNetWorkDefaultConfig.data.deviceDetails
})

// ✅ 完全复制device-an的状态变量
const index = ref(1)
const SlotIndex = ref<number>(0)

// ✅ 完全复制device-an的卡片视图计算
const ViewCard = computed(() => {
  return deviceCards.value.filter((item) => item.operator === index.value)[0]
})

// ==================== 数据监听 ====================
// 监听 props 变化（主要用于设计器模式）
watch(() => props.data, (newData) => {
  // 在计算属性中已经处理了 props.data，这里只需要处理特殊逻辑
  if (newData?.deviceDetails?.currentNetwork) {
    index.value = newData.deviceDetails.currentNetwork
  }
  console.log('🔄 HomeNetWork: 外部数据变化', newData)
}, { deep: true, immediate: true })

// 监听全局设备详情变化，更新当前网络
watch(() => deviceData.value.details?.currentNetwork, (newNetwork) => {
  if (newNetwork) {
    index.value = newNetwork
  }
}, { immediate: true })

// 安全获取运营商信息
function getOperatorInfo(operatorCode: number) {
  const info = DeviceNetWork.find(item => item.value === operatorCode)
  if (info) {
    return info
  }

  // 如果没有找到映射，返回默认信息
  console.warn(`⚠️ 未找到运营商编号 ${operatorCode} 的映射，使用默认信息`)
  return {
    label: `运营商${operatorCode}`,
    class: 'DEFAULT'
  }
}

// ==================== 可点击区域配置 ====================
// ✨ 获取配置的可点击区域列表
const configuredAreas = computed(() => {
  return config.value.clickableAreas || []
})

// ✨ 获取特定位置的区域配置
function getAreaConfig(position: string) {
  return configuredAreas.value.find(area => area.position === position && area.enabled)
}

// ==================== 事件处理 ====================
// ✨ 统一的区域点击处理器
function handleAreaClick(position: string, additionalData?: any) {
  const areaConfig = getAreaConfig(position)
  if (!areaConfig) {
    console.warn(`未找到位置为 ${position} 的区域配置`)
    return
  }

  emit('click', {
    elementType: areaConfig.elementType as 'action' | 'button',
    elementId: areaConfig.eventId || areaConfig.defaultId || 'unknown',
    elementData: { ...areaConfig, ...additionalData },
    componentType: 'HomeNetWork'
  })
}
// ✨ 网络切换方法
const switchNetwork = () => {
  const operator = ViewCard.value?.operator || 1

  handleAreaClick('network-switch', {
    operator,
    operatorName: getOperatorInfo(operator).label
  })
}

// ✨ 充值套餐方法
const handleRecharge = () => {
  handleAreaClick('recharge-area')
}

// 注意：这些函数暂时保留，可能在模板中使用
// function handleCardSelect(card: DeviceCard) {
//   console.log('🔗 选择卡片:', card)
//   emit('cardSelect', card)
// }

// function handleRefresh() {
//   console.log('🔗 刷新网络信息')
//   emit('refresh')
//   loadDeviceInfo()
// }
</script>

<template>
  <div class="HomeNetWork" v-if="deviceCards.length">
    <!-- ✅ 完全复制device-an的头部结构 -->
    <div class="HomeNetWork-head" v-if="config.showNetworkSwitch">
      <div class="HomeNetWork-head-left">
        {{ config.title }}
        <DxTag txt="智能切换" class="primary" style="margin-left: 0.2rem" />
      </div>
      <div class="HomeNetWork-head-right">自动选择最佳网络</div>
    </div>

    <!-- ✅ 完全复制device-an的网络切换导航 -->
    <div class="HomeNetWork-nav" :style="{ '--nav-box-items': deviceCards.length }">
      <div
        class="HomeNetWork-nav-box"
        v-for="(card, i) in deviceCards"
        :key="i"
        :class="{
          ['HomeNetWork-nav-box-' + getOperatorInfo(card.operator).class]: index === card.operator
        }"
        @click="
          () => {
            index = card.operator
            SlotIndex = i
          }
        "
      >
        {{ getOperatorInfo(card.operator).label }}
      </div>
    </div>

    <!-- ✅ 完全复制device-an的卡片显示 -->
    <div
      class="HomeNetWork-card"
      :class="'HomeNetWork-card-' + getOperatorInfo(ViewCard?.operator || 0).class"
      v-if="ViewCard && config.showCardInfo"
    >
      <div class="HomeNetWork-card-head">
        <div class="HomeNetWork-card-head-operator">
          {{ getOperatorInfo(ViewCard.operator).label.slice(2) }}
        </div>

        <div class="HomeNetWork-card-head-info">
          <div class="HomeNetWork-card-head-info-package">
            <div class="HomeNetWork-card-head-info-package-name">
              {{ deviceDetails.packageName || '未有生效套餐' }}
            </div>

            <div class="HomeNetWork-card-head-info-package-tag">
              <div
                class="HomeNetWork-card-head-info-package-tag-that"
                v-if="deviceDetails.currentNetwork === index"
              >
                当前使用
              </div>

              <div class="HomeNetWork-card-head-info-package-tag-btn" v-else @click="switchNetwork">
                切换
              </div>
            </div>
          </div>

          <div class="HomeNetWork-card-head-info-tip">
            <div class="HomeNetWork-card-head-info-tip-signal">
              <Icon icon="mdi:signal" class="HomeNetWork-card-head-info-tip-signal-icon" />
              信号:
              {{
                deviceDetails.currentSignal === '-1'
                  ? '暂不支持'
                  : deviceDetails.currentSignal
              }}
            </div>

            <div class="HomeNetWork-card-head-info-tip-speed">
              <Icon icon="mdi:flash" class="HomeNetWork-card-head-info-tip-speed-icon" />
              速度: --
            </div>
          </div>
        </div>
      </div>

      <div class="HomeNetWork-card-bottom">
        <div class="HomeNetWork-card-bottom-data">
          <div class="HomeNetWork-card-bottom-data-use">
            {{ toGB(deviceDetails.vUseFlow || 0).join('') }}
          </div>

          <div class="HomeNetWork-card-bottom-data-total">
            {{ '&nbsp;/&nbsp;' + toGB(deviceDetails.vTotalFlow || 0).join('') }}
          </div>
        </div>

        <div class="HomeNetWork-card-bottom-rechange" @click="handleRecharge">
          充值套餐
        </div>
      </div>
    </div>

    <!-- ✅ 完全复制device-an的提示信息 -->
    <div class="HomeNetWork-tip" v-if="config.showTip">
      <div class="HomeNetWork-tip-box">
        <Icon icon="mdi:flash" />{{ config.tipText }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* ✅ 完全复制device-an的HomeNetWork样式 */
@import '../../../styles/variables.scss';

.HomeNetWork {
  box-sizing: border-box;
  padding: $padding;
  padding-bottom: 0;
  background-color: #fff;

  &-head {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-left {
      font-size: 0.8rem;
      display: flex;
      align-items: center;
    }

    &-right {
      font-size: 0.6rem;
      color: #666;
    }
  }

  &-nav {
    display: grid;
    grid-template-columns: repeat(var(--nav-box-items), 1fr);
    gap: calc($padding / 1.2);
    margin-top: calc($padding / 1.2);

    &-box {
      text-align: center;
      font-size: 0.7rem;
      box-sizing: border-box;
      border-radius: 0.3rem;
      border: 0.05rem solid rgb(229, 231, 235);
      height: 2rem;
      line-height: 2rem;
      transition: 0.15s linear;
      cursor: pointer;
    }

    &-box-LT {
      background-color: rgba($color: $error, $alpha: 0.8);
      color: #fff;
    }

    &-box-YD {
      background-color: rgba($color: $success, $alpha: 0.8);
      color: #fff;
    }

    &-box-GD {
      background-color: rgba($color: $primary, $alpha: 0.8);
      color: #fff;
    }

    &-box-DX {
      background-color: rgba($color: $primary, $alpha: 0.8);
      color: #fff;
    }

    &-box-wait {
      background-color: $wait;
      color: #fff;
    }

    &-box-LT,
    &-box-YD,
    &-box-GD,
    &-box-DX,
    &-box-wait {
      position: relative;
      overflow: hidden;

      &::after {
        content: '';
        display: block;
        height: 0.2rem;
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        background-color: rgba($color: #fff, $alpha: 0.4);
      }
    }
  }

  &-card {
    border: 0.01rem solid transparent;
    border-radius: $radius;
    box-sizing: border-box;
    padding: $padding;
    padding-bottom: 0;
    margin-top: calc($padding / 1.2);

    &-head {
      width: 100%;
      display: flex;
      justify-content: start;
      align-items: center;
      padding-bottom: calc($padding / 1);
      border-bottom: 0.01rem solid rgb(208, 209, 211);

      &-operator {
        height: 2rem;
        width: 2rem;
        font-size: 0.8rem;
        font-weight: bold;
        border-radius: 50%;
        color: #fff;
        line-height: 2rem;
        text-align: center;
        margin-right: 0.15rem;
      }

      &-info {
        width: calc(100% - 2rem);
        box-sizing: border-box;
        padding-left: 0.4rem;

        &-package {
          display: flex;
          justify-content: space-between;
          align-items: center;

          &-name {
            font-size: 0.7rem;
          }

          &-tag {
            &-that {
              font-size: 0.57rem;
              height: 1.2rem;
              line-height: 1.2rem;
              padding: 0 0.3rem;
              border: 0.03rem solid transparent;
              border-radius: 0.6rem;
            }

            &-btn {
              font-size: 0.57rem;
              height: 1.2rem;
              line-height: 1.2rem;
              padding: 0 0.3rem;
              border: 0.03rem solid transparent;
              border-radius: 0.3rem;
              color: #fff;
              cursor: pointer;
            }
          }
        }

        &-tip {
          display: flex;
          justify-content: start;
          align-items: center;
          margin-top: 0.3rem;

          &-signal,
          &-speed {
            display: flex;
            justify-content: start;
            align-items: center;
            font-size: 0.6rem;

            &-icon {
              margin-right: 0.1rem;
              font-size: 0.6rem;
            }
          }

          &-signal {
            margin-right: 0.8rem;
          }
        }
      }
    }

    &-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: calc($padding / 1.2) 0;

      &-data {
        display: flex;
        justify-content: start;
        align-items: center;

        &-use {
          font-weight: bold;
        }

        &-total {
          font-size: 0.7rem;
          color: #666;
        }
      }

      &-rechange {
        font-size: 0.6rem;
        height: 1.5rem;
        line-height: 1.5rem;
        padding: 0 0.6rem;
        border-radius: 0.3rem;
        color: #fff;
        cursor: pointer;
      }
    }
  }

  // ✅ 运营商主题样式
  &-card-LT {
    border-color: rgba($color: $error, $alpha: 0.3);
    background-color: rgba($color: $error, $alpha: 0.1);

    .HomeNetWork-card-head-operator {
      background-color: $error;
    }

    .HomeNetWork-card-head-info-package-tag-that {
      border-color: rgba($color: $error, $alpha: 0.3);
      color: rgba($color: $error, $alpha: 0.8);
    }

    .HomeNetWork-card-head-info-package-tag-btn {
      background-color: $error;
    }

    .HomeNetWork-card-head-info-tip-signal-icon,
    .HomeNetWork-card-head-info-tip-speed-icon {
      color: rgba($color: $error, $alpha: 0.8);
    }

    .HomeNetWork-card-bottom-data-use {
      color: rgba($color: $error, $alpha: 0.8);
    }
    .HomeNetWork-card-bottom-rechange {
      background-color: rgba($color: $error, $alpha: 0.8);
    }
  }

  &-card-YD {
    border-color: rgba($color: $success, $alpha: 0.3);
    background-color: rgba($color: $success, $alpha: 0.1);

    .HomeNetWork-card-head-operator {
      background-color: $success;
    }

    .HomeNetWork-card-head-info-package-tag-that {
      border-color: rgba($color: $success, $alpha: 0.3);
      color: rgba($color: $success, $alpha: 0.8);
    }

    .HomeNetWork-card-head-info-package-tag-btn {
      background-color: $success;
    }

    .HomeNetWork-card-head-info-tip-signal-icon,
    .HomeNetWork-card-head-info-tip-speed-icon {
      color: rgba($color: $success, $alpha: 0.8);
    }

    .HomeNetWork-card-bottom-data-use {
      color: rgba($color: $success, $alpha: 0.8);
    }
    .HomeNetWork-card-bottom-rechange {
      background-color: rgba($color: $success, $alpha: 0.8);
    }
  }

  &-card-GD,
  &-card-DX {
    border-color: rgba($color: $primary, $alpha: 0.3);
    background-color: rgba($color: $primary, $alpha: 0.1);

    .HomeNetWork-card-head-operator {
      background-color: $primary;
    }

    .HomeNetWork-card-head-info-package-tag-that {
      border-color: rgba($color: $primary, $alpha: 0.3);
      color: rgba($color: $primary, $alpha: 0.8);
    }

    .HomeNetWork-card-head-info-package-tag-btn {
      background-color: $primary;
    }

    .HomeNetWork-card-head-info-tip-signal-icon,
    .HomeNetWork-card-head-info-tip-speed-icon {
      color: rgba($color: $primary, $alpha: 0.8);
    }

    .HomeNetWork-card-bottom-data-use {
      color: rgba($color: $primary, $alpha: 0.8);
    }
    .HomeNetWork-card-bottom-rechange {
      background-color: rgba($color: $primary, $alpha: 0.8);
    }
  }

  &-tip {
    margin-top: calc($padding / 1.2);

    &-box {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 0.6rem;
      color: #666;

      svg {
        margin-right: 0.2rem;
        font-size: 0.7rem;
        color: $primary;
      }
    }
  }
}
</style>
