// HomeNetWork 组件入口文件

import HomeNetWorkVue from './HomeNetWork.vue'
import { HomeNetWorkMetadata, HomeNetWorkDefaultConfig, HomeNetWorkConfigSchema } from './HomeNetWork.config'

// 创建带有配置的组件对象
const HomeNetWork = HomeNetWorkVue as any
HomeNetWork.__lowcodeMetadata = HomeNetWorkMetadata
HomeNetWork.__defaultConfig = { props: HomeNetWorkDefaultConfig, style: {} }
HomeNetWork.__configSchema = HomeNetWorkConfigSchema

// 导出组件
export default HomeNetWork
export { HomeNetWork }

// 导出配置和元数据
export { HomeNetWorkMetadata, HomeNetWorkDefaultConfig, HomeNetWorkConfigSchema }

// 导出类型定义
export interface HomeNetWorkProps {
  config?: {
    showNetworkSwitch?: boolean
    showCardInfo?: boolean
    showTip?: boolean
    title?: string
    tipText?: string
  }
}

export interface HomeNetWorkEvents {
  switchNetwork: (operator: number) => void
  recharge: () => void
}

// 组件安装函数
export function installHomeNetWork(app: any) {
  app.component('HomeNetWork', HomeNetWork)
}
