// HomeNetWork 组件配置和元数据

export const HomeNetWorkMetadata = {
  // 设计器必需的基本信息
  name: 'HomeNetWork',
  displayName: '网络切换',
  category: 'home',
  icon: 'mdi:swap-horizontal',
  description: '智能网络切换组件，支持多运营商卡片切换和网络状态显示',
  version: '1.0.0',
  tags: ['首页', '网络', '切换', '运营商', '卡片'],
  keywords: ['home', 'network', 'switch', 'operator', 'card']
}

// 默认配置 - 设计器期望的格式
export const HomeNetWorkDefaultConfig = {
  data: {
    deviceCards: [
      { operator: 1, iccid: '89860000000000000001', msisdn: '13800000001' },
      { operator: 2, iccid: '89860000000000000002', msisdn: '13800000002' },
      { operator: 3, iccid: '89860000000000000003', msisdn: '13800000003' }
    ],
    deviceDetails: {
      currentNetwork: 1,
      packageName: '标准套餐',
      currentSignal: '4',
      vUseFlow: 6860,
      vTotalFlow: 15360
    }
  },
  config: {
    showNetworkSwitch: true,
    showCardInfo: true,
    showTip: true,
    title: '网络切换',
    tipText: '网络智能切换,随时保持最佳网络状态',
    // 🎯 关键：预留的可点击区域配置
    clickableAreas: [
      {
        position: 'network-switch',
        label: '网络切换操作',
        description: '点击切换到最佳网络运营商',
        elementType: 'action',
        defaultId: 'switch-network',
        eventId: 'switch-network',
        enabled: true
      },
      {
        position: 'recharge-area',
        label: '充值套餐操作',
        description: '点击跳转到套餐充值页面',
        elementType: 'action',
        defaultId: 'recharge-package',
        eventId: 'recharge-package',
        enabled: true
      }
    ]
  }
}

// 组件配置模式 (JSON Schema) - 保持兼容性
export const HomeNetWorkConfigSchema = {
  type: 'object' as const,
  properties: {
    data: {
      type: 'object' as const,
      title: '数据配置',
      properties: {
        deviceCards: {
          type: 'array' as const,
          title: '设备卡片列表',
          default: []
        },
        deviceDetails: {
          type: 'object' as const,
          title: '设备详情',
          properties: {
            currentNetwork: {
              type: 'number' as const,
              title: '当前网络',
              default: 1,
              enum: [1, 2, 3, 4]
            },
            packageName: {
              type: 'string' as const,
              title: '套餐名称',
              default: '标准套餐'
            },
            currentSignal: {
              type: 'string' as const,
              title: '信号强度',
              default: '4'
            },
            vUseFlow: {
              type: 'number' as const,
              title: '已用流量(MB)',
              default: 6860,
              minimum: 0
            },
            vTotalFlow: {
              type: 'number' as const,
              title: '总流量(MB)',
              default: 15360,
              minimum: 1
            }
          }
        }
      }
    },
    config: {
      type: 'object' as const,
      title: '组件配置',
      properties: {
        showNetworkSwitch: {
          type: 'boolean' as const,
          title: '显示网络切换',
          default: true
        },
        showCardInfo: {
          type: 'boolean' as const,
          title: '显示卡片信息',
          default: true
        },
        showTip: {
          type: 'boolean' as const,
          title: '显示提示信息',
          default: true
        },
        title: {
          type: 'string' as const,
          title: '标题文字',
          default: '网络切换'
        },
        tipText: {
          type: 'string' as const,
          title: '提示文字',
          default: '网络智能切换,随时保持最佳网络状态'
        }
      }
    }
  }
}
