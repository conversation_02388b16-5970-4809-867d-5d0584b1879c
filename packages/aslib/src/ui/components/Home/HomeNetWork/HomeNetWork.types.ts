/**
 * HomeNetWork 组件类型定义
 */

/**
 * 可点击区域配置
 */
export interface ClickableArea {
  position: string
  label: string
  action?: string
  description?: string
  elementType?: string
  defaultId?: string
  eventId?: string
  enabled?: boolean
}

/**
 * HomeNetWork 配置
 */
export interface HomeNetWorkConfig {
  /** 是否显示组件 */
  visible?: boolean
  
  /** 组件标题 */
  title?: string
  
  /** 是否显示网络切换功能 */
  showNetworkSwitch?: boolean
  
  /** 是否显示卡片信息 */
  showCardInfo?: boolean
  
  /** 是否显示提示信息 */
  showTip?: boolean

  /** 提示文字 */
  tipText?: string

  /** 自定义文本 */
  customText?: {
    title?: string
    tipText?: string
    networkSwitchText?: string
    cardInfoTitle?: string
    signalStrengthText?: string
    dataUsageText?: string
  }
  
  /** 样式配置 */
  styleConfig?: {
    /** 卡片圆角 */
    borderRadius?: string
    /** 卡片背景色 */
    backgroundColor?: string
    /** 信号强度颜色配置 */
    signalColors?: {
      excellent?: string
      good?: string
      fair?: string
      poor?: string
      none?: string
    }
  }
  
  /** 功能配置 */
  features?: {
    /** 是否支持网络切换 */
    enableNetworkSwitch?: boolean
    /** 是否显示实时信号强度 */
    showRealTimeSignal?: boolean
    /** 是否显示数据使用统计 */
    showDataUsage?: boolean
  }

  /** 可点击区域配置 */
  clickableAreas?: ClickableArea[]
}

/**
 * HomeNetWork 数据
 */
export interface HomeNetWorkData {
  /** 设备卡片信息 */
  deviceCards?: DeviceCard[]
  
  /** 设备详情 */
  deviceDetails?: DeviceDetails
  
  /** 网络状态 */
  networkStatus?: string
  
  /** 当前选中的网络 */
  currentNetwork?: number
  
  /** 信号强度 */
  signalStrength?: number
  
  /** 网络类型 */
  networkType?: string
}

/**
 * 设备卡片信息
 */
export interface DeviceCard {
  /** 运营商 */
  operator: number
  
  /** ICCID */
  iccid: string
  
  /** 手机号码 */
  msisdn: string
  
  /** 卡片状态 */
  status?: CardStatus
  
  /** 卡片类型 */
  type?: CardType
  
  /** 信号强度 */
  signalStrength?: number
  
  /** 网络类型 */
  networkType?: string
  
  /** 是否为主卡 */
  isPrimary?: boolean
}

/**
 * 设备详情
 */
export interface DeviceDetails {
  /** 当前网络 */
  currentNetwork?: number
  
  /** 套餐名称 */
  packageName?: string
  
  /** 当前信号强度 */
  currentSignal?: string
  
  /** 已使用流量 */
  vUseFlow?: number
  
  /** 总流量 */
  vTotalFlow?: number
  
  /** 剩余流量 */
  vResidueFlow?: number
  
  /** 网络状态 */
  networkStatus?: string
  
  /** 连接时间 */
  connectionTime?: string
  
  /** IP地址 */
  ipAddress?: string
}

/**
 * 卡片状态枚举
 */
export enum CardStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  EXPIRED = 'expired'
}

/**
 * 卡片类型枚举
 */
export enum CardType {
  PHYSICAL = 'physical',
  ESIM = 'esim'
}

/**
 * 运营商枚举
 */
export enum Operator {
  UNKNOWN = 0,
  CHINA_MOBILE = 1,
  CHINA_UNICOM = 2,
  CHINA_TELECOM = 3
}

/**
 * 运营商配置
 */
export const OPERATOR_CONFIG = {
  [Operator.UNKNOWN]: {
    name: '未知',
    color: '#999999',
    icon: 'mdi:help-circle'
  },
  [Operator.CHINA_MOBILE]: {
    name: '中国移动',
    color: '#00A0E9',
    icon: 'mdi:cellphone'
  },
  [Operator.CHINA_UNICOM]: {
    name: '中国联通',
    color: '#E60012',
    icon: 'mdi:cellphone'
  },
  [Operator.CHINA_TELECOM]: {
    name: '中国电信',
    color: '#009639',
    icon: 'mdi:cellphone'
  }
} as const

/**
 * 信号强度配置
 */
export const SIGNAL_STRENGTH_CONFIG = {
  0: { label: '无信号', color: '#ff4d4f', icon: 'mdi:signal-cellular-outline' },
  1: { label: '信号差', color: '#ff7a45', icon: 'mdi:signal-cellular-1' },
  2: { label: '信号一般', color: '#ffa940', icon: 'mdi:signal-cellular-2' },
  3: { label: '信号良好', color: '#52c41a', icon: 'mdi:signal-cellular-3' },
  4: { label: '信号优秀', color: '#1890ff', icon: 'mdi:signal-cellular-3' },
  5: { label: '信号满格', color: '#1890ff', icon: 'mdi:signal-cellular-3' }
} as const

/**
 * 网络类型配置
 */
export const NETWORK_TYPE_CONFIG = {
  '2G': { label: '2G', color: '#999999' },
  '3G': { label: '3G', color: '#ffa940' },
  '4G': { label: '4G', color: '#1890ff' },
  '5G': { label: '5G', color: '#52c41a' }
} as const

/**
 * 组件事件类型
 */
export interface HomeNetWorkEvents {
  /** 网络切换 */
  'switch-network': [networkId: number]

  /** 充值套餐 */
  recharge: []

  /** 卡片选择 */
  cardSelect: [card: DeviceCard]

  /** 信号强度变化 */
  signalChange: [strength: number]

  /** 刷新网络信息 */
  refresh: []

  /** 数据加载完成 */
  loaded: [data: HomeNetWorkData]

  /** 组件出错 */
  error: [error: Error]
}

/**
 * 默认配置
 */
export const DEFAULT_HOME_NETWORK_CONFIG: Partial<HomeNetWorkConfig> = {
  visible: true,
  title: '网络状态',
  showNetworkSwitch: true,
  showCardInfo: true,
  showTip: true,
  customText: {
    title: '网络状态',
    tipText: '当前网络状态良好',
    networkSwitchText: '网络切换',
    cardInfoTitle: '卡片信息',
    signalStrengthText: '信号强度',
    dataUsageText: '流量使用'
  },
  styleConfig: {
    borderRadius: '8px',
    backgroundColor: '#ffffff',
    signalColors: {
      excellent: '#1890ff',
      good: '#52c41a',
      fair: '#ffa940',
      poor: '#ff7a45',
      none: '#ff4d4f'
    }
  },
  features: {
    enableNetworkSwitch: true,
    showRealTimeSignal: true,
    showDataUsage: true
  }
}
