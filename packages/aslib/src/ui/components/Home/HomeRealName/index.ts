// HomeRealName 组件入口文件

import HomeRealNameVue from './HomeRealName.vue'
import { HomeRealNameMetadata, HomeRealNameDefaultConfig, HomeRealNameConfigSchema } from './HomeRealName.config'

// 创建带有配置的组件对象
const HomeRealName = HomeRealNameVue as any
HomeRealName.__lowcodeMetadata = HomeRealNameMetadata
HomeRealName.__defaultConfig = { props: HomeRealNameDefaultConfig, style: {} }
HomeRealName.__configSchema = HomeRealNameConfigSchema

// 导出组件
export default HomeRealName
export { HomeRealName }

// 导出配置和元数据
export { HomeRealNameMetadata, HomeRealNameDefaultConfig, HomeRealNameConfigSchema }

// 导出类型定义
export interface HomeRealNameProps {
  config?: {
    visible?: boolean
    autoHide?: boolean
    message?: string
    buttonText?: string
    icon?: string
  }
}

export interface HomeRealNameEvents {
  realNameClick: (deviceNo: string) => void
}

// 组件安装函数
export function installHomeRealName(app: any) {
  app.component('HomeRealName', HomeRealName)
}
