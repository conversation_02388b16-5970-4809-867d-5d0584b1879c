<!-- ✅ 完全复制device-an的HomeRealName.vue -->
<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'
import { useGlobalData } from '@lowcode/aslib/hooks'
import { HomeRealNameDefaultConfig } from './HomeRealName.config'
// 组件配置接口
interface HomeRealNameConfig {
  visible?: boolean
  message?: string
  buttonText?: string
  icon?: string
  // 动态显示控制
  autoHide?: boolean
  nameStatusThreshold?: number[]
}

// 组件数据接口
interface HomeRealNameData {
  deviceNo?: string
  nameStatus?: number
  realNameCards?: Array<{
    cardName: number
    [key: string]: any
  }>
}

// 组件事件接口（暂时保留，后续可能移除）
// interface HomeRealNameEvents {
//   realNameClick: [data: { deviceNo: string }]
// }

// ==================== Props定义 ====================
interface Props {
  /** 组件配置 */
  config?: HomeRealNameConfig
  /** 外部数据（主要用于设计器预览） */
  data?: HomeRealNameData
  /** 是否为设计器模式 */
  designMode?: boolean
  /** 外部样式 */
  style?: Record<string, any>
  /** 外部类名 */
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  designMode: false
})

// ==================== 事件定义 ====================
// ✨ 统一事件架构：所有交互都通过click事件，完全数据驱动
const emit = defineEmits<{
  click: [eventData: {
    elementType: 'action' | 'button'
    elementId: string
    elementData?: any
    componentType: string
  }]
}>()

// ==================== 配置管理 ====================
// 合并默认配置和传入配置
const config = computed(() => ({
  ...HomeRealNameDefaultConfig.config,
  ...props.config
}))

// ==================== 状态管理 ====================
// 使用全局数据钩子
const { deviceData } = useGlobalData()

// 设备详情数据 - 优先使用全局数据
const deviceDetails = computed(() => {
  // 设计器模式：使用演示数据
  if (props.designMode) {
    return props.data || HomeRealNameDefaultConfig.data
  }

  // H5端：优先使用全局数据
  if (deviceData.value.details && Object.keys(deviceData.value.details).length > 0) {
    const globalData = deviceData.value.details

    console.log(globalData,'globalData')
    return {
      deviceNo: globalData.deviceNo,
      nameStatus: globalData.nameStatus,
      realNameCards: deviceData.value.realNameCards || [],
      // 从设备详情中提取卡槽信息
      iccid1: globalData.iccid1,
      iccid2: globalData.iccid2,
      iccid3: globalData.iccid3,
      iccid4: globalData.iccid4,
      cardName1: globalData.cardName1,
      cardName2: globalData.cardName2,
      cardName3: globalData.cardName3,
      cardName4: globalData.cardName4
    }
  }

  // 降级：使用外部传入的数据
  return props.data || HomeRealNameDefaultConfig.data
})

// ==================== 计算属性 ====================
// 解析图标配置（支持 "icon|color" 格式）
const parseIconConfig = (iconValue: string) => {
  if (!iconValue) return { icon: '', color: '' }
  const parts = iconValue.split('|')
  return {
    icon: parts[0] || '',
    color: parts[1] || ''
  }
}

const iconConfig = computed(() => parseIconConfig(config.value.icon || ''))

// ✅ 完全复制 device-an 的实名认证显示逻辑
const shouldShowRealName = computed(() => {
  // 设计器模式：始终显示，方便用户查看和编辑
  if (props.designMode) {
    return config.value.visible !== false
  }

  // 如果手动设置不显示，则不显示
  if (config.value.visible === false) {
    return false
  }

  // 如果不启用自动隐藏，则根据 visible 配置显示
  if (!config.value.autoHide) {
    return config.value.visible ?? true
  }

  // ✅ 完全复制 device-an 的逻辑：检查是否需要实名认证
  const data = deviceDetails.value
  if (!data) {
    console.log('🔍 HomeRealName: 没有设备数据')
    return false
  }

  console.log('🔍 HomeRealName: 检查实名状态', {
    nameStatus: data.nameStatus,
    realNameCards: data.realNameCards
  })

  // 检查卡片实名状态 - 找到需要实名认证的卡片
  const cardsNeedRealName = data.realNameCards?.find(
    (item: any) => item.cardName != 2 && item.cardName != 4
  )

  // 检查设备实名状态 - 设备需要实名认证
  const deviceNeedRealName = data.nameStatus !== 2 && data.nameStatus !== 4

  const shouldShow = cardsNeedRealName || deviceNeedRealName
  console.log('🔍 HomeRealName: 显示结果', {
    cardsNeedRealName: !!cardsNeedRealName,
    deviceNeedRealName,
    shouldShow
  })

  // 当需要实名认证时显示组件
  return shouldShow
})



// ==================== 事件处理 ====================
// ✨ 统一事件架构：实名认证点击处理
const handleRealNameClick = () => {
  emit('click', {
    elementType: 'action',
    elementId: 'real-name-auth',
    elementData: {
      label: '去实名',
      deviceNo: deviceDetails.value?.deviceNo || ''
    },
    componentType: 'HomeRealName'
  })
}
</script>

<template>
  <div
    class="HomeRealName"
    :class="props.class"
    :style="props.style"
    v-if="shouldShowRealName"
  >
    <div class="HomeRealName-svg">
      <Icon
        class="HomeRealName-svg-icon"
        :icon="iconConfig.icon"
        :style="{ color: iconConfig.color }"
      />
    </div>

    <div class="HomeRealName-txt">
      {{ config.message }}
    </div>
  
    <div class="HomeRealName-btn" @click="handleRealNameClick">
      {{ config.buttonText }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '../../../styles/variables.scss';

/* ✅ 完全复制device-an的HomeRealName样式 */
.HomeRealName {
  box-sizing: border-box;
  padding: $padding;
  border-radius: $radius $radius 0 0;
  /* 默认背景色，但允许外部样式覆盖 */
  background-color: rgb(255, 251, 235);
  border-bottom: 0.01rem solid $warning;
  display: flex;
  justify-content: start;
  align-items: center;

  /* 确保外部背景样式能覆盖内部样式 */
  &[style*="background"] {
    background-color: transparent !important;
  }

  &-svg {
    font-size: 1rem;
    color: $warning;
    position: relative;
    top: -0.1rem;
  }

  &-txt {
    width: calc(100% - 1rem - 3rem);
    font-size: 0.6rem;
    box-sizing: border-box;
    padding: 0 calc($padding / 1.5);
    color: rgb(180, 84, 12);
  }

  &-btn {
    width: 3rem;
    height: 1.2rem;
    line-height: 1.2rem;
    border-radius: 0.3rem;
    background-color: $primary;
    text-align: center;
    color: #fff;
    font-size: 0.6rem;
    cursor: pointer;
    
    &:hover {
      opacity: 0.9;
    }
  }
}
</style>
