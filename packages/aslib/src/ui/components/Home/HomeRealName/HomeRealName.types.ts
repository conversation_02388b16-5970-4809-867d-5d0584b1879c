// HomeRealName 组件类型定义

// import { DEFAULT_TEXTS } from '@lowcode/aslib/core'
// 临时使用本地常量，避免循环依赖
const DEFAULT_TEXTS = {
  REAL_NAME_MESSAGE: '为了您的账户安全，请完成实名认证',
  REAL_NAME_BUTTON: '立即认证'
}

/**
 * 可点击区域配置
 */
export interface ClickableArea {
  position: string          // 位置标识
  label: string            // 显示标签
  description: string      // 功能描述
  elementType: string      // 元素类型
  defaultId: string        // 默认ID建议
  eventId?: string         // 用户配置的事件ID
  enabled: boolean         // 是否启用
}

// 组件配置接口
export interface HomeRealNameConfig {
  /** 是否显示组件 */
  visible?: boolean
  /** 自动隐藏 */
  autoHide?: boolean
  /** 提示消息 */
  message?: string
  /** 按钮文本 */
  buttonText?: string
  /** 图标 */
  icon?: string
  /** 可点击区域配置 */
  clickableAreas?: ClickableArea[]
}

// 组件数据接口
export interface HomeRealNameData {
  /** 设备编号 */
  deviceNo?: string
  /** 实名认证状态 */
  nameStatus?: number
  /** 实名卡片列表 */
  realNameCards?: Array<{
    cardName: number
    [key: string]: any
  }>
}

// 组件事件接口
export interface HomeRealNameEvents {
  /** 点击实名认证按钮 */
  realNameClick: [data: { deviceNo: string }]
}

// 默认配置

export const DEFAULT_HOME_REALNAME_CONFIG: HomeRealNameConfig = {
  visible: true,
  autoHide: true,
  message: DEFAULT_TEXTS.REAL_NAME_MESSAGE,
  buttonText: DEFAULT_TEXTS.REAL_NAME_BUTTON,
  icon: 'mdi:alert',
  clickableAreas: [
    {
      position: 'auth-button',
      label: '实名认证按钮',
      description: '点击跳转到实名认证页面',
      elementType: 'action',
      defaultId: 'real-name-auth',
      eventId: 'real-name-auth',
      enabled: true
    }
  ]
}
