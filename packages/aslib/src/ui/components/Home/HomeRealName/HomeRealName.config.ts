// HomeRealName 组件配置和元数据

export const HomeRealNameMetadata = {
  // 设计器必需的基本信息
  name: 'HomeRealName',
  displayName: '实名认证提示',
  category: 'home',
  icon: 'mdi:alert',
  description: '显示实名认证提示信息，引导用户完成实名认证',
  version: '1.0.0',
  tags: ['首页', '实名认证', '提示', '通知'],
  keywords: ['home', 'realname', 'notification', 'alert']
}

// 默认配置 - 设计器期望的格式
export const HomeRealNameDefaultConfig = {
  data: {
    deviceNo: 'DEMO-DEVICE-001',
    nameStatus: 1,  // 1-未实名，2-已实名
    realNameCards: [
      {
        cardName: 1,
        cardNumber: '1234567890'
      }
    ]
  },
  config: {
    visible: true,
    autoHide: true,
    message: '检测到您的设备卡尚未完成实名认证，请尽快完成认证以确保正常使用',
    buttonText: '去实名',
    icon: 'mdi:alert',
    // 🎯 关键：预留的可点击区域配置
    clickableAreas: [
      {
        position: 'realname-button',
        label: '实名认证按钮',
        description: '点击跳转到实名认证页面',
        elementType: 'button',
        defaultId: 'realname-action',
        eventId: 'realname-action',
        enabled: true
      }
    ]
  }
}

// 组件配置模式 (JSON Schema) - 保持兼容性
export const HomeRealNameConfigSchema = {
  type: 'object' as const,
  properties: {
    data: {
      type: 'object' as const,
      title: '数据配置',
      description: '实名认证相关数据',
      properties: {
        deviceNo: {
          type: 'string' as const,
          title: '设备编号',
          default: 'DEMO-DEVICE-001'
        },
        nameStatus: {
          type: 'number' as const,
          title: '实名状态',
          description: '1-未实名，2-已实名',
          default: 1,
          enum: [1, 2]
        }
      }
    },
    config: {
      type: 'object' as const,
      title: '组件配置',
      properties: {
        visible: {
          type: 'boolean' as const,
          title: '显示组件',
          default: true
        },
        autoHide: {
          type: 'boolean' as const,
          title: '自动隐藏',
          default: true
        },
        message: {
          type: 'string' as const,
          title: '提示消息',
          default: '检测到您的设备卡尚未完成实名认证，请尽快完成认证以确保正常使用'
        },
        buttonText: {
          type: 'string' as const,
          title: '按钮文本',
          default: '去实名'
        },
        icon: {
          type: 'string' as const,
          title: '图标',
          default: 'mdi:alert'
        }
      }
    }
  }
}