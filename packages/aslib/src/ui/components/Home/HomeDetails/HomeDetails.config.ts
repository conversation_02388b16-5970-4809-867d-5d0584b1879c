// HomeDetails 组件配置和元数据

export const HomeDetailsMetadata = {
  // 设计器必需的基本信息
  name: 'HomeDetails',
  displayName: '设备信息',
  category: 'home',
  icon: 'mdi:information',
  description: '设备详细信息组件，显示设备编号、IMEI、套餐信息等详细数据',
  version: '1.0.0',
  tags: ['首页', '设备', '信息', '详情'],
  keywords: ['home', 'device', 'info', 'details']
}

// 默认配置 - 直接作为 props 传递给组件
export const HomeDetailsDefaultConfig = {
  data: {
    wifiName: 'ZTE-MF971V_001',
    wifi5gName: 'ZTE-MF971V_5G_001',
    wifiPwd: 'zx123456',
    wifi5gPwd: 'zx123456',
    deviceNo: 'ZTE001234567890',
    currentBatteryLevel: 85,
    imeiNo: '860123456789012345',
    packageName: '标准流量套餐',
    activationDatetime: '2024-01-15T10:30:00',
    supports5g: true
  },
  config: {
    defaultOpen: false,
    showWifiSwitch: true,
    title: '设备信息',
    expandIcon: 'mdi:chevron-right',
    wifiIcon: 'mdi:wifi',
    batteryIcon: 'mdi:battery',
    wifiSwitchIcon: 'mdi:swap-horizontal',
    calendarIcon: 'mdi:calendar',
    // 🎯 关键：预留的可点击区域配置
    clickableAreas: [
      {
        position: 'toggle-button',
        label: '展开收起按钮',
        description: '点击展开或收起设备详细信息',
        elementType: 'button',
        defaultId: 'toggle-details',
        eventId: 'toggle-details',
        enabled: true
      },
      {
        position: 'wifi-switch',
        label: 'WiFi频段切换',
        description: '点击切换2.4G/5G WiFi频段',
        elementType: 'button',
        defaultId: 'wifi-switch',
        eventId: 'wifi-switch',
        enabled: true
      },
      {
        position: 'copy-action',
        label: '复制设备信息',
        description: '点击复制设备相关信息',
        elementType: 'action',
        defaultId: 'copy-info',
        eventId: 'copy-info',
        enabled: true
      }
    ]
  }
}

// 组件配置模式 (JSON Schema) - 保持兼容性
export const HomeDetailsConfigSchema = {
  type: 'object' as const,
  properties: {
    data: {
      type: 'object' as const,
      title: '数据配置',
      properties: {
        wifiName: {
          type: 'string' as const,
          title: '2.4G WiFi名称',
          default: 'ZTE-MF971V_001'
        },
        wifi5gName: {
          type: 'string' as const,
          title: '5G WiFi名称',
          default: 'ZTE-MF971V_5G_001'
        },
        wifiPwd: {
          type: 'string' as const,
          title: '2.4G WiFi密码',
          default: 'zx123456'
        },
        wifi5gPwd: {
          type: 'string' as const,
          title: '5G WiFi密码',
          default: 'zx123456'
        },
        deviceNo: {
          type: 'string' as const,
          title: '设备编号',
          default: 'ZTE001234567890'
        },
        currentBatteryLevel: {
          type: 'number' as const,
          title: '电池电量',
          default: 85,
          minimum: 0,
          maximum: 100
        },
        imeiNo: {
          type: 'string' as const,
          title: 'IMEI号码',
          default: '860123456789012345'
        },
        packageName: {
          type: 'string' as const,
          title: '套餐名称',
          default: '标准流量套餐'
        },
        activationDatetime: {
          type: 'string' as const,
          title: '激活时间',
          default: '2024-01-15T10:30:00'
        },
        supports5g: {
          type: 'boolean' as const,
          title: '支持5G',
          default: true
        }
      }
    },
    config: {
      type: 'object' as const,
      title: '组件配置',
      properties: {
        defaultOpen: {
          type: 'boolean' as const,
          title: '默认展开',
          default: false
        },
        showWifiSwitch: {
          type: 'boolean' as const,
          title: '显示WiFi切换',
          default: true
        },
        title: {
          type: 'string' as const,
          title: '标题文字',
          default: '设备信息'
        }
      }
    }
  }
}
