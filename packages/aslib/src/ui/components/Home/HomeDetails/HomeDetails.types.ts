/**
 * HomeDetails 组件类型定义
 */

/**
 * HomeDetails 配置
 */
export interface HomeDetailsConfig {
  /** 是否显示组件 */
  visible?: boolean
  
  /** 组件标题 */
  title?: string
  
  /** 是否默认展开 */
  defaultOpen?: boolean
  
  /** 是否显示WiFi开关 */
  showWifiSwitch?: boolean
  
  /** 自定义文本 */
  customText?: {
    title?: string
    wifiSectionTitle?: string
    deviceSectionTitle?: string
    packageSectionTitle?: string
    toggleButtonText?: string
    copyButtonText?: string
    copiedText?: string
  }

  /** 展开/收起图标 */
  expandIcon?: string

  /** WiFi图标 */
  wifiIcon?: string

  /** 电池图标 */
  batteryIcon?: string

  /** WiFi切换图标 */
  wifiSwitchIcon?: string

  /** 日历图标 */
  calendarIcon?: string
  
  /** 样式配置 */
  styleConfig?: {
    /** 是否显示展开动画 */
    showAnimation?: boolean
    /** 卡片圆角 */
    borderRadius?: string
    /** 卡片背景色 */
    backgroundColor?: string
    /** 分割线颜色 */
    dividerColor?: string
  }
  
  /** 功能配置 */
  features?: {
    /** 是否支持WiFi密码复制 */
    enablePasswordCopy?: boolean
    /** 是否支持5G网络 */
    support5G?: boolean
    /** 是否显示电池信息 */
    showBattery?: boolean
    /** 是否显示IMEI信息 */
    showIMEI?: boolean
  }
}

/**
 * HomeDetails 数据
 */
export interface HomeDetailsData {
  /** WiFi名称 */
  wifiName?: string
  
  /** 5G WiFi名称 */
  wifi5gName?: string
  
  /** WiFi密码 */
  wifiPwd?: string
  
  /** 5G WiFi密码 */
  wifi5gPwd?: string
  
  /** 设备编号 */
  deviceNo?: string
  
  /** 当前电池电量 */
  currentBatteryLevel?: number
  
  /** IMEI号码 */
  imeiNo?: string
  
  /** 套餐名称 */
  packageName?: string
  
  /** 激活时间 */
  activationDatetime?: string
  
  /** 是否支持5G */
  supports5g?: boolean
  
  /** 设备型号 */
  deviceModel?: string
  
  /** 固件版本 */
  firmwareVersion?: string
  
  /** 硬件版本 */
  hardwareVersion?: string
  
  /** 设备状态 */
  deviceStatus?: number
  
  /** 网络状态 */
  networkStatus?: string
  
  /** 信号强度 */
  signalStrength?: number
}

/**
 * WiFi网络类型枚举
 */
export enum WiFiType {
  WIFI_2_4G = '2.4G',
  WIFI_5G = '5G'
}

/**
 * 设备信息分组
 */
export interface DeviceInfoGroup {
  /** 分组标题 */
  title: string
  /** 分组项目 */
  items: DeviceInfoItem[]
}

export interface DeviceInfoItem {
  /** 项目标签 */
  label: string
  /** 项目值 */
  value: string | number
  /** 是否可复制 */
  copyable?: boolean
  /** 项目图标 */
  icon?: string
  /** 项目类型 */
  type?: 'text' | 'password' | 'battery' | 'date'
}

/**
 * 组件事件类型
 */
export interface HomeDetailsEvents {
  /** 展开/收起切换 */
  toggle: [isOpen: boolean]
  
  /** WiFi密码复制 */
  copyWifiPassword: [type: WiFiType, password: string]
  
  /** 设备信息复制 */
  copyDeviceInfo: [label: string, value: string]
  
  /** WiFi开关切换 */
  wifiToggle: [enabled: boolean]
  
  /** 刷新设备信息 */
  refresh: []
  
  /** 数据加载完成 */
  loaded: [data: HomeDetailsData]
  
  /** 组件出错 */
  error: [error: Error]
}

/**
 * 默认配置
 */
export const DEFAULT_HOME_DETAILS_CONFIG: Partial<HomeDetailsConfig> = {
  visible: true,
  title: '设备详情',
  defaultOpen: false,
  showWifiSwitch: true,
  customText: {
    title: '设备详情',
    wifiSectionTitle: 'WiFi信息',
    deviceSectionTitle: '设备信息',
    packageSectionTitle: '套餐信息',
    toggleButtonText: '展开详情',
    copyButtonText: '复制',
    copiedText: '已复制'
  },
  styleConfig: {
    showAnimation: true,
    borderRadius: '8px',
    backgroundColor: '#ffffff',
    dividerColor: '#f0f0f0'
  },
  features: {
    enablePasswordCopy: true,
    support5G: true,
    showBattery: true,
    showIMEI: true
  }
}

/**
 * 设备信息字段映射
 */
export const DEVICE_INFO_FIELDS = {
  deviceNo: { label: '设备编号', icon: 'mdi:router-wireless', copyable: true },
  imeiNo: { label: 'IMEI', icon: 'mdi:cellphone', copyable: true },
  deviceModel: { label: '设备型号', icon: 'mdi:information' },
  firmwareVersion: { label: '固件版本', icon: 'mdi:chip' },
  hardwareVersion: { label: '硬件版本', icon: 'mdi:memory' },
  packageName: { label: '当前套餐', icon: 'mdi:package-variant' },
  activationDatetime: { label: '激活时间', icon: 'mdi:calendar', type: 'date' },
  currentBatteryLevel: { label: '电池电量', icon: 'mdi:battery', type: 'battery' }
} as const
