<!--
  HomeDetails 组件
  显示设备详细信息，包括WiFi信息、设备信息和套餐信息
-->
<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Icon } from '@iconify/vue'
import { useGlobalData } from '../../../../hooks'
import { HomeDetailsDefaultConfig } from './HomeDetails.config'
import type {
  HomeDetailsConfig,
  HomeDetailsData,
  HomeDetailsEvents
} from './HomeDetails.types'

// ==================== Props定义 ====================
interface Props {
  /** 组件配置 */
  config?: HomeDetailsConfig
  /** 外部数据（主要用于设计器预览） */
  data?: HomeDetailsData
  /** 是否为设计器模式 */
  designMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  designMode: false
})

// ==================== 配置管理 ====================
// 合并默认配置和传入配置
const config = computed(() => ({
  ...HomeDetailsDefaultConfig.config,
  ...props.config
}))

// ==================== 事件定义 ====================
const emit = defineEmits<HomeDetailsEvents>()

// ==================== 状态管理 ====================
// 使用全局数据钩子
const { deviceData } = useGlobalData()

const isOpen = ref(config.value.defaultOpen || false)
const DeviceIs4g = ref(true)
const Change = ref(false)

// 设备详情数据 - 使用全局数据
const deviceDetails = computed(() => {
  // 设计器模式：使用演示数据
  if (props.designMode) {
    return props.data || HomeDetailsDefaultConfig.data
  }

  // H5端：优先使用全局数据
  if (deviceData.value.details && Object.keys(deviceData.value.details).length > 0) {
    return deviceData.value.details
  }

  // 兜底：使用外部传入数据或默认数据
  return props.data || HomeDetailsDefaultConfig.data
})

// 监听 props 变化（主要用于设计器模式）
watch(() => props.data, (newData) => {
  // 在计算属性中已经处理了 props.data，这里不需要额外处理
  console.log('🔄 HomeDetails: 外部数据变化', newData)
}, { deep: true, immediate: true })

watch(() => config.value, (newConfig) => {
  if (newConfig && newConfig.defaultOpen !== undefined) {
    isOpen.value = newConfig.defaultOpen
  }
}, { deep: true, immediate: true })

// ==================== 计算属性 ====================
// 解析图标配置（支持 "icon|color" 格式）
const parseIconConfig = (iconValue: string) => {
  if (!iconValue) return { icon: '', color: '' }
  const parts = iconValue.split('|')
  return {
    icon: parts[0] || '',
    color: parts[1] || ''
  }
}

// 解析各个图标配置
const expandIconConfig = computed(() => parseIconConfig(config.value.expandIcon || ''))
const wifiIconConfig = computed(() => parseIconConfig(config.value.wifiIcon || ''))
const batteryIconConfig = computed(() => parseIconConfig(config.value.batteryIcon || ''))
const wifiSwitchIconConfig = computed(() => parseIconConfig(config.value.wifiSwitchIcon || ''))
const calendarIconConfig = computed(() => parseIconConfig(config.value.calendarIcon || ''))


// ✅ 完全复制device-an的接口定义
interface DetailsIcon {
  type: 'icon'
  clcik: (data: { is4g: boolean; details: any }) => void
  name: string
  visible?: (data: any) => boolean
}

interface DetailsTxt {
  type: 'txt'
  txt: string
  visible?: (data: any) => boolean
}

interface DetailsTheme {
  type: 'time'
  visible?: (data: any) => boolean
}

type DetailsViewData = {
  label: string
  key: (data: { is4g: boolean; details: any }) => string
  iptType: string
  right: (DetailsIcon | DetailsTxt | DetailsTheme)[]
}

// ✅ 完全复制device-an的详情列表配置
const DetailsList = ref<DetailsViewData[]>([
  {
    label: 'Wi-Fi名称',
    key: ({ is4g, details }) => (is4g ? details.wifiName : details.wifi5gName) || '未设置',
    iptType: 'text',
    right: [
      {
        type: 'icon',
        clcik: ({ is4g, details }) => {
          const wifiName = is4g ? details.wifiName : details.wifi5gName
          console.log('复制WiFi名称:', wifiName)
        },
        name: 'copy'
      }
    ]
  },
  {
    label: 'Wi-Fi密码',
    key: ({ is4g, details }) => (is4g ? details.wifiPwd : details.wifi5gPwd) || '未设置',
    iptType: 'password',
    right: [
      {
        type: 'icon',
        clcik: ({ is4g, details }) => {
          const wifiPwd = is4g ? details.wifiPwd : details.wifi5gPwd
          console.log('复制WiFi密码:', wifiPwd)
        },
        name: 'copy'
      }
    ]
  },
  {
    label: '设备编号',
    key: ({ details }) => details.deviceNo || '未知',
    iptType: 'text',
    right: [
      {
        type: 'icon',
        clcik: ({ details }) => {
          console.log('复制设备编号:', details.deviceNo)
        },
        name: 'copy'
      }
    ]
  },
  {
    label: 'IMEI',
    key: ({ details }) => details.imeiNo || '未知',
    iptType: 'text',
    right: [
      {
        type: 'icon',
        clcik: ({ details }) => {
          console.log('复制IMEI:', details.imeiNo)
        },
        name: 'copy'
      }
    ]
  },
  {
    label: '套餐名称',
    key: ({ details }) => details.packageName || '未知',
    iptType: 'text',
    right: [
      {
        type: 'time',
        visible: ({ key }) => !!key({ is4g: DeviceIs4g.value, details: deviceDetails.value })
      }
    ]
  },
  {
    label: '激活时间',
    key: ({ details }) => details.activationDatetime ? formatTime(details.activationDatetime) : '未知',
    iptType: 'text',
    right: []
  }
])

// ✅ 完全复制device-an的图标加载逻辑
const iconLoad = ({ itemConfig, icon }: { itemConfig: DetailsViewData; icon: any }) => {
  if (icon.visible) {
    return icon.visible(itemConfig)
  }
  return true
}

// ==================== 事件处理 ====================

// WiFi开关切换
function handleWifiToggle() {
  DeviceIs4g.value = !DeviceIs4g.value
  Change.value = true
  setTimeout(() => {
    Change.value = false
  }, 1000)

  emit('wifiToggle', DeviceIs4g.value)
  console.log(`🔗 WiFi切换: ${DeviceIs4g.value ? '4G' : '5G'}`)
}


// 时间格式化函数
function formatTime(dateStr: string): string {
  try {
    const date = new Date(dateStr)
    return date.toLocaleDateString('zh-CN')
  } catch {
    return '未知'
  }
}

// 兼容旧的方法名
const WifiChange = handleWifiToggle
</script>

<template>
  <div class="HomeDetails" :class="{ 'HomeDetails-change': Change }">
    <!-- ✅ 完全复制device-an的头部结构 -->
    <div class="HomeDetails-head">
      <div class="HomeDetails-head-left">{{ config.title }}</div>

      <div class="HomeDetails-head-right">
        <div class="HomeDetails-head-right-label" @click="isOpen = !isOpen">
          {{ isOpen ? '收起' : '详情' }}
        </div>
        <div
          class="HomeDetails-head-right-icon"
          :class="{ 'HomeDetails-head-right-icon-open': isOpen }"
        >
          <Icon
            :icon="expandIconConfig.icon || 'mdi:chevron-right'"
            :style="expandIconConfig.color ? { color: expandIconConfig.color } : {}"
          />
        </div>
      </div>
    </div>

    <!-- ✅ 完全复制device-an的基础信息结构 -->
    <div class="HomeDetails-basic">
      <div class="HomeDetails-basic-left">
        <div class="HomeDetails-basic-icon">
          <Icon
            :icon="wifiIconConfig.icon || 'mdi:wifi'"
            :style="wifiIconConfig.color ? { color: wifiIconConfig.color } : {}"
          />
        </div>

        <div class="HomeDetails-basic-one">
          <div class="HomeDetails-basic-one-label">
            {{ DeviceIs4g ? deviceDetails.wifiName : deviceDetails.wifi5gName || '未设置' }}
            <span class="HomeDetails-basic-one-label-band" v-if="deviceDetails.supports5g">
              {{ DeviceIs4g ? '(2.4G)' : '(5G)' }}
            </span>
          </div>
          <div class="HomeDetails-basic-one-bottom">
            <div class="HomeDetails-basic-one-bottom-wifi">
              <Icon
                :icon="wifiIconConfig.icon || 'mdi:wifi'"
                class="HomeDetails-basic-one-bottom-icon"
                :style="wifiIconConfig.color ? { color: wifiIconConfig.color } : {}"
              />
              <div class="HomeDetails-basic-one-bottom-txt">{{ deviceDetails.deviceNo }}</div>
            </div>
            <div class="HomeDetails-basic-one-bottom-power">
              <Icon
                :icon="batteryIconConfig.icon || 'mdi:battery'"
                class="HomeDetails-basic-one-bottom-icon"
                :style="batteryIconConfig.color ? { color: batteryIconConfig.color } : {}"
              />
              <div class="HomeDetails-basic-one-bottom-txt">{{ deviceDetails.currentBatteryLevel }}%</div>
            </div>
          </div>
        </div>
      </div>

      <div class="HomeDetails-basic-right" v-if="isOpen && deviceDetails.supports5g && config.showWifiSwitch">
        <Icon
          :icon="wifiSwitchIconConfig.icon || 'mdi:swap-horizontal'"
          @click="WifiChange()"
          :style="wifiSwitchIconConfig.color ? { color: wifiSwitchIconConfig.color } : {}"
        />
      </div>
    </div>

    <!-- ✅ 完全复制device-an的详情列表结构 -->
    <div
      class="HomeDetails-details"
      v-for="item in DetailsList"
      :key="item.label"
      v-show="isOpen"
    >
      <div class="HomeDetails-details-left">
        <div class="HomeDetails-details-left-label">{{ item.label }}</div>
        <input
          class="HomeDetails-details-left-ipt"
          :type="item.iptType"
          :value="item.key({ is4g: DeviceIs4g, details: deviceDetails })"
          readonly
        />
      </div>

      <div class="HomeDetails-details-right">
        <template v-for="icon in item.right" :key="icon.type">
          <template v-if="iconLoad({ itemConfig: item, icon: icon })">
            <div class="HomeDetails-details-right-icon" v-if="icon.type === 'icon'">
              <Icon
                :icon="icon.name === 'copy' ? 'mdi:content-copy' : `mdi:${icon.name}`"
                @click="icon.clcik({ is4g: DeviceIs4g, details: deviceDetails })"
              />
            </div>

            <div class="HomeDetails-details-right-time" v-else-if="icon.type === 'time'">
              <Icon
                :icon="calendarIconConfig.icon || 'mdi:calendar'"
                style="margin-right: 0.1rem"
                :style="calendarIconConfig.color ? { color: calendarIconConfig.color, marginRight: '0.1rem' } : { marginRight: '0.1rem' }"
              />还有30天
            </div>

            <div class="HomeDetails-details-right-txt" v-else-if="icon.type === 'txt'">
              {{ icon.txt }}
            </div>
          </template>
        </template>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* ✅ 完全复制device-an的HomeDetails样式 */
@import '../../../styles/variables.scss';

.HomeDetails-change {
  animation: RotateYSwitch 1s linear infinite;
}

.HomeDetails {
  box-sizing: border-box;
  padding: $padding;
  padding-bottom: 0;
  background-color: #fff;

  &-head {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-left {
      font-size: 0.8rem;
    }

    &-right {
      display: flex;
      justify-content: start;
      align-items: center;
      font-size: 0.6rem;
      color: $primary;
      cursor: pointer;

      &-icon {
        font-size: 0.5rem;
        transition: 0.15s linear;
        margin-left: 0.15rem;
      }

      &-icon-open {
        transform: rotate(90deg);
      }
    }
  }

  &-basic {
    @include PaddingBox;
    background-color: $box-background;
    border-radius: $radius;
    margin-top: calc($padding / 1.2);
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-left {
      display: flex;
      justify-content: start;
      align-items: center;
    }

    &-icon {
      width: 2.5rem;
      height: 2.5rem;
      background-color: rgba($primary, 0.1);
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: $primary;
      font-size: 1.2rem;
    }

    &-one {
      margin-left: 0.5rem;
      padding-right: $padding;

      &-label {
        font-size: 0.8rem;
        margin-bottom: 0.3rem;

        &-band {
          font-size: 0.6rem;
          color: $primary;
          margin-left: 0.3rem;
        }
      }

      &-bottom {
        display: flex;
        justify-content: start;
        align-items: center;

        &-wifi,
        &-power {
          display: flex;
          justify-content: start;
          align-items: center;
          margin-right: 0.8rem;
        }

        &-icon {
          font-size: 0.6rem;
          color: #666;
          margin-right: 0.1rem;
        }

        &-txt {
          font-size: 0.6rem;
          color: #666;
        }
      }
    }

    &-right {
      width: 1.5rem;
      height: 1.5rem;
      background-color: $background;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: $primary;
      font-size: 0.8rem;
      cursor: pointer;
    }
  }

  &-details {
    @include PaddingBox;
    background-color: $box-background;
    border-radius: $radius;
    margin-top: calc($padding / 1.2);
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-left {
      width: calc(100% - 3rem);

      &-label {
        font-size: 0.6rem;
        color: #666;
        margin-bottom: 0.3rem;
      }

      &-ipt {
        width: 100%;
        border: none;
        outline: none;
        font-size: 0.7rem;
        color: #333;
        background: transparent;
      }
    }

    &-right {
      width: 3rem;
      display: flex;
      justify-content: end;
      align-items: center;

      &-icon {
        width: 1.5rem;
        height: 1.5rem;
        background-color: $background;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: $primary;
        font-size: 0.7rem;
        cursor: pointer;
        margin-left: 0.3rem;
      }

      &-time {
        font-size: 0.6rem;
        color: #666;
        display: flex;
        align-items: center;
        margin-left: 0.3rem;
      }

      &-txt {
        font-size: 0.6rem;
        color: $primary;
        margin-left: 0.3rem;
      }
    }
  }
}

@keyframes RotateYSwitch {
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(180deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}
</style>
