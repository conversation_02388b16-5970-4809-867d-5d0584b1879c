// HomeDetails 组件入口文件

import HomeDetailsVue from './HomeDetails.vue'
import { HomeDetailsMetadata, HomeDetailsDefaultConfig, HomeDetailsConfigSchema } from './HomeDetails.config'

// 创建带有配置的组件对象
const HomeDetails = HomeDetailsVue as any
HomeDetails.__lowcodeMetadata = HomeDetailsMetadata
HomeDetails.__defaultConfig = { props: HomeDetailsDefaultConfig, style: {} }
HomeDetails.__configSchema = HomeDetailsConfigSchema

// 导出组件
export default HomeDetails
export { HomeDetails }

// 导出配置和元数据
export { HomeDetailsMetadata, HomeDetailsDefaultConfig, HomeDetailsConfigSchema }

// 导出类型定义
export interface HomeDetailsProps {
  defaultOpen?: boolean
  showWifiSwitch?: boolean
  title?: string
  expandIcon?: string
  wifiIcon?: string
  batteryIcon?: string
  wifiSwitchIcon?: string
  calendarIcon?: string
}

export interface HomeDetailsEvents {
  toggle: (isOpen: boolean) => void
  wifiSwitch: (is4g: boolean) => void
  copy: (type: string, value: string) => void
}

// 组件安装函数
export function installHomeDetails(app: any) {
  app.component('HomeDetails', HomeDetails)
}
