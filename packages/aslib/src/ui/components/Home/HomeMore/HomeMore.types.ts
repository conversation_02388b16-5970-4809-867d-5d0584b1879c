/**
 * HomeMore 组件类型定义
 */

/**
 * HomeMore 配置
 */
export interface HomeMoreConfig {
  /** 是否显示组件 */
  visible?: boolean
  /** 是否显示标题 */
  showTitle?: boolean

  /** 网格列数 */
  columns?: number

  /** 是否显示图标 */
  showIcons?: boolean

  /** 组件标题 */
  title?: string

  /** 自定义文本 */
  customText?: {
    title?: string
    moreText?: string
  }

  /** 样式配置 */
  styleConfig?: {
    /** 网格间距 */
    gap?: string
    /** 项目圆角 */
    itemBorderRadius?: string
    /** 项目背景色 */
    itemBackgroundColor?: string
    /** 悬停效果 */
    enableHover?: boolean
  }

  /** 菜单项配置（纯展示数据，不包含事件处理） */
  menuItems?: HomeMoreMenuItem[]
}

/**
 * HomeMore 数据
 */
export interface HomeMoreData {
  /** 菜单项列表 */
  menuItems?: HomeMoreMenuItem[]
  
  /** 用户权限 */
  permissions?: string[]
  
  /** 是否显示新功能标识 */
  showNewBadge?: boolean
}

/**
 * 菜单项配置（纯展示数据）
 */
export interface HomeMoreMenuItem {
  /** 唯一标识 */
  id: string

  /** 显示名称 */
  name: string

  /** 图标名称（支持 icon|color 格式） */
  icon: string

  /** 是否显示新功能标识 */
  isNew?: boolean

  /** 是否禁用 */
  disabled?: boolean

  /** 描述信息 */
  description?: string

  /** 自定义样式 */
  style?: {
    color?: string
    backgroundColor?: string
  }

  /** 排序权重 */
  order?: number

  /** 是否可见 */
  visible?: boolean
}

/**
 * 默认菜单项配置（纯展示数据）
 */
export const DEFAULT_MENU_ITEMS: HomeMoreMenuItem[] = [
  {
    id: 'refresh',
    name: '更新信息',
    icon: 'mdi:refresh',
    description: '刷新设备信息',
    order: 1
  },
  {
    id: 'package',
    name: '套餐充值',
    icon: 'mdi:package-variant',
    description: '购买或续费套餐',
    order: 2
  },
  {
    id: 'balance',
    name: '充值余额',
    icon: 'mdi:wallet',
    description: '账户余额充值',
    order: 3
  },
  {
    id: 'wifi',
    name: 'Wi-Fi设置',
    icon: 'mdi:wifi',
    description: 'WiFi密码和设置',
    order: 4
  },
  {
    id: 'history',
    name: '余额明细',
    icon: 'mdi:history',
    description: '查看消费记录',
    order: 5
  },
  {
    id: 'password',
    name: '支付密码',
    icon: 'mdi:lock',
    description: '修改支付密码',
    order: 6
  },
  {
    id: 'service',
    name: '联系客服',
    icon: 'mdi:headset',
    description: '在线客服支持',
    order: 7
  },
  {
    id: 'settings',
    name: '设置',
    icon: 'mdi:cog',
    description: '应用设置选项',
    order: 8
  }
]

/**
 * 组件事件类型
 */
export interface HomeMoreEvents {
  /** 菜单项点击 */
  itemClick: [item: HomeMoreMenuItem]

  /** 更多按钮点击 */
  moreClick: []

  /** 菜单项长按 */
  itemLongPress: [item: HomeMoreMenuItem]

  /** 菜单加载完成 */
  menuLoaded: [items: HomeMoreMenuItem[]]

  // 具体的业务事件（用于低代码平台）
  /** 刷新设备信息 */
  refresh: []

  /** 套餐充值 */
  package: []

  /** 余额充值 */
  balance: []

  /** WiFi设置 */
  wifi: []

  /** 余额明细 */
  history: []

  /** 支付密码 */
  password: []

  /** 客服中心 */
  service: []

  /** 系统设置 */
  settings: []

  /** 自定义操作 */
  customAction: [actionId: string, item: HomeMoreMenuItem]
}
