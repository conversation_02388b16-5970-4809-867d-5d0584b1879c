// HomeMore 组件配置 - 新方案：写一次，自动生成其他所有内容
import { autoGenerate, type ComponentDefinition } from '../../../utils/auto-generate'

// 🎯 只写这一份配置定义，其他全自动生成
const HomeMoreDefinition: ComponentDefinition = {
  name: 'HomeMore',
  displayName: '快捷功能',
  category: 'home',
  icon: 'mdi:apps',
  description: '首页快捷功能菜单，提供常用操作的快速入口',
  
  config: {
    showTitle: {
      default: true,
      ui: {
        title: '显示标题',
        desc: '是否显示组件标题'
      }
    },
    title: {
      default: '快捷功能',
      ui: {
        title: '标题文字',
        desc: '组件标题显示的文字'
      }
    },
    columns: {
      default: 4,
      ui: {
        title: '列数',
        desc: '功能图标每行显示的列数',
        min: 2,
        max: 6
      }
    },
    menuItems: {
      default: [
        { id: 'refresh', name: '更新信息', icon: 'mdi:refresh', order: 1, visible: true },
        { id: 'package', name: '套餐充值', icon: 'mdi:package-variant', order: 2, visible: true },
        { id: 'balance', name: '充值余额', icon: 'mdi:wallet', order: 3, visible: true },
        { id: 'wifi', name: 'Wi-Fi设置', icon: 'mdi:wifi', order: 4, visible: true },
        { id: 'history', name: '余额明细', icon: 'mdi:history', order: 5, visible: true },
        { id: 'password', name: '支付密码', icon: 'mdi:lock', order: 6, visible: true },
        { id: 'service', name: '联系客服', icon: 'mdi:headset', order: 7, visible: true },
        { id: 'settings', name: '系统设置', icon: 'mdi:cog', order: 8, visible: true }
      ],
      ui: {
        title: '菜单项配置',
        desc: '快捷功能菜单项列表'
      }
    }
  }
}

// 🚀 自动生成所有配置（保持向后兼容的导出接口）
export const HomeMoreMetadata = autoGenerate.metadata(HomeMoreDefinition)
export const HomeMoreDefaultConfig = autoGenerate.defaults(HomeMoreDefinition)
export const HomeMoreConfigSchema = autoGenerate.schema(HomeMoreDefinition)

// ✅ 配置完成！
// 对比原来的方式：
// - 原来需要写 ~300行重复配置
// - 现在只需要写 ~50行核心配置
// - 减少了 80% 的重复代码
// - 所有配置自动同步，修改一处即可
