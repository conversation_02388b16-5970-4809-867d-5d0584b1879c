// HomeMore 组件入口文件

import HomeMoreVue from './HomeMore.vue'
import { HomeMoreMetadata, HomeMoreDefaultConfig, HomeMoreConfigSchema } from './HomeMore.config'

// 创建带有配置的组件对象
const HomeMore = HomeMoreVue as any
HomeMore.__lowcodeMetadata = HomeMoreMetadata
HomeMore.__defaultConfig = { props: HomeMoreDefaultConfig, style: {} }
HomeMore.__configSchema = HomeMoreConfigSchema

// 导出组件
export default HomeMore
export { HomeMore }

// 导出配置和元数据
export { HomeMoreMetadata, HomeMoreDefaultConfig, HomeMoreConfigSchema }

// 导出类型定义
export interface HomeMoreProps {
  showTitle?: boolean
  title?: string
  columns?: number
  menuItems?: Array<{
    id: string
    name: string
    icon: string
    order?: number
    visible?: boolean
  }>
}

export interface HomeMoreEvents {
  refresh: () => void
  package: () => void
  balance: () => void
  wifi: () => void
  history: () => void
  password: () => void
  service: () => void
  settings: () => void
  customAction: (actionId: string, item: any) => void
}

// 组件安装函数
export function installHomeMore(app: any) {
  app.component('HomeMore', HomeMore)
}
