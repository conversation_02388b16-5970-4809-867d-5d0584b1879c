<!--
  HomeMore 组件
  显示快捷功能菜单网格
-->
<script setup lang="ts">
import { computed, ref, watch, onBeforeUnmount } from 'vue'
import { Icon } from '@iconify/vue'
import { HomeMoreDefaultConfig } from './HomeMore.config'
import type {
  HomeMoreConfig,
  HomeMoreData,
  HomeMoreMenuItem
} from './HomeMore.types.ts'


// ==================== Props定义 ====================
interface Props {
  /** 组件配置 */
  config?: HomeMoreConfig
  /** 外部数据（主要用于设计器预览） */
  data?: HomeMoreData
  /** 是否为设计器模式 */
  designMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  designMode: false
})

// ==================== 配置管理 ====================
// 合并默认配置和传入配置
const config = computed(() => ({
  ...HomeMoreDefaultConfig.config,
  ...props.config
}))

// ==================== 事件定义 ====================
// ✨ 统一事件架构：所有交互都通过click事件，完全数据驱动
const emit = defineEmits<{
  click: [eventData: {
    elementType: 'menu' | 'action'
    elementId: string
    elementData?: any
    componentType: string
  }]
}>()

// ==================== 状态管理 ====================
// 菜单项列表
const menuItems = ref<HomeMoreMenuItem[]>([])
let initializationTimer: ReturnType<typeof setTimeout> | null = null

// ==================== 计算属性 ====================
// 解析图标配置（支持 "icon|color" 格式）
const parseIconConfig = (iconValue: string) => {
  if (!iconValue) return { icon: '', color: '' }
  const parts = iconValue.split('|')
  return {
    icon: parts[0] || '',
    color: parts[1] || ''
  }
}

const gridStyle = computed(() => ({
  gridTemplateColumns: `repeat(${config.value.columns || 4}, 1fr)`,
  gap: config.value.styleConfig?.gap || '1rem'
}))

// ==================== 数据管理 ====================
// 初始化菜单项（防抖处理）
function initializeMenuItems() {
  // 清除之前的定时器
  if (initializationTimer) {
    clearTimeout(initializationTimer)
  }
  
  // 延迟执行，避免重复调用
  initializationTimer = setTimeout(() => {
    // config 已经有默认值，直接使用即可
    const items: HomeMoreMenuItem[] = config.value.menuItems || []

    console.log('🔄 HomeMore: 初始化菜单项', {
      原始数据: items,
      排序前: items.map(item => ({ id: item.id, name: item.name, order: item.order }))
    })

    // 过滤可见的菜单项并按order排序
    menuItems.value = items
      .filter((item: HomeMoreMenuItem) => item.visible !== false)
      .sort((a: HomeMoreMenuItem, b: HomeMoreMenuItem) => (a.order || 0) - (b.order || 0))

    console.log('✅ HomeMore: 菜单项排序完成', {
      排序后: menuItems.value.map(item => ({ id: item.id, name: item.name, order: item.order }))
    })
  }, 10) // 10ms 防抖
}

// 监听数据变化，统一处理
watch(() => [props.data, config.value.menuItems], ([newData, newMenuItems]) => {
  console.log('🔄 HomeMore: 监听到数据变化', {
    newData,
    newMenuItems: newMenuItems?.map(item => ({ id: item.id, name: item.name, order: item.order }))
  })
  initializeMenuItems()
}, { deep: true, immediate: true })

// 清理定时器
onBeforeUnmount(() => {
  if (initializationTimer) {
    clearTimeout(initializationTimer)
    initializationTimer = null
  }
})

// ==================== 事件处理 ====================
// 处理菜单项点击
function handleItemClick(item: HomeMoreMenuItem) {
  if (item.disabled) {
    return
  }

  // ✨ 统一click事件，携带完整的菜单项信息
  emit('click', {
    elementType: 'menu',
    elementId: item.id,
    elementData: item,
    componentType: 'HomeMore'
  })
}

// 处理菜单项长按
function handleItemLongPress(item: HomeMoreMenuItem) {
  // 长按也使用click事件，通过elementData区分
  emit('click', {
    elementType: 'menu',
    elementId: item.id,
    elementData: { ...item, isLongPress: true },
    componentType: 'HomeMore'
  })
}


</script>

<template>
  <div class="HomeMore">
    <!-- ✅ 完全复制device-an的头部结构 -->
    <div class="HomeMore-head" v-if="config.showTitle">{{ config.title }}</div>

    <!-- ✅ 完全复制device-an的功能网格 -->
    <div class="HomeMore-container" :style="gridStyle">
      <div
        class="HomeMore-container-box"
        v-for="item in menuItems"
        :key="item.id"
        :class="{
          'HomeMore-container-box--disabled': item.disabled,
          'HomeMore-container-box--new': item.isNew
        }"
        @click="() => handleItemClick(item)"
        @contextmenu.prevent="() => handleItemLongPress(item)"
      >
        <div class="HomeMore-container-box-icon">
          <Icon
            :icon="parseIconConfig(item.icon).icon || item.icon"
            :style="{
              color: parseIconConfig(item.icon).color || item.style?.color
            }"
          />
        </div>
        <div class="HomeMore-container-box-label">
          {{ item.name }}
        </div>
        <div v-if="item.isNew" class="HomeMore-container-box-badge">新</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* ✅ 完全复制device-an的HomeMore样式 */
@import '../../../styles/variables.scss';

.HomeMore {
  box-sizing: border-box;
  padding: $padding;
  background-color: #fff;

  &-head {
    font-size: 0.8rem;
  }

  &-container {
    display: grid;
    gap: calc($padding / 1.2);
    margin-top: $padding;

    &-box {
      text-align: center;
      cursor: pointer;

      &-icon {
        width: 2rem;
        height: 2rem;
        background-color: rgb(249, 250, 251);
        text-align: center;
        border-radius: 50%;
        font-size: 0.8rem;
        color: $primary;
        margin: 0 auto;
        line-height: 2rem;
      }

      &-label {
        font-size: 0.5rem;
        color: #666;
        margin-top: 0.1rem;
      }
    }
  }
}
</style>
