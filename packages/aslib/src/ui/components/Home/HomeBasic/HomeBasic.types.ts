/**
 * HomeBasic 组件类型定义
 */

/**
 * 可点击区域配置
 */
export interface ClickableArea {
  position: string          // 位置标识
  label: string            // 显示标签
  description: string      // 功能描述
  elementType: string      // 元素类型
  defaultId: string        // 默认ID建议
  eventId?: string         // 用户配置的事件ID
  enabled: boolean         // 是否启用
}

/**
 * HomeBasic 配置
 */
export interface HomeBasicConfig {
  /** 是否显示组件 */
  visible?: boolean

  /** 组件标题 */
  title?: string

  /** 是否显示网络状态 */
  showNetworkStatus?: boolean

  /** 是否显示流量进度 */
  showFlowProgress?: boolean

  /** 是否显示余额信息 */
  showBalance?: boolean

  /** 自定义文本 */
  customText?: {
    networkTitle?: string
    renewText?: string
    balanceLabel?: string
    rechargeText?: string
    moreText?: string
  }

  /** 网络标题 */
  networkTitle?: string

  /** 续费按钮文字 */
  renewText?: string

  /** 余额标签 */
  balanceLabel?: string

  /** 充值按钮文字 */
  rechargeText?: string

  /** 网络图标 */
  networkIcon?: string

  /** 更多按钮图标 */
  moreIcon?: string

  /** 充值按钮图标 */
  rechargeIcon?: string

  /** 样式配置 */
  styleConfig?: {
    /** 状态指示器颜色 */
    statusColors?: {
      online?: string
      offline?: string
      pending?: string
    }
    /** 进度条颜色 */
    progressColor?: string
    /** 是否显示动画效果 */
    showAnimation?: boolean
  }

  /** 可点击区域配置 */
  clickableAreas?: ClickableArea[]
}

/**
 * HomeBasic 数据
 */
export interface HomeBasicData {
  /** 设备状态 */
  status?: number
  
  /** 套餐名称 */
  packageName?: string
  
  /** 已使用流量 (MB) */
  vUseFlow?: number
  
  /** 总流量 (MB) */
  vTotalFlow?: number
  
  /** 剩余流量 (MB) */
  vResidueFlow?: number
  
  /** 账户余额 */
  balance?: number
  
  /** 设备编号 */
  deviceNo?: string
  
  /** 当前网络类型 */
  currentNetwork?: number
  
  /** 信号强度 */
  signalStrength?: number
  
  /** 网络状态 */
  networkStatus?: string
}

/**
 * 设备状态枚举
 */
export enum DeviceStatus {
  UNKNOWN = 1,
  PENDING = 2,    // 待激活
  ACTIVE = 3,     // 已激活
  SUSPENDED = 4   // 已停机
}

/**
 * 设备状态显示配置
 */
export const DEVICE_STATUS_CONFIG = {
  1: { label: '未知', class: 'error', color: '#9e9e9e' },
  2: { label: '待激活', class: 'warning', color: '#ff9800' },
  3: { label: '已激活', class: 'success', color: '#4caf50' },
  4: { label: '已停机', class: 'error', color: '#f44336' }
} as const

/**
 * 组件事件类型
 */
export interface HomeBasicEvents {
  /** 更多按钮点击 */
  more: []
  
  /** 立即续费按钮点击 */
  renew: []
  
  /** 充值按钮点击 */
  recharge: []
  
  /** 刷新数据 */
  refresh: []
  
  /** 数据加载完成 */
  dataLoaded: [data: HomeBasicData]
  
  /** 流量使用变化 */
  flowUsageChange: [usage: {
    used: number
    total: number
    percentage: number
  }]
}
