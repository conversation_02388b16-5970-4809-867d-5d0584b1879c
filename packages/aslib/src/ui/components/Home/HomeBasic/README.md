# HomeBasic 设备基础信息组件

## 📋 组件描述

HomeBasic组件用于显示设备的基础信息，包括网络连接状态、流量使用情况和账户余额。这是首页的核心组件，为用户提供设备状态的快速概览。

## 🎯 功能特性

- ✅ 显示设备网络连接状态
- ✅ 显示流量使用进度和剩余流量
- ✅ 显示账户余额信息
- ✅ 支持自定义文本和样式
- ✅ 响应式设计，适配移动端
- ✅ 支持设计器预览模式

## 📦 基础用法

```vue
<template>
  <HomeBasic 
    :config="homeConfig"
    :data="deviceData"
    @more="handleMore"
    @renew="handleRenew"
    @recharge="handleRecharge"
  />
</template>

<script setup>
import { HomeBasic } from '@lowcode/ui'

const homeConfig = {
  showNetworkStatus: true,
  showFlowProgress: true,
  showBalance: true,
  customText: {
    networkTitle: '网络连接',
    renewText: '立即续费',
    balanceLabel: '账户余额',
    rechargeText: '充值'
  }
}

const deviceData = {
  deviceStatus: 2, // 已激活
  packageName: '标准套餐',
  vUseFlow: 6860,
  vTotalFlow: 15360,
  balance: 88.50
}

function handleMore() {
  console.log('查看更多')
}

function handleRenew() {
  console.log('立即续费')
}

function handleRecharge() {
  console.log('余额充值')
}
</script>
```

## 🔧 配置选项

### HomeBasicConfig

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `visible` | `boolean` | `true` | 是否显示组件 |
| `title` | `string` | `'设备状态'` | 组件标题 |
| `showNetworkStatus` | `boolean` | `true` | 是否显示网络状态 |
| `showFlowProgress` | `boolean` | `true` | 是否显示流量进度 |
| `showBalance` | `boolean` | `true` | 是否显示余额信息 |
| `customText` | `object` | - | 自定义文本配置 |
| `styleConfig` | `object` | - | 样式配置 |

### customText 配置

```typescript
{
  networkTitle?: string    // 网络状态标题，默认：'网络连接'
  renewText?: string       // 续费按钮文本，默认：'立即续费'
  balanceLabel?: string    // 余额标签文本，默认：'账户余额'
  rechargeText?: string    // 充值按钮文本，默认：'充值'
  moreText?: string        // 更多按钮文本，默认：'更多'
}
```

### styleConfig 配置

```typescript
{
  statusColors?: {
    online?: string        // 在线状态颜色，默认：'#4caf50'
    offline?: string       // 离线状态颜色，默认：'#f44336'
    pending?: string       // 待激活状态颜色，默认：'#ff9800'
  }
  progressColor?: string   // 进度条颜色，默认：'#1976d2'
  showAnimation?: boolean  // 是否显示动画效果，默认：true
}
```

## 📡 事件

| 事件名 | 参数 | 描述 |
|--------|------|------|
| `more` | - | 用户点击更多按钮时触发 |
| `renew` | - | 用户点击立即续费按钮时触发 |
| `recharge` | - | 用户点击充值按钮时触发 |
| `refresh` | - | 刷新设备数据时触发 |
| `loaded` | `data: HomeBasicData` | 数据加载完成时触发 |
| `error` | `error: Error` | 组件出错时触发 |

## 📊 数据类型

### HomeBasicData

```typescript
interface HomeBasicData {
  deviceStatus?: number      // 设备状态：0-未知，1-待激活，2-已激活，3-已停机
  packageName?: string       // 套餐名称
  vUseFlow?: number         // 已使用流量 (MB)
  vTotalFlow?: number       // 总流量 (MB)
  vResidueFlow?: number     // 剩余流量 (MB)
  balance?: number          // 账户余额
  deviceNo?: string         // 设备编号
  currentNetwork?: number   // 当前网络类型
  signalStrength?: number   // 信号强度
  networkStatus?: string    // 网络状态
}
```

### 设备状态枚举

```typescript
enum DeviceStatus {
  UNKNOWN = 0,    // 未知
  PENDING = 1,    // 待激活
  ACTIVE = 2,     // 已激活
  SUSPENDED = 3   // 已停机
}
```

## 🎨 样式定制

### CSS变量

```css
.HomeBasic {
  --status-online-color: #4caf50;
  --status-offline-color: #f44336;
  --status-pending-color: #ff9800;
  --progress-color: #1976d2;
  --border-radius: 8px;
  --padding: 1rem;
}
```

### 自定义样式示例

```vue
<template>
  <HomeBasic 
    :config="customConfig"
    class="my-home-basic"
  />
</template>

<style>
.my-home-basic {
  --status-online-color: #00c853;
  --progress-color: #1976d2;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
```

## 🔄 数据流

1. **设计器模式**: 使用props传入的预览数据
2. **运行时模式**: 通过DataManager调用API获取真实数据
3. **数据合并**: Props数据优先级高于API数据
4. **自动计算**: 剩余流量 = 总流量 - 已用流量

## 📱 响应式设计

组件采用响应式设计，在不同屏幕尺寸下自动调整：

- **移动端**: 紧凑布局，优化触摸操作
- **平板端**: 适中间距，平衡显示效果
- **桌面端**: 宽松布局，完整信息展示

## 🧪 使用示例

### 基础示例

```vue
<HomeBasic />
```

### 自定义配置

```vue
<HomeBasic 
  :config="{
    showNetworkStatus: false,
    customText: {
      renewText: '购买套餐',
      rechargeText: '立即充值'
    }
  }"
/>
```

### 带数据的示例

```vue
<HomeBasic 
  :data="{
    deviceStatus: 2,
    packageName: '高级套餐',
    vUseFlow: 12000,
    vTotalFlow: 51200,
    balance: 150.00
  }"
/>
```

## 🐛 常见问题

### Q: 组件不显示数据？
A: 检查API客户端是否正确初始化，确保调用了`setAPIClient()`

### Q: 流量进度显示异常？
A: 检查`vTotalFlow`是否大于0，确保数据格式正确

### Q: 样式不生效？
A: 确保CSS变量正确设置，检查样式优先级

## 📚 相关文档

- [组件开发规范](../../../docs/COMPONENT_DEVELOPMENT_GUIDE.md)
- [API架构文档](../../../docs/API_ARCHITECTURE.md)
- [最佳实践指南](../../../docs/BEST_PRACTICES.md)
