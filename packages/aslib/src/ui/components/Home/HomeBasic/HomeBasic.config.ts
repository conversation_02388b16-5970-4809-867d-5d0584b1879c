// HomeBasic 组件配置和元数据

export const HomeBasicMetadata = {
  // 设计器必需的基本信息
  name: 'HomeBasic',
  displayName: '首页基础信息',
  category: 'home',
  icon: 'mdi:home-circle',
  description: '首页核心组件，显示网络连接状态、流量使用情况和账户余额',
  version: '1.0.0',
  tags: ['首页', '网络', '流量', '余额', '设备'],
  keywords: ['home', 'network', 'flow', 'balance', 'device']
}

// 默认配置 - 设计器期望的格式
export const HomeBasicDefaultConfig = {
  data: {
    deviceStatus: 1,
    packageName: '标准套餐2',
    vUseFlow: 6860,
    vTotalFlow: 15360,
    balance: 128.50
  },
  config: {
    showNetworkStatus: true,
    showFlowProgress: true,
    showBalance: true,
    networkTitle: '网络连接',
    renewText: '立即续费',
    balanceLabel: '账户余额',
    rechargeText: '充值',
    networkIcon: 'mdi:wifi',
    moreIcon: 'mdi:dots-horizontal',
    rechargeIcon: 'mdi:chevron-right',
    // 🎯 关键：预留的可点击区域配置
    clickableAreas: [
      {
        position: 'top-right',
        label: '右上角更多按钮',
        description: '组件右上角的更多按钮，点击查看设备详情',
        elementType: 'button',
        defaultId: 'more-button',
        eventId: 'more-button',
        enabled: true
      },
      {
        position: 'flow-area',
        label: '流量区域续费按钮',
        description: '流量显示区域的续费按钮，点击购买流量套餐',
        elementType: 'button',
        defaultId: 'renew-button',
        eventId: 'renew-button',
        enabled: true
      },
      {
        position: 'balance-area',
        label: '余额区域充值按钮',
        description: '余额显示区域的充值按钮，点击进行账户充值',
        elementType: 'button',
        defaultId: 'recharge-button',
        eventId: 'recharge-button',
        enabled: true
      }
    ]
  }
}

// 组件配置模式 (JSON Schema) - 保持兼容性
export const HomeBasicConfigSchema = {
  type: 'object' as const,
  properties: {
    data: {
      type: 'object' as const,
      title: '数据配置',
      properties: {
        deviceStatus: {
          type: 'number' as const,
          title: '设备状态',
          description: '1-在线，0-离线，2-连接中，3-异常',
          default: 1,
          enum: [0, 1, 2, 3]
        },
        packageName: {
          type: 'string' as const,
          title: '套餐名称',
          default: '标准套餐2'
        },
        vUseFlow: {
          type: 'number' as const,
          title: '已用流量(MB)',
          default: 6860,
          minimum: 0
        },
        vTotalFlow: {
          type: 'number' as const,
          title: '总流量(MB)',
          default: 15360,
          minimum: 1
        },
        balance: {
          type: 'number' as const,
          title: '账户余额',
          default: 128.50,
          minimum: 0
        }
      }
    },
    config: {
      type: 'object' as const,
      title: '组件配置',
      properties: {
        showNetworkStatus: {
          type: 'boolean' as const,
          title: '显示网络状态',
          default: true
        },
        showFlowProgress: {
          type: 'boolean' as const,
          title: '显示流量进度',
          default: true
        },
        showBalance: {
          type: 'boolean' as const,
          title: '显示账户余额',
          default: true
        },
        networkTitle: {
          type: 'string' as const,
          title: '网络标题',
          default: '网络连接'
        },
        renewText: {
          type: 'string' as const,
          title: '续费按钮文字',
          default: '立即续费'
        },
        balanceLabel: {
          type: 'string' as const,
          title: '余额标签',
          default: '账户余额'
        },
        rechargeText: {
          type: 'string' as const,
          title: '充值按钮文字',
          default: '充值'
        }
      }
    }
  }
}