<!--
  HomeBasic 组件
  显示设备基础信息，包括网络状态、流量使用情况和账户余额
-->
<script setup lang="ts">
import { computed, watch } from 'vue'
import { Icon } from '@iconify/vue'
import { useGlobalData } from '../../../../hooks'
import { toGB } from '../../../utils/formatters'
import { HomeBasicDefaultConfig } from './HomeBasic.config'
import type {
  HomeBasicConfig,
  HomeBasicData
} from './HomeBasic.types'
import { DEVICE_STATUS_CONFIG } from './HomeBasic.types'
import DxTag from '../../Common/DxTag/DxTag.vue'

// ==================== Props定义 ====================
interface Props {
  /** 组件配置 */
  config?: HomeBasicConfig
  /** 外部数据（主要用于设计器预览） */
  data?: HomeBasicData
  /** 是否为设计器模式 */
  designMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  designMode: false
})

// ==================== 配置管理 ====================
// 合并默认配置和传入配置
const config = computed(() => ({
  ...HomeBasicDefaultConfig.config,
  ...props.config
}))

// ==================== 事件定义 ====================
// ✨ 统一事件架构：所有交互都通过click事件，完全数据驱动
const emit = defineEmits<{
  click: [eventData: {
    elementType: 'button' | 'action'
    elementId: string
    elementData?: any
    componentType: string
  }]
}>()

// ==================== 状态管理 ====================
// 使用全局数据钩子
const { deviceData } = useGlobalData()

// 设备详情数据 - 使用全局数据
const deviceDetails = computed(() => {
  // 设计器模式：使用演示数据
  if (props.designMode) {
    return props.data || HomeBasicDefaultConfig.data
  }

  // H5端：优先使用全局数据
  if (deviceData.value.details && Object.keys(deviceData.value.details).length > 0) {
    const globalData = deviceData.value.details
    
    // 将全局数据映射到组件所需的格式
    return {
      status: globalData.status ,
      packageName: globalData.packageName ,
      vUseFlow: globalData.vUseFlow ,
      vTotalFlow: globalData.vTotalFlow ,
      balance: globalData.balance,
      vResidueFlow: globalData.vResidueFlow || (globalData.vTotalFlow - globalData.vUseFlow) 
    }
  }

  // 兜底：使用外部传入数据或默认数据
  return props.data || HomeBasicDefaultConfig.data
})

// ==================== 计算属性 ====================
// 解析图标配置（支持 "icon|color" 格式）
const parseIconConfig = (iconValue: string) => {
  if (!iconValue) return { icon: '', color: '' }
  const parts = iconValue.split('|')
  return {
    icon: parts[0] || '',
    color: parts[1] || ''
  }
}

// 解析各个图标配置
const networkIconConfig = computed(() => parseIconConfig(config.value.networkIcon || ''))
const moreIconConfig = computed(() => parseIconConfig(config.value.moreIcon || ''))
const rechargeIconConfig = computed(() => parseIconConfig(config.value.rechargeIcon || ''))

// 当前设备状态
const currentStatus = computed(() => {
  const status = deviceDetails.value?.status || 1
  return DEVICE_STATUS_CONFIG[status as keyof typeof DEVICE_STATUS_CONFIG] || DEVICE_STATUS_CONFIG[1]
})

// 流量进度百分比
const progress = computed(() => {
  const { vResidueFlow, vTotalFlow } = deviceDetails.value
  if (!vTotalFlow || !vResidueFlow) return '0%'

  const result = (vResidueFlow / vTotalFlow) * 100
  return isNaN(result) ? '0%' : result.toFixed(2) + '%'
})

// ==================== 数据监听 ====================
// 注意：deviceDetails 现在是计算属性，会自动响应全局数据变化
// 外部传入数据的监听（主要用于设计器模式）
watch(() => props.data, (newData) => {
  // 在计算属性中已经处理了 props.data，这里不需要额外处理
  console.log('🔄 HomeBasic: 外部数据变化', newData)
}, { deep: true, immediate: true })

// ==================== 可点击区域配置 ====================
// ✨ 获取配置的可点击区域列表
const configuredAreas = computed(() => {
  return config.value.clickableAreas || []
})

// ✨ 获取特定位置的区域配置
function getAreaConfig(position: string) {
  return configuredAreas.value.find(area => area.position === position && area.enabled)
}

// ==================== 事件处理 ====================
// ✨ 统一的区域点击处理器
function handleAreaClick(position: string) {
  const areaConfig = getAreaConfig(position)
  if (!areaConfig) {
    console.warn(`未找到位置为 ${position} 的区域配置`)
    return
  }

  emit('click', {
    elementType: areaConfig.elementType as 'button' | 'action',
    elementId: areaConfig.eventId || areaConfig.defaultId,
    elementData: areaConfig,
    componentType: 'HomeBasic'
  })
}

// ✨ 具体的按钮处理函数
function handleMore() {
  handleAreaClick('top-right')
}

function handleRenew() {
  handleAreaClick('flow-area')
}

function handleRecharge() {
  handleAreaClick('balance-area')
}
</script>

<template>
  <div class="HomeBasic">
    <!-- ✅ 完全复制device-an的头部结构 -->
    <div class="HomeBasic-head" v-if="config.showNetworkStatus">
      <div class="HomeBasic-head-left">
        <Icon
          class="HomeBasic-head-left-icon"
          :icon="networkIconConfig.icon"
          :style="networkIconConfig.color ? { color: networkIconConfig.color } : {}"
        />
        <div class="HomeBasic-head-left-tit">{{ config.networkTitle }}</div>
        <DxTag :txt="currentStatus.label" :class="currentStatus.class" />
      </div>
      <div class="HomeBasic-head-right" @click="handleMore">
        <Icon
          class="HomeBasic-head-right-icon"
          :icon="moreIconConfig.icon"
          :style="moreIconConfig.color ? { color: moreIconConfig.color } : {}"
        />
      </div>
    </div>

    <!-- ✅ 完全复制device-an的流量显示结构 -->
    <div class="HomeBasic-box" v-if="config.showFlowProgress">
      <div class="HomeBasic-box-label">
        <div class="HomeBasic-box-label-left">剩余流量</div>
        <div class="HomeBasic-box-label-right">套餐总量</div>
      </div>

      <div class="HomeBasic-box-data">
        <div class="HomeBasic-box-data-remain">
          {{ toGB(deviceDetails.vResidueFlow || 0).join('') }}
        </div>

        <div class="HomeBasic-box-data-total">
          <div class="HomeBasic-box-data-total-use">
            {{ toGB(deviceDetails.vUseFlow || 0).join('') }}
          </div>

          <div class="HomeBasic-box-data-total-total">
            {{ '&nbsp;/&nbsp;' + toGB(deviceDetails.vTotalFlow || 0).join('') }}
          </div>
        </div>
      </div>

      <div class="HomeBasic-box-progress" :style="{
        '--progress': progress
      }" />

      <div class="HomeBasic-box-bottom">
        <div class="HomeBasic-box-bottom-left">已用{{ progress }}</div>
        <div class="HomeBasic-box-bottom-right" @click="handleRenew">{{ config.renewText }}</div>
      </div>
    </div>

    <!-- ✅ 完全复制device-an的余额显示结构 -->
    <div class="HomeBasic-balance" v-if="config.showBalance">
      <div class="HomeBasic-balance-head">
        <div class="HomeBasic-balance-head-label">{{ config.balanceLabel }}</div>
        <div class="HomeBasic-balance-head-recharge" @click="handleRecharge">
          {{ config.rechargeText }}
          <Icon
            :icon="rechargeIconConfig.icon"
            class="HomeBasic-balance-head-recharge-icon"
            :style="rechargeIconConfig.color ? { color: rechargeIconConfig.color } : {}"
          />
        </div>
      </div>

      <div class="HomeBasic-balance-bottom">
        <div class="HomeBasic-balance-bottom-money">
          <span>￥</span>{{ deviceDetails.balance }}
        </div>
        <div class="HomeBasic-balance-bottom-tip">可用于购买套餐</div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '../../../styles/variables.scss';
@import '../../../styles/common.scss';

.HomeBasic {
  box-sizing: border-box;
  padding: $padding;
  border-bottom: 0.01rem solid $border;
  background-color: #fff;

  &-head {
    @include FlexBetween;
    font-size: 0.9rem;

    &-left {
      @include FlexStart;

      &-icon {
        color: $primary;
        font-size: 1rem;
      }

      &-tit {
        margin: 0 0.4rem;
        font-size: 0.8rem;
      }
    }

    &-right {
      @include FlexCenter;
      height: 1.3rem;
      width: 1.3rem;
      background-color: $background;
      border-radius: 0.65rem;
      color: #999;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: var(--color-border);
        color: var(--color-primary);
      }
    }
  }

  &-box {
    @include PaddingBox;
    border-radius: $radius;
    background-color: $box-background;
    margin-top: $padding;

    &-label {
      @include FlexBetween;
      color: var(--color-text-secondary);
      font-size: 0.7rem;
    }

    &-data {
      margin: calc($padding / 2) 0;
      @include FlexBetween;

      &-remain {
        font-weight: bold;
        color: var(--color-text-primary);
      }

      &-total {
        @include FlexStart;
        font-size: 0.7rem;

        &-use {
          color: $primary;
        }

        &-total {
          color: var(--color-text-secondary);
        }
      }
    }

    &-progress {
      height: 0.8rem;
      border-radius: 0.4rem;
      background-color: rgb(229, 231, 235);
      position: relative;
      overflow: hidden;

      &::after {
        display: block;
        content: '';
        position: absolute;
        transform: translateX(-100%);
        top: 0;
        left: var(--progress);
        width: 100%;
        height: 100%;
        border-radius: inherit;
        background-color: $primary;
      }
    }

    &-bottom {
      margin-top: calc($padding / 2);
      @include FlexBetween;
      font-size: 0.6rem;

      &-left {
        color: var(--color-text-secondary);
      }

      &-right {
        color: $primary;
        cursor: pointer;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius-sm);
        transition: all 0.3s ease;

        &:hover {
          background-color: rgba($primary, 0.1);
        }
      }
    }
  }

  &-balance {
    @include PaddingBox;
    border-radius: $radius;
    background-color: $box-background;
    margin-top: $padding;

    &-head,
    &-bottom {
      @include FlexBetween;
    }

    &-head {
      &-label {
        font-size: 0.7rem;
        color: var(--color-text-secondary);
      }

      &-recharge {
        @include FlexCenter;
        font-size: 0.6rem;
        color: $primary;
        cursor: pointer;
        padding: var(--spacing-xs);
        border-radius: var(--border-radius-sm);
        transition: all 0.3s ease;

        &:hover {
          background-color: rgba($primary, 0.1);
        }

        &-icon {
          font-size: 0.5rem;
          margin-left: 2px;
        }
      }
    }

    &-bottom {
      margin-top: calc($padding / 1.5);

      &-money {
        font-size: 1.5rem;
        font-weight: bold;
        color: var(--color-text-primary);

        span {
          font-size: 0.6rem;
        }
      }

      &-tip {
        font-size: 0.65rem;
        color: $primary;
        height: 1.6rem;
        line-height: 1.6rem;
        background-color: rgba($primary, 0.2);
        border-radius: 0.3rem;
        padding: 0 calc($padding / 2);
        white-space: nowrap;
      }
    }
  }
}
</style>
