// HomeBasic 组件入口文件

import HomeBasicVue from './HomeBasic.vue'
import { HomeBasicMetadata, HomeBasicDefaultConfig, HomeBasicConfigSchema } from './HomeBasic.config'

// 创建带有配置的组件对象
const HomeBasic = HomeBasicVue as any
HomeBasic.__lowcodeMetadata = HomeBasicMetadata
HomeBasic.__defaultConfig = { props: HomeBasicDefaultConfig, style: {} }
HomeBasic.__configSchema = HomeBasicConfigSchema

// 导出组件
export default HomeBasic
export { HomeBasic }

// 导出配置和元数据
export { HomeBasicMetadata, HomeBasicDefaultConfig, HomeBasicConfigSchema }

// 导出类型定义
export interface HomeBasicProps {
  config?: {
    showNetworkStatus?: boolean
    showFlowProgress?: boolean
    showBalance?: boolean
    customText?: {
      networkTitle?: string
      renewText?: string
      balanceLabel?: string
      rechargeText?: string
    }
    networkTitle?: string
    renewText?: string
    balanceLabel?: string
    rechargeText?: string
    networkIcon?: string
    moreIcon?: string
    rechargeIcon?: string
  }
}

export interface HomeBasicEvents {
  more: () => void
  renew: () => void
  recharge: () => void
}

// 组件安装函数
export function installHomeBasic(app: any) {
  app.component('HomeBasic', HomeBasic)
}
