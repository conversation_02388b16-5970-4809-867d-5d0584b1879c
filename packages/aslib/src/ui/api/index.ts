/**
 * API客户端抽象接口
 * 用于在UI组件中调用API，由应用层注入具体实现
 */

// API响应格式
export interface APIResponse<T = any> {
  code: number | boolean
  data: T
  msg: string
}

// API客户端接口
export interface APIClient {
  // 套餐相关
  getPackageList(params?: { packType?: number } | { classId?: number }): Promise<APIResponse<any[]>>

  // 支付相关
  getPaymentMethods(params: { userId: number; type: number }): Promise<APIResponse<any[]>>
  createPayment(params: {
    orderType: number
    payType: string
    orderNo: string
    notifyUrl: string
    payId: number
    type?: number
    payWay?: string
  }): Promise<APIResponse<any>>

  // 余额支付相关
  balancePaymentOrder(params: {
    orderNo: string
    payPwd?: string
    code?: string
  }): Promise<APIResponse<any>>

  // 设备相关
  getDeviceInfo(): Promise<APIResponse<any>>
  getDeviceCards(): Promise<APIResponse<any[]>>

  // 实名认证相关
  getDeviceCardRealNameAddress(params: {
    deviceId: number
    number: number
  }): Promise<APIResponse<any>>
}

// 全局API客户端实例
let apiClientInstance: APIClient | null = null

// 设置API客户端
export function setAPIClient(client: APIClient) {
  apiClientInstance = client
}

// 获取API客户端
export function getAPIClient(): APIClient {
  if (!apiClientInstance) {
    throw new Error('API客户端未初始化，请先调用 setAPIClient()')
  }
  return apiClientInstance
}

// 导出默认的API客户端获取函数
export const apiClient = {
  get instance() {
    return getAPIClient()
  }
}
