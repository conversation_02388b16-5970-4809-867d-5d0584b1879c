// 专门的工具函数导出文件

// 数据管理和环境配置
export { setDataManagerConfig, createDataManager, getCurrentEnvironment } from './managers/DataManager'

// API接口管理
export { setAPIClient, getAPIClient } from './api'

// 组件事件定义
export { ComponentEvents, getComponentEvents, getAllComponentTypes } from './events/componentEvents'

// 工具函数 - 直接从源文件导入避免循环依赖
export {
  copyToClipboard,
  readFromClipboard,
  isClipboardSupported
} from './utils/clipboard'

export {
  toGB,
  formatFlow,
  formatPercentage,
  formatTime,
  formatCurrency,
  formatPhone,
  formatDeviceNo,
  formatICCID,
  formatFileSize,
  formatNumber,
  truncateText,
  formatSignalStrength,
  formatNetworkType
} from './utils/formatters'

// 内部UI组件（供业务组件使用，不参与低代码拖拽）
export { default as SvgIcon } from './components/Common/SvgIcon'
export { default as DxTag } from './components/Common/DxTag'

// 类型定义
export type { Environment } from './managers/DataManager'
export type { APIClient, APIResponse } from './api'
export type { SvgIconProps } from './components/Common/SvgIcon'
export type { DxTagProps } from './components/Common/DxTag'

// 版本信息
export const version = '0.1.0'
