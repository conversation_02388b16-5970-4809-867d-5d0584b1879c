// 组件事件信息定义
export interface ComponentEventInfo {
  name: string          // 事件显示名称
  description: string   // 事件描述
  trigger: string       // 触发条件说明
  params?: string       // 事件参数说明
}

// 默认事件配置类型（与设计器兼容）
export interface DefaultEventConfig {
  type: 'navigate' | 'custom'
  navigateType?: 'page' | 'external' | 'webview' | 'back'
  target?: string
  method?: string
  onSuccess?: string
  params?: Record<string, any>
  preset?: string
  handler?: string
  webviewTitle?: string
  webviewOptions?: string[]
  // 自定义代码相关
  code?: string
  // 预设操作相关
  message?: string
  confirmTitle?: string
  confirmMessage?: string
  confirmAction?: string
  navigateUrl?: string
  successMessage?: string
  text?: string
  fileUrl?: string
  modalTitle?: string
  modalContent?: string
  targetId?: string
  // 菜单项绑定相关
  menuItemId?: string
  // 统一事件相关
  elementType?: string
  elementId?: string
}

// ✨ 统一事件信息定义
export const ComponentEvents: Record<string, Record<string, ComponentEventInfo>> = {
  HomeBasic: {
    click: {
      name: '统一点击事件',
      description: '用户点击组件内的任意可交互元素（按钮、链接等）',
      trigger: '用户点击时触发，携带元素类型和ID信息',
      params: 'eventData: { elementType: string, elementId: string, elementData?: any, componentType: string }'
    }
  },

  HomeMore: {
    click: {
      name: '统一点击事件',
      description: '用户点击菜单项或其他可交互元素',
      trigger: '用户点击时触发，携带元素类型和ID信息',
      params: 'eventData: { elementType: string, elementId: string, elementData?: any, componentType: string }'
    }
  },

  HomeNetWork: {
    click: {
      name: '统一点击事件',
      description: '用户点击网络相关操作（切换网络、充值等）',
      trigger: '用户点击时触发，携带元素类型和ID信息',
      params: 'eventData: { elementType: string, elementId: string, elementData?: any, componentType: string }'
    }
  },

  HomeRealName: {
    click: {
      name: '统一点击事件',
      description: '用户点击实名认证相关操作',
      trigger: '用户点击时触发，携带元素类型和ID信息',
      params: 'eventData: { elementType: string, elementId: string, elementData?: any, componentType: string }'
    }
  },

  HomeDetails: {
    click: {
      name: '统一点击事件',
      description: '用户点击设备信息相关操作（展开收起、WiFi切换、复制等）',
      trigger: '用户点击时触发，携带元素类型和ID信息',
      params: 'eventData: { elementType: string, elementId: string, elementData?: any, componentType: string }'
    }
  }
}


// 获取指定组件的事件信息
export function getComponentEvents(componentType: string): Record<string, ComponentEventInfo> {
  return ComponentEvents[componentType] || {}
}

// 获取所有组件类型
export function getAllComponentTypes(): string[] {
  return Object.keys(ComponentEvents)
}

// 检查组件是否有事件
export function hasEvents(componentType: string): boolean {
  const events = getComponentEvents(componentType)
  return Object.keys(events).length > 0
}

// ==================== 设计器配置选项 ====================

/**
 * 事件类型选项
 */
export const EventTypeOptions = [
  { label: '页面导航', value: 'navigate' },
  { label: '自定义代码', value: 'custom' }
]

/**
 * 导航方式选项
 */
export const NavigateTypeOptions = [
  { label: '页面跳转', value: 'page' },
  { label: '外部链接（新窗口）', value: 'external' },
  { label: '外部链接（内嵌页面）', value: 'webview' },
  { label: '返回上页', value: 'back' }
]

/**
 * 页面路由选项（基于实际的H5页面路由）
 */
export const PageOptions = [
  // 主要页面
  { label: '首页', value: '/home' },
  { label: '登录页', value: '/login' },

  // 套餐相关
  { label: '套餐列表', value: '/PackageList' },
  { label: '套餐支付', value: '/PackagePayment' },
  { label: '套餐订单', value: '/PackageOrder' },

  // 余额相关
  { label: '余额充值', value: '/BalanceList' },
  { label: '余额明细', value: '/BalanceDetails' },
  { label: '余额支付', value: '/BalancePayment' },

  // 实名认证
  { label: '实名认证', value: '/RealName' },
  { label: '实名卡片', value: '/RealNameCards' },

  // 设备相关
  { label: '设备设置', value: '/EditDevice' },
  { label: '支付密码', value: '/EditPassword' },
  { label: '设备通知', value: '/DeviceNotice' },

  // 服务相关
  { label: '联系客服', value: '/LayoutService' },

  // 支付相关
  { label: '微信支付', value: '/wechat-payment' },
  { label: '扫码支付', value: '/qrcode-payment' },
  { label: '内嵌网页', value: '/webview' },

  // 动态页面
  { label: '动态页面', value: '/page/' },
  { label: '应用页面', value: '/app/' }
]

/**
 * UI操作选项
 */
export const UIActionOptions = [
  { label: '显示消息', value: 'showMessage' },
  { label: '确认对话框', value: 'showConfirm' },
  { label: '复制文本', value: 'copyText' },
  { label: '下载文件', value: 'downloadFile' },
  { label: '打开模态框', value: 'openModal' },
  { label: '切换组件显示', value: 'toggleComponent' }
]

/**
 * API操作选项
 */
export const APIActionOptions = [
  { label: '刷新设备信息', value: 'refreshDevice' },
  { label: '同步数据', value: 'syncData' },
  { label: '更新状态', value: 'updateStatus' }
]

/**
 * 预设操作选项（向后兼容）
 * @deprecated 请使用 UIActionOptions 和 APIActionOptions
 */
export const PresetOptions = [
  { label: '自定义代码', value: '' },
  ...UIActionOptions,
  ...APIActionOptions
]

// ==================== 默认事件配置生成 ====================

/**
 * 创建默认事件配置（与设计器兼容）
 */
export function createDefaultEventConfig(eventName: string, _componentType?: string): DefaultEventConfig {
  // 基础配置
  const baseConfig = {
    method: 'GET',
    onSuccess: 'none',
    params: {},
    handler: '',
    webviewOptions: ['showNavBar', 'showBackButton']
  }

  // 根据事件名称定制默认行为
  switch (eventName) {
    // 页面跳转类事件
    case 'more':
      return {
        type: 'navigate' as const,
        navigateType: 'page',
        target: '/home',
        webviewTitle: '更多功能',
        ...baseConfig
      }

    case 'renew':
    case 'package':
      return {
        type: 'navigate' as const,
        navigateType: 'page',
        target: '/PackageList',
        webviewTitle: '套餐充值',
        ...baseConfig
      }

    case 'recharge':
    case 'balance':
      return {
        type: 'navigate' as const,
        navigateType: 'page',
        target: '/BalanceList',
        webviewTitle: '余额充值',
        ...baseConfig
      }

    case 'wifi':
      return {
        type: 'navigate' as const,
        navigateType: 'page',
        target: '/EditDevice',
        webviewTitle: 'Wi-Fi设置',
        ...baseConfig
      }

    case 'history':
      return {
        type: 'navigate' as const,
        navigateType: 'page',
        target: '/BalanceDetails',
        webviewTitle: '余额明细',
        ...baseConfig
      }

    case 'password':
      return {
        type: 'navigate' as const,
        navigateType: 'page',
        target: '/EditPassword',
        webviewTitle: '支付密码',
        ...baseConfig
      }

    case 'service':
      return {
        type: 'navigate' as const,
        navigateType: 'page',
        target: '/LayoutService',
        webviewTitle: '联系客服',
        ...baseConfig
      }

    case 'refresh':
      return {
        type: 'custom' as const,
        navigateType: 'page',
        preset: 'showMessage',
        message: '正在刷新设备信息...',
        webviewTitle: '',
        ...baseConfig
      }

    case 'switch-network':
      return {
        type: 'custom' as const,
        navigateType: 'page',
        preset: 'showMessage',
        message: '网络切换功能开发中...',
        webviewTitle: '',
        ...baseConfig
      }

    case 'realNameClick':
      return {
        type: 'navigate' as const,
        navigateType: 'page',
        target: '/RealNameCards',
        webviewTitle: '实名卡片',
        ...baseConfig
      }

    // ✨ 统一click事件配置
    case 'click':
      return {
        type: 'navigate' as const,
        navigateType: 'page',
        target: '/default',
        elementType: 'button',
        elementId: '',
        webviewTitle: '',
        ...baseConfig
      }

    // 默认配置
    default:
      return {
        type: 'custom' as const,
        navigateType: 'page',
        preset: 'showMessage',
        message: `${eventName}事件被触发`,
        webviewTitle: '',
        ...baseConfig
      }
  }
}
