// 专门的组件导出文件 - 只导出真正的业务组件

// 首页业务组件（完整功能）
export { default as HomeBasic } from './components/Home/HomeBasic'
export { default as HomeNetWork } from './components/Home/HomeNetWork'
export { default as HomeMore } from './components/Home/HomeMore'
export { default as HomeDetails } from './components/Home/HomeDetails'
export { default as HomeRealName } from './components/Home/HomeRealName'

// 注意：SvgIcon 和 DxTag 是内部UI组件，不参与低代码拖拽

// 业务组件类型定义
export type {
  HomeBasicProps,
  HomeBasicEvents
} from './components/Home/HomeBasic'

export type {
  HomeNetWorkProps,
  HomeNetWorkEvents
} from './components/Home/HomeNetWork'

export type {
  HomeMoreProps,
  HomeMoreEvents
} from './components/Home/HomeMore'

export type {
  HomeDetailsProps,
  HomeDetailsEvents
} from './components/Home/HomeDetails'

export type {
  HomeRealNameProps,
  HomeRealNameEvents
} from './components/Home/HomeRealName'

// 业务组件安装函数
import type { App } from 'vue'
import HomeBasicComponent from './components/Home/HomeBasic'
import HomeNetWorkComponent from './components/Home/HomeNetWork'
import HomeMoreComponent from './components/Home/HomeMore'
import HomeDetailsComponent from './components/Home/HomeDetails'
import HomeRealNameComponent from './components/Home/HomeRealName'

const components = [
  HomeBasicComponent,
  HomeNetWorkComponent,
  HomeMoreComponent,
  HomeDetailsComponent,
  HomeRealNameComponent
]

const install = (app: App) => {
  components.forEach(component => {
    const name = component.name || component.__name || 'UnknownComponent'
    app.component(name, component)
  })
}

export default {
  install
}
