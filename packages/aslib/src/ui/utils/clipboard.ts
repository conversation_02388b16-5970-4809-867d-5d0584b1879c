/**
 * 剪贴板工具函数
 */

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @returns Promise<boolean> 是否复制成功
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    // 优先使用现代 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      return true
    }
    
    // 降级方案：使用 document.execCommand
    return fallbackCopyToClipboard(text)
  } catch (error) {
    console.warn('复制到剪贴板失败:', error)
    return fallbackCopyToClipboard(text)
  }
}

/**
 * 降级复制方案
 * @param text 要复制的文本
 * @returns boolean 是否复制成功
 */
function fallbackCopyToClipboard(text: string): boolean {
  try {
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    
    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)
    
    return successful
  } catch (error) {
    console.warn('降级复制方案失败:', error)
    return false
  }
}

/**
 * 读取剪贴板内容
 * @returns Promise<string> 剪贴板内容
 */
export async function readFromClipboard(): Promise<string> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      return await navigator.clipboard.readText()
    }
    throw new Error('Clipboard API not available')
  } catch (error) {
    console.warn('读取剪贴板失败:', error)
    return ''
  }
}

/**
 * 检查是否支持剪贴板操作
 * @returns boolean 是否支持
 */
export function isClipboardSupported(): boolean {
  return !!(navigator.clipboard || document.execCommand)
}

/**
 * 复制对象为JSON字符串
 * @param obj 要复制的对象
 * @returns Promise<boolean> 是否复制成功
 */
export async function copyObjectAsJSON(obj: any): Promise<boolean> {
  try {
    const jsonString = JSON.stringify(obj, null, 2)
    return await copyToClipboard(jsonString)
  } catch (error) {
    console.warn('复制对象失败:', error)
    return false
  }
}

/**
 * 复制并显示提示消息
 * @param text 要复制的文本
 * @param successMessage 成功提示消息
 * @param errorMessage 失败提示消息
 * @returns Promise<boolean> 是否复制成功
 */
export async function copyWithFeedback(
  text: string, 
  successMessage: string = '复制成功',
  errorMessage: string = '复制失败'
): Promise<boolean> {
  const success = await copyToClipboard(text)
  
  // 这里可以集成具体的提示组件
  if (success) {
    console.log(successMessage)
    // TODO: 显示成功提示
  } else {
    console.error(errorMessage)
    // TODO: 显示错误提示
  }
  
  return success
}
