/**
 * 通用格式化工具函数
 */

/**
 * 将MB转换为合适的单位显示
 * @param mb 流量大小(MB)
 * @returns [数值, 单位]
 */
export function toGB(mb: number): string[] {
  if (mb >= 1024) {
    return [(mb / 1024).toFixed(1), 'GB']
  }
  return [mb.toString(), 'MB']
}

/**
 * 格式化流量显示
 * @param mb 流量大小(MB)
 * @returns 格式化后的字符串，如 "1.5GB" 或 "500MB"
 */
export function formatFlow(mb: number): string {
  const [value, unit] = toGB(mb)
  return `${value}${unit}`
}

/**
 * 格式化百分比
 * @param value 数值
 * @param total 总数
 * @param decimals 小数位数，默认2位
 * @returns 百分比字符串，如 "85.50%"
 */
export function formatPercentage(value: number, total: number, decimals: number = 2): string {
  if (!total || total === 0) return '0%'
  const percentage = (value / total) * 100
  return `${percentage.toFixed(decimals)}%`
}

/**
 * 格式化时间显示
 * @param dateStr 时间字符串
 * @param format 格式类型
 * @returns 格式化后的时间字符串
 */
export function formatTime(dateStr: string, format: 'date' | 'datetime' | 'relative' = 'date'): string {
  if (!dateStr) return '未知'
  
  try {
    const date = new Date(dateStr)
    
    switch (format) {
      case 'date':
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        })
      case 'datetime':
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      case 'relative':
        const now = new Date()
        const diff = now.getTime() - date.getTime()
        const days = Math.floor(diff / (1000 * 60 * 60 * 24))
        
        if (days === 0) return '今天'
        if (days === 1) return '昨天'
        if (days < 7) return `${days}天前`
        if (days < 30) return `${Math.floor(days / 7)}周前`
        if (days < 365) return `${Math.floor(days / 30)}个月前`
        return `${Math.floor(days / 365)}年前`
      default:
        return dateStr
    }
  } catch {
    return dateStr
  }
}

/**
 * 格式化金额显示
 * @param amount 金额
 * @param currency 货币符号，默认￥
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的金额字符串
 */
export function formatCurrency(amount: number, currency: string = '￥', decimals: number = 2): string {
  return `${currency}${amount.toFixed(decimals)}`
}

/**
 * 格式化手机号显示（中间4位用*替代）
 * @param phone 手机号
 * @returns 格式化后的手机号
 */
export function formatPhone(phone: string): string {
  if (!phone || phone.length !== 11) return phone
  return `${phone.slice(0, 3)}****${phone.slice(7)}`
}

/**
 * 格式化设备编号显示（只显示前4位和后4位）
 * @param deviceNo 设备编号
 * @returns 格式化后的设备编号
 */
export function formatDeviceNo(deviceNo: string): string {
  if (!deviceNo || deviceNo.length < 8) return deviceNo
  const start = deviceNo.slice(0, 4)
  const end = deviceNo.slice(-4)
  const middle = '*'.repeat(Math.max(0, deviceNo.length - 8))
  return `${start}${middle}${end}`
}

/**
 * 格式化ICCID显示
 * @param iccid ICCID
 * @returns 格式化后的ICCID
 */
export function formatICCID(iccid: string): string {
  if (!iccid) return iccid
  // 每4位添加一个空格
  return iccid.replace(/(.{4})/g, '$1 ').trim()
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${(bytes / Math.pow(k, i)).toFixed(1)} ${sizes[i]}`
}

/**
 * 格式化数字显示（添加千分位分隔符）
 * @param num 数字
 * @returns 格式化后的数字字符串
 */
export function formatNumber(num: number): string {
  return num.toLocaleString('zh-CN')
}

/**
 * 截断文本并添加省略号
 * @param text 文本
 * @param maxLength 最大长度
 * @param suffix 后缀，默认...
 * @returns 截断后的文本
 */
export function truncateText(text: string, maxLength: number, suffix: string = '...'): string {
  if (!text || text.length <= maxLength) return text
  return text.slice(0, maxLength - suffix.length) + suffix
}

/**
 * 格式化信号强度显示
 * @param strength 信号强度 (0-5)
 * @returns 信号强度描述
 */
export function formatSignalStrength(strength: number): string {
  const levels = ['无信号', '信号差', '信号一般', '信号良好', '信号优秀', '信号满格']
  return levels[Math.max(0, Math.min(5, strength))] || '未知'
}

/**
 * 格式化网络类型显示
 * @param networkType 网络类型
 * @returns 格式化后的网络类型
 */
export function formatNetworkType(networkType: string): string {
  const typeMap: Record<string, string> = {
    '2G': '2G网络',
    '3G': '3G网络', 
    '4G': '4G网络',
    '5G': '5G网络',
    'WiFi': 'WiFi网络',
    'unknown': '未知网络'
  }
  return typeMap[networkType] || networkType
}
