// 真正的工具函数统一导出
import { ref, readonly } from 'vue'

// 数据管理器相关 (从managers导入)
export {
  DataManager,
  dataManager,
  createDataManager,
  getCurrentEnvironment
} from '../managers/DataManager'

// 组件事件相关 (从events导入)
export {
  ComponentEvents,
  getComponentEvents,
  getAllComponentTypes,
  hasEvents,
  createDefaultEventConfig,
  // 设计器配置选项
  EventTypeOptions,
  NavigateTypeOptions,
  PageOptions,
  PresetOptions,
  // 类型定义
  type ComponentEventInfo,
  type DefaultEventConfig
} from '../events/componentEvents'

// API相关
export {
  setAPIClient,
  getAPIClient,
  apiClient
} from '../api'

// 真正的工具函数
export {
  copyToClipboard,
  readFromClipboard,
  isClipboardSupported
} from './clipboard'

export {
  toGB,
  formatFlow,
  formatPercentage,
  formatTime,
  formatCurrency,
  formatPhone,
  formatDeviceNo,
  formatICCID,
  formatFileSize,
  formatNumber,
  truncateText,
  formatSignalStrength,
  formatNetworkType
} from './formatters'

// 环境类型
export type Environment = 'designer' | 'runtime'

// 数据管理器配置接口
interface DataManagerConfig {
  environment: Environment
  mockData?: Record<string, any>
}

// 全局配置
let globalConfig: DataManagerConfig = {
  environment: 'runtime'
}

/**
 * 设置数据管理器环境配置
 * 保持原有API不变
 */
export function setDataManagerConfig(config: DataManagerConfig) {
  globalConfig = { ...globalConfig, ...config }
  console.log(`📊 数据管理器环境设置为: ${config.environment}`)
}

// ==================== 组件开发规范 ====================

/**
 * 标准组件配置接口
 * 所有组件都应该继承这个基础配置
 */
export interface BaseComponentConfig {
  /** 是否显示组件 */
  visible?: boolean
  /** 自定义CSS类名 */
  className?: string
  /** 自定义样式 */
  style?: Record<string, any>
  /** 组件标题 */
  title?: string
  /** 组件描述 */
  description?: string
}

/**
 * 标准组件Props接口
 * 所有组件都应该继承这个基础Props
 */
export interface BaseComponentProps {
  /** 组件配置 */
  config?: BaseComponentConfig
  /** 外部数据（主要用于设计器预览） */
  data?: Record<string, any>
  /** 是否为设计器模式 */
  designMode?: boolean
}

/**
 * 标准组件事件接口
 * 定义组件可以发射的事件类型
 */
export interface BaseComponentEvents {
  /** 组件加载完成 */
  loaded: [data?: any]
  /** 组件出错 */
  error: [error: Error]
  /** 组件点击 */
  click: [event: MouseEvent]
  /** 组件数据变化 */
  change: [data: any]
}

/**
 * 组件事件常量定义
 * 统一管理所有组件的事件名称
 */
export const COMPONENT_EVENTS = {
  // 基础事件
  BASE: {
    LOADED: 'loaded',
    ERROR: 'error',
    CLICK: 'click',
    CHANGE: 'change'
  },

  // 设备相关事件
  DEVICE: {
    REFRESH: 'refresh',
    MORE: 'more',
    SETTINGS: 'settings',
    STATUS_CHANGE: 'statusChange'
  },

  // 套餐相关事件
  PACKAGE: {
    SELECT: 'select',
    PURCHASE: 'purchase',
    RENEW: 'renew',
    DETAILS: 'details'
  },

  // 支付相关事件
  PAYMENT: {
    PAY: 'pay',
    CANCEL: 'cancel',
    SUCCESS: 'success',
    FAILED: 'failed'
  },

  // 导航相关事件
  NAVIGATION: {
    GO_BACK: 'goBack',
    GO_TO: 'goTo',
    REDIRECT: 'redirect'
  }
} as const

/**
 * 组件分类定义
 */
export const COMPONENT_CATEGORIES = {
  BUSINESS: 'business',    // 业务组件
  LAYOUT: 'layout',       // 布局组件
  FORM: 'form',          // 表单组件
  DISPLAY: 'display',    // 展示组件
  FEEDBACK: 'feedback',  // 反馈组件
  NAVIGATION: 'navigation', // 导航组件
  OTHER: 'other'         // 其他组件
} as const

/**
 * 组件状态定义
 */
export const COMPONENT_STATES = {
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
  EMPTY: 'empty',
  DISABLED: 'disabled'
} as const

// ==================== 组件开发工具函数 ====================

/**
 * 创建标准化的组件Props
 * @param defaultConfig 默认配置
 * @returns 标准化的Props定义函数
 */
export function createComponentProps<T extends BaseComponentConfig>(
  defaultConfig: T
) {
  return {
    config: {
      type: Object as () => T,
      default: () => defaultConfig
    },
    data: {
      type: Object,
      default: () => ({})
    },
    designMode: {
      type: Boolean,
      default: false
    }
  }
}

/**
 * 创建标准化的组件事件处理器
 * @param emit Vue的emit函数
 * @returns 事件处理器对象
 */
export function createEventHandlers(emit: any) {
  return {
    /** 发射加载完成事件 */
    onLoaded: (data?: any) => {
      console.log('📡 组件加载完成:', data)
      emit(COMPONENT_EVENTS.BASE.LOADED, data)
    },

    /** 发射错误事件 */
    onError: (error: Error) => {
      console.error('📡 组件错误:', error)
      emit(COMPONENT_EVENTS.BASE.ERROR, error)
    },

    /** 发射点击事件 */
    onClick: (event: MouseEvent) => {
      console.log('📡 组件点击:', event)
      emit(COMPONENT_EVENTS.BASE.CLICK, event)
    },

    /** 发射数据变化事件 */
    onChange: (data: any) => {
      console.log('📡 组件数据变化:', data)
      emit(COMPONENT_EVENTS.BASE.CHANGE, data)
    }
  }
}

/**
 * 统一的错误处理函数
 * @param error 错误对象
 * @param operation 操作名称
 * @param component 组件名称
 */
export function handleComponentError(
  error: any,
  operation: string,
  component: string = '组件'
) {
  const errorMessage = `❌ ${component} ${operation}失败: ${error.message || error}`
  console.error(errorMessage, error)

  // 可以在这里添加错误上报逻辑
  // reportError({ error, operation, component })

  return {
    message: errorMessage,
    code: error.code || 'UNKNOWN_ERROR',
    timestamp: Date.now()
  }
}

/**
 * 组件数据加载状态管理
 * @param initialState 初始状态
 * @returns 状态管理对象
 */
export function useComponentState<T>(initialState?: T) {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const data = ref<T | null>(initialState || null)

  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
    if (isLoading) {
      error.value = null
    }
  }

  const setError = (err: string | Error) => {
    error.value = err instanceof Error ? err.message : err
    loading.value = false
  }

  const setData = (newData: T) => {
    data.value = newData
    loading.value = false
    error.value = null
  }

  const reset = () => {
    loading.value = false
    error.value = null
    data.value = initialState || null
  }

  return {
    loading: readonly(loading),
    error: readonly(error),
    data: readonly(data),
    setLoading,
    setError,
    setData,
    reset
  }
}

// ==================== 管理器导出 ====================

// 组件管理器
export {
  ComponentManager,
  componentManager,
  registerComponent,
  registerComponents,
  setVueApp,
  getComponentStats,
  searchComponents,
  type ComponentRegistration
} from '../managers/ComponentManager'
