/**
 * 自动配置生成器 - 写一次配置，自动生成所有其他内容
 */

export interface FieldDefinition {
  default: any
  ui: {
    title: string
    desc?: string
    min?: number
    max?: number
    options?: Array<{ label: string; value: any }>
  }
}

export interface ComponentDefinition {
  name: string
  displayName: string
  category?: string
  icon?: string
  description?: string
  config: Record<string, FieldDefinition>
}

export const autoGenerate = {
  /**
   * 自动生成组件元数据
   */
  metadata(def: ComponentDefinition) {
    return {
      name: def.name,
      displayName: def.displayName,
      category: def.category || 'home',
      icon: def.icon || 'mdi:apps',
      description: def.description || `${def.displayName}组件`,
      version: '1.0.0',
      tags: [def.displayName, '组件'],
      keywords: [def.name.toLowerCase(), def.displayName]
    }
  },

  /**
   * 自动生成默认配置
   */
  defaults(def: ComponentDefinition) {
    const config: Record<string, any> = {}
    Object.entries(def.config).forEach(([key, field]) => {
      config[key] = field.default
    })
    return { config }
  },

  /**
   * 自动生成JSON Schema
   */
  schema(def: ComponentDefinition) {
    const properties: Record<string, any> = {}
    
    Object.entries(def.config).forEach(([key, field]) => {
      const schemaField: any = {
        type: inferType(field.default),
        title: field.ui.title,
        default: field.default
      }
      
      // 添加描述
      if (field.ui.desc) {
        schemaField.description = field.ui.desc
      }
      
      // 添加数值范围
      if (field.ui.min !== undefined) {
        schemaField.minimum = field.ui.min
      }
      if (field.ui.max !== undefined) {
        schemaField.maximum = field.ui.max
      }
      
      // 添加选项
      if (field.ui.options) {
        schemaField.enum = field.ui.options.map(opt => opt.value)
      }
      
      // 处理数组类型
      if (Array.isArray(field.default) && field.default.length > 0) {
        schemaField.items = inferArrayItemSchema(field.default[0])
      }
      
      properties[key] = schemaField
    })
    
    return {
      type: 'object' as const,
      properties: {
        config: {
          type: 'object' as const,
          title: '组件配置',
          description: `${def.displayName}组件的配置选项`,
          properties
        }
      }
    }
  }
}

/**
 * 自动推导数据类型
 */
function inferType(value: any): string {
  if (typeof value === 'boolean') return 'boolean'
  if (typeof value === 'number') return 'number'
  if (typeof value === 'string') return 'string'
  if (Array.isArray(value)) return 'array'
  if (value && typeof value === 'object') return 'object'
  return 'string'
}

/**
 * 推导数组项的Schema
 */
function inferArrayItemSchema(item: any): any {
  if (!item || typeof item !== 'object') {
    return { type: inferType(item) }
  }
  
  const properties: Record<string, any> = {}
  const required: string[] = []
  
  Object.entries(item).forEach(([key, value]) => {
    properties[key] = {
      type: inferType(value),
      title: key,
      description: `${key}字段`
    }
    // 假设所有字段都是必需的
    required.push(key)
  })
  
  return {
    type: 'object',
    properties,
    required
  }
}
