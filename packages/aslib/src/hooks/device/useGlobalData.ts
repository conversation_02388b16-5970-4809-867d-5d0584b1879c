import { ref, computed, reactive, watch } from 'vue'

// ==================== 类型定义 ====================

/**
 * 设备数据接口
 */
export interface DeviceData {
  details: Record<string, any>
  cards: any[]
  realNameCards: any[]
  key: string
  loading: boolean
  [key: string]: any
}

/**
 * 用户数据接口
 */
export interface UserData {
  profile: Record<string, any>
  permissions: string[]
  token: string
  [key: string]: any
}

/**
 * 应用配置接口
 */
export interface AppData {
  config: Record<string, any>
  theme: Record<string, any>
  locale: string
  [key: string]: any
}

/**
 * 全局数据存储接口
 */
export interface GlobalDataStore {
  device: DeviceData
  user: UserData
  app: AppData
  custom: Record<string, any>
}

/**
 * 数据变化监听器类型
 */
export type DataListener<T = any> = (data: T, oldData?: T) => void

// ==================== 全局存储实例 ====================

/**
 * 全局数据存储实例
 */
const globalStore = reactive<GlobalDataStore>({
  device: {
    details: {},
    cards: [],
    realNameCards: [],
    key: '',
    loading: false
  },
  user: {
    profile: {},
    permissions: [],
    token: ''
  },
  app: {
    config: {},
    theme: {},
    locale: 'zh-CN'
  },
  custom: {}
})

/**
 * 数据变化监听器映射
 */
const listeners = new Map<string, Set<DataListener>>()

/**
 * 数据历史记录（用于撤销/重做）
 */
const dataHistory = ref<Array<{ timestamp: number; snapshot: any; action: string }>>([])
const maxHistorySize = 50

// ==================== 工具函数 ====================

/**
 * 触发数据变化监听
 */
function notifyListeners(key: string, newData: any, oldData?: any) {
  const keyListeners = listeners.get(key)
  if (keyListeners) {
    keyListeners.forEach(callback => {
      try {
        callback(newData, oldData)
      } catch (error) {
        console.error(`GlobalData listener error for key "${key}":`, error)
      }
    })
  }
}

/**
 * 添加历史记录
 */
function addToHistory(action: string) {
  const snapshot = JSON.parse(JSON.stringify(globalStore))
  dataHistory.value.push({
    timestamp: Date.now(),
    snapshot,
    action
  })
  
  // 限制历史记录大小
  if (dataHistory.value.length > maxHistorySize) {
    dataHistory.value.shift()
  }
}

/**
 * 深度获取对象属性
 */
function getNestedValue(obj: any, path: string) {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

/**
 * 深度设置对象属性
 */
function setNestedValue(obj: any, path: string, value: any) {
  const keys = path.split('.')
  const lastKey = keys.pop()!
  const target = keys.reduce((current, key) => {
    if (!(key in current)) {
      current[key] = {}
    }
    return current[key]
  }, obj)
  
  const oldValue = target[lastKey]
  target[lastKey] = value
  return oldValue
}

// ==================== 主要钩子函数 ====================

/**
 * 全局数据管理钩子
 * 提供统一的数据存储、访问和管理接口
 */
export function useGlobalData() {
  
  // ==================== 设备数据管理 ====================
  
  /**
   * 设置设备详情数据
   */
  const setDeviceDetails = (details: Record<string, any>) => {
    const oldData = globalStore.device.details
    globalStore.device.details = { ...details }
    notifyListeners('device.details', globalStore.device.details, oldData)
    addToHistory('setDeviceDetails')
    console.log('🔄 GlobalData: 设备详情已更新', details)
  }
  
  /**
   * 更新设备详情（部分更新）
   */
  const updateDeviceDetails = (updates: Record<string, any>) => {
    const oldData = { ...globalStore.device.details }
    Object.assign(globalStore.device.details, updates)
    notifyListeners('device.details', globalStore.device.details, oldData)
    addToHistory('updateDeviceDetails')
    console.log('🔄 GlobalData: 设备详情已部分更新', updates)
  }
  
  /**
   * 设置设备卡片数据
   */
  const setDeviceCards = (cards: any[]) => {
    const oldData = [...globalStore.device.cards]
    globalStore.device.cards = [...cards]
    notifyListeners('device.cards', globalStore.device.cards, oldData)
    addToHistory('setDeviceCards')
    console.log('🔄 GlobalData: 设备卡片已更新', cards)
  }
  
  /**
   * 设置实名卡片数据
   */
  const setRealNameCards = (realNameCards: any[]) => {
    const oldData = [...globalStore.device.realNameCards]
    globalStore.device.realNameCards = [...realNameCards]
    notifyListeners('device.realNameCards', globalStore.device.realNameCards, oldData)
    addToHistory('setRealNameCards')
    console.log('🔄 GlobalData: 实名卡片已更新', realNameCards)
  }
  
  /**
   * 设置设备认证key
   */
  const setDeviceKey = (key: string) => {
    const oldData = globalStore.device.key
    globalStore.device.key = key
    notifyListeners('device.key', key, oldData)
    addToHistory('setDeviceKey')
    console.log('🔄 GlobalData: 设备key已更新')
  }
  
  /**
   * 设置设备加载状态
   */
  const setDeviceLoading = (loading: boolean) => {
    const oldData = globalStore.device.loading
    globalStore.device.loading = loading
    notifyListeners('device.loading', loading, oldData)
  }
  
  /**
   * 批量更新设备数据
   */
  const updateDeviceData = (data: Partial<DeviceData>) => {
    const oldData = { ...globalStore.device }
    Object.assign(globalStore.device, data)
    notifyListeners('device', globalStore.device, oldData)
    addToHistory('updateDeviceData')
    console.log('🔄 GlobalData: 设备数据批量更新', data)
  }
  
  // ==================== 用户数据管理 ====================
  
  /**
   * 设置用户资料
   */
  const setUserProfile = (profile: Record<string, any>) => {
    const oldData = { ...globalStore.user.profile }
    globalStore.user.profile = { ...profile }
    notifyListeners('user.profile', globalStore.user.profile, oldData)
    addToHistory('setUserProfile')
    console.log('🔄 GlobalData: 用户资料已更新', profile)
  }
  
  /**
   * 设置用户权限
   */
  const setUserPermissions = (permissions: string[]) => {
    const oldData = [...globalStore.user.permissions]
    globalStore.user.permissions = [...permissions]
    notifyListeners('user.permissions', globalStore.user.permissions, oldData)
    addToHistory('setUserPermissions')
    console.log('🔄 GlobalData: 用户权限已更新', permissions)
  }
  
  /**
   * 设置用户token
   */
  const setUserToken = (token: string) => {
    const oldData = globalStore.user.token
    globalStore.user.token = token
    notifyListeners('user.token', token, oldData)
    addToHistory('setUserToken')
    console.log('🔄 GlobalData: 用户token已更新')
  }
  
  // ==================== 应用配置管理 ====================
  
  /**
   * 设置应用配置
   */
  const setAppConfig = (config: Record<string, any>) => {
    const oldData = { ...globalStore.app.config }
    globalStore.app.config = { ...config }
    notifyListeners('app.config', globalStore.app.config, oldData)
    addToHistory('setAppConfig')
    console.log('🔄 GlobalData: 应用配置已更新', config)
  }
  
  /**
   * 设置应用主题
   */
  const setAppTheme = (theme: Record<string, any>) => {
    const oldData = { ...globalStore.app.theme }
    globalStore.app.theme = { ...theme }
    notifyListeners('app.theme', globalStore.app.theme, oldData)
    addToHistory('setAppTheme')
    console.log('🔄 GlobalData: 应用主题已更新', theme)
  }
  
  /**
   * 设置应用语言
   */
  const setAppLocale = (locale: string) => {
    const oldData = globalStore.app.locale
    globalStore.app.locale = locale
    notifyListeners('app.locale', locale, oldData)
    addToHistory('setAppLocale')
    console.log('🔄 GlobalData: 应用语言已更新', locale)
  }
  
  // ==================== 自定义数据管理 ====================
  
  /**
   * 设置自定义数据
   */
  const setCustomData = (key: string, data: any) => {
    const oldData = globalStore.custom[key]
    globalStore.custom[key] = data
    notifyListeners(`custom.${key}`, data, oldData)
    addToHistory(`setCustomData:${key}`)
    console.log(`🔄 GlobalData: 自定义数据 ${key} 已更新`, data)
  }
  
  /**
   * 获取自定义数据
   */
  const getCustomData = (key: string) => {
    return globalStore.custom[key]
  }
  
  /**
   * 删除自定义数据
   */
  const removeCustomData = (key: string) => {
    const oldData = globalStore.custom[key]
    delete globalStore.custom[key]
    notifyListeners(`custom.${key}`, undefined, oldData)
    addToHistory(`removeCustomData:${key}`)
    console.log(`🔄 GlobalData: 自定义数据 ${key} 已删除`)
  }
  
  // ==================== 通用数据操作 ====================
  
  /**
   * 通用数据设置（支持路径）
   */
  const setData = (path: string, value: any) => {
    const oldValue = getNestedValue(globalStore, path)
    setNestedValue(globalStore, path, value)
    notifyListeners(path, value, oldValue)
    addToHistory(`setData:${path}`)
    console.log(`🔄 GlobalData: 数据路径 ${path} 已更新`, value)
  }
  
  /**
   * 通用数据获取（支持路径）
   */
  const getData = (path: string) => {
    return getNestedValue(globalStore, path)
  }
  
  // ==================== 数据监听 ====================
  
  /**
   * 监听数据变化
   */
  const watchData = (key: string, callback: DataListener) => {
    if (!listeners.has(key)) {
      listeners.set(key, new Set())
    }
    listeners.get(key)!.add(callback)
    
    // 返回取消监听的函数
    return () => {
      listeners.get(key)?.delete(callback)
      if (listeners.get(key)?.size === 0) {
        listeners.delete(key)
      }
    }
  }
  
  // ==================== 响应式数据访问 ====================
  
  /**
   * 获取设备数据（响应式）
   */
  const deviceData = computed(() => globalStore.device)
  
  /**
   * 获取用户数据（响应式）
   */
  const userData = computed(() => globalStore.user)
  
  /**
   * 获取应用数据（响应式）
   */
  const appData = computed(() => globalStore.app)
  
  /**
   * 获取自定义数据（响应式）
   */
  const customData = computed(() => globalStore.custom)
  
  /**
   * 获取所有数据（响应式）
   */
  const allData = computed(() => globalStore)
  
  // ==================== 工具方法 ====================
  
  /**
   * 清空所有数据
   */
  const clearAllData = () => {
    const oldData = JSON.parse(JSON.stringify(globalStore))
    
    globalStore.device = {
      details: {},
      cards: [],
      realNameCards: [],
      key: '',
      loading: false
    }
    globalStore.user = {
      profile: {},
      permissions: [],
      token: ''
    }
    globalStore.app = {
      config: {},
      theme: {},
      locale: 'zh-CN'
    }
    globalStore.custom = {}
    
    notifyListeners('*', globalStore, oldData)
    addToHistory('clearAllData')
    console.log('🔄 GlobalData: 所有数据已清空')
  }
  
  /**
   * 获取数据快照
   */
  const getSnapshot = () => {
    return JSON.parse(JSON.stringify(globalStore))
  }
  
  /**
   * 恢复数据快照
   */
  const restoreSnapshot = (snapshot: any) => {
    const oldData = JSON.parse(JSON.stringify(globalStore))
    Object.assign(globalStore, snapshot)
    notifyListeners('*', globalStore, oldData)
    addToHistory('restoreSnapshot')
    console.log('🔄 GlobalData: 数据快照已恢复')
  }
  
  /**
   * 获取历史记录
   */
  const getHistory = () => {
    return [...dataHistory.value]
  }
  
  /**
   * 清空历史记录
   */
  const clearHistory = () => {
    dataHistory.value = []
    console.log('🔄 GlobalData: 历史记录已清空')
  }
  
  return {
    // 设备数据方法
    setDeviceDetails,
    updateDeviceDetails,
    setDeviceCards,
    setRealNameCards,
    setDeviceKey,
    setDeviceLoading,
    updateDeviceData,
    
    // 用户数据方法
    setUserProfile,
    setUserPermissions,
    setUserToken,
    
    // 应用配置方法
    setAppConfig,
    setAppTheme,
    setAppLocale,
    
    // 自定义数据方法
    setCustomData,
    getCustomData,
    removeCustomData,
    
    // 通用数据方法
    setData,
    getData,
    
    // 数据监听方法
    watchData,
    
    // 响应式数据
    deviceData,
    userData,
    appData,
    customData,
    allData,
    
    // 工具方法
    clearAllData,
    getSnapshot,
    restoreSnapshot,
    getHistory,
    clearHistory
  }
}
