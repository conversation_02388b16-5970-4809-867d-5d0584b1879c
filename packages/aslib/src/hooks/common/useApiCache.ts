import { ref, computed, reactive } from 'vue'
import type { AxiosRequestConfig, AxiosResponse } from 'axios'

// ==================== 类型定义 ====================

/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
  data: T
  timestamp: number
  expiry: number
  key: string
  loading: boolean
  error: Error | null
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  /** 缓存过期时间（毫秒），默认5分钟 */
  ttl?: number
  /** 是否在组件卸载时清理缓存 */
  clearOnUnmount?: boolean
  /** 缓存键前缀 */
  keyPrefix?: string
  /** 最大缓存数量 */
  maxSize?: number
  /** 是否启用持久化存储 */
  persistent?: boolean
  /** 持久化存储键名 */
  storageKey?: string
}

/**
 * 请求配置接口
 */
export interface RequestConfig extends AxiosRequestConfig {
  /** 缓存键，如果不提供则自动生成 */
  cacheKey?: string
  /** 是否强制刷新缓存 */
  forceRefresh?: boolean
  /** 缓存配置 */
  cache?: CacheConfig
}

// ==================== 全局缓存存储 ====================

/**
 * 全局缓存存储
 */
const globalCache = reactive<Map<string, CacheItem>>(new Map())

/**
 * 默认缓存配置
 */
const defaultCacheConfig: Required<CacheConfig> = {
  ttl: 5 * 60 * 1000, // 5分钟
  clearOnUnmount: false,
  keyPrefix: 'api_cache_',
  maxSize: 100,
  persistent: false,
  storageKey: 'lowcode_api_cache'
}

// ==================== 工具函数 ====================

/**
 * 生成缓存键
 */
function generateCacheKey(config: AxiosRequestConfig): string {
  const { method = 'GET', url = '', params, data } = config
  const paramsStr = params ? JSON.stringify(params) : ''
  const dataStr = data ? JSON.stringify(data) : ''
  return `${method.toUpperCase()}_${url}_${paramsStr}_${dataStr}`
}

/**
 * 检查缓存是否过期
 */
function isCacheExpired(item: CacheItem): boolean {
  return Date.now() > item.expiry
}

/**
 * 清理过期缓存
 */
function cleanExpiredCache() {
  const now = Date.now()
  for (const [key, item] of globalCache.entries()) {
    if (now > item.expiry) {
      globalCache.delete(key)
    }
  }
}

/**
 * 限制缓存大小
 */
function limitCacheSize(maxSize: number) {
  if (globalCache.size <= maxSize) return
  
  // 按时间戳排序，删除最旧的缓存
  const entries = Array.from(globalCache.entries())
  entries.sort((a, b) => a[1].timestamp - b[1].timestamp)
  
  const toDelete = entries.slice(0, globalCache.size - maxSize)
  toDelete.forEach(([key]) => globalCache.delete(key))
}

/**
 * 从本地存储加载缓存
 */
function loadFromStorage(storageKey: string): void {
  try {
    const stored = localStorage.getItem(storageKey)
    if (stored) {
      const data = JSON.parse(stored)
      Object.entries(data).forEach(([key, item]: [string, any]) => {
        if (!isCacheExpired(item)) {
          globalCache.set(key, item)
        }
      })
    }
  } catch (error) {
    console.warn('Failed to load cache from storage:', error)
  }
}

/**
 * 保存缓存到本地存储
 */
function saveToStorage(storageKey: string): void {
  try {
    const data: Record<string, CacheItem> = {}
    globalCache.forEach((item, key) => {
      if (!isCacheExpired(item)) {
        data[key] = item
      }
    })
    localStorage.setItem(storageKey, JSON.stringify(data))
  } catch (error) {
    console.warn('Failed to save cache to storage:', error)
  }
}

// ==================== 主要钩子函数 ====================

/**
 * API缓存管理钩子
 * 提供智能的API请求缓存和管理功能
 */
export function useApiCache(defaultConfig?: CacheConfig) {
  const config = { ...defaultCacheConfig, ...defaultConfig }
  
  // 如果启用持久化，加载缓存
  if (config.persistent) {
    loadFromStorage(config.storageKey)
  }
  
  /**
   * 获取缓存项
   */
  const getCacheItem = (key: string): CacheItem | undefined => {
    const item = globalCache.get(key)
    if (item && isCacheExpired(item)) {
      globalCache.delete(key)
      return undefined
    }
    return item
  }
  
  /**
   * 设置缓存项
   */
  const setCacheItem = <T>(key: string, data: T, ttl?: number): void => {
    const now = Date.now()
    const item: CacheItem<T> = {
      data,
      timestamp: now,
      expiry: now + (ttl || config.ttl),
      key,
      loading: false,
      error: null
    }
    
    globalCache.set(key, item)
    
    // 限制缓存大小
    limitCacheSize(config.maxSize)
    
    // 保存到本地存储
    if (config.persistent) {
      saveToStorage(config.storageKey)
    }
  }
  
  /**
   * 删除缓存项
   */
  const deleteCacheItem = (key: string): boolean => {
    const deleted = globalCache.delete(key)
    if (deleted && config.persistent) {
      saveToStorage(config.storageKey)
    }
    return deleted
  }
  
  /**
   * 清空所有缓存
   */
  const clearCache = (): void => {
    globalCache.clear()
    if (config.persistent) {
      localStorage.removeItem(config.storageKey)
    }
    console.log('🗑️ ApiCache: 所有缓存已清空')
  }
  
  /**
   * 清理过期缓存
   */
  const cleanCache = (): void => {
    cleanExpiredCache()
    if (config.persistent) {
      saveToStorage(config.storageKey)
    }
    console.log('🧹 ApiCache: 过期缓存已清理')
  }
  
  /**
   * 预加载数据到缓存
   */
  const preloadCache = <T>(key: string, data: T, ttl?: number): void => {
    setCacheItem(key, data, ttl)
    console.log(`📦 ApiCache: 数据已预加载到缓存 ${key}`)
  }
  
  /**
   * 批量设置缓存
   */
  const batchSetCache = <T>(items: Array<{ key: string; data: T; ttl?: number }>): void => {
    items.forEach(({ key, data, ttl }) => {
      setCacheItem(key, data, ttl)
    })
    console.log(`📦 ApiCache: 批量设置缓存 ${items.length} 项`)
  }
  
  /**
   * 获取缓存统计信息
   */
  const getCacheStats = () => {
    const now = Date.now()
    let validCount = 0
    let expiredCount = 0
    let totalSize = 0
    
    globalCache.forEach((item) => {
      if (now > item.expiry) {
        expiredCount++
      } else {
        validCount++
      }
      totalSize += JSON.stringify(item).length
    })
    
    return {
      total: globalCache.size,
      valid: validCount,
      expired: expiredCount,
      size: totalSize,
      maxSize: config.maxSize
    }
  }
  
  /**
   * 检查缓存是否存在且有效
   */
  const hasValidCache = (key: string): boolean => {
    const item = getCacheItem(key)
    return !!item && !isCacheExpired(item)
  }
  
  /**
   * 获取所有缓存键
   */
  const getCacheKeys = (): string[] => {
    return Array.from(globalCache.keys())
  }
  
  /**
   * 根据模式删除缓存
   */
  const deleteCacheByPattern = (pattern: string | RegExp): number => {
    let deletedCount = 0
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern
    
    for (const key of globalCache.keys()) {
      if (regex.test(key)) {
        globalCache.delete(key)
        deletedCount++
      }
    }
    
    if (deletedCount > 0 && config.persistent) {
      saveToStorage(config.storageKey)
    }
    
    console.log(`🗑️ ApiCache: 删除了 ${deletedCount} 个匹配的缓存项`)
    return deletedCount
  }
  
  /**
   * 刷新缓存（重新设置过期时间）
   */
  const refreshCache = (key: string, ttl?: number): boolean => {
    const item = globalCache.get(key)
    if (item) {
      item.expiry = Date.now() + (ttl || config.ttl)
      if (config.persistent) {
        saveToStorage(config.storageKey)
      }
      return true
    }
    return false
  }
  
  // 响应式数据
  const cacheSize = computed(() => globalCache.size)
  const cacheStats = computed(() => getCacheStats())
  
  // 定期清理过期缓存
  const cleanupInterval = setInterval(() => {
    cleanCache()
  }, 60000) // 每分钟清理一次
  
  // 清理函数
  const cleanup = () => {
    clearInterval(cleanupInterval)
    if (config.clearOnUnmount) {
      clearCache()
    }
  }
  
  return {
    // 基础缓存操作
    getCacheItem,
    setCacheItem,
    deleteCacheItem,
    clearCache,
    cleanCache,
    
    // 高级缓存操作
    preloadCache,
    batchSetCache,
    hasValidCache,
    refreshCache,
    
    // 缓存查询
    getCacheKeys,
    getCacheStats,
    deleteCacheByPattern,
    
    // 响应式数据
    cacheSize,
    cacheStats,
    
    // 工具方法
    cleanup,
    
    // 配置
    config
  }
}

/**
 * 全局缓存实例（单例）
 */
let globalCacheInstance: ReturnType<typeof useApiCache> | null = null

/**
 * 获取全局缓存实例
 */
export function getGlobalApiCache(config?: CacheConfig) {
  if (!globalCacheInstance) {
    globalCacheInstance = useApiCache(config)
  }
  return globalCacheInstance
}
