// ==================== 通用 Hooks 导出 ====================

// API缓存管理
export {
  useApiCache,
  getGlobalApiCache,
  type CacheItem,
  type CacheConfig,
  type RequestConfig
} from './useApiCache'

// 事件总线
export {
  useEventBus,
  getGlobalEventBus,
  type EventListener,
  type EventConfig
} from './useEventBus'

// 本地存储管理
export {
  useStorage,
  useBatchStorage,
  storageUtils,
  serializers,
  type StorageType,
  type Serializer,
  type StorageConfig
} from './useStorage'

export const version = '1.0.0'

export default {
  version
}