import { ref, computed, watch, Ref } from 'vue'

// ==================== 类型定义 ====================

/**
 * 存储类型
 */
export type StorageType = 'localStorage' | 'sessionStorage'

/**
 * 序列化器接口
 */
export interface Serializer<T> {
  read: (value: string) => T
  write: (value: T) => string
}

/**
 * 存储配置接口
 */
export interface StorageConfig<T> {
  /** 存储类型 */
  type?: StorageType
  /** 默认值 */
  defaultValue?: T
  /** 自定义序列化器 */
  serializer?: Serializer<T>
  /** 是否在写入时立即同步 */
  syncDefaults?: boolean
  /** 存储键前缀 */
  keyPrefix?: string
  /** 是否启用加密 */
  encrypt?: boolean
  /** 加密密钥 */
  encryptKey?: string
}

// ==================== 内置序列化器 ====================

/**
 * 默认序列化器
 */
const defaultSerializer: Serializer<any> = {
  read: (value: string) => {
    try {
      return JSON.parse(value)
    } catch {
      return value
    }
  },
  write: (value: any) => {
    return typeof value === 'string' ? value : JSON.stringify(value)
  }
}

/**
 * 字符串序列化器
 */
const stringSerializer: Serializer<string> = {
  read: (value: string) => value,
  write: (value: string) => value
}

/**
 * 数字序列化器
 */
const numberSerializer: Serializer<number> = {
  read: (value: string) => Number(value),
  write: (value: number) => String(value)
}

/**
 * 布尔序列化器
 */
const booleanSerializer: Serializer<boolean> = {
  read: (value: string) => value === 'true',
  write: (value: boolean) => String(value)
}

/**
 * 日期序列化器
 */
const dateSerializer: Serializer<Date> = {
  read: (value: string) => new Date(value),
  write: (value: Date) => value.toISOString()
}

// ==================== 加密工具 ====================

/**
 * 简单的加密函数（基于Base64）
 */
function simpleEncrypt(text: string, key: string): string {
  try {
    const encoded = btoa(unescape(encodeURIComponent(text + key)))
    return encoded
  } catch {
    return text
  }
}

/**
 * 简单的解密函数
 */
function simpleDecrypt(encoded: string, key: string): string {
  try {
    const decoded = decodeURIComponent(escape(atob(encoded)))
    return decoded.slice(0, -key.length)
  } catch {
    return encoded
  }
}

// ==================== 工具函数 ====================

/**
 * 获取存储对象
 */
function getStorage(type: StorageType): Storage {
  return type === 'localStorage' ? localStorage : sessionStorage
}

/**
 * 检查存储是否可用
 */
function isStorageAvailable(type: StorageType): boolean {
  try {
    const storage = getStorage(type)
    const testKey = '__storage_test__'
    storage.setItem(testKey, 'test')
    storage.removeItem(testKey)
    return true
  } catch {
    return false
  }
}

// ==================== 主要钩子函数 ====================

/**
 * 本地存储管理钩子
 * 提供响应式的本地存储操作
 */
export function useStorage<T>(
  key: string,
  config: StorageConfig<T> = {}
): [Ref<T>, (value: T) => void, () => void] {
  
  const {
    type = 'localStorage',
    defaultValue,
    serializer = defaultSerializer,
    syncDefaults = true,
    keyPrefix = '',
    encrypt = false,
    encryptKey = 'lowcode_default_key'
  } = config
  
  const fullKey = keyPrefix ? `${keyPrefix}${key}` : key
  const storage = getStorage(type)
  const isAvailable = isStorageAvailable(type)
  
  /**
   * 读取存储值
   */
  const read = (): T => {
    if (!isAvailable) {
      return defaultValue as T
    }
    
    try {
      const item = storage.getItem(fullKey)
      if (item === null) {
        return defaultValue as T
      }
      
      let value = item
      if (encrypt) {
        value = simpleDecrypt(item, encryptKey)
      }
      
      return serializer.read(value)
    } catch (error) {
      console.warn(`Failed to read storage key "${fullKey}":`, error)
      return defaultValue as T
    }
  }
  
  /**
   * 写入存储值
   */
  const write = (value: T): void => {
    if (!isAvailable) {
      console.warn(`Storage type "${type}" is not available`)
      return
    }
    
    try {
      let serialized = serializer.write(value)
      if (encrypt) {
        serialized = simpleEncrypt(serialized, encryptKey)
      }
      
      storage.setItem(fullKey, serialized)
    } catch (error) {
      console.error(`Failed to write storage key "${fullKey}":`, error)
    }
  }
  
  /**
   * 删除存储值
   */
  const remove = (): void => {
    if (!isAvailable) {
      return
    }
    
    try {
      storage.removeItem(fullKey)
      storedValue.value = defaultValue as T
    } catch (error) {
      console.error(`Failed to remove storage key "${fullKey}":`, error)
    }
  }
  
  // 创建响应式引用
  const storedValue = ref<T>(read())
  
  // 监听值变化，自动写入存储
  watch(
    storedValue,
    (newValue) => {
      write(newValue)
    },
    { deep: true }
  )
  
  // 监听存储变化（跨标签页同步）
  if (isAvailable && type === 'localStorage') {
    window.addEventListener('storage', (e) => {
      if (e.key === fullKey && e.newValue !== null) {
        try {
          let value = e.newValue
          if (encrypt) {
            value = simpleDecrypt(e.newValue, encryptKey)
          }
          storedValue.value = serializer.read(value)
        } catch (error) {
          console.warn(`Failed to sync storage key "${fullKey}":`, error)
        }
      }
    })
  }
  
  // 如果启用同步默认值且当前值为空，写入默认值
  if (syncDefaults && defaultValue !== undefined && storedValue.value === undefined) {
    storedValue.value = defaultValue
  }
  
  return [storedValue as Ref<T>, (value: T) => { storedValue.value = value }, remove]
}

/**
 * 批量存储管理钩子
 */
export function useBatchStorage<T extends Record<string, any>>(
  keys: (keyof T)[],
  config: StorageConfig<any> = {}
) {
  const storageItems = {} as Record<keyof T, ReturnType<typeof useStorage>>
  
  // 为每个键创建存储项
  keys.forEach(key => {
    storageItems[key] = useStorage(String(key), config)
  })
  
  /**
   * 获取所有值
   */
  const getAll = (): Partial<T> => {
    const result = {} as Partial<T>
    keys.forEach(key => {
      result[key] = storageItems[key][0].value as T[keyof T]
    })
    return result
  }
  
  /**
   * 设置所有值
   */
  const setAll = (values: Partial<T>): void => {
    Object.entries(values).forEach(([key, value]) => {
      if (storageItems[key as keyof T]) {
        storageItems[key as keyof T][1](value)
      }
    })
  }
  
  /**
   * 清空所有值
   */
  const clearAll = (): void => {
    keys.forEach(key => {
      storageItems[key][2]()
    })
  }
  
  /**
   * 响应式的所有值
   */
  const allValues = computed(() => getAll())
  
  return {
    items: storageItems,
    getAll,
    setAll,
    clearAll,
    allValues
  }
}

/**
 * 存储工具函数集合
 */
export const storageUtils = {
  /**
   * 获取存储大小（字节）
   */
  getStorageSize(type: StorageType = 'localStorage'): number {
    if (!isStorageAvailable(type)) return 0
    
    const storage = getStorage(type)
    let total = 0
    
    for (let i = 0; i < storage.length; i++) {
      const key = storage.key(i)
      if (key) {
        const value = storage.getItem(key) || ''
        total += key.length + value.length
      }
    }
    
    return total
  },
  
  /**
   * 清空存储
   */
  clearStorage(type: StorageType = 'localStorage'): void {
    if (!isStorageAvailable(type)) return
    getStorage(type).clear()
  },
  
  /**
   * 获取所有存储键
   */
  getStorageKeys(type: StorageType = 'localStorage'): string[] {
    if (!isStorageAvailable(type)) return []
    
    const storage = getStorage(type)
    const keys: string[] = []
    
    for (let i = 0; i < storage.length; i++) {
      const key = storage.key(i)
      if (key) keys.push(key)
    }
    
    return keys
  },
  
  /**
   * 按前缀删除存储项
   */
  removeByPrefix(prefix: string, type: StorageType = 'localStorage'): number {
    if (!isStorageAvailable(type)) return 0
    
    const storage = getStorage(type)
    const keysToRemove: string[] = []
    
    for (let i = 0; i < storage.length; i++) {
      const key = storage.key(i)
      if (key && key.startsWith(prefix)) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => storage.removeItem(key))
    return keysToRemove.length
  },
  
  /**
   * 导出存储数据
   */
  exportStorage(type: StorageType = 'localStorage'): Record<string, string> {
    if (!isStorageAvailable(type)) return {}
    
    const storage = getStorage(type)
    const data: Record<string, string> = {}
    
    for (let i = 0; i < storage.length; i++) {
      const key = storage.key(i)
      if (key) {
        data[key] = storage.getItem(key) || ''
      }
    }
    
    return data
  },
  
  /**
   * 导入存储数据
   */
  importStorage(data: Record<string, string>, type: StorageType = 'localStorage'): void {
    if (!isStorageAvailable(type)) return
    
    const storage = getStorage(type)
    Object.entries(data).forEach(([key, value]) => {
      storage.setItem(key, value)
    })
  }
}

// 导出序列化器
export const serializers = {
  default: defaultSerializer,
  string: stringSerializer,
  number: numberSerializer,
  boolean: booleanSerializer,
  date: dateSerializer
}
