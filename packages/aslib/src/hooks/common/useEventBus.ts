import { ref, onUnmounted } from 'vue'

// ==================== 类型定义 ====================

/**
 * 事件监听器类型
 */
export type EventListener<T = any> = (data: T) => void | Promise<void>

/**
 * 事件配置接口
 */
export interface EventConfig {
  /** 是否只触发一次 */
  once?: boolean
  /** 事件优先级（数字越大优先级越高） */
  priority?: number
  /** 事件命名空间 */
  namespace?: string
}

/**
 * 事件监听器信息
 */
interface EventListenerInfo<T = any> {
  listener: EventListener<T>
  config: EventConfig
  id: string
}

/**
 * 事件历史记录
 */
interface EventHistory {
  event: string
  data: any
  timestamp: number
  namespace?: string
}

// ==================== 全局事件总线 ====================

/**
 * 全局事件监听器映射
 */
const globalListeners = new Map<string, EventListenerInfo[]>()

/**
 * 事件历史记录
 */
const eventHistory = ref<EventHistory[]>([])

/**
 * 最大历史记录数量
 */
const maxHistorySize = 1000

/**
 * 监听器ID计数器
 */
let listenerIdCounter = 0

// ==================== 工具函数 ====================

/**
 * 生成监听器ID
 */
function generateListenerId(): string {
  return `listener_${++listenerIdCounter}_${Date.now()}`
}

/**
 * 添加事件历史记录
 */
function addEventHistory(event: string, data: any, namespace?: string) {
  eventHistory.value.push({
    event,
    data: JSON.parse(JSON.stringify(data)),
    timestamp: Date.now(),
    namespace
  })
  
  // 限制历史记录大小
  if (eventHistory.value.length > maxHistorySize) {
    eventHistory.value.shift()
  }
}

/**
 * 获取完整事件名（包含命名空间）
 */
function getFullEventName(event: string, namespace?: string): string {
  return namespace ? `${namespace}:${event}` : event
}

// ==================== 主要钩子函数 ====================

/**
 * 事件总线钩子
 * 提供组件间通信和事件管理功能
 */
export function useEventBus(defaultNamespace?: string) {
  
  // 当前实例的监听器ID列表（用于清理）
  const instanceListeners = ref<string[]>([])
  
  /**
   * 监听事件
   */
  const on = <T = any>(
    event: string, 
    listener: EventListener<T>, 
    config: EventConfig = {}
  ): (() => void) => {
    const fullEvent = getFullEventName(event, config.namespace || defaultNamespace)
    const listenerId = generateListenerId()
    
    const listenerInfo: EventListenerInfo<T> = {
      listener,
      config: {
        once: false,
        priority: 0,
        ...config
      },
      id: listenerId
    }
    
    if (!globalListeners.has(fullEvent)) {
      globalListeners.set(fullEvent, [])
    }
    
    const listeners = globalListeners.get(fullEvent)!
    listeners.push(listenerInfo)
    
    // 按优先级排序
    listeners.sort((a, b) => (b.config.priority || 0) - (a.config.priority || 0))
    
    // 记录监听器ID
    instanceListeners.value.push(listenerId)
    
    console.log(`📡 EventBus: 监听事件 ${fullEvent}`)
    
    // 返回取消监听的函数
    return () => off(event, listenerId, config.namespace || defaultNamespace)
  }
  
  /**
   * 监听事件（只触发一次）
   */
  const once = <T = any>(
    event: string, 
    listener: EventListener<T>, 
    config: Omit<EventConfig, 'once'> = {}
  ): (() => void) => {
    return on(event, listener, { ...config, once: true })
  }
  
  /**
   * 取消监听事件
   */
  const off = (event: string, listenerId?: string, namespace?: string): boolean => {
    const fullEvent = getFullEventName(event, namespace || defaultNamespace)
    const listeners = globalListeners.get(fullEvent)
    
    if (!listeners) return false
    
    if (listenerId) {
      // 删除指定监听器
      const index = listeners.findIndex(l => l.id === listenerId)
      if (index !== -1) {
        listeners.splice(index, 1)
        // 从实例监听器列表中移除
        const instanceIndex = instanceListeners.value.indexOf(listenerId)
        if (instanceIndex !== -1) {
          instanceListeners.value.splice(instanceIndex, 1)
        }
        console.log(`📡 EventBus: 取消监听事件 ${fullEvent} (${listenerId})`)
        return true
      }
    } else {
      // 删除所有监听器
      const count = listeners.length
      globalListeners.delete(fullEvent)
      console.log(`📡 EventBus: 取消所有监听事件 ${fullEvent} (${count} 个监听器)`)
      return count > 0
    }
    
    return false
  }
  
  /**
   * 触发事件
   */
  const emit = async <T = any>(
    event: string, 
    data?: T, 
    namespace?: string
  ): Promise<void> => {
    const fullEvent = getFullEventName(event, namespace || defaultNamespace)
    const listeners = globalListeners.get(fullEvent)
    
    if (!listeners || listeners.length === 0) {
      console.log(`📡 EventBus: 触发事件 ${fullEvent}，但没有监听器`)
      return
    }
    
    console.log(`📡 EventBus: 触发事件 ${fullEvent}，${listeners.length} 个监听器`)
    
    // 添加到历史记录
    addEventHistory(event, data, namespace || defaultNamespace)
    
    // 执行监听器
    const toRemove: string[] = []
    
    for (const listenerInfo of listeners) {
      try {
        await listenerInfo.listener(data)
        
        // 如果是一次性监听器，标记为删除
        if (listenerInfo.config.once) {
          toRemove.push(listenerInfo.id)
        }
      } catch (error) {
        console.error(`EventBus listener error for event "${fullEvent}":`, error)
      }
    }
    
    // 删除一次性监听器
    toRemove.forEach(id => {
      const index = listeners.findIndex(l => l.id === id)
      if (index !== -1) {
        listeners.splice(index, 1)
      }
    })
  }
  
  /**
   * 同步触发事件（不等待异步监听器）
   */
  const emitSync = <T = any>(
    event: string, 
    data?: T, 
    namespace?: string
  ): void => {
    const fullEvent = getFullEventName(event, namespace || defaultNamespace)
    const listeners = globalListeners.get(fullEvent)
    
    if (!listeners || listeners.length === 0) {
      return
    }
    
    console.log(`📡 EventBus: 同步触发事件 ${fullEvent}，${listeners.length} 个监听器`)
    
    // 添加到历史记录
    addEventHistory(event, data, namespace || defaultNamespace)
    
    // 同步执行监听器
    const toRemove: string[] = []
    
    for (const listenerInfo of listeners) {
      try {
        const result = listenerInfo.listener(data)
        
        // 如果返回Promise，不等待
        if (result instanceof Promise) {
          result.catch(error => {
            console.error(`EventBus async listener error for event "${fullEvent}":`, error)
          })
        }
        
        // 如果是一次性监听器，标记为删除
        if (listenerInfo.config.once) {
          toRemove.push(listenerInfo.id)
        }
      } catch (error) {
        console.error(`EventBus listener error for event "${fullEvent}":`, error)
      }
    }
    
    // 删除一次性监听器
    toRemove.forEach(id => {
      const index = listeners.findIndex(l => l.id === id)
      if (index !== -1) {
        listeners.splice(index, 1)
      }
    })
  }
  
  /**
   * 获取事件监听器数量
   */
  const getListenerCount = (event: string, namespace?: string): number => {
    const fullEvent = getFullEventName(event, namespace || defaultNamespace)
    return globalListeners.get(fullEvent)?.length || 0
  }
  
  /**
   * 获取所有事件名称
   */
  const getEventNames = (): string[] => {
    return Array.from(globalListeners.keys())
  }
  
  /**
   * 清空所有监听器
   */
  const clear = (namespace?: string): void => {
    if (namespace) {
      // 清空指定命名空间的监听器
      const prefix = `${namespace}:`
      for (const [event] of globalListeners) {
        if (event.startsWith(prefix)) {
          globalListeners.delete(event)
        }
      }
      console.log(`📡 EventBus: 清空命名空间 ${namespace} 的所有监听器`)
    } else {
      // 清空所有监听器
      globalListeners.clear()
      instanceListeners.value = []
      console.log('📡 EventBus: 清空所有监听器')
    }
  }
  
  /**
   * 获取事件历史记录
   */
  const getHistory = (event?: string, namespace?: string): EventHistory[] => {
    if (!event) {
      return [...eventHistory.value]
    }
    
    const fullEvent = getFullEventName(event, namespace || defaultNamespace)
    return eventHistory.value.filter(h => {
      const historyEvent = getFullEventName(h.event, h.namespace)
      return historyEvent === fullEvent
    })
  }
  
  /**
   * 清空事件历史记录
   */
  const clearHistory = (): void => {
    eventHistory.value = []
    console.log('📡 EventBus: 清空事件历史记录')
  }
  
  /**
   * 等待事件触发
   */
  const waitFor = <T = any>(
    event: string, 
    timeout?: number, 
    namespace?: string
  ): Promise<T> => {
    return new Promise((resolve, reject) => {
      let timeoutId: number | undefined
      
      const unsubscribe = once<T>(event, (data) => {
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
        resolve(data)
      }, { namespace: namespace || defaultNamespace })
      
      if (timeout) {
        timeoutId = window.setTimeout(() => {
          unsubscribe()
          reject(new Error(`Event "${event}" timeout after ${timeout}ms`))
        }, timeout)
      }
    })
  }
  
  // 组件卸载时清理监听器
  onUnmounted(() => {
    instanceListeners.value.forEach(listenerId => {
      // 查找并删除监听器
      for (const [event, listeners] of globalListeners) {
        const index = listeners.findIndex(l => l.id === listenerId)
        if (index !== -1) {
          listeners.splice(index, 1)
          if (listeners.length === 0) {
            globalListeners.delete(event)
          }
          break
        }
      }
    })
    instanceListeners.value = []
  })
  
  return {
    // 基础事件方法
    on,
    once,
    off,
    emit,
    emitSync,
    
    // 查询方法
    getListenerCount,
    getEventNames,
    getHistory,
    
    // 工具方法
    clear,
    clearHistory,
    waitFor,
    
    // 配置
    defaultNamespace
  }
}

/**
 * 全局事件总线实例（单例）
 */
let globalEventBusInstance: ReturnType<typeof useEventBus> | null = null

/**
 * 获取全局事件总线实例
 */
export function getGlobalEventBus(defaultNamespace?: string) {
  if (!globalEventBusInstance) {
    globalEventBusInstance = useEventBus(defaultNamespace)
  }
  return globalEventBusInstance
}
