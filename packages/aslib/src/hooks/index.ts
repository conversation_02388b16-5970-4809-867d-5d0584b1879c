// ==================== 原有的通用导出（保持向后兼容） ====================

// 从 common 导出的通用 hooks
export {
  useApiCache,
  getGlobalApiCache,
  type CacheItem,
  type CacheConfig,
  type RequestConfig,
  useEventBus,
  getGlobalEventBus,
  type EventListener,
  type EventConfig,
  useStorage,
  useBatchStorage,
  storageUtils,
  serializers,
  type StorageType,
  type Serializer,
  type StorageConfig
} from './common'

// 从 device 导出的设备相关 hooks（保持向后兼容）
export {
  useGlobalData,
  type GlobalDataStore,
  type DeviceData,
  type UserData,
  type AppData,
  type DataListener
} from './device'

// ==================== 新的分层导出 ====================
// 通用 hooks
export * as CommonHooks from './common'

// Device 应用专用 hooks
export * as DeviceHooks from './device'

// Mall 应用专用 hooks  
export * as MallHooks from './mall'

// ==================== 版本信息 ====================
export const version = '1.0.0'

// ==================== 默认导出 ====================
const hooks = {
  version
}

export default hooks
