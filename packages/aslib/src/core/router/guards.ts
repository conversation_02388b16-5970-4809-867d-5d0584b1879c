/**
 * 全局路由守卫 - 应用类型驱动
 */

import type { Router } from 'vue-router'
import { getApplicationConfig } from '../applications'

// 应用类型配置
export const APP_GUARD_CONFIGS = {
  device: {
    tokenKey: 'ZX-DEVICE-TOKEN',
    appName: '设备端'
  },
  mall: {
    tokenKey: 'ZX-MALL-TOKEN',
    appName: '商城端'
  }
} as const

/**
 * 设置全局路由守卫
 */
export function setupGlobalRouterGuards(router: Router) {
  // 全局前置守卫 - 处理通用逻辑
  router.beforeEach(async (to, _from, next) => {
    console.log('🛡️ 路由守卫检查:', {
      to: to.path,
      fullPath: to.fullPath,
      name: to.name,
      meta: to.meta,
      params: to.params
    })

    // 设置页面标题
    if (to.meta?.title) {
      const appType = to.meta?.appType || 'device'
      const appName = APP_GUARD_CONFIGS[appType as keyof typeof APP_GUARD_CONFIGS]?.appName || 'H5应用'
      document.title = `${to.meta.title} - ${appName}`
    }

    // 全局登录检查（作为兜底，具体路由的守卫会在beforeEnter中处理）
    // 🎯 临时简化：只对明确需要认证的动态页面进行检查
    const needsAuth = to.meta?.requiresAuth && to.meta?.isDynamicPage
    console.log('🛡️ 登录检查:', {
      requiresAuth: to.meta?.requiresAuth,
      isDynamicPage: to.meta?.isDynamicPage,
      needsAuth,
      path: to.path
    })

    if (needsAuth) {
      const appType = to.meta?.appType || 'device'
      const appConfig = APP_GUARD_CONFIGS[appType as keyof typeof APP_GUARD_CONFIGS]
      const applicationConfig = getApplicationConfig(appType as any)

      if (appConfig && applicationConfig) {
        const token = localStorage.getItem(appConfig.tokenKey)
        console.log('🛡️ Token检查:', { appType, tokenKey: appConfig.tokenKey, token })

        if (!token || token === 'NOT_LOGIN') {
          const redirectPath = to.fullPath
          const loginPath = `/${appType}${applicationConfig.application.loginPage}`
          console.log('🛡️ 重定向到登录页:', { from: redirectPath, to: loginPath })
          next({
            path: loginPath,
            query: {
              redirect: redirectPath
            }
          })
          return
        }
      }
    }

    next()
  })
  
  // 路由错误处理
  router.onError((error) => {
    console.error('❌ 路由错误:', error)
  })
}

/**
 * 检查应用登录状态
 */
export function checkAppAuthentication(appType: string): boolean {
  const appConfig = APP_GUARD_CONFIGS[appType as keyof typeof APP_GUARD_CONFIGS]
  if (!appConfig) return false
  
  const token = localStorage.getItem(appConfig.tokenKey)
  return !!(token && token !== 'NOT_LOGIN')
}

/**
 * 获取应用登录页路径
 */
export function getAppLoginPath(appType: string): string {
  const applicationConfig = getApplicationConfig(appType)
  if (applicationConfig) {
    return `/${appType}${applicationConfig.application.loginPage}`
  }
  return '/device/login' // 默认值
}

/**
 * 获取应用首页路径
 */
export function getAppHomePath(appType: string): string {
  const applicationConfig = getApplicationConfig(appType)
  if (applicationConfig) {
    return `/${appType}${applicationConfig.application.homePage}`
  }
  return '/device/home' // 默认值
}

/**
 * 获取应用Token Key
 */
export function getAppTokenKey(appType: string): string {
  const appConfig = APP_GUARD_CONFIGS[appType as keyof typeof APP_GUARD_CONFIGS]
  return appConfig?.tokenKey || 'ZX-DEVICE-TOKEN'
}
