/**
 * 路由配置文件
 * 
 * 集中管理所有路由相关的配置常量，便于维护和修改
 */

/**
 * 路由格式配置
 */
export const ROUTE_PATTERNS = {
  // 低代码页面路由格式（简化，无需appId参数）
  LOWCODE: '',
  
  // 原生页面路由格式
  NATIVE: '/:appType',
  
  // 动态页面路由格式（用于H5端，简化）
  DYNAMIC: '/:pagePath+',
  
  // WebView路由格式
  WEBVIEW: '/webview/:url+'
} as const

/**
 * 特殊路径标识
 */
export const SPECIAL_PATHS = {
  // 首页路径
  HOME: '/home',
  
  // 根路径
  ROOT: '/',
  
  // 404页面
  NOT_FOUND: '/404'
} as const

/**
 * 路由前缀配置
 */
export const ROUTE_PREFIXES = {
  // 应用前缀分隔符
  SEPARATOR: '/',
  
  // 低代码应用前缀（移除app前缀，直接使用根路径）
  LOWCODE_APP: '',
  
  // API前缀
  API: '/api'
} as const

/**
 * 路由元数据默认值
 */
export const DEFAULT_ROUTE_META = {
  // 默认需要认证
  requiresAuth: true,
  
  // 默认可导航
  navigatable: true,
  
  // 默认启用
  enabled: true,
  
  // 默认不可配置
  configurable: false
} as const

/**
 * 路由构建工具函数
 */
export class RoutePathBuilder {
  /**
   * 构建低代码路由路径 - 简化版本，直接使用页面路径
   */
  static buildLowcodePath(pagePath: string): string {
    // 标准化路径
    return RoutePathBuilder.normalizePath(pagePath)
  }

  /**
   * 构建原生路由路径
   */
  static buildNativePath(pagePath: string, appType: string): string {
    const appPrefix = `${ROUTE_PREFIXES.SEPARATOR}${appType}`
    
    // 检查路径是否已包含应用前缀
    if (pagePath.startsWith(appPrefix)) {
      return pagePath
    }
    
    return `${appPrefix}${pagePath}`
  }

  /**
   * 构建动态路由路径 - 简化版本
   */
  static buildDynamicPath(): string {
    return ROUTE_PATTERNS.DYNAMIC
  }

  /**
   * 检查是否为特殊路径
   */
  static isSpecialPath(path: string): boolean {
    return Object.values(SPECIAL_PATHS).includes(path as any)
  }

  /**
   * 标准化路径
   */
  static normalizePath(path: string): string {
    // 确保路径以 / 开头
    if (!path.startsWith(ROUTE_PREFIXES.SEPARATOR)) {
      path = `${ROUTE_PREFIXES.SEPARATOR}${path}`
    }
    
    // 移除末尾的 /
    if (path.length > 1 && path.endsWith(ROUTE_PREFIXES.SEPARATOR)) {
      path = path.slice(0, -1)
    }
    
    return path
  }
}

/**
 * 路由验证工具
 */
export class RouteValidator {
  /**
   * 验证路由路径格式
   */
  static validatePath(path: string): boolean {
    // 路径不能为空
    if (!path || typeof path !== 'string') {
      return false
    }
    
    // 路径必须以 / 开头
    if (!path.startsWith(ROUTE_PREFIXES.SEPARATOR)) {
      return false
    }
    
    // 路径不能包含特殊字符（除了 / : + * ?）
    const validPathRegex = /^[a-zA-Z0-9\-_/:+*?]+$/
    return validPathRegex.test(path)
  }

  /**
   * 验证应用类型
   */
  static validateAppType(appType: string): boolean {
    // 应用类型不能为空
    if (!appType || typeof appType !== 'string') {
      return false
    }
    
    // 应用类型只能包含字母、数字、连字符
    const validAppTypeRegex = /^[a-zA-Z0-9\-_]+$/
    return validAppTypeRegex.test(appType)
  }
}
