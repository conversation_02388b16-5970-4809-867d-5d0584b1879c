/**
 * 路由模块统一导出
 */

// 导出路由生成器
export { RouteGenerator } from './generator'
export type { NavigatableRoute, GuardResult } from './generator'

// 导出路由守卫
export {
  setupGlobalRouterGuards,
  checkAppAuthentication,
  getAppLoginPath,
  getAppHomePath,
  getAppTokenKey,
  APP_GUARD_CONFIGS
} from './guards'

// 导出路由工具
export * from '../utils/route'

// 导出路由配置
export {
  RoutePathBuilder,
  RouteValidator,
  ROUTE_PATTERNS,
  SPECIAL_PATHS,
  ROUTE_PREFIXES,
  DEFAULT_ROUTE_META
} from './config'
