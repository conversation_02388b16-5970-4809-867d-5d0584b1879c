/**
 * 路由生成器
 * 
 * 从统一路由定义生成不同平台的路由配置
 */

import type { RouteRecordRaw } from 'vue-router'
import type { UnifiedRoute, RouteGuard } from '../types/application'
import { getUnifiedRoutesByApplicationType } from '../applications'
import { RoutePathBuilder, RouteValidator } from './config'
import { checkAppAuthentication } from './guards'

// 应用类型定义
type ApplicationTypeId = 'device' | 'mall'

// ==================== 路由生成器类 ====================

export class RouteGenerator {
  /**
   * 为H5应用生成Vue路由配置
   */
  static generateH5Routes(appType: ApplicationTypeId): RouteRecordRaw[] {
    const routes = getUnifiedRoutesByApplicationType(appType)

    return routes
      .filter(route => route.enabled && !route.configurable) // 🎯 只生成启用的非可配置路由
      .map(route => this.convertToVueRoute(route, appType))
  }

  /**
   * 为PC设计器生成可选择的路由
   */
  static generateNavigatableRoutes(appType: ApplicationTypeId): NavigatableRoute[] {
    const routes = getUnifiedRoutesByApplicationType(appType)

    return routes
      .filter(route => route.navigatable && route.enabled)
      .map(route => ({
        path: route.path,
        name: route.name,
        title: route.title,
        category: route.category,
        requiresAuth: route.requiresAuth,
        configurable: route.configurable,
        icon: route.icon,
        description: route.description
      }))
  }
  
  /**
   * 生成路由守卫函数
   */
  static generateRouteGuards(guards: RouteGuard[]): any[] {
    return guards
      .filter(guard => guard.enabled)
      .map(guard => this.createGuardFunction(guard))
  }
  
  /**
   * 转换为Vue路由配置
   */
  private static convertToVueRoute(route: UnifiedRoute, appType: ApplicationTypeId): RouteRecordRaw {
    const baseMeta = {
      title: route.title,
      requiresAuth: route.requiresAuth,
      configurable: route.configurable,
      category: route.category,
      appType,
      ...route.meta
    }

    // 处理组件或重定向
    if (route.redirect) {
      return {
        path: this.buildRoutePath(route, appType),
        name: `${appType}${route.name}`,
        redirect: route.redirect,
        meta: baseMeta
      }
    } else {
      const vueRoute: RouteRecordRaw = {
        path: this.buildRoutePath(route, appType),
        name: `${appType}${route.name}`,
        component: this.createComponentLoader(route, appType),
        meta: {
          ...baseMeta,
          isDynamicPage: route.configurable,
          componentPath: route.component
        }
      }

      // 处理路由守卫
      if (route.guards && route.guards.length > 0) {
        const guards = this.generateRouteGuards(route.guards)
        if (guards.length > 0) {
          vueRoute.beforeEnter = guards
        }
      }

      return vueRoute
    }
  }

  /**
   * 创建组件加载器 - 返回占位符组件
   */
  private static createComponentLoader(route: UnifiedRoute, appType: ApplicationTypeId) {
    // 返回一个简单的占位符组件，实际组件由H5应用根据meta信息加载
    return () => Promise.resolve({
      template: '<div>Loading...</div>'
    })
  }
  
  /**
   * 构建路由路径
   */
  private static buildRoutePath(route: UnifiedRoute, appType: ApplicationTypeId): string {
    // 验证输入参数
    if (!RouteValidator.validatePath(route.path)) {
      throw new Error(`Invalid route path: ${route.path}`)
    }

    if (!RouteValidator.validateAppType(appType)) {
      throw new Error(`Invalid app type: ${appType}`)
    }

    if (route.configurable) {
      // 低代码页面使用动态路由格式
      return RoutePathBuilder.buildLowcodePath(route.path)
    } else {
      // 原生页面使用应用类型前缀
      return RoutePathBuilder.buildNativePath(route.path, appType)
    }
  }
  
  /**
   * 创建守卫函数
   */
  private static createGuardFunction(guard: RouteGuard): any {
    return async (to: any, from: any, next: any) => {
      try {
        const result = await this.executeGuard(guard, to, from)
        
        if (result.allowed) {
          next()
        } else if (result.redirect) {
          next(result.redirect)
        } else {
          next(false)
        }
      } catch (error) {
        console.error(`路由守卫执行失败 [${guard.name}]:`, error)
        next(false)
      }
    }
  }
  
  /**
   * 执行守卫逻辑
   */
  private static async executeGuard(guard: RouteGuard, to: any, from: any): Promise<GuardResult> {
    const { config } = guard
    
    // 权限检查
    if (config.permissions && config.permissions.length > 0) {
      const hasPermission = await this.checkPermissions(config.permissions)
      if (!hasPermission) {
        return {
          allowed: false,
          redirect: config.redirectTo || '/403',
          message: config.messageText || '权限不足'
        }
      }
    }
    
    // 角色检查
    if (config.roles && config.roles.length > 0) {
      const hasRole = await this.checkRoles(config.roles)
      if (!hasRole) {
        return {
          allowed: false,
          redirect: config.redirectTo || '/403',
          message: config.messageText || '角色权限不足'
        }
      }
    }
    
    // 登录检查
    if (config.requiresAuth) {
      const isAuthenticated = await this.checkAuthentication()
      if (!isAuthenticated) {
        const loginPath = config.redirectToLogin || `/${to.meta.appType}/login`
        return {
          allowed: false,
          redirect: `${loginPath}?redirect=${encodeURIComponent(to.fullPath)}`,
          message: config.messageText || '请先登录'
        }
      }
    }
    
    // 自定义验证
    if (config.customValidator) {
      const isValid = await this.executeCustomValidator(config.customValidator, to, from)
      if (!isValid) {
        return {
          allowed: false,
          redirect: config.redirectTo,
          message: config.messageText || '验证失败'
        }
      }
    }
    
    // 重定向条件检查
    if (config.redirectCondition && config.redirectTo) {
      const shouldRedirect = await this.evaluateCondition(config.redirectCondition, to, from)
      if (shouldRedirect) {
        return {
          allowed: false,
          redirect: config.redirectTo,
          message: config.messageText
        }
      }
    }
    
    return { allowed: true }
  }
  
  // 权限检查方法
  private static async checkPermissions(_permissions: string[]): Promise<boolean> {
    // 实际项目中根据用户权限检查
    return true
  }

  private static async checkRoles(_roles: string[]): Promise<boolean> {
    // 实际项目中根据用户角色检查
    return true
  }

  private static async checkAuthentication(): Promise<boolean> {
    // 检查登录状态 - 检查所有应用类型的token
    return checkAppAuthentication('device') || checkAppAuthentication('mall')
  }

  private static async executeCustomValidator(validator: string, to: any, _from: any): Promise<boolean> {
    // 执行自定义验证逻辑
    switch (validator) {
      case 'checkLoginStatus':
        const appType = to.meta?.appType || 'device'
        return checkAppAuthentication(appType)
      
      default:
        console.warn(`未知的自定义验证器: ${validator}`)
        return true
    }
  }

  private static async evaluateCondition(condition: string, to: any, _from: any): Promise<boolean> {
    // 评估条件表达式
    switch (condition) {
      case 'isLoggedIn':
        const appType = to.meta?.appType || 'device'
        return checkAppAuthentication(appType)
      
      default:
        console.warn(`未知的条件表达式: ${condition}`)
        return false
    }
  }
}

// ==================== 类型定义 ====================

export interface NavigatableRoute {
  path: string
  name: string
  title: string
  category: string
  requiresAuth: boolean
  configurable: boolean
  icon?: string
  description?: string
}

export interface GuardResult {
  allowed: boolean
  redirect?: string
  message?: string
}

// ==================== 导出 ====================

export default RouteGenerator
