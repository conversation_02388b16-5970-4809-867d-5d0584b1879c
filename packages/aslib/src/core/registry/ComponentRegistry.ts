// 组件注册管理器 - 扩展版本

import type { ExtendedComponentMetadata } from '../types/ExtendedComponentMetadata'
import type { ComponentConfig } from '../types/schema'
import { UnifiedEventManager } from '../events/UnifiedEventManager'

// 组件注册项
export interface ComponentRegistration {
  id: string
  type: string
  metadata: ExtendedComponentMetadata
  component: any
  defaultConfig: ComponentConfig
  configSchema?: any
}

// 组件注册表（保持向后兼容）
const componentRegistry = new Map<string, any>()

// 扩展的组件注册表
const extendedComponentRegistry = new Map<string, ComponentRegistration>()
const componentsByCategory = new Map<string, ComponentRegistration[]>()

// === 原有API（保持向后兼容） ===

// 注册单个组件
export function registerComponent(name: string, component: any) {
  componentRegistry.set(name, component)

  // ✨ 自动注册组件事件
  UnifiedEventManager.registerComponentEvents(name, component)
}

// 注册多个组件
export function registerComponents(components: Record<string, any>) {
  console.log('🔄 批量注册组件并自动发现事件...')

  Object.entries(components).forEach(([name, component]) => {
    registerComponent(name, component)
  })

  // 输出事件注册统计
  const stats = UnifiedEventManager.getEventStats()
  console.log(`✅ 组件注册完成，共注册 ${stats.totalComponents} 个组件，${stats.totalEvents} 个事件`)
  console.log('📊 组件事件详情:', stats.componentStats)
}

// 获取组件
export function getComponent(name: string) {
  return componentRegistry.get(name)
}

// 获取所有已注册的组件
export function getAllComponents() {
  console.log('📦 获取所有已注册组件:', componentRegistry)
  return Array.from(componentRegistry.entries())
}

// 检查组件是否已注册
export function hasComponent(name: string): boolean {
  return componentRegistry.has(name)
}

// 取消注册组件
export function unregisterComponent(name: string) {
  componentRegistry.delete(name)
}

// 清空所有组件
export function clearRegistry() {
  componentRegistry.clear()
}

// 获取组件数量
export function getComponentCount(): number {
  return componentRegistry.size
}

// 获取所有组件名称
export function getComponentNames(): string[] {
  return Array.from(componentRegistry.keys())
}

// === 新的扩展API ===

// 注册低代码组件
export function registerLowcodeComponent(registration: ComponentRegistration): void {
  const { type, metadata } = registration

  // 验证注册信息
  validateRegistration(registration)

  // 注册到扩展注册表
  extendedComponentRegistry.set(type, registration)

  // 同时注册到原有注册表（保持兼容性）
  componentRegistry.set(type, registration.component)

  // 按分类索引
  const category = metadata.category
  if (!componentsByCategory.has(category)) {
    componentsByCategory.set(category, [])
  }
  componentsByCategory.get(category)!.push(registration)

  console.log(`✅ 低代码组件 ${metadata.displayName} (${type}) 注册成功`)
}

// 批量注册低代码组件
export function registerLowcodeComponents(registrations: ComponentRegistration[]): void {
  registrations.forEach(registration => {
    try {
      registerLowcodeComponent(registration)
    } catch (error) {
      console.error(`❌ 组件 ${registration.type} 注册失败:`, error)
    }
  })
}

// 获取低代码组件
export function getLowcodeComponent(type: string): ComponentRegistration | null {
  return extendedComponentRegistry.get(type) || null
}

// 获取所有低代码组件
export function getAllLowcodeComponents(): ComponentRegistration[] {
  return Array.from(extendedComponentRegistry.values())
}

// 按分类获取组件
export function getComponentsByCategory(category: string): ComponentRegistration[] {
  return componentsByCategory.get(category) || []
}

// 搜索组件
export function searchComponents(query: string): ComponentRegistration[] {
  const searchTerm = query.toLowerCase()

  return getAllLowcodeComponents().filter(registration => {
    const { metadata } = registration

    // 搜索名称
    if (metadata.name.toLowerCase().includes(searchTerm) ||
        metadata.displayName.toLowerCase().includes(searchTerm)) {
      return true
    }

    // 搜索描述
    if (metadata.description?.toLowerCase().includes(searchTerm)) {
      return true
    }

    // 搜索标签
    if (metadata.tags?.some(tag => tag.toLowerCase().includes(searchTerm))) {
      return true
    }

    return false
  })
}

// 获取可拖拽组件
export function getDraggableComponents(): ComponentRegistration[] {
  return getAllLowcodeComponents().filter(registration =>
    registration.metadata.editor?.configurable !== false
  )
}

// 获取组件分类列表
export function getCategories(): string[] {
  return Array.from(componentsByCategory.keys())
}

// 验证组件注册信息
function validateRegistration(registration: ComponentRegistration): void {
  const { type, metadata, component } = registration

  if (!type) {
    throw new Error('组件类型不能为空')
  }

  if (!metadata) {
    throw new Error('组件元数据不能为空')
  }

  if (!metadata.name) {
    throw new Error('组件名称不能为空')
  }

  if (!metadata.displayName) {
    throw new Error('组件显示名称不能为空')
  }

  if (!component) {
    throw new Error('组件实例不能为空')
  }
}

// 组件装饰器
export function LowcodeComponent(metadata: ExtendedComponentMetadata) {
  return function<T extends new (...args: any[]) => any>(constructor: T) {
    // 将元数据附加到组件构造函数
    (constructor as any).__lowcodeMetadata = metadata

    return constructor
  }
}
