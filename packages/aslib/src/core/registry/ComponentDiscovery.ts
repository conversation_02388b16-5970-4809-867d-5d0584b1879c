// 组件自动发现和注册工具

import type { ExtendedComponentMetadata } from '../types/ExtendedComponentMetadata'
import type { ComponentConfig } from '../types/schema'
import { registerLowcodeComponent, type ComponentRegistration } from './ComponentRegistry'

// 组件发现器
export class ComponentDiscovery {
  private discoveredComponents: Map<string, ComponentRegistration> = new Map()
  
  // 从组件构造函数发现并注册组件
  discoverFromConstructor(constructor: any, componentName?: string): ComponentRegistration | null {
    try {
      // 检查是否有低代码元数据
      const metadata = this.extractMetadata(constructor)
      if (!metadata) {
        console.warn(`⚠️ 组件 ${componentName || constructor.name} 没有低代码元数据`)
        return null
      }
      
      // 创建注册项
      const registration: ComponentRegistration = {
        id: metadata.name,
        type: metadata.name,
        metadata,
        component: constructor,
        defaultConfig: this.generateDefaultConfig(metadata),
        configSchema: this.generateConfigSchema(metadata)
      }
      
      // 缓存发现的组件
      this.discoveredComponents.set(metadata.name, registration)
      
      console.log(`🔍 发现低代码组件: ${metadata.displayName} (${metadata.name})`)
      return registration
      
    } catch (error) {
      console.error(`❌ 发现组件失败:`, error)
      return null
    }
  }
  
  // 批量发现组件
  discoverFromComponents(components: Record<string, any>): ComponentRegistration[] {
    const discovered: ComponentRegistration[] = []
    
    Object.entries(components).forEach(([name, component]) => {
      const registration = this.discoverFromConstructor(component, name)
      if (registration) {
        discovered.push(registration)
      }
    })
    
    return discovered
  }
  
  // 自动注册发现的组件
  autoRegisterDiscovered(): void {
    const registrations = Array.from(this.discoveredComponents.values())
    
    registrations.forEach(registration => {
      try {
        registerLowcodeComponent(registration)
      } catch (error) {
        console.error(`❌ 自动注册组件 ${registration.type} 失败:`, error)
      }
    })
    
    console.log(`🎯 自动注册了 ${registrations.length} 个低代码组件`)
  }
  
  // 获取发现的组件
  getDiscoveredComponents(): ComponentRegistration[] {
    return Array.from(this.discoveredComponents.values())
  }
  
  // 清空发现的组件
  clearDiscovered(): void {
    this.discoveredComponents.clear()
  }
  
  // 提取组件元数据
  private extractMetadata(constructor: any): ExtendedComponentMetadata | null {
    // 尝试多种方式获取元数据
    let metadata = constructor.__lowcodeMetadata ||
                   constructor.__vccOpts?.__lowcodeMetadata ||
                   constructor.default?.__lowcodeMetadata
    
    if (!metadata) {
      // 尝试从组件选项中获取
      const options = constructor.__vccOpts || constructor.default || constructor
      metadata = options.__lowcodeMetadata
    }
    
    return metadata || null
  }
  
  // 生成默认配置
  private generateDefaultConfig(metadata: ExtendedComponentMetadata): ComponentConfig {
    const defaultProps: Record<string, any> = {}
    const defaultStyle: Record<string, any> = {}
    
    // 生成默认属性
    metadata.props?.forEach(propConfig => {
      if (propConfig.defaultValue !== undefined) {
        defaultProps[propConfig.name] = propConfig.defaultValue
      }
    })
    
    // 生成默认样式
    metadata.styles?.forEach(styleConfig => {
      if (styleConfig.defaultValue !== undefined) {
        styleConfig.properties.forEach(property => {
          defaultStyle[property] = styleConfig.defaultValue
        })
      }
    })
    
    return {
      id: '',
      type: metadata.name,
      props: defaultProps,
      style: defaultStyle,
      visible: true,
      editable: metadata.editor?.configurable !== false,
      metadata: metadata.name
    }
  }
  
  // 生成配置模式
  private generateConfigSchema(metadata: ExtendedComponentMetadata): any {
    const schema = {
      type: 'object',
      properties: {
        props: {
          type: 'object',
          properties: {},
          title: '属性配置'
        },
        style: {
          type: 'object',
          properties: {},
          title: '样式配置'
        },
        events: {
          type: 'object',
          properties: {},
          title: '事件配置'
        }
      }
    }
    
    // 生成属性模式
    metadata.props?.forEach(propConfig => {
      (schema.properties.props as any).properties[propConfig.name] = {
        type: this.mapPropTypeToSchemaType(propConfig.type),
        title: propConfig.label,
        description: propConfig.description,
        default: propConfig.defaultValue
      }
      
      if (propConfig.options) {
        (schema.properties.props as any).properties[propConfig.name].enum = 
          propConfig.options.map(opt => opt.value)
      }
    })
    
    // 生成样式模式
    metadata.styles?.forEach(styleConfig => {
      styleConfig.properties.forEach(property => {
        (schema.properties.style as any).properties[property] = {
          type: 'string',
          title: `${styleConfig.label} - ${property}`,
          default: styleConfig.defaultValue
        }
      })
    })
    
    // 生成事件模式
    metadata.events?.forEach(eventConfig => {
      (schema.properties.events as any).properties[eventConfig.name] = {
        type: 'string',
        title: eventConfig.label,
        description: eventConfig.description
      }
    })
    
    return schema
  }
  
  // 映射属性类型到JSON Schema类型
  private mapPropTypeToSchemaType(propType: string): string {
    switch (propType) {
      case 'string':
      case 'textarea':
      case 'color':
      case 'image':
      case 'select':
        return 'string'
      case 'number':
        return 'number'
      case 'boolean':
      case 'switch':
        return 'boolean'
      case 'object':
        return 'object'
      case 'array':
        return 'array'
      default:
        return 'string'
    }
  }
}

// 创建全局发现器实例
export const componentDiscovery = new ComponentDiscovery()

// 便捷函数：发现并注册组件
export function discoverAndRegisterComponents(components: Record<string, any>): void {
  const discovered = componentDiscovery.discoverFromComponents(components)
  
  discovered.forEach(registration => {
    try {
      registerLowcodeComponent(registration)
    } catch (error) {
      console.error(`❌ 注册组件 ${registration.type} 失败:`, error)
    }
  })
  
  console.log(`🎯 发现并注册了 ${discovered.length} 个低代码组件`)
}

// 便捷函数：从单个组件发现并注册
export function discoverAndRegisterComponent(component: any, name?: string): boolean {
  const registration = componentDiscovery.discoverFromConstructor(component, name)
  
  if (registration) {
    try {
      registerLowcodeComponent(registration)
      return true
    } catch (error) {
      console.error(`❌ 注册组件 ${registration.type} 失败:`, error)
    }
  }
  
  return false
}
