// 导出类型
export type * from './types/schema'
export type * from './types/ComponentMetadata'
export type * from './types/ExtendedComponentMetadata'
export type * from './types/UnifiedEvents'

// 导出元数据系统
export {
  StandardComponentMetadataExtractor,
  defaultMetadataExtractor,
  extractComponentInfo
} from './metadata/ComponentMetadataExtractor'
export {
  MemoryComponentMetadataCache,
  defaultMetadataCache,
  withCache
} from './metadata/ComponentMetadataCache'

// 导出错误处理系统
export {
  ErrorType,
  ErrorSeverity,
  DefaultErrorHandler,
  globalErrorHandler,
  handleComponentError,
  createErrorInfo
} from './error/ErrorHandler'
export type {
  ErrorInfo,
  RecoveryAction,
  ErrorHandler
} from './error/ErrorHandler'

// 导出性能优化系统
export {
  ComponentLazyLoader,
  defaultComponentLazyLoader,
  lazyLoadComponent,
  preloadComponent
} from './performance/ComponentLazyLoader'
export {
  PerformanceMonitor,
  defaultPerformanceMonitor,
  performanceMonitor,
  measurePerformance
} from './performance/PerformanceMonitor'
export type {
  LazyLoadConfig,
  ComponentLoadResult,
  LoadingState
} from './performance/ComponentLazyLoader'
export type {
  PerformanceMetric,
  PerformanceReport
} from './performance/PerformanceMonitor'

// 导出核心类
export { DataManager, dataManager } from './data/DataManager'
export { EventManager } from './events/EventManager'
export {
  UnifiedEventManager,
  unifiedEventManager,
  EVENT_NAME_MAP,
  EVENT_DESCRIPTION_RULES
} from './events/UnifiedEventManager'

// 导出渲染器组件
export { default as ComponentRenderer } from './renderer/ComponentRenderer.vue'
export { default as PageRenderer } from './renderer/PageRenderer.vue'

// 导出组件注册功能
export {
  registerComponent,
  registerComponents,
  getComponent,
  getAllComponents,
  hasComponent,
  unregisterComponent,
  clearRegistry,
  getComponentCount,
  getComponentNames,
  // 新的低代码组件注册功能
  registerLowcodeComponent,
  registerLowcodeComponents,
  getLowcodeComponent,
  getAllLowcodeComponents,
  getComponentsByCategory,
  searchComponents,
  getDraggableComponents,
  getCategories,
  LowcodeComponent
} from './registry/ComponentRegistry'

// 导出组件发现功能
export {
  ComponentDiscovery,
  componentDiscovery,
  discoverAndRegisterComponents,
  discoverAndRegisterComponent
} from './registry/ComponentDiscovery'

// 导出组件注册类型
export type {
  ComponentRegistration
} from './registry/ComponentRegistry'

// 导出适配器
export { DeviceApiAdapter, createDeviceApiAdapter, API_ENDPOINTS } from './adapters/DeviceApiAdapter'
export type { DeviceApiClient, ApiResponse } from './adapters/DeviceApiAdapter'

// 导出工具函数
export * from './utils'
export * from './utils/env'

// 示例配置已移除，现在通过API动态获取页面配置

// 导出配置
export * from './config/constants'

// 导出应用类型和路由管理
export * from './applications'

// 导出路由模块
export * from './router'

// 导出类型定义
export type {
  ApplicationType,
  PageRoute,
  UnifiedRoute,
  RouteGuard,
  RouteGuardConfig,
  PageCategory,
  ApplicationConfig
} from './types/application'
export * from './config/themes'

// 导出新的API架构
export * from './api'

// 版本信息
export const version = '0.1.0'
