import { ref, reactive } from 'vue'
import type { DataSource, RenderContext } from '../types/schema'

// 数据缓存
const dataCache = new Map<string, { data: any; timestamp: number; ttl: number }>()

// 数据管理器类
export class DataManager {
  private context: RenderContext

  constructor(context: RenderContext = {}) {
    this.context = reactive(context)
  }

  // 获取数据
  async fetchData(dataSource: DataSource): Promise<any> {
    const { type, source, params, transform, cache, refresh } = dataSource

    // 处理缓存配置
    const cacheEnabled = typeof cache === 'boolean' ? cache : cache?.enabled

    // 处理刷新配置
    const refreshInterval = typeof refresh === 'number' ? refresh : refresh?.interval || 300000

    // 检查缓存
    if (cacheEnabled && source) {
      const cacheKey = this.getCacheKey(source, params)
      const cached = dataCache.get(cacheKey)
      if (cached && Date.now() - cached.timestamp < refreshInterval) {
        return this.transformData(cached.data, transform)
      }
    }

    let data: any

    switch (type) {
      case 'static':
        data = params || {}
        break
      default:
        throw new Error(`Unsupported data source type: ${type}`)
    }

    // 缓存数据
    if (cacheEnabled && source) {
      const cacheKey = this.getCacheKey(source, params)
      dataCache.set(cacheKey, {
        data,
        timestamp: Date.now(),
        ttl: refreshInterval
      })
    }

    return this.transformData(data, transform)
  }




  // 数据转换
  private transformData(data: any, transform?: string): any {
    if (!transform) return data

    try {
      const func = new Function('data', 'context', `
        return (${transform})(data, context)
      `)
      return func(data, this.context)
    } catch (error) {
      console.error('Data transform error:', error)
      return data
    }
  }

  // 生成缓存键
  private getCacheKey(source: string, params?: Record<string, any>): string {
    return `${source}:${JSON.stringify(params || {})}`
  }

  // 更新上下文
  updateContext(newContext: Partial<RenderContext>): void {
    Object.assign(this.context, newContext)
  }

  // 获取上下文
  getContext(): RenderContext {
    return this.context
  }

  // 清除缓存
  clearCache(pattern?: string): void {
    if (pattern) {
      for (const key of dataCache.keys()) {
        if (key.includes(pattern)) {
          dataCache.delete(key)
        }
      }
    } else {
      dataCache.clear()
    }
  }

  // 创建响应式数据
  createReactiveData(dataSource: DataSource) {
    const data = ref(null)
    const loading = ref(false)
    const error = ref(null)

    const load = async () => {
      loading.value = true
      error.value = null
      try {
        data.value = await this.fetchData(dataSource)
      } catch (err) {
        error.value = err as any
      } finally {
        loading.value = false
      }
    }

    // 自动刷新
    const refreshInterval = typeof dataSource.refresh === 'number'
      ? dataSource.refresh
      : dataSource.refresh?.interval

    if (refreshInterval && refreshInterval > 0) {
      setInterval(load, refreshInterval)
    }

    return {
      data,
      loading,
      error,
      load,
      reload: load
    }
  }
}

// 创建全局数据管理器实例
export const dataManager = new DataManager({})
