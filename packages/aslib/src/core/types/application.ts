/**
 * 应用类型定义
 *
 * 定义低代码平台支持的应用类型
 * 为PC端设计器提供应用类型选择和路由管理
 */

// ==================== 应用类型接口 ====================

export interface ApplicationType {
  /** 应用类型ID */
  id: string
  /** 应用类型名称 */
  name: string
  /** 应用类型描述 */
  description: string
  /** 应用图标 */
  icon: string
  /** 应用颜色主题 */
  color: string
  /** 是否启用 */
  enabled: boolean
  /** 排序权重 */
  order: number
  /** 默认页面路径 */
  defaultPage: string
  /** 登录页面路径 */
  loginPage: string
  /** 首页路径 */
  homePage: string
  /** 支持的功能特性 */
  features: string[]
}

// ==================== 页面路由接口 ====================

export interface PageRoute {
  /** 路由路径 */
  path: string
  /** 路由名称 */
  name: string
  /** 页面标题 */
  title: string
  /** 页面描述 */
  description?: string
  /** 页面分类 */
  category: string
  /** 是否需要认证 */
  requiresAuth: boolean
  /** 是否可配置（低代码） */
  configurable: boolean
  /** 是否可以被跳转选择 */
  navigatable: boolean
  /** 页面图标 */
  icon?: string
  /** 参数说明 */
  params?: PageParam[]
}

// ==================== 统一路由定义（增强版） ====================

export interface UnifiedRoute {
  // 基础信息
  /** 路由路径 */
  path: string
  /** 路由名称 */
  name: string
  /** 页面标题 */
  title: string
  /** 页面描述 */
  description?: string
  /** 页面图标 */
  icon?: string

  // 分类和权限
  /** 页面分类 */
  category: 'auth' | 'home' | 'business' | 'user' | 'payment' | 'system'
  /** 是否需要认证 */
  requiresAuth: boolean

  // 功能控制
  /** 是否可低代码配置 */
  configurable: boolean
  /** 是否可在PC设计器中选择跳转 */
  navigatable: boolean
  /** 是否启用（渐进式开放） */
  enabled: boolean

  // Vue路由配置
  /** 组件路径，如 './pages/Login/index.vue' */
  component?: string
  /** 重定向路径 */
  redirect?: string
  /** Vue路由元信息 */
  meta?: Record<string, any>

  // 路由守卫配置
  /** 路由守卫列表 */
  guards?: RouteGuard[]

  // 参数配置
  /** 参数说明 */
  params?: PageParam[]
}

export interface PageParam {
  /** 参数名 */
  name: string
  /** 参数类型 */
  type: 'string' | 'number' | 'boolean'
  /** 是否必需 */
  required: boolean
  /** 参数描述 */
  description?: string
  /** 默认值 */
  defaultValue?: any
}

// ==================== 路由守卫接口 ====================

export interface RouteGuard {
  /** 守卫类型 */
  type: 'beforeEnter' | 'beforeLeave'
  /** 守卫名称 */
  name: string
  /** 守卫描述 */
  description?: string
  /** 是否启用 */
  enabled: boolean
  /** 守卫配置 */
  config: RouteGuardConfig
}

export interface RouteGuardConfig {
  // 权限检查
  permissions?: string[]
  roles?: string[]

  // 登录检查
  requiresAuth?: boolean
  redirectToLogin?: string

  // 自定义验证
  customValidator?: string // 函数名或表达式

  // 重定向配置
  redirectTo?: string
  redirectCondition?: string

  // 提示配置
  showMessage?: boolean
  messageText?: string
  messageType?: 'info' | 'warning' | 'error'

  // 数据预加载
  preloadData?: string[]

  // 缓存控制
  cacheControl?: {
    enabled: boolean
    duration: number // 秒
    key?: string
  }
}

// ==================== 页面分类接口 ====================

export interface PageCategory {
  /** 分类ID */
  id: string
  /** 分类名称 */
  name: string
  /** 分类图标 */
  icon: string
  /** 排序权重 */
  order: number
}

// ==================== 应用配置接口 ====================

export interface ApplicationConfig {
  /** 应用信息 */
  application: ApplicationType
  /** 路由配置 */
  routes: Record<string, PageRoute>
}

// ==================== 页面分类常量 ====================

export const PAGE_CATEGORIES: Record<string, PageCategory> = {
  auth: { id: 'auth', name: '认证页面', icon: 'lock', order: 1 },
  home: { id: 'home', name: '首页', icon: 'home', order: 2 },
  business: { id: 'business', name: '业务页面', icon: 'briefcase', order: 3 },
  payment: { id: 'payment', name: '支付相关', icon: 'credit-card', order: 4 },
  user: { id: 'user', name: '用户中心', icon: 'user', order: 5 },
  system: { id: 'system', name: '系统页面', icon: 'settings', order: 6 }
} as const

// ==================== 类型导出 ====================

export type ApplicationTypeId = 'device' | 'mall'
export type PageCategoryId = keyof typeof PAGE_CATEGORIES
