// JSON Schema 类型定义
export interface JSONSchemaProperty {
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  title?: string
  description?: string
  default?: any
  enum?: any[]
  minimum?: number
  maximum?: number
  multipleOf?: number
  properties?: Record<string, JSONSchemaProperty>
  items?: JSONSchemaProperty
  required?: string[]
}

export interface ComponentConfigSchema {
  type: 'object'
  properties: {
    data?: JSONSchemaProperty
    config?: JSONSchemaProperty
  }
  required?: string[]
}

// 组件元数据接口
export interface ComponentMetadata {
  /** 组件显示名称 */
  name: string
  /** 组件分类 */
  category: 'business' | 'layout' | 'form' | 'display' | 'other'
  /** 组件图标 (Iconify 图标名) */
  icon: string
  /** 组件描述 */
  description: string
  /** 组件标签 (可选) */
  tags?: string[]
  /** 是否为容器组件 */
  isContainer?: boolean
}

// 组件默认配置接口
export interface ComponentDefaultConfig {
  /** 默认属性 */
  props: Record<string, any>
  /** 默认样式 */
  style: Record<string, any>
  /** 默认事件 (可选) */
  events?: Record<string, any>
}

// 组件库项目接口
export interface ComponentLibraryItem {
  /** 唯一标识 */
  id: string
  /** 组件类型名 */
  type: string
  /** 组件元数据 */
  metadata: ComponentMetadata
  /** 默认配置 */
  defaultConfig: ComponentDefaultConfig
  /** 配置模式 (用于属性编辑器) */
  configSchema: ComponentConfigSchema
}

// 组件注册配置接口
export interface ComponentRegistrationConfig {
  /** 组件元数据 */
  metadata: ComponentMetadata
  /** 默认配置 */
  defaultConfig: ComponentDefaultConfig
  /** 配置模式 */
  configSchema: ComponentConfigSchema
}

// 标准化的组件元数据提取器
export interface ComponentMetadataExtractor {
  /** 提取组件元数据 */
  extractMetadata(component: any): ComponentMetadata | null
  /** 提取默认配置 */
  extractDefaultConfig(component: any): ComponentDefaultConfig | null
  /** 提取配置模式 */
  extractConfigSchema(component: any): ComponentConfigSchema | null
}

// 组件元数据缓存接口
export interface ComponentMetadataCache {
  /** 获取缓存的元数据 */
  get(componentType: string): ComponentLibraryItem | null
  /** 设置缓存的元数据 */
  set(componentType: string, item: ComponentLibraryItem): void
  /** 清除缓存 */
  clear(): void
  /** 检查是否有缓存 */
  has(componentType: string): boolean
}
