/**
 * 环境变量类型声明
 */

/// <reference types="vite/client" />

interface ImportMetaEnv {
  // API配置
  readonly VITE_DEVICE_AN_API_URL: string
  readonly VITE_LOWCODE_API_URL: string
  readonly VITE_PROJECT_A_API_URL: string
  readonly VITE_PROJECT_B_API_URL: string
  
  // 应用配置
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_VERSION: string
  readonly VITE_APP_ENV: 'development' | 'production' | 'test'
  
  // 功能开关
  readonly VITE_DEBUG_MODE: string
  readonly VITE_ENABLE_PERFORMANCE_MONITOR: string
  readonly VITE_ENABLE_ERROR_REPORTING: string
  readonly VITE_ENABLE_HOT_RELOAD: string
  readonly VITE_ENABLE_CODE_EDITOR: string
  
  // 设计器配置
  readonly VITE_CANVAS_WIDTH: string
  readonly VITE_CANVAS_HEIGHT: string
  readonly VITE_ENABLE_GRID: string
  readonly VITE_ENABLE_RULER: string
  
  // 第三方服务
  readonly VITE_SENTRY_DSN: string
  readonly VITE_ANALYTICS_ID: string
  readonly VITE_CDN_URL: string
  
  // 开发配置
  readonly VITE_DEV_PORT: string
  readonly VITE_HTTPS: string
  readonly VITE_PROXY_TARGET: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// Node.js环境变量扩展
declare namespace NodeJS {
  interface ProcessEnv {
    // API配置
    DEVICE_AN_API_URL?: string
    LOWCODE_API_URL?: string
    PROJECT_A_API_URL?: string
    PROJECT_B_API_URL?: string
    
    // 应用配置
    APP_TITLE?: string
    APP_VERSION?: string
    NODE_ENV?: 'development' | 'production' | 'test'
    
    // 功能开关
    DEBUG_MODE?: string
    ENABLE_PERFORMANCE_MONITOR?: string
    ENABLE_ERROR_REPORTING?: string
    
    // 第三方服务
    SENTRY_DSN?: string
    ANALYTICS_ID?: string
    CDN_URL?: string
  }
}
