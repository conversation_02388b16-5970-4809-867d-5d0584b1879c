/**
 * 统一事件架构类型定义
 * 完全数据驱动，零硬编码
 */

// 可点击元素类型
export type ClickableElementType = 'button' | 'menu' | 'action' | 'link' | 'card' | 'icon'

// 统一的点击事件数据
export interface UnifiedClickEventData {
  elementType: ClickableElementType  // 元素类型
  elementId: string                  // 元素ID（从配置中来）
  elementData?: any                  // 元素的完整数据
  componentType: string              // 组件类型
  timestamp?: number                 // 事件时间戳
}

// 统一的组件事件接口
export interface UnifiedComponentEvents {
  click: [eventData: UnifiedClickEventData]
}

// 可点击元素配置
export interface ClickableElementConfig {
  id: string                        // 唯一标识
  type: ClickableElementType        // 元素类型
  label: string                     // 显示文本
  icon?: string                     // 图标
  position?: string                 // 位置标识
  visible?: boolean                 // 是否可见
  disabled?: boolean                // 是否禁用
  description?: string              // 描述
  defaultAction?: ActionConfig      // 默认动作配置
  metadata?: Record<string, any>    // 额外元数据
}

// 动作配置
export interface ActionConfig {
  type: 'navigate' | 'external' | 'webview' | 'custom' | 'api' | 'message'
  target?: string                   // 目标地址
  params?: Record<string, any>      // 参数
  handler?: string                  // 自定义处理代码
  message?: string                  // 消息内容
  confirm?: {                       // 确认对话框
    title: string
    message: string
    confirmText?: string
    cancelText?: string
  }
}

// 事件配置规则
export interface EventConfigRule {
  elementType?: ClickableElementType  // 匹配的元素类型
  elementId?: string                  // 匹配的元素ID
  when?: string                       // 条件表达式
  action: ActionConfig                // 执行的动作
  priority?: number                   // 优先级（数字越大优先级越高）
}

// 组件的统一事件配置
export interface UnifiedEventConfig {
  click?: EventConfigRule[]           // 点击事件规则列表
}

// 组件配置中的可点击元素定义
export interface ComponentClickableElements {
  buttons?: ClickableElementConfig[]  // 按钮列表
  menus?: ClickableElementConfig[]    // 菜单项列表
  actions?: ClickableElementConfig[]  // 操作项列表
  links?: ClickableElementConfig[]    // 链接列表
  others?: ClickableElementConfig[]   // 其他可点击元素
}

// 扩展的组件配置接口
export interface UnifiedComponentConfig {
  // 原有配置...
  clickableElements?: ComponentClickableElements
  defaultActions?: Record<string, ActionConfig>  // 默认动作映射
}

// 事件处理上下文
export interface EventHandlerContext {
  router?: any
  apiClient?: any
  utils?: {
    showMessage: (message: string) => void
    showConfirm: (options: any) => Promise<boolean>
    showLoading: (message?: string) => void
    hideLoading: () => void
  }
  component?: any
  eventData?: UnifiedClickEventData
}

// 预设动作库
export const PRESET_ACTIONS = {
  // 导航类
  navigation: {
    toDeviceDetails: { type: 'navigate' as const, target: '/device/details' },
    toDeviceSettings: { type: 'navigate' as const, target: '/device/settings' },
    toPackageList: { type: 'navigate' as const, target: '/PackageList' },
    toPackageBuy: { type: 'navigate' as const, target: '/packages/buy' },
    toBalanceRecharge: { type: 'navigate' as const, target: '/balance/recharge' },
    toBalanceManage: { type: 'navigate' as const, target: '/balance' },
    toWifiSettings: { type: 'navigate' as const, target: '/wifi/settings' },
    toUsageHistory: { type: 'navigate' as const, target: '/usage/history' },
    toSettings: { type: 'navigate' as const, target: '/settings' },
    toService: { type: 'navigate' as const, target: '/service' }
  },
  
  // 消息类
  messages: {
    success: { type: 'message' as const, message: '操作成功' },
    loading: { type: 'message' as const, message: '正在处理...' },
    comingSoon: { type: 'message' as const, message: '功能开发中，敬请期待' },
    networkSwitching: { type: 'message' as const, message: '正在切换网络...' }
  },
  
  // API类
  api: {
    refreshDevice: { type: 'api' as const, target: '/api/device/refresh' },
    refreshData: { type: 'api' as const, target: '/api/data/refresh' }
  }
} as const

// 工具函数：根据元素ID获取预设动作
export function getPresetAction(elementId: string): ActionConfig | null {
  const presetMap: Record<string, ActionConfig> = {
    // 按钮预设
    'more': PRESET_ACTIONS.navigation.toDeviceDetails,
    'renew': PRESET_ACTIONS.navigation.toPackageList,
    'recharge': PRESET_ACTIONS.navigation.toBalanceRecharge,
    'refresh': PRESET_ACTIONS.api.refreshDevice,
    
    // 菜单预设
    'package': PRESET_ACTIONS.navigation.toPackageList,
    'balance': PRESET_ACTIONS.navigation.toBalanceRecharge,
    'wifi': PRESET_ACTIONS.navigation.toWifiSettings,
    'history': PRESET_ACTIONS.navigation.toUsageHistory,
    'settings': PRESET_ACTIONS.navigation.toSettings,
    'service': PRESET_ACTIONS.navigation.toService,
    
    // 操作预设
    'switch-network': PRESET_ACTIONS.messages.networkSwitching,
    'real-name-auth': PRESET_ACTIONS.navigation.toDeviceDetails
  }
  
  return presetMap[elementId] || null
}

// 工具函数：创建默认的可点击元素配置
export function createDefaultClickableElement(
  id: string, 
  type: ClickableElementType, 
  label: string
): ClickableElementConfig {
  return {
    id,
    type,
    label,
    visible: true,
    disabled: false,
    defaultAction: getPresetAction(id) || PRESET_ACTIONS.messages.comingSoon
  }
}
