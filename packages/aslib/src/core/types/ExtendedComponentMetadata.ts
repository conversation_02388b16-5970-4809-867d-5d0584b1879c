// 扩展的组件元数据类型定义 - 专为低代码平台设计

// 组件属性配置项
export interface ComponentPropConfig {
  name: string                    // 属性名
  label: string                   // 显示标签
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'select' | 'color' | 'image' | 'textarea' | 'switch'
  defaultValue?: any              // 默认值
  required?: boolean              // 是否必填
  description?: string            // 属性描述
  placeholder?: string            // 输入提示
  options?: Array<{               // 选择项（type为select时）
    label: string
    value: any
    disabled?: boolean
  }>
  validation?: {                  // 验证规则
    min?: number
    max?: number
    pattern?: string
    message?: string
  }
  group?: string                  // 属性分组
  visible?: boolean | string      // 是否可见（可以是条件表达式）
  editable?: boolean              // 是否可编辑
  dependencies?: string[]         // 依赖的其他属性
}

// 组件样式配置项
export interface ComponentStyleConfig {
  name: string                    // 样式属性名
  label: string                   // 显示标签
  type: 'size' | 'color' | 'spacing' | 'border' | 'background' | 'text' | 'layout' | 'shadow' | 'transform'
  properties: string[]            // 对应的CSS属性
  defaultValue?: any              // 默认值
  group?: string                  // 样式分组
  responsive?: boolean            // 是否支持响应式
  units?: string[]                // 支持的单位（px, %, rem等）
}

// 组件事件配置项
export interface ComponentEventConfig {
  name: string                    // 事件名
  label: string                   // 显示标签
  description?: string            // 事件描述
  params?: Array<{                // 事件参数
    name: string
    type: string
    description?: string
  }>
  defaultHandler?: string         // 默认处理函数
  category?: 'user' | 'lifecycle' | 'data' | 'custom' // 事件分类
}

// 组件数据源配置
export interface ComponentDataSourceConfig {
  required: boolean               // 是否需要数据源
  types: Array<'api' | 'store' | 'static' | 'computed' | 'props'>  // 支持的数据源类型
  schema?: any                    // 数据结构定义
  mockData?: any                  // 模拟数据
  apiConfig?: {                   // API配置
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
    headers?: Record<string, string>
    params?: Record<string, any>
  }
  storeConfig?: {                 // Store配置
    storeName: string
    statePath: string
  }
}

// 组件预览配置
export interface ComponentPreviewConfig {
  width?: number                  // 预览宽度
  height?: number                 // 预览高度
  minWidth?: number               // 最小宽度
  minHeight?: number              // 最小高度
  mockProps?: Record<string, any> // 预览用的模拟属性
  mockData?: any                  // 预览用的模拟数据
  screenshot?: string             // 预览截图
  thumbnail?: string              // 缩略图
}

// 组件编辑器配置
export interface ComponentEditorConfig {
  resizable?: boolean             // 是否可调整大小
  movable?: boolean               // 是否可移动
  deletable?: boolean             // 是否可删除
  copyable?: boolean              // 是否可复制
  configurable?: boolean          // 是否可配置
  nestable?: boolean              // 是否可嵌套子组件
  acceptChildren?: string[]       // 可接受的子组件类型
  maxChildren?: number            // 最大子组件数量
  sortable?: boolean              // 子组件是否可排序
  lockAspectRatio?: boolean       // 是否锁定宽高比
}

// 组件业务配置
export interface ComponentBusinessConfig {
  businessType: string           // 业务类型（设备管理、支付、认证等）
  apiDependencies?: string[]      // 依赖的API接口
  permissions?: string[]          // 需要的权限
  features?: string[]             // 支持的功能特性
  limitations?: string[]          // 使用限制
}

// 扩展的组件元数据接口
export interface ExtendedComponentMetadata {
  // 基本信息
  name: string                    // 组件名称（唯一标识）
  displayName: string             // 显示名称
  description?: string            // 组件描述
  version?: string                // 版本号
  author?: string                 // 作者
  createTime?: string             // 创建时间
  updateTime?: string             // 更新时间
  
  // 分类信息
  category: 'layout' | 'form' | 'display' | 'feedback' | 'navigation' | 'business' | 'system' | 'payment' | 'auth'
  subCategory?: string            // 子分类
  tags?: string[]                 // 标签
  keywords?: string[]             // 搜索关键词
  
  // 图标配置
  icon: string                    // Iconify图标名称
  color?: string                  // 主题色
  
  // 属性配置
  props?: ComponentPropConfig[]   // 组件属性配置
  
  // 样式配置
  styles?: ComponentStyleConfig[] // 样式配置
  
  // 事件配置
  events?: ComponentEventConfig[] // 事件配置
  
  // 数据源配置
  dataSource?: ComponentDataSourceConfig // 数据源配置
  
  // 预览配置
  preview?: ComponentPreviewConfig // 预览配置
  
  // 编辑器配置
  editor?: ComponentEditorConfig  // 编辑器配置
  
  // 业务配置
  business?: ComponentBusinessConfig // 业务相关配置
  
  // 依赖信息
  dependencies?: string[]         // 依赖的其他组件
  peerDependencies?: string[]     // 同级依赖
  
  // 兼容性
  platforms?: Array<'h5' | 'pc' | 'app'> // 支持的平台
  browsers?: string[]             // 支持的浏览器
  
  // 文档和示例
  documentation?: string          // 文档链接
  examples?: Array<{              // 使用示例
    name: string
    description: string
    config: any
  }>
  
  // 状态信息
  status?: 'stable' | 'beta' | 'alpha' | 'deprecated' // 组件状态
  deprecated?: {                  // 废弃信息
    version: string
    reason: string
    replacement?: string
  }
}

// 组件库项目扩展接口
export interface ExtendedComponentLibraryItem {
  id: string                      // 唯一标识
  type: string                    // 组件类型名
  metadata: ExtendedComponentMetadata // 扩展元数据
  defaultConfig: {                // 默认配置
    props: Record<string, any>
    style: Record<string, any>
    events?: Record<string, any>
    dataSource?: any
  }
  configSchema: any               // 配置模式
  component?: any                 // 组件实例（可选）
}

// 组件分类定义
export interface ComponentCategory {
  id: string                      // 分类ID
  name: string                    // 分类名称
  icon: string                    // 分类图标
  description?: string            // 分类描述
  order?: number                  // 排序
  children?: ComponentCategory[]  // 子分类
}

// 组件库配置
export interface ComponentLibraryConfig {
  name: string                    // 组件库名称
  version: string                 // 版本
  description?: string            // 描述
  categories: ComponentCategory[] // 分类配置
  components: ExtendedComponentLibraryItem[] // 组件列表
  theme?: {                       // 主题配置
    primaryColor: string
    secondaryColor: string
    borderRadius: number
    spacing: number
  }
}
