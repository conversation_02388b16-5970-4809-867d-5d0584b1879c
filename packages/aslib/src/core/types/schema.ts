// 页面配置Schema类型定义

// 导入扩展的组件元数据类型
export * from './ExtendedComponentMetadata'

// 组件事件信息
export interface ComponentEventInfo {
  name: string          // 事件显示名称
  description: string   // 事件描述
  trigger: string       // 触发条件说明
  params?: string       // 事件参数说明
}

// 组件数据源配置（扩展版本）
export interface DataSource {
  type: 'api' | 'store' | 'static' | 'computed' | 'props' | 'hybrid'
  source?: string          // API端点或store路径
  params?: Record<string, any>  // 请求参数
  transform?: string       // 数据转换函数代码
  cache?: CacheConfig | boolean     // 缓存配置 (boolean 为简化格式)
  refresh?: RefreshConfig | number  // 刷新配置 (number 为简化格式，表示刷新间隔)
}

// 缓存配置
export interface CacheConfig {
  enabled: boolean        // 是否启用缓存
  ttl: number            // 缓存时间(ms)
  key?: string           // 自定义缓存键
  storage: 'memory' | 'localStorage' | 'sessionStorage'
}

// 刷新配置
export interface RefreshConfig {
  auto: boolean          // 是否自动刷新
  interval: number       // 刷新间隔(ms)
  trigger?: string[]     // 触发刷新的事件
}

// 业务流程配置
export interface BusinessFlowConfig {
  mode: 'navigation' | 'embedded' | 'hybrid' | 'smart'

  // 跳转模式配置
  navigation?: {
    paymentPage: string      // 支付页面路径
    resultPage?: string      // 结果页面路径
    openInNewTab?: boolean   // 是否新标签页打开
  }

  // 内嵌模式配置
  embedded?: {
    showInModal: boolean     // 是否显示在弹窗中
    modalSize: 'small' | 'medium' | 'large'
    allowCancel: boolean     // 是否允许取消
    autoClose: boolean       // 完成后自动关闭
  }

  // 混合模式配置
  hybrid?: {
    simpleThreshold: number  // 简单操作的阈值（如金额）
    simpleActions: string[]  // 简单操作列表
    complexActions: string[] // 复杂操作列表
  }

  // 智能模式配置
  smart?: {
    rules: FlowRule[]        // 智能选择规则
    fallback: 'navigation' | 'embedded' // 默认模式
  }
}

// 智能选择规则
export interface FlowRule {
  condition: string          // 条件表达式
  mode: 'navigation' | 'embedded'
  reason?: string           // 选择原因
}

// 组件事件配置
export interface EventHandler {
  type: 'navigate' | 'custom'
  target?: string         // 导航目标
  params?: Record<string, any>  // 事件参数
  handler?: string        // 自定义处理函数代码
}

// 组件配置（扩展版本）
export interface ComponentConfig {
  id: string              // 组件唯一ID
  type: string            // 组件类型 (PackageCardList, DeviceInfoCard等)
  props?: Record<string, any>   // 组件属性
  style?: Record<string, any>   // 样式配置
  dataSource?: DataSource       // 数据源配置
  events?: Record<string, EventHandler>  // 事件处理配置
  children?: ComponentConfig[]  // 子组件
  visible?: boolean | string    // 是否可见（支持条件表达式）
  editable?: boolean      // 是否可编辑

  // 新增：业务流程配置
  flowConfig?: BusinessFlowConfig  // 业务流程配置

  // 新增：组件元数据引用
  metadata?: string       // 组件元数据ID或名称

  // 新增：组件状态
  state?: 'loading' | 'ready' | 'error' | 'disabled'

  // 新增：组件权限
  permissions?: string[]  // 需要的权限列表
}

// 页面布局配置
export interface LayoutConfig {
  type: 'flex' | 'grid' | 'absolute'
  direction?: 'row' | 'column'
  wrap?: boolean
  justify?: 'start' | 'center' | 'end' | 'space-between' | 'space-around'
  align?: 'start' | 'center' | 'end' | 'stretch'
  gap?: number
  padding?: number | string
  margin?: number | string
}

// 页面配置
export interface PageConfig {
  id: string              // 页面ID
  name: string            // 页面名称
  path: string            // 路由路径
  title?: string          // 页面标题
  layout: LayoutConfig    // 布局配置
  components: ComponentConfig[]  // 组件列表
  style?: Record<string, any>    // 页面样式配置
  dataSource?: DataSource        // 页面级数据源
  meta?: Record<string, any>     // 页面元数据
  editable: boolean       // 是否允许编辑

  // 新增：应用相关字段
  appId?: string          // 所属应用ID
  pageType?: 'home' | 'custom' | 'system'  // 页面类型
  published?: boolean     // 是否已发布
  updateTime?: string | Date  // 更新时间
}

// 应用配置
export interface AppConfig {
  id: string              // 应用ID
  name: string            // 应用名称
  version: string         // 版本号
  pages: PageConfig[]     // 页面列表
  globalData?: DataSource // 全局数据源
  theme?: Record<string, any>  // 主题配置
}

// 渲染上下文
export interface RenderContext {
  pageData?: any          // 页面数据
  globalData?: any        // 全局数据
  userInfo?: any          // 用户信息
  deviceInfo?: any        // 设备信息
  [key: string]: any      // 其他上下文数据
}
