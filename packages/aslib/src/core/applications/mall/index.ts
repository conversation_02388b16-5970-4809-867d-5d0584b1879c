/**
 * 商城端应用配置
 */

import type { ApplicationType, PageRoute } from '../../types'
import { buildApplicationRoutes } from '../../utils/route'

// ==================== 应用类型定义 ====================

export const MALL_APPLICATION: ApplicationType = {
  id: 'mall',
  name: '商城端',
  description: '电商购物业务应用',
  icon: 'shopping-cart',
  color: '#52c41a',
  enabled: true, // 暂未开发完成
  order: 2,
  defaultPage: '/home', // 基础路径，会自动添加前缀
  loginPage: '/login',   // 登录页路径
  homePage: '/home',     // 首页路径
  features: ['product-catalog', 'shopping-cart', 'order-management', 'payment']
}

// ==================== 基础路由定义（不含前缀） ====================

const MALL_BASE_ROUTES: Record<string, PageRoute> = {
  // 认证页面
  login: {
    path: '/login',
    name: 'MallLogin',
    title: '商城登录',
    description: '商城用户登录',
    category: 'auth',
    requiresAuth: false,
    configurable: false,
    navigatable: false,
    icon: 'log-in'
  },

  // 首页
  home: {
    path: '/home',
    name: 'MallHome',
    title: '商城首页',
    description: '商城主页',
    category: 'home',
    requiresAuth: false,
    configurable: true,
    navigatable: true,
    icon: 'home'
  },

  // 商品相关
  products: {
    path: '/products',
    name: 'ProductList',
    title: '商品列表',
    description: '商品列表页面',
    category: 'business',
    requiresAuth: false,
    configurable: true,
    navigatable: true,
    icon: 'package'
  },

  productDetail: {
    path: '/product/:id',
    name: 'ProductDetail',
    title: '商品详情',
    description: '商品详情页面',
    category: 'business',
    requiresAuth: false,
    configurable: true,
    navigatable: false,
    icon: 'eye',
    params: [
      { name: 'id', type: 'string', required: true, description: '商品ID' }
    ]
  },

  categories: {
    path: '/categories',
    name: 'CategoryList',
    title: '商品分类',
    description: '商品分类页面',
    category: 'business',
    requiresAuth: false,
    configurable: true,
    navigatable: true,
    icon: 'grid'
  },

  search: {
    path: '/search',
    name: 'ProductSearch',
    title: '商品搜索',
    description: '商品搜索页面',
    category: 'business',
    requiresAuth: false,
    configurable: false,
    navigatable: false,
    icon: 'search',
    params: [
      { name: 'keyword', type: 'string', required: false, description: '搜索关键词' }
    ]
  },

  // 购物车和订单
  cart: {
    path: '/cart',
    name: 'ShoppingCart',
    title: '购物车',
    description: '购物车页面',
    category: 'business',
    requiresAuth: true,
    configurable: false,
    navigatable: true,
    icon: 'shopping-cart'
  },

  checkout: {
    path: '/checkout',
    name: 'Checkout',
    title: '结算',
    description: '订单结算页面',
    category: 'payment',
    requiresAuth: true,
    configurable: false,
    navigatable: false,
    icon: 'credit-card'
  },

  orders: {
    path: '/orders',
    name: 'OrderList',
    title: '订单列表',
    description: '用户订单列表',
    category: 'business',
    requiresAuth: true,
    configurable: false,
    navigatable: true,
    icon: 'file-text'
  },

  orderDetail: {
    path: '/order/:id',
    name: 'OrderDetail',
    title: '订单详情',
    description: '订单详情页面',
    category: 'business',
    requiresAuth: true,
    configurable: false,
    navigatable: false,
    icon: 'file-text',
    params: [
      { name: 'id', type: 'string', required: true, description: '订单ID' }
    ]
  },

  // 用户中心
  profile: {
    path: '/profile',
    name: 'UserProfile',
    title: '个人中心',
    description: '用户个人信息',
    category: 'user',
    requiresAuth: true,
    configurable: false,
    navigatable: true,
    icon: 'user'
  },

  addresses: {
    path: '/addresses',
    name: 'AddressList',
    title: '收货地址',
    description: '收货地址管理',
    category: 'user',
    requiresAuth: true,
    configurable: false,
    navigatable: true,
    icon: 'map-pin'
  },

  favorites: {
    path: '/favorites',
    name: 'FavoriteList',
    title: '收藏夹',
    description: '商品收藏列表',
    category: 'user',
    requiresAuth: true,
    configurable: false,
    navigatable: true,
    icon: 'heart'
  },

  coupons: {
    path: '/coupons',
    name: 'CouponList',
    title: '优惠券',
    description: '用户优惠券',
    category: 'user',
    requiresAuth: true,
    configurable: false,
    navigatable: true,
    icon: 'gift'
  },

  // 支付相关
  payment: {
    path: '/payment',
    name: 'Payment',
    title: '支付',
    description: '订单支付页面',
    category: 'payment',
    requiresAuth: true,
    configurable: false,
    navigatable: false,
    icon: 'credit-card',
    params: [
      { name: 'orderId', type: 'string', required: true, description: '订单ID' },
      { name: 'amount', type: 'number', required: true, description: '支付金额' }
    ]
  },

  paymentResult: {
    path: '/payment/result',
    name: 'PaymentResult',
    title: '支付结果',
    description: '支付结果页面',
    category: 'payment',
    requiresAuth: true,
    configurable: false,
    navigatable: false,
    icon: 'check-circle',
    params: [
      { name: 'status', type: 'string', required: true, description: '支付状态' },
      { name: 'orderId', type: 'string', required: true, description: '订单ID' }
    ]
  },

  // 系统页面
  about: {
    path: '/about',
    name: 'About',
    title: '关于我们',
    description: '关于商城',
    category: 'system',
    requiresAuth: false,
    configurable: true,
    navigatable: true,
    icon: 'info'
  },

  help: {
    path: '/help',
    name: 'Help',
    title: '帮助中心',
    description: '帮助和常见问题',
    category: 'system',
    requiresAuth: false,
    configurable: true,
    navigatable: true,
    icon: 'help-circle'
  },

  contact: {
    path: '/contact',
    name: 'Contact',
    title: '联系客服',
    description: '客服联系方式',
    category: 'system',
    requiresAuth: false,
    configurable: false,
    navigatable: true,
    icon: 'headphones'
  }
}

// ==================== 构建完整路由（含前缀） ====================

export const MALL_ROUTES = buildApplicationRoutes(MALL_APPLICATION.id, MALL_BASE_ROUTES)

// ==================== 导出 ====================

export default {
  application: MALL_APPLICATION,
  routes: MALL_ROUTES
}
