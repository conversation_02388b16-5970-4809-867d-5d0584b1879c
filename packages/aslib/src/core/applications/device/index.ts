/**
 * 设备充值端应用配置
 */

import type { ApplicationType, UnifiedRoute } from '../../types'
import { buildApplicationRoutes, getDefaultPagePath } from '../../utils/route'

// ==================== 应用类型定义 ====================

export const DEVICE_APPLICATION: ApplicationType = {
  id: 'device',
  name: '设备充值端',
  description: '设备管理和充值业务应用',
  icon: 'smartphone',
  color: '#1890ff',
  enabled: true,
  order: 1,
  defaultPage: '/home', // 基础路径，会自动添加前缀
  loginPage: '/login',   // 登录页路径
  homePage: '/home',     // 首页路径
  features: ['device-management', 'package-purchase', 'balance-recharge', 'real-name-auth']
}

// ==================== 基础路由定义（不含前缀） ====================

const DEVICE_BASE_ROUTES: Record<string, UnifiedRoute> = {
  // 认证页面
  login: {
    path: '/login',
    name: 'Login',
    title: '登录',
    description: '用户登录页面',
    category: 'auth',
    requiresAuth: false,
    configurable: false,
    navigatable: false, // 登录页面不应该被跳转
    enabled: true,
    component: './pages/Login/index.vue',
    icon: 'log-in',
    guards: [
      {
        type: 'beforeEnter',
        name: 'checkAlreadyLoggedIn',
        description: '检查是否已登录，已登录则跳转到首页',
        enabled: false,  // 🎯 临时禁用登录页面守卫，避免复杂的重定向逻辑
        config: {
          customValidator: 'checkLoginStatus',
          redirectTo: '/home',
          redirectCondition: 'isLoggedIn',
          showMessage: false
        }
      }
    ]
  },

  // 首页
  home: {
    path: '/home',
    name: 'Home',
    title: '首页',
    description: '设备管理主页',
    category: 'home',
    requiresAuth: true,
    configurable: true,
    navigatable: true, // 首页可以跳转
    enabled: true,
    icon: 'home',
    guards: [
      {
        type: 'beforeEnter',
        name: 'requireAuth',
        description: '需要登录才能访问',
        enabled: true,
        config: {
          requiresAuth: true,
          redirectToLogin: '/device/login',
          showMessage: true,
          messageText: '请先登录',
          messageType: 'warning'
        }
      },
      {
        type: 'beforeEnter',
        name: 'preloadDeviceData',
        description: '预加载设备数据',
        enabled: true,
        config: {
          preloadData: ['deviceInfo', 'packageList'],
          showMessage: false
        }
      }
    ]
  },

  // 套餐管理
  packageList: {
    path: '/PackageList',
    name: 'PackageList',
    title: '套餐列表',
    description: '查看和购买套餐',
    category: 'business',
    requiresAuth: true,
    configurable: false,  // 开放为可配置页面
    navigatable: true, // 套餐列表可以跳转
    enabled: true,
    component: './pages/PackageList/index.vue',
    icon: 'package',
    guards: [
      {
        type: 'beforeEnter',
        name: 'requireAuth',
        description: '需要登录才能访问',
        enabled: true,
        config: {
          requiresAuth: true,
          redirectToLogin: '/device/login',
          showMessage: true,
          messageText: '请先登录查看套餐',
          messageType: 'warning'
        }
      }
    ]
  },

  packagePayment: {
    path: '/PackagePayment',
    name: 'PackagePayment',
    title: '套餐支付',
    description: '套餐购买支付',
    category: 'payment',
    requiresAuth: true,
    configurable: false,
    navigatable: false, // 支付页面不能直接跳转，需要参数
    enabled: true,
    component: './pages/PackagePayment/index.vue',
    icon: 'credit-card',
    params: [
      { name: 'packageId', type: 'string', required: true, description: '套餐ID' },
      { name: 'amount', type: 'number', required: true, description: '支付金额' }
    ],
    guards: [
      {
        type: 'beforeEnter',
        name: 'requireAuth',
        description: '需要登录才能支付',
        enabled: true,
        config: {
          requiresAuth: true,
          redirectToLogin: '/device/login'
        }
      }
    ]
  },

  packageOrder: {
    path: '/PackageOrder',
    name: 'PackageOrder',
    title: '套餐订单',
    description: '套餐订单详情',
    category: 'business',
    requiresAuth: true,
    configurable: false,
    navigatable: false, // 订单详情需要订单ID参数
    enabled: true,
    component: './pages/PackageOrder/index.vue',
    icon: 'file-text',
    params: [
      { name: 'orderId', type: 'string', required: true, description: '订单ID' }
    ],
    guards: [
      {
        type: 'beforeEnter',
        name: 'requireAuth',
        description: '需要登录才能查看订单',
        enabled: true,
        config: {
          requiresAuth: true,
          redirectToLogin: '/device/login'
        }
      }
    ]
  },

  // 余额管理
  balanceList: {
    path: '/BalanceList',
    name: 'BalanceList',
    title: '余额充值',
    description: '账户余额充值',
    category: 'business',
    requiresAuth: true,
    configurable: false,  // 改为支持低代码化
    navigatable: true, // 余额充值页面可以跳转
    enabled: true,
    component: './pages/BalanceList/index.vue',
    icon: 'dollar-sign',
    guards: [
      {
        type: 'beforeEnter',
        name: 'requireAuth',
        description: '需要登录才能充值',
        enabled: true,
        config: {
          requiresAuth: true,
          redirectToLogin: '/device/login'
        }
      }
    ]
  },

  balancePayment: {
    path: '/BalancePayment',
    name: 'BalancePayment',
    title: '余额支付',
    description: '余额充值支付',
    category: 'payment',
    requiresAuth: true,
    configurable: false,
    navigatable: false, // 支付页面不能直接跳转，需要金额参数
    enabled: true,
    component: './pages/BalancePayment/index.vue',
    icon: 'credit-card',
    params: [
      { name: 'amount', type: 'number', required: true, description: '充值金额' }
    ],
    guards: [
      {
        type: 'beforeEnter',
        name: 'requireAuth',
        description: '需要登录才能支付',
        enabled: true,
        config: {
          requiresAuth: true,
          redirectToLogin: '/device/login'
        }
      }
    ]
  },

  balanceDetails: {
    path: '/BalanceDetails',
    name: 'BalanceDetails',
    title: '余额明细',
    description: '账户余额使用明细',
    category: 'business',
    requiresAuth: true,
    configurable: true,  // 开放为可配置页面
    navigatable: true, // 余额明细可以跳转
    enabled: true,
    component: './pages/BalanceDetails/index.vue',
    icon: 'list',
    guards: [
      {
        type: 'beforeEnter',
        name: 'requireAuth',
        description: '需要登录才能查看余额明细',
        enabled: true,
        config: {
          requiresAuth: true,
          redirectToLogin: '/device/login'
        }
      }
    ]
  },

  // 用户中心
  editDevice: {
    path: '/EditDevice',
    name: 'EditDevice',
    title: '设备设置',
    description: '设备参数设置',
    category: 'user',
    requiresAuth: true,
    configurable: true,  // 开放为可配置页面
    navigatable: true, // 设备设置可以跳转
    enabled: true,
    component: './pages/EditDevice/index.vue',
    icon: 'settings',
    guards: [
      {
        type: 'beforeEnter',
        name: 'requireAuth',
        description: '需要登录才能设置设备',
        enabled: true,
        config: {
          requiresAuth: true,
          redirectToLogin: '/device/login'
        }
      }
    ]
  },

  editPassword: {
    path: '/EditPassword',
    name: 'EditPassword',
    title: '支付密码',
    description: '修改支付密码',
    category: 'user',
    requiresAuth: true,
    configurable: true,  // 开放为可配置页面
    navigatable: true, // 支付密码设置可以跳转
    enabled: true,
    component: './pages/EditPassword/index.vue',
    icon: 'lock',
    guards: [
      {
        type: 'beforeEnter',
        name: 'requireAuth',
        description: '需要登录才能修改密码',
        enabled: true,
        config: {
          requiresAuth: true,
          redirectToLogin: '/device/login'
        }
      }
    ]
  },

  realName: {
    path: '/RealName',
    name: 'RealName',
    title: '实名认证',
    description: '实名认证页面',
    category: 'user',
    requiresAuth: true,
    configurable: false,
    navigatable: true, // 实名认证可以跳转
    enabled: true,
    component: './pages/RealName/index.vue',
    icon: 'user-check',
    guards: [
      {
        type: 'beforeEnter',
        name: 'requireAuth',
        description: '需要登录才能实名认证',
        enabled: true,
        config: {
          requiresAuth: true,
          redirectToLogin: '/device/login'
        }
      }
    ]
  },

  realNameCards: {
    path: '/RealNameCards',
    name: 'RealNameCards',
    title: '实名卡片',
    description: '实名认证卡片管理',
    category: 'user',
    requiresAuth: true,
    configurable: false,
    navigatable: true, // 实名卡片管理可以跳转
    enabled: true,
    component: './pages/RealNameCards/index.vue',  // 🔧 修复：添加缺失的component字段
    icon: 'credit-card'
  },

  deviceNotice: {
    path: '/DeviceNotice',
    name: 'DeviceNotice',
    title: '设备通知',
    description: '设备相关通知',
    category: 'user',
    requiresAuth: true,
    configurable: false,
    navigatable: true, // 设备通知可以跳转
    enabled: true,
    component: './pages/DeviceNotice/index.vue',  // 🔧 修复：添加缺失的component字段
    icon: 'bell'
  },

  layoutService: {
    path: '/LayoutService',
    name: 'LayoutService',
    title: '客服',
    description: '在线客服',
    category: 'user',
    requiresAuth: true,
    configurable: false,
    navigatable: true, // 客服页面可以跳转
    enabled: true,
    component: './pages/LayoutService/index.vue',  // 🔧 修复：添加缺失的component字段
    icon: 'headphones'
  },

  // 支付相关
  wechatPayment: {
    path: '/wechat-payment',
    name: 'WeChatPayment',
    title: '微信支付',
    description: '微信支付页面',
    category: 'payment',
    requiresAuth: false,
    configurable: false,
    navigatable: false, // 支付页面不能直接跳转
    enabled: true,
    component: './pages/WeChatPayment/index.vue',  // 🔧 修复：添加缺失的component字段
    icon: 'smartphone'
  },

  qrcodePayment: {
    path: '/qrcode-payment',
    name: 'QrCodePayment',
    title: '二维码支付',
    description: '二维码支付页面',
    category: 'payment',
    requiresAuth: false,
    configurable: false,
    navigatable: false, // 支付页面不能直接跳转
    enabled: true,
    component: './pages/QrCodePayment/index.vue',
    icon: 'qr-code'
  },

  // 系统页面
  webview: {
    path: '/WebView',
    name: 'WebView',
    title: '网页',
    description: '内嵌网页显示',
    category: 'system',
    requiresAuth: false,
    configurable: false,
    navigatable: false, // WebView需要URL参数，不能直接跳转
    icon: 'globe',
    params: [
      { name: 'url', type: 'string', required: true, description: '网页地址' },
      { name: 'title', type: 'string', required: false, description: '页面标题' }
    ]
  }
}

// ==================== 构建完整路由（含前缀） ====================

export const DEVICE_ROUTES = buildApplicationRoutes(DEVICE_APPLICATION.id, DEVICE_BASE_ROUTES)

// ==================== 导出 ====================

export default {
  application: DEVICE_APPLICATION,
  routes: DEVICE_ROUTES
}
