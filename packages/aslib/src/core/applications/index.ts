/**
 * 应用类型统一管理
 * 
 * 统一导出所有应用类型配置
 * 为PC端设计器和H5端提供一致的数据源
 */

import type { ApplicationType, PageRoute, UnifiedRoute, PageCategory, ApplicationConfig } from '../types/application'
import { PAGE_CATEGORIES } from '../types/application'

// 导入各应用配置
import deviceConfig from './device'
import mallConfig from './mall'

// ==================== 应用类型注册表 ====================

const APPLICATION_CONFIGS: Record<string, ApplicationConfig> = {
  device: deviceConfig,
  mall: mallConfig
}

// ==================== 应用类型映射 ====================

export const APPLICATION_TYPES: Record<string, ApplicationType> = {
  device: deviceConfig.application,
  mall: mallConfig.application
}

/**
 * 获取应用配置
 */
export function getApplicationConfig(appType: string): ApplicationConfig | undefined {
  return APPLICATION_CONFIGS[appType]
}

// ==================== 路由映射 ====================

export const APPLICATION_ROUTES: Record<string, Record<string, PageRoute>> = {
  device: deviceConfig.routes,
  mall: mallConfig.routes
}

// ==================== 导出页面分类 ====================

export { PAGE_CATEGORIES }

// ==================== 工具函数 ====================

/**
 * 获取所有启用的应用类型
 */
export function getEnabledApplicationTypes(): ApplicationType[] {
  return Object.values(APPLICATION_TYPES)
    .filter(type => type.enabled)
    .sort((a, b) => a.order - b.order)
}

/**
 * 获取所有应用类型（包括未启用的）
 */
export function getAllApplicationTypes(): ApplicationType[] {
  return Object.values(APPLICATION_TYPES)
    .sort((a, b) => a.order - b.order)
}

/**
 * 根据应用类型获取路由列表
 */
export function getRoutesByApplicationType(appType: string): PageRoute[] {
  const routes = APPLICATION_ROUTES[appType] || {}
  return Object.values(routes)
}

/**
 * 根据应用类型获取统一路由列表（支持路由守卫）
 */
export function getUnifiedRoutesByApplicationType(appType: string): UnifiedRoute[] {
  const config = getApplicationConfig(appType)
  if (!config) return []

  return Object.values(config.routes) as UnifiedRoute[]
}

/**
 * 根据应用类型和分类获取路由
 */
export function getRoutesByCategory(appType: string, category: string): PageRoute[] {
  const routes = getRoutesByApplicationType(appType)
  return routes.filter(route => route.category === category)
}

/**
 * 获取可配置的路由（用于低代码）
 */
export function getConfigurableRoutes(appType: string): PageRoute[] {
  const routes = getRoutesByApplicationType(appType)
  return routes.filter(route => route.configurable)
}

/**
 * 获取分组后的路由（用于PC端选择器）
 */
export function getGroupedRoutes(appType: string): Record<string, PageRoute[]> {
  const routes = getRoutesByApplicationType(appType)
  const grouped: Record<string, PageRoute[]> = {}
  
  // 初始化所有分类
  Object.values(PAGE_CATEGORIES).forEach(category => {
    grouped[category.id] = []
  })
  
  // 按分类分组路由
  routes.forEach(route => {
    if (grouped[route.category]) {
      grouped[route.category].push(route)
    }
  })
  
  return grouped
}

/**
 * 搜索路由
 */
export function searchRoutes(appType: string, keyword: string): PageRoute[] {
  const routes = getRoutesByApplicationType(appType)
  const lowerKeyword = keyword.toLowerCase()
  
  return routes.filter(route =>
    route.name.toLowerCase().includes(lowerKeyword) ||
    route.title.toLowerCase().includes(lowerKeyword) ||
    route.path.toLowerCase().includes(lowerKeyword) ||
    route.description?.toLowerCase().includes(lowerKeyword)
  )
}

/**
 * 验证路由是否存在
 */
export function isValidRoute(appType: string, path: string): boolean {
  const routes = getRoutesByApplicationType(appType)
  return routes.some(route => route.path === path)
}

/**
 * 获取路由的显示名称
 */
export function getRouteDisplayName(appType: string, path: string): string {
  const routes = getRoutesByApplicationType(appType)
  const route = routes.find(r => r.path === path)
  return route?.title || route?.name || path
}

/**
 * 获取应用类型信息
 */
export function getApplicationType(appType: string): ApplicationType | undefined {
  return APPLICATION_TYPES[appType]
}

/**
 * 获取页面分类列表
 */
export function getPageCategories(): PageCategory[] {
  return Object.values(PAGE_CATEGORIES).sort((a, b) => a.order - b.order)
}

/**
 * 根据路径获取页面信息
 */
export function getPageByPath(appType: string, path: string): PageRoute | undefined {
  const routes = getRoutesByApplicationType(appType)
  return routes.find(route => route.path === path)
}

/**
 * 获取应用的默认页面
 */
export function getDefaultPage(appType: string): string {
  const appConfig = APPLICATION_TYPES[appType]
  return appConfig?.defaultPage || '/home'
}

/**
 * 检查应用是否启用
 */
export function isApplicationEnabled(appType: string): boolean {
  const appConfig = APPLICATION_TYPES[appType]
  return appConfig?.enabled || false
}

/**
 * 获取应用支持的功能特性
 */
export function getApplicationFeatures(appType: string): string[] {
  const appConfig = APPLICATION_TYPES[appType]
  return appConfig?.features || []
}

/**
 * 检查应用是否支持某个功能
 */
export function hasFeature(appType: string, feature: string): boolean {
  const features = getApplicationFeatures(appType)
  return features.includes(feature)
}

// ==================== 类型导出 ====================

export type {
  ApplicationType,
  PageRoute,
  PageCategory,
  ApplicationConfig
} from '../types/application'

// ==================== 常量导出 ====================

export {
  DEVICE_APPLICATION,
  DEVICE_ROUTES
} from './device'

export {
  MALL_APPLICATION,
  MALL_ROUTES
} from './mall'
