// Device API 适配器 - 用于集成现有的device-an API

export interface ApiResponse<T = any> {
  code: boolean
  data: T
  msg: string
}

export interface DeviceApiClient {
  request(config: {
    url: string
    method: string
    params?: any
    data?: any
  }): Promise<ApiResponse>
}

// API端点映射
export const API_ENDPOINTS = {
  // 设备相关
  DEVICE_DETAILS: 'GetDeviceDetails',
  DEVICE_CARDS: 'GetDeviceCards',
  DEVICE_RULE_DETAILS: 'GetDeviceRuleDetails',
  DEVICE_REAL_NAME_CARDS: 'GetDeviceRealNameCards',
  RENEW_DEVICE: 'RenewDevice',
  
  // 套餐相关
  PACKAGE_LIST: 'GetPackageList',
  PACKAGE_ORDER: 'GetPackageOrder',
  CREATE_PACKAGE_ORDER: 'CreatePackageOrder',
  
  // 余额相关
  BALANCE_LIST: 'GetBalanceList',
  BALANCE_DETAILS: 'GetBalanceDetails',
  BALANCE_TEMPLATE: 'GetBalanceTemplate',
  CREATE_BALANCE_ORDER: 'CreateBalanceOrder',
  MONTH_BALANCE_DATA: 'GetMonthBalanceData',
  
  // 用户相关
  USER_NOTICE: 'GetNotice',
  
  // 支付相关
  PAYMENT_METHODS: 'GetPaymentMethods',
  PAYMENT_PARAMETER: 'GetPaymentParameter'
}

// 数据转换器
export class DeviceApiAdapter {
  private apiClient: DeviceApiClient

  constructor(apiClient: DeviceApiClient) {
    this.apiClient = apiClient
  }

  // 获取设备详情
  async getDeviceDetails() {
    const response = await this.apiClient.request({
      url: API_ENDPOINTS.DEVICE_DETAILS,
      method: 'GET'
    })
    
    if (!response.code) {
      throw new Error(response.msg || '获取设备详情失败')
    }
    
    return this.transformDeviceDetails(response.data)
  }

  // 获取套餐列表
  async getPackageList() {
    const response = await this.apiClient.request({
      url: API_ENDPOINTS.PACKAGE_LIST,
      method: 'GET'
    })
    
    if (!response.code) {
      throw new Error(response.msg || '获取套餐列表失败')
    }
    
    return this.transformPackageList(response.data)
  }

  // 获取余额列表
  async getBalanceList() {
    const response = await this.apiClient.request({
      url: API_ENDPOINTS.BALANCE_LIST,
      method: 'GET'
    })
    
    if (!response.code) {
      throw new Error(response.msg || '获取余额列表失败')
    }
    
    return this.transformBalanceList(response.data)
  }

  // 获取余额明细
  async getBalanceDetails(params: { page: number; pageSize: number }) {
    const response = await this.apiClient.request({
      url: API_ENDPOINTS.BALANCE_DETAILS,
      method: 'GET',
      params
    })
    
    if (!response.code) {
      throw new Error(response.msg || '获取余额明细失败')
    }
    
    return this.transformBalanceDetails(response.data)
  }

  // 获取月度余额数据
  async getMonthBalanceData() {
    const response = await this.apiClient.request({
      url: API_ENDPOINTS.MONTH_BALANCE_DATA,
      method: 'GET'
    })
    
    if (!response.code) {
      throw new Error(response.msg || '获取月度数据失败')
    }
    
    return response.data
  }

  // 数据转换方法
  private transformDeviceDetails(data: any) {
    return {
      status: data.status,
      vTotalFlow: data.vTotalFlow,
      vUseFlow: data.vUseFlow,
      vResidueFlow: data.vResidueFlow,
      balance: data.balance,
      deviceName: data.wifiName,
      packageName: data.packageName,
      becomedueDatetime: data.becomedueDatetime,
      // 保留原始数据
      _raw: data
    }
  }

  private transformPackageList(data: any) {
    if (Array.isArray(data)) {
      return data.map(item => ({
        id: item.id,
        name: item.name,
        packageTotal: item.packageTotal,
        packagePrice: item.packagePrice,
        popular: item.popular,
        packageType: item.packageType,
        packageValidity: item.packageValidity,
        validityDays: item.validityDays,
        packageIntroduce: item.packageIntroduce,
        _raw: item
      }))
    }
    return []
  }

  private transformBalanceList(data: any) {
    if (data.list && Array.isArray(data.list)) {
      return data.list.map((item: any) => ({
        id: item.id,
        prestorePrice: item.prestorePrice,
        prestoreGive: item.prestoreGive,
        _raw: item
      }))
    }
    return []
  }

  private transformBalanceDetails(data: any) {
    if (data.rows && Array.isArray(data.rows)) {
      return data.rows.map((item: any) => ({
        id: item.id,
        systemOrdernumber: item.systemOrdernumber,
        balanceBefore: item.balanceBefore,
        balanceAfter: item.balanceAfter,
        balanceAmount: item.balanceAmount,
        unit: item.unit,
        balanceAlterationTime: item.balanceAlterationTime,
        balanceRemark: item.balanceRemark,
        _raw: item
      }))
    }
    return []
  }

  // 通用请求方法
  async request(endpoint: string, params?: any, method = 'GET') {
    const response = await this.apiClient.request({
      url: endpoint,
      method,
      params: method === 'GET' ? params : undefined,
      data: method !== 'GET' ? params : undefined
    })
    
    if (!response.code) {
      throw new Error(response.msg || '请求失败')
    }
    
    return response.data
  }
}

// 创建适配器实例的工厂函数
export function createDeviceApiAdapter(apiClient: DeviceApiClient): DeviceApiAdapter {
  return new DeviceApiAdapter(apiClient)
}
