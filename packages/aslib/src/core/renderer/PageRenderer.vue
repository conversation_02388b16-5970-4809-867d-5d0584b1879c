<template>
  <div class="page-renderer" :class="pageClass" :style="pageStyle">
    <!-- 页面容器 -->
    <div class="page-content" :style="layoutStyle">
      <ComponentRenderer
        v-for="component in pageConfig.components"
        :key="component.id"
        :config="component"
        :context="renderContext"
        :data-manager="dataManager"
        :event-manager="eventManager"
      />
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="page-loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中...</div>
    </div>
    
    <!-- 错误状态 -->
    <div v-if="error" class="page-error">
      <div class="error-icon">⚠️</div>
      <div class="error-message">{{ error.message || '页面加载失败' }}</div>
      <button @click="reload" class="error-retry">重试</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import ComponentRenderer from './ComponentRenderer.vue'
import { DataManager } from '../data/DataManager'
import { EventManager } from '../events/EventManager'
import type { PageConfig, RenderContext } from '../types/schema'

const props = defineProps<{
  config: PageConfig
  initialContext?: RenderContext
  apiClient?: any
  router?: any
}>()

const emit = defineEmits<{
  loaded: [data: any]
  error: [error: any]
  contextChange: [context: RenderContext]
}>()

// 页面状态
const loading = ref(false)
const error = ref<any>(null)
const pageData = ref<any>(null)

// 渲染上下文
const renderContext = reactive<RenderContext>({
  ...props.initialContext,
  pageData: pageData.value
})

// 数据管理器
const dataManager = new DataManager(renderContext, props.apiClient)

// 事件管理器
const eventManager = new EventManager(renderContext, props.router, props.apiClient)

// 页面配置
const pageConfig = computed(() => props.config)

// 页面样式类
const pageClass = computed(() => {
  const classes = ['page-renderer']
  
  if (pageConfig.value.editable) {
    classes.push('page-editable')
  }
  
  if (loading.value) {
    classes.push('page-loading-state')
  }
  
  return classes.join(' ')
})

// 页面样式
const pageStyle = computed(() => {
  const style: Record<string, any> = {}
  
  // 应用主题样式
  if (pageConfig.value.meta?.theme) {
    Object.assign(style, pageConfig.value.meta.theme)
  }
  
  return style
})

// 布局样式
const layoutStyle = computed(() => {
  const layout = pageConfig.value.layout
  const style: Record<string, any> = {}
  
  // 布局类型
  if (layout.type === 'flex') {
    style.display = 'flex'
    style.flexDirection = layout.direction || 'column'
    style.flexWrap = layout.wrap ? 'wrap' : 'nowrap'
    style.justifyContent = layout.justify || 'start'
    style.alignItems = layout.align || 'stretch'
  } else if (layout.type === 'grid') {
    style.display = 'grid'
    // 网格布局配置可以在这里扩展
  } else if (layout.type === 'absolute') {
    style.position = 'relative'
  }
  
  // 间距
  if (layout.gap) {
    style.gap = typeof layout.gap === 'number' ? `${layout.gap}px` : layout.gap
  }
  
  if (layout.padding) {
    style.padding = typeof layout.padding === 'number' ? `${layout.padding}px` : layout.padding
  }
  
  if (layout.margin) {
    style.margin = typeof layout.margin === 'number' ? `${layout.margin}px` : layout.margin
  }
  
  return style
})

// 加载页面数据
const loadPageData = async () => {
  if (!pageConfig.value.dataSource) return
  
  loading.value = true
  error.value = null
  
  try {
    pageData.value = await dataManager.fetchData(pageConfig.value.dataSource)
    renderContext.pageData = pageData.value
    emit('loaded', pageData.value)
  } catch (err) {
    error.value = err
    emit('error', err)
    console.error(`Failed to load page data for ${pageConfig.value.id}:`, err)
  } finally {
    loading.value = false
  }
}

// 重新加载
const reload = () => {
  loadPageData()
}

// 更新上下文
const updateContext = (newContext: Partial<RenderContext>) => {
  Object.assign(renderContext, newContext)
  dataManager.updateContext(renderContext)
  eventManager.updateContext(renderContext)
  emit('contextChange', renderContext)
}

// 监听配置变化
watch(() => props.config, () => {
  loadPageData()
}, { deep: true })

// 生命周期
onMounted(() => {
  loadPageData()
})

// 暴露方法
defineExpose({
  reload,
  updateContext,
  getContext: () => renderContext,
  getPageData: () => pageData.value
})
</script>

<style scoped>
.page-renderer {
  width: 100%;
  min-height: 100vh;
  position: relative;

  /* 在设计器环境中调整高度 */
  &.designer-mode {
    min-height: auto;
    height: auto;
  }
}

.page-content {
  width: 100%;
  min-height: inherit;

  /* 确保内容可以正常流动 */
  &.designer-mode {
    min-height: auto;
  }
}

.page-editable {
  outline: 2px dashed #1989fa;
  outline-offset: 4px;
}

.page-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1000;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1989fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}

.page-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  padding: 32px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-message {
  color: #ff4d4f;
  margin-bottom: 16px;
  font-size: 14px;
}

.error-retry {
  background-color: #1989fa;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
