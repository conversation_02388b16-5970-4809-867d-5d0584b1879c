<template>
  <component
    v-if="resolvedComponent && config.visible !== false"
    :is="resolvedComponent"
    v-bind="mergedProps"
    v-on="eventHandlers"
    :style="computedStyle"
    :class="computedClass"
  >
    <!-- 渲染子组件 -->
    <template v-if="config.children?.length">
      <ComponentRenderer
        v-for="child in config.children"
        :key="child.id"
        :config="child"
        :context="context"
        :data-manager="dataManager"
        :event-manager="eventManager"
      />
    </template>
    
    <!-- 插槽内容 -->
    <template v-for="(slot, name) in slots" :key="name" #[name]>
      <component :is="slot" />
    </template>
  </component>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import type { ComponentConfig, RenderContext } from '../types/schema'
import type { DataManager } from '../data/DataManager'
import type { EventManager } from '../events/EventManager'
import { getComponent } from '../registry/ComponentRegistry'

const props = defineProps<{
  config: ComponentConfig
  context: RenderContext
  dataManager: DataManager
  eventManager: EventManager
  slots?: Record<string, any>
}>()

// 组件数据
const componentData = ref<any>(null)
const loading = ref(false)
const error = ref<any>(null)

// 解析组件
const resolvedComponent = computed(() => {
  const component = getComponent(props.config.type)
  if (!component) {
    console.warn(`Component ${props.config.type} not found in registry`)
    return null
  }
  return component
})

// 合并属性
const mergedProps = computed(() => {
  const baseProps = { ...props.config.props }
  
  // 如果有数据源，将数据注入到props中
  if (componentData.value) {
    baseProps.data = componentData.value
  }
  
  // 注入加载状态
  baseProps.loading = loading.value
  baseProps.error = error.value
  
  return baseProps
})

// 计算样式
const computedStyle = computed(() => {
  const style = { ...props.config.style }
  
  // 处理响应式样式
  if (style.responsive) {
    // 根据屏幕尺寸调整样式
    const screenWidth = window.innerWidth
    if (screenWidth < 768 && style.responsive.mobile) {
      Object.assign(style, style.responsive.mobile)
    } else if (screenWidth >= 768 && screenWidth < 1024 && style.responsive.tablet) {
      Object.assign(style, style.responsive.tablet)
    } else if (screenWidth >= 1024 && style.responsive.desktop) {
      Object.assign(style, style.responsive.desktop)
    }
    delete style.responsive
  }
  
  return style
})

// 计算CSS类
const computedClass = computed(() => {
  const classes = []
  
  // 基础类名
  classes.push(`lowcode-component`)
  classes.push(`lowcode-${props.config.type.toLowerCase()}`)
  
  // 自定义类名
  if (props.config.style?.className) {
    classes.push(props.config.style.className)
  }
  
  // 状态类名
  if (loading.value) classes.push('lowcode-loading')
  if (error.value) classes.push('lowcode-error')
  if (!props.config.editable) classes.push('lowcode-readonly')
  
  return classes.join(' ')
})

// 事件处理器
const eventHandlers = computed(() => {
  if (!props.config.events) return {}
  
  return props.eventManager.createEventHandlers(props.config.events)
})

// 加载组件数据
const loadData = async () => {
  if (!props.config.dataSource) return
  
  loading.value = true
  error.value = null
  
  try {
    componentData.value = await props.dataManager.fetchData(props.config.dataSource)
  } catch (err) {
    error.value = err
    console.error(`Failed to load data for component ${props.config.id}:`, err)
  } finally {
    loading.value = false
  }
}

// 监听配置变化
watch(() => props.config.dataSource, loadData, { deep: true })

// 监听上下文变化
watch(() => props.context, () => {
  // 如果数据源依赖上下文，重新加载数据
  if (props.config.dataSource?.type === 'computed' || 
      props.config.dataSource?.type === 'store') {
    loadData()
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  loadData()
  
  // 如果配置了自动刷新，设置定时器
  if (props.config.dataSource?.refresh) {
    const refreshConfig = props.config.dataSource.refresh
    if (typeof refreshConfig === 'object' && refreshConfig.auto) {
      const interval = setInterval(loadData, refreshConfig.interval)
      onUnmounted(() => clearInterval(interval))
    } else if (typeof refreshConfig === 'number') {
      // 兼容旧的数字格式
      const interval = setInterval(loadData, refreshConfig)
      onUnmounted(() => clearInterval(interval))
    }
  }
})

// 暴露组件实例方法
defineExpose({
  reload: loadData,
  getData: () => componentData.value,
  getConfig: () => props.config
})
</script>

<style scoped>
.lowcode-component {
  position: relative;
}

.lowcode-loading {
  opacity: 0.6;
  pointer-events: none;
}

.lowcode-error {
  border: 1px solid #ff4d4f;
  background-color: rgba(255, 77, 79, 0.1);
}

.lowcode-readonly {
  pointer-events: none;
}
</style>
