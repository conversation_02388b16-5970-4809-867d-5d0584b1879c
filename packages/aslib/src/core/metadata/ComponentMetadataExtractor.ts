import type { 
  ComponentMetadata, 
  ComponentDefaultConfig, 
  ComponentConfigSchema,
  ComponentMetadataExtractor 
} from '../types/ComponentMetadata'

/**
 * 标准化的组件元数据提取器
 * 提供统一的元数据获取逻辑，减少复杂性和提高可靠性
 */
export class StandardComponentMetadataExtractor implements ComponentMetadataExtractor {
  
  /**
   * 提取组件元数据
   * 按照标准化的优先级顺序查找元数据
   */
  extractMetadata(component: any): ComponentMetadata | null {
    if (!component) {
      return null
    }

    // 标准化的元数据查找路径，按优先级排序
    const metadataPaths = [
      // 1. 直接属性（最高优先级）
      component.__componentMetadata,
      // 2. Vue 3 编译选项
      component.__vccOpts?.__componentMetadata,
      // 3. 默认导出
      component.default?.__componentMetadata,
      // 4. 备用属性名
      component.componentMetadata,
    ]

    for (const metadata of metadataPaths) {
      if (this.isValidMetadata(metadata)) {
        return this.normalizeMetadata(metadata)
      }
    }

    return null
  }

  /**
   * 提取默认配置
   */
  extractDefaultConfig(component: any): ComponentDefaultConfig | null {
    if (!component) {
      return null
    }

    const configPaths = [
      component.__defaultConfig,
      component.__vccOpts?.__defaultConfig,
      component.default?.__defaultConfig,
      component.defaultConfig,
    ]

    for (const config of configPaths) {
      if (this.isValidDefaultConfig(config)) {
        return this.normalizeDefaultConfig(config)
      }
    }

    return null
  }

  /**
   * 提取配置模式
   */
  extractConfigSchema(component: any): ComponentConfigSchema | null {
    if (!component) {
      return null
    }

    const schemaPaths = [
      component.__configSchema,
      component.__vccOpts?.__configSchema,
      component.default?.__configSchema,
      component.configSchema,
    ]

    for (const schema of schemaPaths) {
      if (this.isValidConfigSchema(schema)) {
        return this.normalizeConfigSchema(schema)
      }
    }

    return null
  }

  /**
   * 验证元数据是否有效
   */
  private isValidMetadata(metadata: any): boolean {
    return metadata && 
           typeof metadata === 'object' && 
           typeof metadata.name === 'string' &&
           typeof metadata.category === 'string' &&
           typeof metadata.icon === 'string'
  }

  /**
   * 验证默认配置是否有效
   */
  private isValidDefaultConfig(config: any): boolean {
    return config && 
           typeof config === 'object' && 
           (config.props || config.style)
  }

  /**
   * 验证配置模式是否有效
   */
  private isValidConfigSchema(schema: any): boolean {
    return schema && 
           typeof schema === 'object' && 
           schema.type === 'object' &&
           schema.properties
  }

  /**
   * 标准化元数据
   */
  private normalizeMetadata(metadata: any): ComponentMetadata {
    return {
      name: metadata.name,
      category: metadata.category || 'other',
      icon: metadata.icon || 'mdi:puzzle',
      description: metadata.description || '',
      tags: Array.isArray(metadata.tags) ? metadata.tags : [],
      isContainer: Boolean(metadata.isContainer)
    }
  }

  /**
   * 标准化默认配置
   */
  private normalizeDefaultConfig(config: any): ComponentDefaultConfig {
    return {
      props: config.props || {},
      style: config.style || {},
      events: config.events || {}
    }
  }

  /**
   * 标准化配置模式
   */
  private normalizeConfigSchema(schema: any): ComponentConfigSchema {
    return {
      type: 'object',
      properties: schema.properties || {},
      required: Array.isArray(schema.required) ? schema.required : []
    }
  }
}

/**
 * 默认的元数据提取器实例
 */
export const defaultMetadataExtractor = new StandardComponentMetadataExtractor()

/**
 * 便捷函数：提取组件的完整元数据信息
 */
export function extractComponentInfo(componentType: string, component: any) {
  const extractor = defaultMetadataExtractor
  
  const metadata = extractor.extractMetadata(component)
  const defaultConfig = extractor.extractDefaultConfig(component)
  const configSchema = extractor.extractConfigSchema(component)

  return {
    componentType,
    metadata,
    defaultConfig,
    configSchema,
    isValid: metadata !== null
  }
}
