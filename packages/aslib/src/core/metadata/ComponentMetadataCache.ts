import type { ComponentLibraryItem, ComponentMetadataCache } from '../types/ComponentMetadata'

/**
 * 内存组件元数据缓存
 * 提供高效的元数据缓存机制，避免重复解析
 */
export class MemoryComponentMetadataCache implements ComponentMetadataCache {
  private cache = new Map<string, ComponentLibraryItem>()
  private readonly maxSize: number
  private accessOrder = new Map<string, number>()
  private accessCounter = 0

  constructor(maxSize: number = 100) {
    this.maxSize = maxSize
  }

  /**
   * 获取缓存的元数据
   */
  get(componentType: string): ComponentLibraryItem | null {
    const item = this.cache.get(componentType)
    if (item) {
      // 更新访问顺序
      this.accessOrder.set(componentType, ++this.accessCounter)
      return item
    }
    return null
  }

  /**
   * 设置缓存的元数据
   */
  set(componentType: string, item: ComponentLibraryItem): void {
    // 如果缓存已满，移除最少使用的项
    if (this.cache.size >= this.maxSize && !this.cache.has(componentType)) {
      this.evictLeastRecentlyUsed()
    }

    this.cache.set(componentType, item)
    this.accessOrder.set(componentType, ++this.accessCounter)
  }

  /**
   * 清除缓存
   */
  clear(): void {
    this.cache.clear()
    this.accessOrder.clear()
    this.accessCounter = 0
  }

  /**
   * 检查是否有缓存
   */
  has(componentType: string): boolean {
    return this.cache.has(componentType)
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return this.cache.size
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      accessCounter: this.accessCounter,
      keys: Array.from(this.cache.keys())
    }
  }

  /**
   * 移除最少使用的项
   */
  private evictLeastRecentlyUsed(): void {
    let lruKey: string | null = null
    let lruAccess = Infinity

    for (const [key, access] of this.accessOrder) {
      if (access < lruAccess) {
        lruAccess = access
        lruKey = key
      }
    }

    if (lruKey) {
      this.cache.delete(lruKey)
      this.accessOrder.delete(lruKey)
    }
  }
}

/**
 * 默认的元数据缓存实例
 */
export const defaultMetadataCache = new MemoryComponentMetadataCache()

/**
 * 缓存装饰器函数
 * 为元数据获取函数添加缓存功能
 */
export function withCache<T extends (...args: any[]) => any>(
  fn: T,
  cache: ComponentMetadataCache,
  keyGenerator: (...args: Parameters<T>) => string
): T {
  return ((...args: Parameters<T>) => {
    const key = keyGenerator(...args)
    
    // 尝试从缓存获取
    const cached = cache.get(key)
    if (cached) {
      return cached
    }

    // 执行原函数
    const result = fn(...args)
    
    // 缓存结果（如果有效）
    if (result) {
      cache.set(key, result)
    }

    return result
  }) as T
}
