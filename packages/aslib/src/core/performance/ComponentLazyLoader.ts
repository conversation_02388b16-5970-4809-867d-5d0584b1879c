/**
 * 组件懒加载器
 * 提供组件的按需加载和缓存机制，提高应用启动性能
 */

export interface LazyLoadConfig {
  /** 预加载优先级 */
  priority: 'high' | 'medium' | 'low'
  /** 是否在空闲时预加载 */
  preloadOnIdle: boolean
  /** 最大并发加载数 */
  maxConcurrent: number
  /** 缓存策略 */
  cacheStrategy: 'memory' | 'persistent' | 'none'
}

export interface ComponentLoadResult {
  component: any
  metadata: any
  loadTime: number
  fromCache: boolean
}

export interface LoadingState {
  isLoading: boolean
  progress: number
  error?: Error
}

/**
 * 组件懒加载器类
 */
export class ComponentLazyLoader {
  private loadedComponents = new Map<string, any>()
  private loadingPromises = new Map<string, Promise<any>>()
  private loadingStates = new Map<string, LoadingState>()
  private config: LazyLoadConfig
  private loadQueue: Array<{ componentType: string; priority: number }> = []
  private currentLoading = 0

  constructor(config: Partial<LazyLoadConfig> = {}) {
    this.config = {
      priority: 'medium',
      preloadOnIdle: true,
      maxConcurrent: 3,
      cacheStrategy: 'memory',
      ...config
    }

    // 在空闲时预加载组件
    if (this.config.preloadOnIdle && typeof window !== 'undefined') {
      this.setupIdlePreloading()
    }
  }

  /**
   * 懒加载组件
   */
  async loadComponent(componentType: string): Promise<ComponentLoadResult> {
    const startTime = Date.now()

    // 检查是否已加载
    if (this.loadedComponents.has(componentType)) {
      return {
        component: this.loadedComponents.get(componentType),
        metadata: null,
        loadTime: 0,
        fromCache: true
      }
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(componentType)) {
      const component = await this.loadingPromises.get(componentType)!
      return {
        component,
        metadata: null,
        loadTime: Date.now() - startTime,
        fromCache: false
      }
    }

    // 开始加载
    const loadingPromise = this.performLoad(componentType)
    this.loadingPromises.set(componentType, loadingPromise)

    try {
      const component = await loadingPromise
      const loadTime = Date.now() - startTime

      // 缓存组件
      this.loadedComponents.set(componentType, component)
      
      // 清理加载状态
      this.loadingPromises.delete(componentType)
      this.loadingStates.delete(componentType)

      return {
        component,
        metadata: null,
        loadTime,
        fromCache: false
      }
    } catch (error) {
      // 清理失败的加载
      this.loadingPromises.delete(componentType)
      this.loadingStates.set(componentType, {
        isLoading: false,
        progress: 0,
        error: error as Error
      })
      throw error
    }
  }

  /**
   * 预加载组件
   */
  async preloadComponent(componentType: string, priority: 'high' | 'medium' | 'low' = 'low'): Promise<void> {
    // 如果已加载或正在加载，跳过
    if (this.loadedComponents.has(componentType) || this.loadingPromises.has(componentType)) {
      return
    }

    // 添加到加载队列
    const priorityValue = this.getPriorityValue(priority)
    this.loadQueue.push({ componentType, priority: priorityValue })
    this.loadQueue.sort((a, b) => b.priority - a.priority)

    // 处理队列
    this.processLoadQueue()
  }

  /**
   * 批量预加载组件
   */
  async preloadComponents(componentTypes: string[], priority: 'high' | 'medium' | 'low' = 'low'): Promise<void> {
    const promises = componentTypes.map(type => this.preloadComponent(type, priority))
    await Promise.allSettled(promises)
  }

  /**
   * 获取加载状态
   */
  getLoadingState(componentType: string): LoadingState {
    return this.loadingStates.get(componentType) || {
      isLoading: false,
      progress: 0
    }
  }

  /**
   * 获取已加载的组件
   */
  getLoadedComponent(componentType: string): any {
    return this.loadedComponents.get(componentType)
  }

  /**
   * 检查组件是否已加载
   */
  isComponentLoaded(componentType: string): boolean {
    return this.loadedComponents.has(componentType)
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.loadedComponents.clear()
    this.loadingPromises.clear()
    this.loadingStates.clear()
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      loadedCount: this.loadedComponents.size,
      loadingCount: this.loadingPromises.size,
      queueLength: this.loadQueue.length,
      currentLoading: this.currentLoading
    }
  }

  /**
   * 执行实际的组件加载
   */
  private async performLoad(componentType: string): Promise<any> {
    // 设置加载状态
    this.loadingStates.set(componentType, {
      isLoading: true,
      progress: 0
    })

    try {
      // 模拟动态导入（实际实现中应该根据组件类型动态导入）
      // 这里需要根据实际的组件注册系统来实现
      const component = await this.dynamicImportComponent(componentType)
      
      // 更新进度
      this.loadingStates.set(componentType, {
        isLoading: true,
        progress: 100
      })

      return component
    } catch (error) {
      console.error(`Failed to load component ${componentType}:`, error)
      throw error
    }
  }

  /**
   * 动态导入组件（需要根据实际情况实现）
   */
  private async dynamicImportComponent(componentType: string): Promise<any> {
    // 这里应该根据组件类型动态导入对应的组件
    // 例如：
    // switch (componentType) {
    //   case 'DeviceInfo':
    //     return import('@lowcode/ui/DeviceInfo')
    //   case 'PackageCard':
    //     return import('@lowcode/ui/PackageCard')
    //   default:
    //     throw new Error(`Unknown component type: ${componentType}`)
    // }
    
    // 临时实现：从全局组件注册表获取
    const { getAllComponents } = await import('../registry/ComponentRegistry')
    const components = getAllComponents()
    const found = components.find(([type]) => type === componentType)
    
    if (!found) {
      throw new Error(`Component ${componentType} not found`)
    }

    return found[1]
  }

  /**
   * 获取优先级数值
   */
  private getPriorityValue(priority: string): number {
    switch (priority) {
      case 'high': return 3
      case 'medium': return 2
      case 'low': return 1
      default: return 1
    }
  }

  /**
   * 处理加载队列
   */
  private async processLoadQueue(): Promise<void> {
    while (this.loadQueue.length > 0 && this.currentLoading < this.config.maxConcurrent) {
      const item = this.loadQueue.shift()
      if (!item) break

      this.currentLoading++
      
      try {
        await this.loadComponent(item.componentType)
      } catch (error) {
        console.error(`Failed to preload component ${item.componentType}:`, error)
      } finally {
        this.currentLoading--
      }
    }
  }

  /**
   * 设置空闲时预加载
   */
  private setupIdlePreloading(): void {
    if ('requestIdleCallback' in window) {
      const idleCallback = (deadline: IdleDeadline) => {
        while (deadline.timeRemaining() > 0 && this.loadQueue.length > 0) {
          this.processLoadQueue()
        }
        
        if (this.loadQueue.length > 0) {
          requestIdleCallback(idleCallback)
        }
      }
      
      requestIdleCallback(idleCallback)
    } else {
      // 降级到setTimeout
      setTimeout(() => {
        this.processLoadQueue()
      }, 100)
    }
  }
}

/**
 * 默认的组件懒加载器实例
 */
export const defaultComponentLazyLoader = new ComponentLazyLoader({
  priority: 'medium',
  preloadOnIdle: true,
  maxConcurrent: 3,
  cacheStrategy: 'memory'
})

/**
 * 便捷函数：懒加载组件
 */
export async function lazyLoadComponent(componentType: string): Promise<ComponentLoadResult> {
  return defaultComponentLazyLoader.loadComponent(componentType)
}

/**
 * 便捷函数：预加载组件
 */
export async function preloadComponent(componentType: string, priority?: 'high' | 'medium' | 'low'): Promise<void> {
  return defaultComponentLazyLoader.preloadComponent(componentType, priority)
}
