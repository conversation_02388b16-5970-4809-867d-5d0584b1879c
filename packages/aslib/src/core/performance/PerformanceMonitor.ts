/**
 * 性能监控系统
 * 监控组件加载、渲染等关键性能指标
 */

export interface PerformanceMetric {
  name: string
  value: number
  unit: 'ms' | 'bytes' | 'count'
  timestamp: number
  category: 'load' | 'render' | 'memory' | 'network'
  details?: any
}

export interface PerformanceReport {
  metrics: PerformanceMetric[]
  summary: {
    totalLoadTime: number
    averageRenderTime: number
    memoryUsage: number
    componentCount: number
  }
  recommendations: string[]
}

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private timers = new Map<string, number>()
  private observers: PerformanceObserver[] = []
  private maxMetrics = 1000

  constructor() {
    this.setupPerformanceObservers()
  }

  /**
   * 开始计时
   */
  startTimer(name: string): void {
    this.timers.set(name, performance.now())
  }

  /**
   * 结束计时并记录指标
   */
  endTimer(name: string, category: PerformanceMetric['category'] = 'load', details?: any): number {
    const startTime = this.timers.get(name)
    if (!startTime) {
      console.warn(`Timer ${name} not found`)
      return 0
    }

    const duration = performance.now() - startTime
    this.timers.delete(name)

    this.recordMetric({
      name,
      value: duration,
      unit: 'ms',
      timestamp: Date.now(),
      category,
      details
    })

    return duration
  }

  /**
   * 记录性能指标
   */
  recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric)

    // 限制指标数量
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }

    // 输出到控制台（开发模式）
    if ((typeof window !== 'undefined' && window.location.hostname === 'localhost') ||
        (typeof process !== 'undefined' && process.env.NODE_ENV === 'development')) {
      console.log(`📊 Performance: ${metric.name} = ${metric.value}${metric.unit}`, metric.details)
    }
  }

  /**
   * 记录组件加载时间
   */
  recordComponentLoad(componentType: string, loadTime: number, fromCache: boolean = false): void {
    this.recordMetric({
      name: `component-load-${componentType}`,
      value: loadTime,
      unit: 'ms',
      timestamp: Date.now(),
      category: 'load',
      details: { componentType, fromCache }
    })
  }

  /**
   * 记录组件渲染时间
   */
  recordComponentRender(componentType: string, renderTime: number): void {
    this.recordMetric({
      name: `component-render-${componentType}`,
      value: renderTime,
      unit: 'ms',
      timestamp: Date.now(),
      category: 'render',
      details: { componentType }
    })
  }

  /**
   * 记录内存使用情况
   */
  recordMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      this.recordMetric({
        name: 'memory-usage',
        value: memory.usedJSHeapSize,
        unit: 'bytes',
        timestamp: Date.now(),
        category: 'memory',
        details: {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        }
      })
    }
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): PerformanceReport {
    const loadMetrics = this.metrics.filter(m => m.category === 'load')
    const renderMetrics = this.metrics.filter(m => m.category === 'render')
    const memoryMetrics = this.metrics.filter(m => m.category === 'memory')

    const totalLoadTime = loadMetrics.reduce((sum, m) => sum + m.value, 0)
    const averageRenderTime = renderMetrics.length > 0 
      ? renderMetrics.reduce((sum, m) => sum + m.value, 0) / renderMetrics.length 
      : 0

    const latestMemory = memoryMetrics[memoryMetrics.length - 1]
    const memoryUsage = latestMemory ? latestMemory.value : 0

    const componentCount = new Set(
      this.metrics
        .filter(m => m.details?.componentType)
        .map(m => m.details.componentType)
    ).size

    return {
      metrics: [...this.metrics],
      summary: {
        totalLoadTime,
        averageRenderTime,
        memoryUsage,
        componentCount
      },
      recommendations: this.generateRecommendations()
    }
  }

  /**
   * 获取特定类别的指标
   */
  getMetricsByCategory(category: PerformanceMetric['category']): PerformanceMetric[] {
    return this.metrics.filter(m => m.category === category)
  }

  /**
   * 获取特定组件的指标
   */
  getComponentMetrics(componentType: string): PerformanceMetric[] {
    return this.metrics.filter(m => m.details?.componentType === componentType)
  }

  /**
   * 清除所有指标
   */
  clearMetrics(): void {
    this.metrics = []
    this.timers.clear()
  }

  /**
   * 导出性能数据
   */
  exportMetrics(): string {
    return JSON.stringify({
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      metrics: this.metrics,
      summary: this.getPerformanceReport().summary
    }, null, 2)
  }

  /**
   * 设置性能观察器
   */
  private setupPerformanceObservers(): void {
    if (typeof PerformanceObserver === 'undefined') {
      return
    }

    try {
      // 观察导航时间
      const navObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming
            this.recordMetric({
              name: 'page-load',
              value: navEntry.loadEventEnd - navEntry.startTime,
              unit: 'ms',
              timestamp: Date.now(),
              category: 'load',
              details: {
                domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.startTime,
                firstPaint: navEntry.loadEventStart - navEntry.startTime
              }
            })
          }
        }
      })
      navObserver.observe({ entryTypes: ['navigation'] })
      this.observers.push(navObserver)

      // 观察资源加载时间
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming
            this.recordMetric({
              name: `resource-load-${resourceEntry.name.split('/').pop()}`,
              value: resourceEntry.responseEnd - resourceEntry.startTime,
              unit: 'ms',
              timestamp: Date.now(),
              category: 'network',
              details: {
                url: resourceEntry.name,
                size: resourceEntry.transferSize,
                type: resourceEntry.initiatorType
              }
            })
          }
        }
      })
      resourceObserver.observe({ entryTypes: ['resource'] })
      this.observers.push(resourceObserver)

    } catch (error) {
      console.warn('Failed to setup performance observers:', error)
    }
  }

  /**
   * 生成性能优化建议
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = []
    const report = this.getPerformanceReport()

    // 检查加载时间
    if (report.summary.totalLoadTime > 3000) {
      recommendations.push('总加载时间过长，建议启用组件懒加载')
    }

    // 检查渲染时间
    if (report.summary.averageRenderTime > 100) {
      recommendations.push('组件渲染时间较长，建议优化组件实现或使用虚拟滚动')
    }

    // 检查内存使用
    if (report.summary.memoryUsage > 50 * 1024 * 1024) { // 50MB
      recommendations.push('内存使用较高，建议清理未使用的组件缓存')
    }

    // 检查组件数量
    if (report.summary.componentCount > 50) {
      recommendations.push('组件数量较多，建议使用分页或虚拟列表')
    }

    return recommendations
  }

  /**
   * 销毁监控器
   */
  destroy(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.clearMetrics()
  }
}

/**
 * 默认性能监控器实例
 */
export const defaultPerformanceMonitor = new PerformanceMonitor()

/**
 * 性能监控装饰器
 */
export function performanceMonitor(category: PerformanceMetric['category'] = 'load') {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const timerName = `${target.constructor.name}.${propertyKey}`
      defaultPerformanceMonitor.startTimer(timerName)

      try {
        const result = await originalMethod.apply(this, args)
        defaultPerformanceMonitor.endTimer(timerName, category, { args })
        return result
      } catch (error) {
        defaultPerformanceMonitor.endTimer(timerName, category, { args, error: error instanceof Error ? error.message : String(error) })
        throw error
      }
    }

    return descriptor
  }
}

/**
 * 便捷函数：测量函数执行时间
 */
export async function measurePerformance<T>(
  name: string,
  fn: () => T | Promise<T>,
  category: PerformanceMetric['category'] = 'load'
): Promise<T> {
  defaultPerformanceMonitor.startTimer(name)
  try {
    const result = await fn()
    defaultPerformanceMonitor.endTimer(name, category)
    return result
  } catch (error) {
    defaultPerformanceMonitor.endTimer(name, category, { error: error instanceof Error ? error.message : String(error) })
    throw error
  }
}
