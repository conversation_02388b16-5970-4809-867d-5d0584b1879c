/**
 * 统一事件处理器
 * 完全数据驱动，零硬编码的事件处理
 */

import type {
  UnifiedClickEventData,
  EventConfigRule,
  ActionConfig,
  EventHandlerContext,
  UnifiedEventConfig
} from '../types/UnifiedEvents'

export class UnifiedEventHandler {
  private context: EventHandlerContext

  constructor(context: EventHandlerContext) {
    this.context = context
  }

  /**
   * 处理统一的点击事件
   */
  async handleClick(eventData: UnifiedClickEventData, component: any): Promise<void> {
    console.log('🎯 统一事件处理器 - 处理点击事件:', eventData)

    try {
      // 1. 查找匹配的事件配置规则
      const matchedRule = this.findMatchingRule(eventData, component)
      
      if (matchedRule) {
        console.log('✅ 找到匹配的事件规则:', matchedRule)
        await this.executeAction(matchedRule.action, eventData)
      } else {
        console.log('⚠️ 未找到匹配的事件规则，使用默认处理')
        await this.handleDefaultAction(eventData, component)
      }
    } catch (error) {
      console.error('❌ 事件处理失败:', error)
      this.context.utils?.showMessage('操作失败，请重试')
    }
  }

  /**
   * 查找匹配的事件配置规则
   */
  private findMatchingRule(eventData: UnifiedClickEventData, component: any): EventConfigRule | null {
    const eventConfig: UnifiedEventConfig = component.events || {}
    const clickRules = eventConfig.click || []

    console.log('🔍 查找事件规则:', { eventData, clickRules })

    // 按优先级排序（优先级高的在前）
    const sortedRules = clickRules.sort((a, b) => (b.priority || 0) - (a.priority || 0))

    for (const rule of sortedRules) {
      if (this.isRuleMatched(rule, eventData)) {
        return rule
      }
    }

    return null
  }

  /**
   * 检查规则是否匹配
   */
  private isRuleMatched(rule: EventConfigRule, eventData: UnifiedClickEventData): boolean {
    // 检查元素类型匹配
    if (rule.elementType && rule.elementType !== eventData.elementType) {
      return false
    }

    // 检查元素ID匹配
    if (rule.elementId && rule.elementId !== eventData.elementId) {
      return false
    }

    // 检查条件表达式
    if (rule.when) {
      try {
        // 创建安全的执行环境
        const context = {
          eventData,
          elementType: eventData.elementType,
          elementId: eventData.elementId,
          componentType: eventData.componentType
        }
        
        // 简单的条件表达式求值
        const result = this.evaluateCondition(rule.when, context)
        if (!result) {
          return false
        }
      } catch (error) {
        console.warn('条件表达式执行失败:', rule.when, error)
        return false
      }
    }

    return true
  }

  /**
   * 安全的条件表达式求值
   */
  private evaluateCondition(condition: string, context: any): boolean {
    try {
      // 简单的条件匹配，避免使用eval
      // 支持格式：eventData.elementId === 'package'
      if (condition.includes('eventData.elementId')) {
        const match = condition.match(/eventData\.elementId\s*===\s*['"]([^'"]+)['"]/)
        if (match) {
          return context.eventData.elementId === match[1]
        }
      }
      
      if (condition.includes('eventData.elementType')) {
        const match = condition.match(/eventData\.elementType\s*===\s*['"]([^'"]+)['"]/)
        if (match) {
          return context.eventData.elementType === match[1]
        }
      }

      // 更多条件类型可以在这里扩展
      console.warn('不支持的条件表达式:', condition)
      return false
    } catch (error) {
      console.error('条件表达式求值失败:', error)
      return false
    }
  }

  /**
   * 执行动作配置
   */
  private async executeAction(action: ActionConfig, eventData: UnifiedClickEventData): Promise<void> {
    console.log('🚀 执行动作:', action)

    switch (action.type) {
      case 'navigate':
        await this.handleNavigate(action, eventData)
        break
      case 'external':
        await this.handleExternal(action, eventData)
        break
      case 'webview':
        await this.handleWebview(action, eventData)
        break
      case 'message':
        await this.handleMessage(action, eventData)
        break
      case 'api':
        await this.handleApi(action, eventData)
        break
      case 'custom':
        await this.handleCustom(action, eventData)
        break
      default:
        console.warn('未知的动作类型:', action.type)
    }
  }

  /**
   * 处理页面导航
   */
  private async handleNavigate(action: ActionConfig, eventData: UnifiedClickEventData): Promise<void> {
    if (!action.target) {
      console.warn('导航动作缺少目标地址')
      return
    }

    console.log('🔗 导航到页面:', action.target)
    
    if (this.context.router) {
      await this.context.router.push(action.target)
    } else {
      console.warn('路由器不可用')
    }
  }

  /**
   * 处理外部链接
   */
  private async handleExternal(action: ActionConfig, eventData: UnifiedClickEventData): Promise<void> {
    if (!action.target) {
      console.warn('外部链接动作缺少目标地址')
      return
    }

    console.log('🔗 打开外部链接:', action.target)
    window.open(action.target, '_blank')
  }

  /**
   * 处理webview
   */
  private async handleWebview(action: ActionConfig, eventData: UnifiedClickEventData): Promise<void> {
    console.log('🔗 打开webview:', action.target)
    // 这里可以实现webview打开逻辑
    this.context.utils?.showMessage(`即将打开: ${action.target}`)
  }

  /**
   * 处理消息显示
   */
  private async handleMessage(action: ActionConfig, eventData: UnifiedClickEventData): Promise<void> {
    const message = action.message || '操作完成'
    console.log('💬 显示消息:', message)
    this.context.utils?.showMessage(message)
  }

  /**
   * 处理API调用
   */
  private async handleApi(action: ActionConfig, eventData: UnifiedClickEventData): Promise<void> {
    if (!action.target) {
      console.warn('API动作缺少目标地址')
      return
    }

    console.log('📡 调用API:', action.target)
    
    try {
      if (this.context.apiClient) {
        const response = await this.context.apiClient.request({
          url: action.target,
          method: 'POST',
          data: action.params || {}
        })
        console.log('✅ API调用成功:', response)
        this.context.utils?.showMessage('操作成功')
      } else {
        console.warn('API客户端不可用')
      }
    } catch (error) {
      console.error('❌ API调用失败:', error)
      this.context.utils?.showMessage('操作失败')
    }
  }

  /**
   * 处理自定义代码
   */
  private async handleCustom(action: ActionConfig, eventData: UnifiedClickEventData): Promise<void> {
    if (!action.handler) {
      console.warn('自定义动作缺少处理代码')
      return
    }

    console.log('⚙️ 执行自定义代码:', action.handler)
    
    try {
      // 创建安全的执行环境
      const func = new Function('context', 'eventData', 'action', action.handler)
      await func(this.context, eventData, action)
    } catch (error) {
      console.error('❌ 自定义代码执行失败:', error)
      this.context.utils?.showMessage('操作失败')
    }
  }

  /**
   * 处理默认动作（当没有匹配的规则时）
   */
  private async handleDefaultAction(eventData: UnifiedClickEventData, component: any): Promise<void> {
    console.log('🔄 执行默认动作')
    
    // 尝试从元素数据中获取默认动作
    if (eventData.elementData?.defaultAction) {
      await this.executeAction(eventData.elementData.defaultAction, eventData)
      return
    }

    // 最后的兜底处理
    this.context.utils?.showMessage(`功能开发中: ${eventData.elementId}`)
  }

  /**
   * 更新上下文
   */
  updateContext(context: Partial<EventHandlerContext>): void {
    this.context = { ...this.context, ...context }
  }
}
