import type { ComponentEventInfo } from '../types/schema'

// 事件名称映射表
export const EVENT_NAME_MAP: Record<string, string> = {
  // 通用事件
  'click': '点击',
  'change': '变更',
  'submit': '提交',
  'cancel': '取消',
  'confirm': '确认',
  'close': '关闭',
  'open': '打开',
  'show': '显示',
  'hide': '隐藏',
  'toggle': '切换',
  'select': '选择',
  'focus': '聚焦',
  'blur': '失焦',
  'input': '输入',
  'search': '搜索',
  'filter': '筛选',
  'sort': '排序',
  'refresh': '刷新',
  'reload': '重新加载',
  'reset': '重置',
  'clear': '清空',
  'save': '保存',
  'delete': '删除',
  'edit': '编辑',
  'add': '添加',
  'remove': '移除',
  'update': '更新',
  'upload': '上传',
  'download': '下载',
  'share': '分享',
  'copy': '复制',
  'paste': '粘贴',
  'cut': '剪切',
  
  // 导航事件
  'navigate': '导航',
  'back': '返回',
  'forward': '前进',
  'goto': '跳转',
  'redirect': '重定向',
  
  // 业务事件
  'more': '更多',
  'renew': '续费',
  'recharge': '充值',
  'balance': '余额',
  'package': '套餐',
  // 'wifi': 'Wi-Fi设置',
  'history': '历史记录',
  'switch-network': '切换网络',
  'real-name-click': '实名认证',
  'device-refresh': '刷新设备',
  'flow-refresh': '刷新流量',
  'balance-refresh': '刷新余额',
  
  // 表单事件
  'form-submit': '表单提交',
  'form-reset': '表单重置',
  'form-validate': '表单验证',
  'field-change': '字段变更',
  'field-blur': '字段失焦',
  'field-focus': '字段聚焦',
  
  // 列表事件
  'item-click': '项目点击',
  'item-select': '项目选择',
  'item-delete': '项目删除',
  'item-edit': '项目编辑',
  'list-refresh': '列表刷新',
  'list-load-more': '加载更多',
  'list-filter': '列表筛选',
  'list-sort': '列表排序',
  
  // 弹窗事件
  'modal-open': '打开弹窗',
  'modal-close': '关闭弹窗',
  'modal-confirm': '弹窗确认',
  'modal-cancel': '弹窗取消',
  'popup-show': '显示弹出层',
  'popup-hide': '隐藏弹出层',
  
  // 媒体事件
  'play': '播放',
  'pause': '暂停',
  'stop': '停止',
  'next': '下一个',
  'prev': '上一个',
  'volume-change': '音量变更',
  'seek': '跳转播放',
  
  // 数据事件
  'data-load': '数据加载',
  'data-loaded': '数据加载完成',
  'data-error': '数据加载错误',
  'data-refresh': '数据刷新',
  'data-change': '数据变更',
  'data-save': '数据保存',
  'data-delete': '数据删除'
}

// 事件描述生成规则
export const EVENT_DESCRIPTION_RULES: Record<string, string> = {
  'more': '点击右上角的"更多"按钮',
  'renew': '点击"立即续费"按钮，通常用于跳转到套餐购买页面',
  'recharge': '点击"充值"按钮，通常用于跳转到余额充值页面',
  'balance': '点击余额相关按钮，通常用于查看余额详情',
  'package': '点击套餐相关按钮，通常用于查看或购买套餐',
  'wifi': '点击Wi-Fi设置按钮，通常用于配置设备网络',
  'history': '点击历史记录按钮，通常用于查看使用记录',
  'switch-network': '点击网络切换按钮，用于切换运营商网络',
  'real-name-click': '点击实名认证按钮，用于跳转到实名认证页面',
  'device-refresh': '点击刷新设备按钮，用于重新获取设备信息',
  'refresh': '点击刷新按钮，用于重新加载数据',
  'click': '点击组件触发的通用事件',
  'change': '组件状态或值发生变化时触发',
  'submit': '提交表单或数据时触发',
  'cancel': '取消操作时触发',
  'confirm': '确认操作时触发'
}

/**
 * 统一事件管理器
 * 负责自动发现、注册和管理组件事件
 */
export class UnifiedEventManager {
  private static eventRegistry = new Map<string, ComponentEventInfo[]>()
  private static initialized = false

  /**
   * 初始化事件管理器
   */
  static initialize(): void {
    if (this.initialized) return
    
    console.log('🎯 初始化统一事件管理器...')
    this.initialized = true
    console.log('✅ 统一事件管理器初始化完成')
  }

  /**
   * 从组件自动提取事件信息
   * 注意：已禁用自动提取，统一使用 ComponentEvents 中的定义
   */
  static extractEventsFromComponent(component: any): ComponentEventInfo[] {
    // 禁用自动事件提取，避免读取组件内部的具体事件定义
    // 所有组件都应该使用 ComponentEvents 中定义的统一事件
    return []
  }

  /**
   * 从模板中提取事件（备用方案）
   */
  private static extractEventsFromTemplate(_component: any): ComponentEventInfo[] {
    // 这里可以实现更复杂的模板解析逻辑
    // 暂时返回空数组，后续可以扩展
    return []
  }

  /**
   * 创建事件信息对象
   */
  private static createEventInfo(eventName: string): ComponentEventInfo {
    return {
      name: this.getEventDisplayName(eventName),
      description: this.getEventDescription(eventName),
      trigger: this.getEventTrigger(eventName),
      params: this.getEventParams(eventName)
    }
  }

  /**
   * 获取事件显示名称
   */
  static getEventDisplayName(eventName: string): string {
    return EVENT_NAME_MAP[eventName] || this.formatEventName(eventName)
  }

  /**
   * 获取事件描述
   */
  static getEventDescription(eventName: string): string {
    return EVENT_DESCRIPTION_RULES[eventName] || `触发${this.getEventDisplayName(eventName)}事件`
  }

  /**
   * 获取事件触发条件
   */
  static getEventTrigger(eventName: string): string {
    const displayName = this.getEventDisplayName(eventName)
    return `用户${displayName}时触发`
  }

  /**
   * 获取事件参数说明
   */
  static getEventParams(eventName: string): string | undefined {
    const paramRules: Record<string, string> = {
      'switch-network': 'operator: number - 运营商编号',
      'real-name-click': 'deviceNo: string - 设备编号',
      'item-click': 'item: object - 点击的项目数据',
      'item-select': 'item: object, selected: boolean - 选择的项目和状态',
      'change': 'value: any - 变更后的值',
      'input': 'value: string - 输入的值',
      'select': 'value: any - 选择的值'
    }
    
    return paramRules[eventName]
  }

  /**
   * 格式化事件名称（驼峰转中文）
   */
  private static formatEventName(eventName: string): string {
    // 处理kebab-case: switch-network -> 切换网络
    if (eventName.includes('-')) {
      return eventName.split('-').map(part => EVENT_NAME_MAP[part] || part).join('')
    }
    
    // 处理camelCase: switchNetwork -> 切换网络
    const words = eventName.replace(/([A-Z])/g, ' $1').trim().toLowerCase().split(' ')
    return words.map(word => EVENT_NAME_MAP[word] || word).join('')
  }

  /**
   * 注册组件事件
   */
  static registerComponentEvents(componentType: string, component: any): void {
    if (!this.initialized) {
      this.initialize()
    }

    try {
      const events = this.extractEventsFromComponent(component)
      
      if (events.length > 0) {
        this.eventRegistry.set(componentType, events)
        console.log(`✅ 自动注册 ${componentType} 的 ${events.length} 个事件:`, 
                   events.map(e => e.name).join(', '))
      } else {
        console.log(`ℹ️ 组件 ${componentType} 没有定义事件`)
      }
    } catch (error) {
      console.error(`❌ 注册组件 ${componentType} 事件失败:`, error)
    }
  }

  /**
   * 获取组件事件
   */
  static getComponentEvents(componentType: string): ComponentEventInfo[] {
    return this.eventRegistry.get(componentType) || []
  }

  /**
   * 检查组件是否有事件
   */
  static hasEvents(componentType: string): boolean {
    const events = this.getComponentEvents(componentType)
    return events.length > 0
  }

  /**
   * 获取所有已注册的组件类型
   */
  static getRegisteredComponentTypes(): string[] {
    return Array.from(this.eventRegistry.keys())
  }

  /**
   * 获取事件统计信息
   */
  static getEventStats(): { totalComponents: number; totalEvents: number; componentStats: Record<string, number> } {
    const componentStats: Record<string, number> = {}
    let totalEvents = 0

    this.eventRegistry.forEach((events, componentType) => {
      componentStats[componentType] = events.length
      totalEvents += events.length
    })

    return {
      totalComponents: this.eventRegistry.size,
      totalEvents,
      componentStats
    }
  }

  /**
   * 清空事件注册表（主要用于测试）
   */
  static clearRegistry(): void {
    this.eventRegistry.clear()
    console.log('🔄 事件注册表已清空')
  }

  /**
   * 创建事件处理器（H5端使用）
   */
  static createEventHandlers(
    componentType: string,
    eventConfigs: Record<string, any>,
    context: { router?: any; apiClient?: any; [key: string]: any }
  ): Record<string, Function> {
    const handlers: Record<string, Function> = {}
    const availableEvents = this.getComponentEvents(componentType)

    availableEvents.forEach(eventInfo => {
      // 从事件显示名称反推事件名称
      const eventName = this.getEventNameFromDisplayName(eventInfo.name, componentType)
      const eventConfig = eventConfigs[eventName]

      if (eventConfig) {
        handlers[eventName] = (eventData?: any) => {
          this.handleEvent(eventConfig, eventData, context)
        }
      }
    })

    return handlers
  }

  /**
   * 从显示名称反推事件名称
   */
  private static getEventNameFromDisplayName(displayName: string, componentType: string): string {
    // 从注册的事件中查找匹配的事件名称
    const events = this.getComponentEvents(componentType)
    const matchedEvent = events.find(event => event.name === displayName)

    if (matchedEvent) {
      // 从EVENT_NAME_MAP中反向查找
      for (const [eventName, eventDisplayName] of Object.entries(EVENT_NAME_MAP)) {
        if (eventDisplayName === displayName) {
          return eventName
        }
      }
    }

    // 如果找不到，返回显示名称的小写形式
    return displayName.toLowerCase()
  }

  /**
   * 处理事件
   */
  private static handleEvent(
    eventConfig: any,
    eventData: any,
    context: { router?: any; apiClient?: any; [key: string]: any }
  ): void {
    try {
      // 这里可以集成现有的EventManager逻辑
      console.log('🎯 处理事件:', eventConfig, eventData)

      // 根据事件类型处理
      switch (eventConfig.type) {
        case 'navigate':
          this.handleNavigateEvent(eventConfig, eventData, context)
          break
        case 'custom':
          this.handleCustomEvent(eventConfig, eventData, context)
          break
        default:
          console.warn('未知的事件类型:', eventConfig.type)
      }
    } catch (error) {
      console.error('事件处理失败:', error)
    }
  }

  /**
   * 处理导航事件
   */
  private static handleNavigateEvent(
    eventConfig: any,
    eventData: any,
    context: { router?: any; [key: string]: any }
  ): void {
    if (!context.router) {
      console.warn('Router未配置，无法处理导航事件')
      return
    }

    const { target, params } = eventConfig

    if (target?.startsWith('/')) {
      // 绝对路径
      context.router.push({
        path: target,
        query: { ...params, ...eventData }
      })
    } else if (target?.includes('://')) {
      // 外部链接
      window.open(target, '_blank')
    } else {
      // 命名路由
      context.router.push({
        name: target,
        params: { ...params, ...eventData }
      })
    }
  }

  /**
   * 处理自定义事件
   */
  private static handleCustomEvent(
    eventConfig: any,
    eventData: any,
    context: { [key: string]: any }
  ): void {
    const { handler } = eventConfig

    if (typeof handler === 'string') {
      try {
        const func = new Function('context', 'params', 'eventData', `
          return (${handler})(context, params, eventData)
        `)

        func(context, eventConfig.params, eventData)
      } catch (error) {
        console.error('自定义事件处理器执行失败:', error)
      }
    } else if (typeof handler === 'function') {
      handler(context, eventConfig.params, eventData)
    }
  }
}

// 导出默认实例
export const unifiedEventManager = UnifiedEventManager
