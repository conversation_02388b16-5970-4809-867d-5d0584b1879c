import type { EventHandler, RenderContext } from '../types/schema'

// 事件管理器类
export class EventManager {
  private context: RenderContext
  private router?: any
  private apiClient?: any

  constructor(context: RenderContext = {}, router?: any, apiClient?: any) {
    this.context = context
    this.router = router
    this.apiClient = apiClient
  }

  // 处理事件
  async handleEvent(eventConfig: EventHandler, eventData?: any): Promise<any> {
    const { type, target, params, handler } = eventConfig

    try {
      switch (type) {
        case 'navigate':
          return this.handleNavigation(target!, params, eventData)
        case 'custom':
          return this.handleCustom(handler!, params, eventData)
        default:
          throw new Error(`Unsupported event type: ${type}`)
      }
    } catch (error) {
      console.error('Event handling error:', error)
      throw error
    }
  }

  // 处理导航事件
  private handleNavigation(target: string, params?: Record<string, any>, eventData?: any): void {
    if (!this.router) {
      console.warn('Router not configured for navigation')
      return
    }

    // 支持不同的导航方式
    if (target.startsWith('/')) {
      // 绝对路径
      this.router.push({
        path: target,
        query: { ...params, ...eventData }
      })
    } else if (target.includes('://')) {
      // 外部链接
      window.open(target, '_blank')
    } else {
      // 命名路由
      this.router.push({
        name: target,
        params: { ...params, ...eventData }
      })
    }
  }





  // 处理自定义事件
  private handleCustom(handlerCode: string, params?: Record<string, any>, eventData?: any): any {
    try {
      console.log('🔍 执行自定义代码:', handlerCode)

      // 暂时禁用自定义代码执行，避免语法错误
      if (!handlerCode || handlerCode.trim() === '') {
        console.log('🔍 空的自定义代码，跳过执行')
        return
      }

      // 检查代码是否包含可能的语法错误
      if (handlerCode.includes('context') && !handlerCode.includes('function')) {
        console.warn('🔍 检测到可能的语法错误，跳过执行:', handlerCode)
        return
      }

      const func = new Function('context', 'params', 'eventData', 'router', 'api', `
        return (${handlerCode})(context, params, eventData, router, api)
      `)

      return func(this.context, params, eventData, this.router, this.apiClient)
    } catch (error) {
      console.error('Custom handler error:', error)
      console.error('Handler code:', handlerCode)
      // 不再抛出错误，避免中断执行
      return null
    }
  }



  // 创建事件处理函数
  createEventHandler(eventConfig: EventHandler) {
    return async (eventData?: any) => {
      return this.handleEvent(eventConfig, eventData)
    }
  }

  // 批量创建事件处理函数
  createEventHandlers(events: Record<string, EventHandler>): Record<string, Function> {
    const handlers: Record<string, Function> = {}
    
    for (const [eventName, eventConfig] of Object.entries(events)) {
      handlers[eventName] = this.createEventHandler(eventConfig)
    }
    
    return handlers
  }

  // 更新上下文
  updateContext(newContext: Partial<RenderContext>): void {
    Object.assign(this.context, newContext)
  }

  // 清理资源
  cleanup(): void {
    // 清理相关资源
  }
}
