/**
 * 错误类型枚举
 */
export enum ErrorType {
  COMPONENT_LOAD_FAILED = 'COMPONENT_LOAD_FAILED',
  COMPONENT_RENDER_FAILED = 'COMPONENT_RENDER_FAILED',
  METADATA_EXTRACTION_FAILED = 'METADATA_EXTRACTION_FAILED',
  CONFIG_VALIDATION_FAILED = 'CONFIG_VALIDATION_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 错误严重级别
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  type: ErrorType
  severity: ErrorSeverity
  message: string
  details?: any
  timestamp: number
  componentType?: string
  stack?: string
  userMessage?: string
  recoveryActions?: RecoveryAction[]
}

/**
 * 恢复操作接口
 */
export interface RecoveryAction {
  label: string
  action: () => void | Promise<void>
  type: 'retry' | 'fallback' | 'ignore' | 'reload'
}

/**
 * 错误处理器接口
 */
export interface ErrorHandler {
  handleError(error: Error | ErrorInfo, context?: any): void
  addErrorListener(listener: (error: ErrorInfo) => void): void
  removeErrorListener(listener: (error: ErrorInfo) => void): void
  getErrorHistory(): ErrorInfo[]
  clearErrorHistory(): void
}

/**
 * 默认错误处理器实现
 */
export class DefaultErrorHandler implements ErrorHandler {
  private errorHistory: ErrorInfo[] = []
  private listeners: ((error: ErrorInfo) => void)[] = []
  private maxHistorySize = 100

  /**
   * 处理错误
   */
  handleError(error: Error | ErrorInfo, context?: any): void {
    const errorInfo = this.normalizeError(error, context)
    
    // 添加到历史记录
    this.addToHistory(errorInfo)
    
    // 通知监听器
    this.notifyListeners(errorInfo)
    
    // 根据严重级别决定处理方式
    this.processError(errorInfo)
  }

  /**
   * 添加错误监听器
   */
  addErrorListener(listener: (error: ErrorInfo) => void): void {
    this.listeners.push(listener)
  }

  /**
   * 移除错误监听器
   */
  removeErrorListener(listener: (error: ErrorInfo) => void): void {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 获取错误历史
   */
  getErrorHistory(): ErrorInfo[] {
    return [...this.errorHistory]
  }

  /**
   * 清除错误历史
   */
  clearErrorHistory(): void {
    this.errorHistory = []
  }

  /**
   * 标准化错误信息
   */
  private normalizeError(error: Error | ErrorInfo, context?: any): ErrorInfo {
    if ('type' in error && 'severity' in error) {
      // 已经是 ErrorInfo 格式
      return error as ErrorInfo
    }

    // 从 Error 对象创建 ErrorInfo
    const err = error as Error
    return {
      type: this.inferErrorType(err, context),
      severity: this.inferErrorSeverity(err, context),
      message: err.message || '未知错误',
      details: context,
      timestamp: Date.now(),
      stack: err.stack,
      userMessage: this.generateUserMessage(err, context),
      recoveryActions: this.generateRecoveryActions(err, context)
    }
  }

  /**
   * 推断错误类型
   */
  private inferErrorType(error: Error, _context?: any): ErrorType {
    const message = error.message.toLowerCase()
    
    if (message.includes('component') && message.includes('load')) {
      return ErrorType.COMPONENT_LOAD_FAILED
    }
    if (message.includes('render') || message.includes('mount')) {
      return ErrorType.COMPONENT_RENDER_FAILED
    }
    if (message.includes('metadata') || message.includes('extract')) {
      return ErrorType.METADATA_EXTRACTION_FAILED
    }
    if (message.includes('network') || message.includes('fetch')) {
      return ErrorType.NETWORK_ERROR
    }
    if (message.includes('config') || message.includes('validation')) {
      return ErrorType.CONFIG_VALIDATION_FAILED
    }
    
    return ErrorType.UNKNOWN_ERROR
  }

  /**
   * 推断错误严重级别
   */
  private inferErrorSeverity(error: Error, _context?: any): ErrorSeverity {
    const message = error.message.toLowerCase()
    
    if (message.includes('critical') || message.includes('fatal')) {
      return ErrorSeverity.CRITICAL
    }
    if (message.includes('render') || message.includes('mount')) {
      return ErrorSeverity.HIGH
    }
    if (message.includes('load') || message.includes('network')) {
      return ErrorSeverity.MEDIUM
    }
    
    return ErrorSeverity.LOW
  }

  /**
   * 生成用户友好的错误消息
   */
  private generateUserMessage(error: Error, context?: any): string {
    const type = this.inferErrorType(error, context)
    
    switch (type) {
      case ErrorType.COMPONENT_LOAD_FAILED:
        return '组件加载失败，请检查网络连接或稍后重试'
      case ErrorType.COMPONENT_RENDER_FAILED:
        return '组件渲染出现问题，请检查组件配置'
      case ErrorType.METADATA_EXTRACTION_FAILED:
        return '组件信息获取失败，可能是组件配置有误'
      case ErrorType.CONFIG_VALIDATION_FAILED:
        return '配置验证失败，请检查配置参数'
      case ErrorType.NETWORK_ERROR:
        return '网络连接出现问题，请检查网络设置'
      default:
        return '操作失败，请稍后重试'
    }
  }

  /**
   * 生成恢复操作
   */
  private generateRecoveryActions(error: Error, context?: any): RecoveryAction[] {
    const type = this.inferErrorType(error, context)
    const actions: RecoveryAction[] = []
    
    switch (type) {
      case ErrorType.COMPONENT_LOAD_FAILED:
        actions.push({
          label: '重新加载',
          action: () => window.location.reload(),
          type: 'retry'
        })
        break
      case ErrorType.COMPONENT_RENDER_FAILED:
        actions.push({
          label: '重置配置',
          action: () => {
            // 实现重置逻辑
          },
          type: 'fallback'
        })
        break
      case ErrorType.NETWORK_ERROR:
        actions.push({
          label: '重试',
          action: () => {
            // 实现重试逻辑
          },
          type: 'retry'
        })
        break
    }
    
    // 总是提供忽略选项
    actions.push({
      label: '忽略',
      action: () => {},
      type: 'ignore'
    })
    
    return actions
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(errorInfo: ErrorInfo): void {
    this.errorHistory.unshift(errorInfo)
    
    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize)
    }
  }

  /**
   * 通知监听器
   */
  private notifyListeners(errorInfo: ErrorInfo): void {
    this.listeners.forEach(listener => {
      try {
        listener(errorInfo)
      } catch (err) {
        console.error('Error in error listener:', err)
      }
    })
  }

  /**
   * 处理错误
   */
  private processError(errorInfo: ErrorInfo): void {
    // 根据严重级别决定处理方式
    switch (errorInfo.severity) {
      case ErrorSeverity.CRITICAL:
        console.error('Critical error:', errorInfo)
        break
      case ErrorSeverity.HIGH:
        console.error('High severity error:', errorInfo)
        break
      case ErrorSeverity.MEDIUM:
        console.warn('Medium severity error:', errorInfo)
        break
      case ErrorSeverity.LOW:
        console.info('Low severity error:', errorInfo)
        break
    }
  }
}

/**
 * 全局错误处理器实例
 */
export const globalErrorHandler = new DefaultErrorHandler()

/**
 * 便捷函数：处理组件相关错误
 */
export function handleComponentError(
  error: Error,
  componentType: string,
  operation: string
): void {
  globalErrorHandler.handleError(error, {
    componentType,
    operation,
    timestamp: Date.now()
  })
}

/**
 * 便捷函数：创建错误信息
 */
export function createErrorInfo(
  type: ErrorType,
  message: string,
  options: Partial<ErrorInfo> = {}
): ErrorInfo {
  return {
    type,
    severity: ErrorSeverity.MEDIUM,
    message,
    timestamp: Date.now(),
    ...options
  }
}
