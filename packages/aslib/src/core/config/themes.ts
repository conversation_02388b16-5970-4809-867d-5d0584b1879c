/**
 * 主题配置
 * 统一管理加载动画、颜色主题等配置
 */

import { COLORS, ANIMATION_DURATION } from './constants'

// ==================== 加载动画主题 ====================

/** 加载动画基础配置 */
export interface LoadingThemeConfig {
  type: 'circular' | 'wave' | 'gradient' | 'dots' | 'bars'
  size: 'small' | 'medium' | 'large'
  color: string
  backgroundColor: string
  text: string
  textColor: string
  overlayOpacity: number
  duration: number
  gradient?: {
    from: string
    to: string
    direction: string
  }
}

/** 加载动画预设主题 */
export const LOADING_THEMES = {
  // 科技蓝主题
  tech: {
    type: 'circular',
    size: 'large',
    color: COLORS.BRAND_BLUE,
    backgroundColor: 'rgba(15, 23, 42, 0.95)',
    text: '系统处理中...',
    textColor: COLORS.BRAND_BLUE,
    overlayOpacity: 0.8,
    duration: ANIMATION_DURATION.LOADING,
    gradient: {
      from: COLORS.BRAND_BLUE,
      to: '#0066cc',
      direction: '45deg'
    }
  },
  
  // 清新绿主题
  nature: {
    type: 'wave',
    size: 'medium',
    color: COLORS.BRAND_GREEN,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    text: '加载中，请稍候...',
    textColor: '#389e0d',
    overlayOpacity: 0.6,
    duration: ANIMATION_DURATION.LOADING
  },
  
  // 渐变紫主题
  gradient: {
    type: 'gradient',
    size: 'large',
    color: COLORS.BRAND_PURPLE,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    text: '正在处理数据...',
    textColor: '#d946ef',
    overlayOpacity: 0.7,
    duration: ANIMATION_DURATION.LOADING,
    gradient: {
      from: COLORS.BRAND_PURPLE,
      to: '#eb2f96',
      direction: '135deg'
    }
  },
  
  // 系统默认主题
  system: {
    type: 'circular',
    size: 'medium',
    color: COLORS.PRIMARY,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    text: '加载中...',
    textColor: COLORS.GRAY_600,
    overlayOpacity: 0.5,
    duration: ANIMATION_DURATION.LOADING
  }
} as const

// ==================== 系统主题 ====================

/** 系统主题配置 */
export interface SystemThemeConfig {
  variant: 'ios' | 'macos' | 'windows' | 'material' | 'system'
  blur: number
  saturation: number
  brightness: number
  animation: 'breathe' | 'fade' | 'scale' | 'rotate' | 'pulse'
  accentColor: string
  vibrancy: boolean
  shadow: boolean
  border: boolean
}

/** 系统主题预设 */
export const SYSTEM_THEMES = {
  // iOS风格
  ios: {
    variant: 'ios',
    blur: 30,
    saturation: 1.3,
    brightness: 1.2,
    animation: 'breathe',
    accentColor: COLORS.PRIMARY,
    vibrancy: true,
    shadow: false,
    border: false
  },
  
  // macOS风格
  macos: {
    variant: 'macos',
    blur: 25,
    saturation: 1.1,
    brightness: 1.05,
    animation: 'fade',
    accentColor: COLORS.PRIMARY,
    vibrancy: true,
    shadow: true,
    border: true
  },
  
  // Windows风格
  windows: {
    variant: 'windows',
    blur: 15,
    saturation: 1.0,
    brightness: 1.0,
    animation: 'scale',
    accentColor: '#0078D4',
    vibrancy: false,
    shadow: true,
    border: false
  },
  
  // Material Design风格
  material: {
    variant: 'material',
    blur: 10,
    saturation: 1.2,
    brightness: 1.1,
    animation: 'pulse',
    accentColor: '#1976d2',
    vibrancy: false,
    shadow: true,
    border: false
  }
} as const

// ==================== 移动端主题 ====================

/** 移动端主题预设 */
export const MOBILE_THEMES = {
  // 旋转圆环
  ring: {
    variant: 'ios',
    accentColor: COLORS.PRIMARY,
    animation: 'rotate'
  },
  
  // 脉冲点
  pulse: {
    variant: 'macos',
    accentColor: COLORS.SUCCESS,
    animation: 'pulse'
  },
  
  // 三点波动
  dots: {
    variant: 'windows',
    accentColor: COLORS.WARNING,
    animation: 'bounce'
  },
  
  // 呼吸环
  breath: {
    variant: 'material',
    accentColor: COLORS.ERROR,
    animation: 'breathe'
  },
  
  // 流动条
  bars: {
    variant: 'system',
    accentColor: COLORS.INFO,
    animation: 'flow'
  }
} as const

// ==================== 主题工具函数 ====================

/** 获取加载主题 */
export function getLoadingTheme(themeName: keyof typeof LOADING_THEMES = 'system'): LoadingThemeConfig {
  return LOADING_THEMES[themeName] as LoadingThemeConfig
}

/** 获取系统主题 */
export function getSystemTheme(themeName: keyof typeof SYSTEM_THEMES = 'ios'): SystemThemeConfig {
  return SYSTEM_THEMES[themeName] as SystemThemeConfig
}

/** 获取移动端主题 */
export function getMobileTheme(themeName: keyof typeof MOBILE_THEMES = 'ring') {
  return MOBILE_THEMES[themeName]
}

/** 根据平台自动选择主题 */
export function getAutoTheme(): SystemThemeConfig {
  const userAgent = navigator.userAgent.toLowerCase()
  
  if (userAgent.includes('mac')) {
    return getSystemTheme('macos')
  } else if (userAgent.includes('windows')) {
    return getSystemTheme('windows')
  } else if (userAgent.includes('iphone') || userAgent.includes('ipad')) {
    return getSystemTheme('ios')
  } else if (userAgent.includes('android')) {
    return getSystemTheme('material')
  } else {
    return getSystemTheme('system')
  }
}

// ==================== 主题配置管理 ====================

/** 当前主题配置 */
export const CURRENT_THEME_CONFIG = {
  // 默认加载主题
  loading: 'system' as keyof typeof LOADING_THEMES,
  
  // 默认系统主题
  system: 'ios' as keyof typeof SYSTEM_THEMES,
  
  // 默认移动端主题
  mobile: 'ring' as keyof typeof MOBILE_THEMES,
  
  // 是否启用自动主题
  autoTheme: true,
  
  // 是否启用暗色模式
  darkMode: 'auto' as 'auto' | 'light' | 'dark'
} as const

/** 更新主题配置 */
export function updateThemeConfig(config: Partial<typeof CURRENT_THEME_CONFIG>) {
  Object.assign(CURRENT_THEME_CONFIG, config)
  
  // 保存到本地存储
  localStorage.setItem('themeConfig', JSON.stringify(CURRENT_THEME_CONFIG))
}

/** 从本地存储加载主题配置 */
export function loadThemeConfig() {
  try {
    const saved = localStorage.getItem('themeConfig')
    if (saved) {
      const config = JSON.parse(saved)
      Object.assign(CURRENT_THEME_CONFIG, config)
    }
  } catch (error) {
    console.warn('加载主题配置失败:', error)
  }
}
