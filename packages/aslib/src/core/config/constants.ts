/**
 * 核心常量配置
 * 
 * 定义应用的基础常量和配置信息
 */

// ==================== 版本信息 ====================

/** 应用版本 */
export const APP_VERSION = '1.0.0'

/** API版本 */
export const API_VERSION = 'v1'

// ==================== 应用配置 ====================

/** 应用名称 */
export const APP_NAME = '安生低代码平台'

/** 应用描述 */
export const APP_DESCRIPTION = '基于Vue3的低代码开发平台'

/** 应用作者 */
export const APP_AUTHOR = '安生'

// ==================== 路由配置 ====================

/** 核心业务路由（保持向后兼容） */
export const ROUTES = {
  // 认证相关
  LOGIN: '/login',
  
  // 主要页面
  HOME: '/home',
  
  // 套餐管理
  PACKAGE_LIST: '/PackageList',
  PACKAGE_PAYMENT: '/PackagePayment',
  PACKAGE_ORDER: '/PackageOrder',
  
  // 余额管理
  BALANCE_LIST: '/BalanceList',
  BALANCE_PAYMENT: '/BalancePayment',
  BALANCE_DETAILS: '/BalanceDetails',
  
  // 支付相关
  QR_CODE_PAYMENT: '/QrCodePayment',
  WECHAT_PAYMENT: '/WeChatPayment',
  
  // 实名认证
  REAL_NAME: '/RealName',
  REAL_NAME_CARDS: '/RealNameCards',
  
  // 设备管理
  EDIT_DEVICE: '/EditDevice',
  EDIT_PASSWORD: '/EditPassword',
  DEVICE_NOTICE: '/DeviceNotice',
  
  // 其他
  WEBVIEW: '/WebView',
  
  // 动态路由模式
  APP_DYNAMIC: '/app/:appId',
  PAGE_DYNAMIC: '/page/:pageId'
} as const

// ==================== 业务页面配置 ====================

/** 允许访问的业务页面 */
export const ALLOWED_BUSINESS_PAGES = [
  '/PackageList',
  '/PackagePayment',
  '/PackageOrder',
  '/BalanceList',
  '/BalancePayment',
  '/BalanceDetails',
  '/EditDevice',
  '/EditPassword',
  '/RealName',
  '/RealNameCards',
  '/DeviceNotice',
  '/LayoutService'
] as const

// ==================== 默认文本配置 ====================

/** 默认文本常量 */
export const DEFAULT_TEXTS = {
  // 实名认证相关
  REAL_NAME_MESSAGE: '为了您的账户安全，请完成实名认证',
  REAL_NAME_BUTTON: '立即认证',

  // 功能开发相关
  FEATURE_DEVELOPING: '功能开发中',

  // 页面标题
  PAGE_TITLE_WEBVIEW: '网页',

  // 通用文本
  CONFIRM: '确认',
  CANCEL: '取消',
  SUBMIT: '提交',
  SAVE: '保存',
  DELETE: '删除',
  EDIT: '编辑',
  VIEW: '查看',
  BACK: '返回',
  NEXT: '下一步',
  PREVIOUS: '上一步',

  // 状态文本
  LOADING: '加载中...',
  SUCCESS: '操作成功',
  ERROR: '操作失败',
  WARNING: '警告',
  INFO: '提示',

  // 表单文本
  REQUIRED: '必填项',
  OPTIONAL: '选填项',
  PLACEHOLDER: '请输入',
  SELECT_PLACEHOLDER: '请选择',

  // 网络相关
  NETWORK_ERROR: '网络连接失败',
  TIMEOUT_ERROR: '请求超时',
  SERVER_ERROR: '服务器错误'
} as const

// ==================== 应用类型和路由管理已迁移 ====================
// 
// 应用类型和路由配置已迁移到 packages/core/src/applications/ 目录
// 请使用以下方式导入：
// import { APPLICATION_TYPES, APPLICATION_ROUTES, getEnabledApplicationTypes } from '@lowcode/aslib/core'
//

// ==================== 本地存储键名 ====================

/** 本地存储键名 */
export const STORAGE_KEYS = {
  // 用户相关
  USER_TOKEN: 'userToken',
  USER_INFO: 'userInfo',
  
  // 应用相关
  CURRENT_APP_ID: 'currentAppId',
  APP_CONFIG: 'appConfig',
  
  // 设备相关
  DEVICE_INFO: 'deviceInfo',
  DEVICE_KEY: 'key',
  
  // 界面相关
  THEME_MODE: 'themeMode',
  LOADING_THEME: 'loadingTheme'
} as const

// ==================== 颜色配置 ====================

/** 颜色常量 */
export const COLORS = {
  // 品牌色
  BRAND_BLUE: '#1890ff',
  BRAND_GREEN: '#52c41a',
  BRAND_PURPLE: '#722ed1',

  // 主色调
  PRIMARY: '#1890ff',
  SUCCESS: '#52c41a',
  WARNING: '#faad14',
  ERROR: '#ff4d4f',
  INFO: '#13c2c2',

  // 灰度色
  GRAY_50: '#fafafa',
  GRAY_100: '#f5f5f5',
  GRAY_200: '#f0f0f0',
  GRAY_300: '#d9d9d9',
  GRAY_400: '#bfbfbf',
  GRAY_500: '#8c8c8c',
  GRAY_600: '#595959',
  GRAY_700: '#434343',
  GRAY_800: '#262626',
  GRAY_900: '#1f1f1f',

  // 背景色
  BG_WHITE: '#ffffff',
  BG_GRAY: '#fafafa',
  BG_DARK: '#141414'
} as const

// ==================== 动画配置 ====================

/** 动画时长 */
export const ANIMATION_DURATION = {
  // 基础动画
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,

  // 特殊动画
  LOADING: 1200,
  TRANSITION: 250,
  HOVER: 200
} as const

// ==================== 环境检测 ====================

/** 获取当前环境 */
export function getCurrentEnvironment() {
  return import.meta.env.MODE || 'development'
}

/** 是否为开发环境 */
export function isDevelopment() {
  return getCurrentEnvironment() === 'development'
}

/** 是否为生产环境 */
export function isProduction() {
  return getCurrentEnvironment() === 'production'
}
