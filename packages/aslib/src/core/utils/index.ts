import type { ComponentConfig, PageConfig } from '../types/schema'
import { generateId } from './id'

// 导出ID工具
export * from './id'
export * from './route'

// 深度克隆
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as any
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as any
  if (typeof obj === 'object') {
    const clonedObj = {} as any
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  return obj
}

// 合并配置
export function mergeConfig<T extends Record<string, any>>(
  defaultConfig: T,
  userConfig: Partial<T>
): T {
  return { ...defaultConfig, ...userConfig }
}

// 验证组件配置
export function validateComponentConfig(config: ComponentConfig): boolean {
  if (!config.id || !config.type) {
    console.error('Component config must have id and type')
    return false
  }
  
  // 验证子组件
  if (config.children) {
    for (const child of config.children) {
      if (!validateComponentConfig(child)) {
        return false
      }
    }
  }
  
  return true
}

// 验证页面配置
export function validatePageConfig(config: PageConfig): boolean {
  if (!config.id || !config.name || !config.path) {
    console.error('Page config must have id, name and path')
    return false
  }
  
  if (!config.layout) {
    console.error('Page config must have layout')
    return false
  }
  
  // 验证组件
  for (const component of config.components) {
    if (!validateComponentConfig(component)) {
      return false
    }
  }
  
  return true
}

// 查找组件
export function findComponent(
  components: ComponentConfig[],
  predicate: (component: ComponentConfig) => boolean
): ComponentConfig | null {
  for (const component of components) {
    if (predicate(component)) {
      return component
    }
    
    if (component.children) {
      const found = findComponent(component.children, predicate)
      if (found) return found
    }
  }
  
  return null
}

// 查找组件通过ID
export function findComponentById(
  components: ComponentConfig[],
  id: string
): ComponentConfig | null {
  return findComponent(components, component => component.id === id)
}

// 遍历组件树
export function traverseComponents(
  components: ComponentConfig[],
  callback: (component: ComponentConfig, parent?: ComponentConfig) => void,
  parent?: ComponentConfig
): void {
  for (const component of components) {
    callback(component, parent)
    
    if (component.children) {
      traverseComponents(component.children, callback, component)
    }
  }
}

// 扁平化组件树
export function flattenComponents(components: ComponentConfig[]): ComponentConfig[] {
  const result: ComponentConfig[] = []
  
  traverseComponents(components, (component) => {
    result.push(component)
  })
  
  return result
}

// 创建默认组件配置
export function createDefaultComponentConfig(type: string): ComponentConfig {
  return {
    id: generateId('component'),
    type,
    props: {},
    style: {},
    visible: true,
    editable: true
  }
}

// 创建默认页面配置
export function createDefaultPageConfig(name: string, path: string): PageConfig {
  return {
    id: generateId('page'),
    name,
    path,
    layout: {
      type: 'flex',
      direction: 'column',
      padding: 16
    },
    components: [],
    editable: true
  }
}

// 格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout>
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean

  return function executedFunction(this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 安全的JSON解析
export function safeJsonParse<T = any>(str: string, defaultValue: T): T {
  try {
    return JSON.parse(str)
  } catch {
    return defaultValue
  }
}

// 安全的JSON字符串化
export function safeJsonStringify(obj: any, space?: number): string {
  try {
    return JSON.stringify(obj, null, space)
  } catch {
    return '{}'
  }
}
