/**
 * 环境变量工具函数
 */

// 环境变量获取函数
export function getEnvVar(key: string, defaultValue: string = ''): string {
  // 在浏览器环境中尝试获取 Vite 环境变量
  if (typeof window !== 'undefined') {
    try {
      // 使用动态访问避免编译时错误
      const importMeta = (globalThis as any).import?.meta
      if (importMeta?.env && importMeta.env[key]) {
        return importMeta.env[key]
      }
    } catch (e) {
      // import.meta 不可用时忽略错误
    }
  }
  
  // 在Node.js环境中使用 process.env
  if (typeof process !== 'undefined' && process.env && process.env[key]) {
    return process.env[key]
  }
  
  return defaultValue
}

// 获取布尔类型环境变量
export function getBooleanEnv(key: string, defaultValue: boolean = false): boolean {
  const value = getEnvVar(key, defaultValue.toString())
  return value === 'true' || value === '1'
}

// 获取数字类型环境变量
export function getNumberEnv(key: string, defaultValue: number = 0): number {
  const value = getEnvVar(key, defaultValue.toString())
  const parsed = parseInt(value, 10)
  return isNaN(parsed) ? defaultValue : parsed
}

// 获取API基础URL
export function getAPIBaseURL(apiName: string, defaultURL: string): string {
  const envKey = `VITE_${apiName.toUpperCase()}_API_URL`
  return getEnvVar(envKey, defaultURL)
}

// 预定义的API URL获取函数
export const getDeviceAnAPIURL = () => {
  // 开发环境使用代理路径，生产环境使用完整URL
  const defaultURL = isDevelopment() ? '/Fan' : 'https://api.device-an.com'
  return getAPIBaseURL('DEVICE_AN', defaultURL)
}

export const getLowcodeAPIURL = () => getAPIBaseURL('LOWCODE', 'http://localhost:3002')
export const getProjectAAPIURL = () => getAPIBaseURL('PROJECT_A', 'https://api.project-a.com')

// 环境检测
export function isProduction(): boolean {
  return getEnvVar('NODE_ENV', 'development') === 'production' ||
         getEnvVar('VITE_APP_ENV', 'development') === 'production'
}

export function isDevelopment(): boolean {
  return !isProduction()
}

export function isDebugMode(): boolean {
  return getBooleanEnv('VITE_DEBUG_MODE', isDevelopment())
}

// 应用配置
export function getAppConfig() {
  return {
    title: getEnvVar('VITE_APP_TITLE', '低代码应用'),
    version: getEnvVar('VITE_APP_VERSION', '1.0.0'),
    env: getEnvVar('VITE_APP_ENV', 'development'),
    debug: isDebugMode(),
    
    // API配置
    apis: {
      deviceAn: getDeviceAnAPIURL(),
      lowcode: getLowcodeAPIURL(),
      projectA: getProjectAAPIURL()
    },
    
    // 功能开关
    features: {
      performanceMonitor: getBooleanEnv('VITE_ENABLE_PERFORMANCE_MONITOR', true),
      errorReporting: getBooleanEnv('VITE_ENABLE_ERROR_REPORTING', false),
      hotReload: getBooleanEnv('VITE_ENABLE_HOT_RELOAD', isDevelopment())
    }
  }
}

// 设计器配置
export function getDesignerConfig() {
  return {
    canvas: {
      width: getNumberEnv('VITE_CANVAS_WIDTH', 375),
      height: getNumberEnv('VITE_CANVAS_HEIGHT', 667)
    },
    
    features: {
      grid: getBooleanEnv('VITE_ENABLE_GRID', true),
      ruler: getBooleanEnv('VITE_ENABLE_RULER', true),
      codeEditor: getBooleanEnv('VITE_ENABLE_CODE_EDITOR', true)
    },
    
    monaco: {
      cdn: getEnvVar('VITE_MONACO_CDN', ''),
      theme: getEnvVar('VITE_MONACO_THEME', 'vs-dark')
    }
  }
}

// 导出默认配置
export const ENV_CONFIG = getAppConfig()
export const DESIGNER_CONFIG = getDesignerConfig()
