// ID 生成工具

let counter = 0

// 生成唯一ID
export function generateId(prefix = 'id'): string {
  return `${prefix}_${Date.now()}_${++counter}`
}

// 生成UUID (简化版)
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

// 检查ID是否有效
export function isValidId(id: string): boolean {
  return typeof id === 'string' && id.length > 0
}
