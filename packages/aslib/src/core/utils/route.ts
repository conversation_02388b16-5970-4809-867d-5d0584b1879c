/**
 * 路由工具函数
 * 
 * 提供统一的路径拼接和管理功能
 */

import type { ApplicationType, PageRoute } from '../types/application'

// ==================== 路径前缀配置 ====================

/**
 * 路径前缀配置
 * 可以在这里统一管理所有应用的路径前缀
 */
export const ROUTE_PREFIXES: Record<string, string> = {
  device: '/device',
  mall: '/mall'
  // 未来可以添加更多应用类型
  // admin: '/admin',
  // mobile: '/mobile'
}

/**
 * 默认路径前缀（当应用ID不在配置中时使用）
 */
export const DEFAULT_ROUTE_PREFIX = ''

// ==================== 路径工具函数 ====================

/**
 * 根据应用ID获取路径前缀
 */
export function getRoutePrefix(appId: string): string {
  return ROUTE_PREFIXES[appId] || DEFAULT_ROUTE_PREFIX
}

/**
 * 构建完整的路由路径
 */
export function buildRoutePath(appId: string, path: string): string {
  const prefix = getRoutePrefix(appId)
  
  // 如果路径已经包含前缀，直接返回
  if (path.startsWith(prefix + '/') || path === prefix) {
    return path
  }
  
  // 处理根路径
  if (path === '/') {
    return prefix || '/'
  }
  
  // 拼接前缀和路径
  return prefix + path
}

/**
 * 从完整路径中提取应用ID
 */
export function extractAppIdFromPath(path: string): string | null {
  for (const [appId, prefix] of Object.entries(ROUTE_PREFIXES)) {
    if (path.startsWith(prefix + '/') || path === prefix) {
      return appId
    }
  }
  return null
}

/**
 * 从完整路径中移除应用前缀
 */
export function removeRoutePrefix(path: string): string {
  const appId = extractAppIdFromPath(path)
  if (!appId) return path
  
  const prefix = getRoutePrefix(appId)
  if (path.startsWith(prefix)) {
    return path.substring(prefix.length) || '/'
  }
  
  return path
}

/**
 * 检查路径是否属于指定应用
 */
export function isPathBelongsToApp(path: string, appId: string): boolean {
  const prefix = getRoutePrefix(appId)
  return path.startsWith(prefix + '/') || path === prefix
}

/**
 * 为应用配置构建完整的路由
 */
export function buildApplicationRoutes(
  appId: string,
  routes: Record<string, PageRoute>
): Record<string, PageRoute> {
  const result: Record<string, PageRoute> = {}

  for (const [key, route] of Object.entries(routes)) {
    result[key] = {
      ...route,
      path: buildRoutePath(appId, route.path)
    }
  }

  return result
}

/**
 * 获取应用的默认页面路径
 */
export function getDefaultPagePath(application: ApplicationType): string {
  return buildRoutePath(application.id, application.defaultPage || '/home')
}

/**
 * 获取应用的登录页面路径
 */
export function getLoginPagePath(application: ApplicationType): string {
  return buildRoutePath(application.id, '/login')
}

/**
 * 验证路由路径格式
 */
export function validateRoutePath(path: string): boolean {
  // 路径必须以 / 开头
  if (!path.startsWith('/')) return false
  
  // 不能包含连续的 //
  if (path.includes('//')) return false
  
  // 不能以 / 结尾（除非是根路径）
  if (path.length > 1 && path.endsWith('/')) return false
  
  return true
}

/**
 * 标准化路由路径
 */
export function normalizeRoutePath(path: string): string {
  // 确保以 / 开头
  if (!path.startsWith('/')) {
    path = '/' + path
  }
  
  // 移除末尾的 /（除非是根路径）
  if (path.length > 1 && path.endsWith('/')) {
    path = path.slice(0, -1)
  }
  
  // 替换连续的 / 为单个 /
  path = path.replace(/\/+/g, '/')
  
  return path
}

// ==================== 路由匹配工具 ====================

/**
 * 检查路径是否匹配路由模式
 */
export function matchRoute(pattern: string, path: string): boolean {
  // 简单的路径匹配，支持 :param 参数
  const patternParts = pattern.split('/')
  const pathParts = path.split('/')
  
  if (patternParts.length !== pathParts.length) {
    return false
  }
  
  for (let i = 0; i < patternParts.length; i++) {
    const patternPart = patternParts[i]
    const pathPart = pathParts[i]
    
    // 参数匹配
    if (patternPart.startsWith(':')) {
      continue
    }
    
    // 精确匹配
    if (patternPart !== pathPart) {
      return false
    }
  }
  
  return true
}

/**
 * 从路径中提取参数
 */
export function extractRouteParams(pattern: string, path: string): Record<string, string> {
  const params: Record<string, string> = {}
  const patternParts = pattern.split('/')
  const pathParts = path.split('/')
  
  if (patternParts.length !== pathParts.length) {
    return params
  }
  
  for (let i = 0; i < patternParts.length; i++) {
    const patternPart = patternParts[i]
    const pathPart = pathParts[i]
    
    if (patternPart.startsWith(':')) {
      const paramName = patternPart.substring(1)
      params[paramName] = pathPart
    }
  }
  
  return params
}
