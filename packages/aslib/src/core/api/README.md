# API架构设计文档

## 🎯 设计目标

解决低代码平台作为基建时，需要集成多个不同项目API的问题，提供：

- **清晰分离**: 不同项目的API完全隔离
- **统一接口**: 所有API调用使用相同的接口
- **灵活认证**: 支持多种认证策略
- **易于扩展**: 新项目接入只需配置，无需修改核心代码

## 🏗️ 架构概览

```
H5应用 → API网关 → 适配器 → 认证策略 → 外部API
```

### 核心组件

1. **API网关 (APIGateway)**: 统一入口，路由分发
2. **适配器 (Adapter)**: 处理不同API的协议差异
3. **认证策略 (AuthStrategy)**: 处理不同的认证方式
4. **项目配置 (ProjectConfig)**: 管理多项目配置

## 🚀 快速开始

### 1. 基础使用

```typescript
import { quickInit, apiRequest } from '@lowcode/core/api'

// 初始化Device-An项目
const gateway = quickInit('DEVICE_AN')

// 发起API请求
const response = await apiRequest({
  url: '/front/deviceIndex/deviceLogin',
  method: 'POST',
  data: { deviceNo: 'DEVICE001' }
})
```

### 2. 混合项目使用

```typescript
import { quickInit, apiRequest } from '@lowcode/core/api'

// 初始化混合项目（Device-An + 低代码平台）
const gateway = quickInit('HYBRID')

// Device-An API调用
const deviceInfo = await apiRequest({
  url: '/frontDevice/device/getDeviceInfo',
  method: 'GET'
})

// 低代码平台API调用
const appConfig = await apiRequest({
  url: '/api/app/config',
  method: 'GET'
})
```

### 3. 自定义项目配置

```typescript
import { 
  initializeGlobalGateway, 
  createMultiProjectGatewayConfig,
  createDefaultRoutes 
} from '@lowcode/core/api'

// 创建自定义配置
const config = createMultiProjectGatewayConfig()

// 添加新的适配器
config.adapters['my-project'] = {
  name: 'my-project',
  baseURL: 'https://api.my-project.com',
  timeout: 15000,
  auth: { 
    type: 'custom',
    config: { apiKey: 'your-api-key' }
  }
}

// 初始化网关
const gateway = initializeGlobalGateway(config)

// 添加路由
gateway.addRoute({
  pattern: /^\/my-api\//,
  adapter: 'my-project'
})
```

## 🔐 认证策略

### Device-An认证

```typescript
// 自动处理Device-An的认证头
// deviceToken, Conten-Date, Conten-Zx
const response = await apiRequest({
  url: '/front/deviceIndex/deviceLogin',
  method: 'POST',
  data: { deviceNo: 'DEVICE001' }
})
```

### JWT认证

```typescript
// 自动添加Authorization头
const response = await apiRequest({
  url: '/api/user/profile',
  method: 'GET'
})
```

### 自定义认证

```typescript
// 在适配器配置中指定
{
  auth: {
    type: 'custom',
    config: {
      apiKey: 'your-api-key',
      customHeaders: {
        'X-Client-ID': 'your-client-id'
      }
    }
  }
}
```

## 🛣️ 路由配置

### 基于路径前缀

```typescript
gateway.addRoute({
  pattern: '/device/',
  adapter: 'device-an'
})
```

### 基于正则表达式

```typescript
gateway.addRoute({
  pattern: /^\/api\/v[12]\//,
  adapter: 'api-v1-v2'
})
```

### 带转换的路由

```typescript
gateway.addRoute({
  pattern: '/legacy/',
  adapter: 'new-api',
  transform: {
    request: (config) => ({
      ...config,
      url: config.url.replace('/legacy/', '/v2/')
    })
  }
})
```

## 📦 适配器开发

### 创建自定义适配器

```typescript
import { FetchAPIAdapter } from '@lowcode/core/api'

export class MyProjectAdapter extends FetchAPIAdapter {
  protected adaptResponse<T>(response: any): APIResponse<T> {
    // 自定义响应格式转换
    return {
      success: response.status === 'ok',
      data: response.result,
      message: response.message
    }
  }
  
  // 项目特定的API方法
  async getProjectData(): Promise<APIResponse<any>> {
    return this.request({
      url: '/project/data',
      method: 'GET'
    })
  }
}
```

## 🔧 高级配置

### 缓存配置

```typescript
const response = await apiRequest({
  url: '/api/data',
  method: 'GET',
  cache: {
    key: 'api-data',
    ttl: 300000, // 5分钟
    enabled: true
  }
})
```

### 重试配置

```typescript
const response = await apiRequest({
  url: '/api/data',
  method: 'GET',
  retry: {
    times: 3,
    delay: 1000,
    backoff: 2
  }
})
```

### 拦截器

```typescript
const config = {
  adapters: {
    'my-api': {
      name: 'my-api',
      baseURL: 'https://api.example.com',
      timeout: 10000,
      auth: { type: 'none' },
      interceptors: {
        request: (config) => {
          console.log('请求发送:', config)
          return config
        },
        response: (response) => {
          console.log('响应接收:', response)
          return response
        },
        error: (error) => {
          console.error('请求错误:', error)
        }
      }
    }
  }
}
```

## 🌍 环境管理

### 开发环境

```typescript
const gateway = quickInit('HYBRID', 'development')
```

### 生产环境

```typescript
const gateway = quickInit('HYBRID', 'production')
```

### 环境变量

```bash
# .env (Vite项目)
VITE_DEVICE_AN_API_URL=https://api.device-an.com
VITE_LOWCODE_API_URL=http://localhost:3002
VITE_PROJECT_A_API_URL=https://api.project-a.com

# .env (Node.js项目)
DEVICE_AN_API_URL=https://api.device-an.com
LOWCODE_API_URL=http://localhost:3002
PROJECT_A_API_URL=https://api.project-a.com
```

## 🔍 监控和调试

### 健康检查

```typescript
const health = await gateway.healthCheck()
console.log('API健康状态:', health)
// { 'device-an': true, 'lowcode': true }
```

### 错误处理

```typescript
try {
  const response = await apiRequest({
    url: '/api/data',
    method: 'GET'
  })
} catch (error) {
  if (error instanceof AuthError) {
    // 处理认证错误
  } else if (error instanceof NetworkError) {
    // 处理网络错误
  }
}
```

## 📋 最佳实践

1. **项目隔离**: 每个项目使用独立的适配器
2. **认证分离**: 不同认证策略完全独立
3. **配置外置**: 使用环境变量管理API地址
4. **错误处理**: 统一的错误处理机制
5. **缓存策略**: 合理使用缓存减少请求
6. **监控日志**: 记录API调用状态和性能
