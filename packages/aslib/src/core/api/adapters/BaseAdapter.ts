/**
 * API适配器基类
 */

import type { 
  APIAdapter, 
  AdapterConfig, 
  RequestConfig, 
  APIResponse, 
  AuthStrategy 
} from '../types'
import { APIError, NetworkError } from '../types'
import { AuthStrategyFactory } from '../auth/strategies'

export abstract class BaseAPIAdapter implements APIAdapter {
  public readonly name: string
  public readonly baseURL: string
  public readonly authStrategy: AuthStrategy
  
  protected config: AdapterConfig
  protected requestCache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  
  constructor(config: AdapterConfig) {
    this.config = config
    this.name = config.name
    this.baseURL = config.baseURL
    this.authStrategy = AuthStrategyFactory.create(config.auth.type, config.auth.config)
  }
  
  // 统一请求方法
  async request<T>(requestConfig: RequestConfig): Promise<APIResponse<T>> {
    try {
      // 1. 应用认证策略
      const authenticatedConfig = await this.authStrategy.authenticate(requestConfig)
      
      // 2. 应用请求拦截器
      const interceptedConfig = this.applyRequestInterceptor(authenticatedConfig)
      
      // 3. 检查缓存
      if (interceptedConfig.cache?.enabled) {
        const cached = this.getFromCache<T>(interceptedConfig.cache.key)
        if (cached) {
          return {
            success: true,
            data: cached,
            message: 'From cache'
          }
        }
      }
      
      // 4. 执行HTTP请求
      const response = await this.performRequest(interceptedConfig)
      
      // 5. 应用响应拦截器
      const processedResponse = this.applyResponseInterceptor(response)
      
      // 6. 转换为统一格式
      const unifiedResponse = this.adaptResponse<T>(processedResponse)
      
      // 7. 缓存结果
      if (interceptedConfig.cache?.enabled && unifiedResponse.success) {
        this.setCache(interceptedConfig.cache.key, unifiedResponse.data, interceptedConfig.cache.ttl)
      }
      
      return unifiedResponse
      
    } catch (error) {
      return this.handleError<T>(error)
    }
  }
  
  // 健康检查
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.performRequest({
        url: '/health',
        method: 'GET',
        timeout: 5000
      })
      return response.status >= 200 && response.status < 300
    } catch {
      return false
    }
  }
  
  // 抽象方法：子类必须实现
  protected abstract performRequest(config: RequestConfig): Promise<any>
  protected abstract adaptResponse<T>(response: any): APIResponse<T>
  
  // 应用请求拦截器
  protected applyRequestInterceptor(config: RequestConfig): RequestConfig {
    if (this.config.interceptors?.request) {
      return this.config.interceptors.request(config)
    }
    
    // 默认处理
    return {
      ...config,
      url: this.buildURL(config.url),
      timeout: config.timeout || this.config.timeout,
      headers: {
        ...this.config.headers,
        ...config.headers
      }
    }
  }
  
  // 应用响应拦截器
  protected applyResponseInterceptor(response: any): any {
    if (this.config.interceptors?.response) {
      return this.config.interceptors.response(response)
    }
    return response
  }
  
  // 错误处理
  protected async handleError<T>(error: any): Promise<APIResponse<T>> {
    console.error(`[${this.name}] API Error:`, error)
    
    // 应用错误拦截器
    if (this.config.interceptors?.error) {
      this.config.interceptors.error(error)
    }
    
    // 处理认证错误
    if (this.isAuthError(error)) {
      await this.authStrategy.handleAuthError(error)
      throw new APIError('认证失败', 401, this.name, error)
    }
    
    // 处理网络错误
    if (this.isNetworkError(error)) {
      throw new NetworkError('网络连接失败', this.name)
    }
    
    // 返回错误响应
    return {
      success: false,
      data: null as T,
      message: error.message || '请求失败'
    }
  }
  
  // 构建完整URL
  protected buildURL(url: string): string {
    if (url.startsWith('http')) {
      return url
    }
    return `${this.baseURL.replace(/\/$/, '')}/${url.replace(/^\//, '')}`
  }
  
  // 缓存管理
  protected getFromCache<T>(key: string): T | null {
    const cached = this.requestCache.get(key)
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data
    }
    this.requestCache.delete(key)
    return null
  }
  
  protected setCache(key: string, data: any, ttl: number): void {
    this.requestCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
  
  // 错误类型判断
  protected isAuthError(error: any): boolean {
    return error.status === 401 ||
           error.response?.status === 401 ||
           error.code === 401 ||
           error.isAuthError === true ||
           // 检查Device-An特有的NOT_LOGIN响应格式
           (error.response?.data?.code === 0 && error.response?.data?.msg === 'NOT_LOGIN')
  }
  
  protected isNetworkError(error: any): boolean {
    return error.code === 'NETWORK_ERROR' ||
           error.message?.includes('Network') ||
           error.name === 'NetworkError'
  }
  
  // 清理资源
  public dispose(): void {
    this.requestCache.clear()
    this.authStrategy.clearAuth()
  }
}

// ==================== 具体适配器实现 ====================

// Fetch API适配器
export class FetchAPIAdapter extends BaseAPIAdapter {
  protected async performRequest(config: RequestConfig): Promise<any> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), config.timeout || 10000)

    // 处理GET请求的params参数
    let url = config.url
    if (config.params && config.method === 'GET') {
      const searchParams = new URLSearchParams()
      Object.keys(config.params).forEach(key => {
        if (config.params![key] !== undefined && config.params![key] !== null) {
          searchParams.append(key, String(config.params![key]))
        }
      })
      const queryString = searchParams.toString()
      if (queryString) {
        url += (url.includes('?') ? '&' : '?') + queryString
      }
    }

    try {
      // 处理请求头和请求体
      let body: string | FormData | undefined
      let headers = { ...config.headers }

      if (config.data) {
        if (config.data instanceof FormData) {
          // FormData类型：不设置Content-Type，让浏览器自动设置multipart/form-data
          body = config.data
          // 删除可能存在的Content-Type，让浏览器自动设置
          delete headers['Content-Type']
          delete headers['content-type']
        } else {
          // 普通POST请求：设置JSON格式
          body = JSON.stringify(config.data)
          headers['Content-Type'] = 'application/json'
        }
      }

      const response = await fetch(url, {
        method: config.method,
        headers,
        body,
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new APIError(`HTTP ${response.status}: ${response.statusText}`, response.status, this.name)
      }

      const data = await response.json()
      return { data, status: response.status, headers: response.headers }
    } catch (error) {
      clearTimeout(timeoutId)
      throw error
    }
  }

  protected adaptResponse<T>(response: any): APIResponse<T> {
    // 默认适配逻辑，子类可以重写
    return {
      success: true,
      data: response.data,
      message: 'Success'
    }
  }
}
