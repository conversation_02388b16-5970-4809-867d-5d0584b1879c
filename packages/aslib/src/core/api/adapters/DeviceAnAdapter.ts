/**
 * Device-An API适配器
 */

import { FetchAPIAdapter } from './BaseAdapter'
import type { APIResponse, AdapterConfig } from '../types'
import { getDeviceAnAPIURL } from '../../utils/env'

export class DeviceAnAdapter extends FetchAPIAdapter {
  constructor(config?: Partial<AdapterConfig>) {
    const defaultConfig: AdapterConfig = {
      name: 'device-an',
      baseURL: getDeviceAnAPIURL(),
      timeout: 30000,
      auth: {
        type: 'device-an'
      },
      headers: {
        'Content-Type': 'application/json'
      }
    }

    super({ ...defaultConfig, ...config })
  }
  
  // Device-An特定的响应适配
  protected adaptResponse<T>(response: any): APIResponse<T> {
    const { data } = response

    // Device-An的响应格式：{code, data, msg}
    if (typeof data === 'object' && 'code' in data) {
      // 检查是否是认证失败的响应
      if (data.code === 0 && data.msg === 'NOT_LOGIN') {
        // 抛出认证错误，让BaseAdapter的handleError处理
        const authError = new Error('Authentication failed') as any
        authError.code = 401  // 设置为401让isAuthError能识别
        authError.response = { data: data }
        authError.isAuthError = true
        throw authError
      }

      return {
        success: data.code === 200,
        data: data.data,
        message: data.msg || (data.code === 200 ? 'Success' : 'Error'),
        code: data.code
      }
    }

    // 降级处理
    return {
      success: true,
      data: data,
      message: 'Success'
    }
  }
  
  // Device-An特定的API方法
  
  // 设备登录
  async deviceLogin(deviceNo: string, groupId?: number): Promise<APIResponse<any>> {
    return this.request({
      url: 'front/deviceIndex/deviceLogin',
      method: 'POST',
      data: { deviceNo, groupId }
    })
  }
  
  // 获取设备信息
  async getDeviceInfo(): Promise<APIResponse<any>> {
    return this.request({
      url: 'frontDevice/device/getDeviceInfo',
      method: 'GET'
    })
  }
  
  // 获取设备卡片列表
  async getDeviceCards(): Promise<APIResponse<any[]>> {
    return this.request({
      url: 'frontDevice/device/getDeviceCardList',
      method: 'GET'
    })
  }
  
  // 更新设备信息
  async updateDevice(data: any): Promise<APIResponse<any>> {
    return this.request({
      url: 'frontDevice/device/updateDevice',
      method: 'POST',
      data
    })
  }
  
  // 获取套餐列表
  async getPackageList(params?: any): Promise<APIResponse<any[]>> {
    return this.request({
      url: 'frontDevice/package/getPackageList',
      method: 'GET',
      params
    })
  }
  
  // 购买套餐
  async purchasePackage(data: any): Promise<APIResponse<any>> {
    return this.request({
      url: 'frontDevice/package/purchasePackage',
      method: 'POST',
      data
    })
  }
  
  // 获取支付方式
  async getPaymentMethods(params: any): Promise<APIResponse<any[]>> {
    return this.request({
      url: 'frontDevice/payment/getPaymentMethods',
      method: 'GET',
      params
    })
  }
  
  // 创建支付订单
  async createPayment(data: any): Promise<APIResponse<any>> {
    return this.request({
      url: 'frontDevice/payment/createPayment',
      method: 'POST',
      data
    })
  }
  
  // 获取系统配置
  async getSystemConfig(): Promise<APIResponse<any>> {
    return this.request({
      url: 'Lin/backend/backstage/getsitesettings',
      method: 'GET'
    })
  }
  
  // 获取余额明细
  async getBalanceDetails(params: any): Promise<APIResponse<any[]>> {
    return this.request({
      url: 'frontDevice/balance/getBalanceDetails',
      method: 'GET',
      params
    })
  }
  
  // 文件上传
  async uploadFile(file: File, onProgress?: (progress: number) => void): Promise<APIResponse<any>> {
    const formData = new FormData()
    formData.append('file', file)
    
    return this.request({
      url: 'frontDevice/file/upload',
      method: 'POST',
      data: formData,
      headers: {
        // 让浏览器自动设置Content-Type
      }
    })
  }
}
