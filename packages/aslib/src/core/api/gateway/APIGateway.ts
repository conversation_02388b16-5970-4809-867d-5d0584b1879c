/**
 * API网关 - 统一管理多个API适配器
 */

import type { 
  APIGatewayConfig, 
  RouteConfig, 
  RequestConfig, 
  APIResponse, 
  APIAdapter,
  AdapterConfig 
} from '../types'
import { APIError } from '../types'
import { DeviceAnAdapter } from '../adapters/DeviceAnAdapter'
import { FetchAPIAdapter } from '../adapters/BaseAdapter'

export class APIGateway {
  private adapters = new Map<string, APIAdapter>()
  private routes: RouteConfig[] = []
  private config: APIGatewayConfig
  
  constructor(config: APIGatewayConfig) {
    this.config = config
    this.initializeAdapters()
  }
  
  // 初始化适配器
  private initializeAdapters(): void {
    Object.entries(this.config.adapters).forEach(([name, adapterConfig]) => {
      const adapter = this.createAdapter(adapterConfig)
      this.adapters.set(name, adapter)
    })
  }
  
  // 创建适配器实例
  private createAdapter(config: AdapterConfig): APIAdapter {
    switch (config.auth.type) {
      case 'device-an':
        return new DeviceAnAdapter(config)
      default:
        return new FetchAPIAdapter(config)
    }
  }
  
  // 注册路由
  public addRoute(route: RouteConfig): void {
    this.routes.push(route)
  }
  
  // 批量注册路由
  public addRoutes(routes: RouteConfig[]): void {
    this.routes.push(...routes)
  }
  
  // 注册适配器
  public registerAdapter(name: string, adapter: APIAdapter): void {
    this.adapters.set(name, adapter)
  }
  
  // 统一请求方法
  async request<T>(config: RequestConfig): Promise<APIResponse<T>> {
    try {
      // 1. 路由匹配
      const { adapter, transformedConfig } = this.matchRoute(config)
      
      // 2. 应用全局配置
      const finalConfig = this.applyGlobalConfig(transformedConfig)
      
      // 3. 执行请求
      const response = await adapter.request<T>(finalConfig)
      
      return response
      
    } catch (error) {
      console.error('[APIGateway] Request failed:', error)
      throw error
    }
  }
  
  // 路由匹配
  private matchRoute(config: RequestConfig): { adapter: APIAdapter; transformedConfig: RequestConfig } {
    // 查找匹配的路由
    for (const route of this.routes) {
      if (this.isRouteMatch(route.pattern, config.url)) {
        const adapter = this.adapters.get(route.adapter)
        if (!adapter) {
          throw new APIError(`适配器 ${route.adapter} 未找到`)
        }
        
        // 应用路由转换
        const transformedConfig = route.transform?.request 
          ? route.transform.request(config)
          : config
          
        return { adapter, transformedConfig }
      }
    }
    
    // 使用默认适配器
    const defaultAdapterName = this.config.defaultAdapter
    if (defaultAdapterName) {
      const adapter = this.adapters.get(defaultAdapterName)
      if (adapter) {
        return { adapter, transformedConfig: config }
      }
    }
    
    throw new APIError(`没有找到匹配的路由: ${config.url}`)
  }
  
  // 路由匹配检查
  private isRouteMatch(pattern: string | RegExp, url: string): boolean {
    if (typeof pattern === 'string') {
      return url.startsWith(pattern)
    }
    return pattern.test(url)
  }
  
  // 应用全局配置
  private applyGlobalConfig(config: RequestConfig): RequestConfig {
    return {
      ...config,
      timeout: config.timeout || this.config.globalTimeout,
      retry: config.retry || this.config.globalRetry
    }
  }
  
  // 获取适配器
  public getAdapter(name: string): APIAdapter | undefined {
    return this.adapters.get(name)
  }
  
  // 获取所有适配器
  public getAllAdapters(): Map<string, APIAdapter> {
    return new Map(this.adapters)
  }
  
  // 健康检查
  async healthCheck(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {}
    
    const checks = Array.from(this.adapters.entries()).map(async ([name, adapter]) => {
      try {
        const isHealthy = await adapter.healthCheck()
        results[name] = isHealthy
      } catch {
        results[name] = false
      }
    })
    
    await Promise.all(checks)
    return results
  }
  
  // 清理资源
  public dispose(): void {
    this.adapters.forEach(adapter => {
      if ('dispose' in adapter && typeof adapter.dispose === 'function') {
        adapter.dispose()
      }
    })
    this.adapters.clear()
    this.routes = []
  }
}

// ==================== 预设配置 ====================

// Device-An项目配置
export const createDeviceAnGatewayConfig = (baseURL: string): APIGatewayConfig => ({
  adapters: {
    'device-an': {
      name: 'device-an',
      baseURL,
      timeout: 30000,
      auth: {
        type: 'device-an'
      }
    }
  },
  defaultAdapter: 'device-an',
  globalTimeout: 30000,
  globalRetry: {
    times: 3,
    delay: 1000,
    backoff: 2
  }
})

// 多项目配置示例
export const createMultiProjectGatewayConfig = (): APIGatewayConfig => ({
  adapters: {
    'device-an': {
      name: 'device-an',
      baseURL: process.env.DEVICE_AN_API_URL || 'https://api.device-an.com',
      timeout: 30000,
      auth: { type: 'device-an' }
    },
    'project-a': {
      name: 'project-a',
      baseURL: process.env.PROJECT_A_API_URL || 'https://api.project-a.com',
      timeout: 15000,
      auth: { type: 'jwt' }
    },
    'lowcode': {
      name: 'lowcode',
      baseURL: process.env.LOWCODE_API_URL || 'http://localhost:3002',
      timeout: 10000,
      auth: { type: 'none' }
    }
  },
  defaultAdapter: 'lowcode',
  globalTimeout: 15000
})

// 路由配置示例
export const createDefaultRoutes = (): RouteConfig[] => [
  {
    pattern: /^\/device\//,
    adapter: 'device-an'
  },
  {
    pattern: /^\/front/,
    adapter: 'device-an'
  },
  {
    pattern: /^\/api\/app/,
    adapter: 'lowcode'
  },
  {
    pattern: /^\/api\/project-a/,
    adapter: 'project-a',
    transform: {
      request: (config) => ({
        ...config,
        url: config.url.replace('/api/project-a', '')
      })
    }
  }
]
