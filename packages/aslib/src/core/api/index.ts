/**
 * API模块统一导出 - 简化版本
 */

// 基础类型导出
export * from './types'
export * from './auth/strategies'
export * from './adapters/BaseAdapter'
export * from './adapters/DeviceAnAdapter'
export * from './gateway/APIGateway'

// 导入需要的类
import { APIGateway } from './gateway/APIGateway'
import type { APIGatewayConfig } from './types'

// 项目配置管理
export class ProjectConfigManager {
  private static configs = new Map<string, any>()
  private static currentProject: string | null = null

  // 注册项目配置
  static registerProject(config: any): void {
    this.configs.set(config.projectId, config)
  }

  // 切换项目
  static switchProject(projectId: string): void {
    if (!this.configs.has(projectId)) {
      throw new Error(`项目配置未找到: ${projectId}`)
    }
    this.currentProject = projectId
  }

  // 获取当前项目配置
  static getCurrentConfig(): any | null {
    if (!this.currentProject) {
      return null
    }
    return this.configs.get(this.currentProject) || null
  }

  // 获取项目配置
  static getProjectConfig(projectId: string): any | null {
    return this.configs.get(projectId) || null
  }

  // 获取所有项目
  static getAllProjects(): string[] {
    return Array.from(this.configs.keys())
  }
}

// 全局API网关实例
let globalGateway: APIGateway | null = null

// 初始化全局网关
export function initializeGlobalGateway(config: APIGatewayConfig): APIGateway {
  if (globalGateway) {
    globalGateway.dispose()
  }
  globalGateway = new APIGateway(config)
  return globalGateway
}

// 获取全局网关
export function getGlobalGateway(): APIGateway {
  if (!globalGateway) {
    throw new Error('全局API网关未初始化，请先调用 initializeGlobalGateway')
  }
  return globalGateway
}

// 便捷的API调用方法
export async function apiRequest<T>(config: any): Promise<any> {
  const gateway = getGlobalGateway()
  return gateway.request<T>(config)
}

// 导入环境变量工具
import { getDeviceAnAPIURL, getLowcodeAPIURL } from '../utils/env'

// 预设项目配置
export const PRESET_CONFIGS = {
  // Device-An项目
  DEVICE_AN: {
    projectId: 'device-an',
    name: 'Device-An项目',
    adapters: {
      'device-an': {
        name: 'device-an',
        baseURL: getDeviceAnAPIURL(),
        timeout: 30000,
        auth: { type: 'device-an' as const }
      }
    },
    routes: [
      { pattern: /.*/, adapter: 'device-an' }
    ]
  },

  // 低代码平台
  LOWCODE: {
    projectId: 'lowcode',
    name: '低代码平台',
    adapters: {
      'lowcode': {
        name: 'lowcode',
        baseURL: getLowcodeAPIURL(),
        timeout: 10000,
        auth: { type: 'none' as const }
      }
    },
    routes: [
      { pattern: /^\/api\//, adapter: 'lowcode' }
    ]
  },

  // 混合项目（Device-An + 低代码）
  HYBRID: {
    projectId: 'hybrid',
    name: '混合项目',
    adapters: {
      'device-an': {
        name: 'device-an',
        baseURL: getDeviceAnAPIURL(),
        timeout: 30000,
        auth: { type: 'device-an' as const }
      },
      'lowcode': {
        name: 'lowcode',
        baseURL: getLowcodeAPIURL(),
        timeout: 10000,
        auth: { type: 'none' as const }
      }
    },
    routes: [
      { pattern: /^\/front/, adapter: 'device-an' },
      { pattern: /^\/frontDevice/, adapter: 'device-an' },
      { pattern: /^\/device/, adapter: 'device-an' },
      { pattern: /^\/api\/app/, adapter: 'lowcode' },
      { pattern: /^\/api\//, adapter: 'lowcode' }
    ]
  }
}

// 快速初始化方法
export function quickInit(projectType: keyof typeof PRESET_CONFIGS, environment: 'development' | 'production' = 'development') {
  const config = PRESET_CONFIGS[projectType]

  // 根据环境调整配置
  if (environment === 'production') {
    // 生产环境配置调整
    Object.values(config.adapters).forEach((adapter: any) => {
      adapter.timeout = Math.min(adapter.timeout, 15000) // 限制超时时间
    })
  }

  const gatewayConfig = {
    adapters: config.adapters,
    defaultAdapter: Object.keys(config.adapters)[0],
    globalTimeout: 15000
  }

  const gateway = initializeGlobalGateway(gatewayConfig)
  gateway.addRoutes(config.routes)

  return gateway
}
