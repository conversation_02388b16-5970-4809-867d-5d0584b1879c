/**
 * 认证策略实现
 */

import { md5 } from 'js-md5'
import type { AuthStrategy, RequestConfig } from '../types'

// ==================== Device-An认证策略 ====================

export class DeviceAnAuthStrategy implements AuthStrategy {
  name = 'device-an'
  
  async authenticate(config: RequestConfig): Promise<RequestConfig> {
    const { ZHIXUN_YAN, ZHIXUN_DATE } = this.generateAuthKey()
    const deviceToken = this.getDeviceToken()
    
    return {
      ...config,
      headers: {
        ...config.headers,
        'deviceToken': deviceToken,
        'Conten-Date': ZHIXUN_DATE.toString(),
        'Conten-Zx': ZHIXUN_YAN,
        'Content-Type': this.getContentType(config)
      }
    }
  }
  
  async handleAuthError(error: any): Promise<void> {
    console.warn('Device-An认证失败:', error.message)
    
    // 检查是否是token失效
    if (this.isTokenExpired(error)) {
      this.clearAuth()
      // 跳转到登录页面
      const loginUrl = this.getLoginUrl()
      if (typeof window !== 'undefined') {
        window.location.href = loginUrl
      }
    }
  }
  
  clearAuth(): void {
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('ZX-DEVICE-TOKEN')
    }
  }
  
  private generateAuthKey() {
    const timestamp = Math.floor(Date.now() / 1000)
    const secret = this.getAuthSecret()
    return {
      ZHIXUN_YAN: md5(timestamp + secret),
      ZHIXUN_DATE: timestamp
    }
  }

  private getAuthSecret(): string {
    // 优先从环境变量获取
    if (typeof process !== 'undefined' && process.env?.VITE_AUTH_SECRET) {
      return process.env.VITE_AUTH_SECRET
    }

    // 浏览器环境从window获取
    if (typeof window !== 'undefined' && (window as any).__AUTH_SECRET__) {
      return (window as any).__AUTH_SECRET__
    }

    // 开发环境警告
    if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
      console.warn('⚠️ 使用默认认证密钥，生产环境请设置 VITE_AUTH_SECRET 环境变量')
    }

    // 默认密钥（仅用于开发环境）
    return 'django-insecure-_=0rpi4cfhzdus5ih*4^8p%j)zdg%y2i^_d6_tbe(z$tfk!yp%'
  }
  
  private getDeviceToken(): string {
    if (typeof localStorage !== 'undefined') {
      try {
        const storedData = localStorage.getItem('ZX-DEVICE-TOKEN')

        if (storedData) {
          // 尝试解析JSON格式的token
          const parsed = JSON.parse(storedData)
          if (parsed && parsed.key) {
            return parsed.key
          }
          // 如果不是JSON格式，直接返回原始值
          return storedData
        }
      } catch (error) {
        // 如果解析失败，使用原始值
        const storedToken = localStorage.getItem('ZX-DEVICE-TOKEN')
        if (storedToken && storedToken !== 'NOT_LOGIN') {
          return storedToken
        }
      }
    }

    return 'NOT_LOGIN'
  }
  
  private getContentType(config: RequestConfig): string {
    if (config.data instanceof FormData) {
      return 'multipart/form-data'
    }
    return 'application/json'
  }
  
  private isTokenExpired(error: any): boolean {
    // Device-An的token失效判断逻辑
    return error.code === 401 ||
           error.message?.includes('token') ||
           error.response?.status === 401 ||
           // 检查Device-An特有的NOT_LOGIN响应格式
           (error.response?.data?.code === 0 && error.response?.data?.msg === 'NOT_LOGIN') ||
           error.isAuthError === true
  }
  
  private getLoginUrl(): string {
    const currentAppId = localStorage.getItem('currentAppId')
    const currentPath = typeof window !== 'undefined' ? window.location.pathname + window.location.search : ''

    let loginUrl = '/login'
    const params = new URLSearchParams()

    if (currentAppId) {
      params.append('appId', currentAppId)
    }

    if (currentPath && currentPath !== '/login') {
      params.append('redirect', currentPath)
    }

    const queryString = params.toString()
    return queryString ? `${loginUrl}?${queryString}` : loginUrl
  }
}

// ==================== JWT认证策略 ====================

export class JWTAuthStrategy implements AuthStrategy {
  name = 'jwt'
  
  async authenticate(config: RequestConfig): Promise<RequestConfig> {
    const token = this.getToken()
    
    return {
      ...config,
      headers: {
        ...config.headers,
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
  }
  
  async handleAuthError(error: any): Promise<void> {
    console.warn('JWT认证失败:', error.message)
    
    if (error.response?.status === 401) {
      await this.refreshToken()
    }
  }
  
  async refreshToken(): Promise<void> {
    try {
      const refreshToken = this.getRefreshToken()
      if (!refreshToken) {
        throw new Error('No refresh token available')
      }
      
      // 调用刷新token的API
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken })
      })
      
      if (response.ok) {
        const data = await response.json()
        this.setToken(data.accessToken)
        this.setRefreshToken(data.refreshToken)
      } else {
        throw new Error('Token refresh failed')
      }
    } catch (error) {
      console.error('Token refresh error:', error)
      this.clearAuth()
    }
  }
  
  clearAuth(): void {
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('jwt-token')
      localStorage.removeItem('jwt-refresh-token')
    }
  }
  
  private getToken(): string {
    if (typeof localStorage !== 'undefined') {
      return localStorage.getItem('jwt-token') || ''
    }
    return ''
  }
  
  private getRefreshToken(): string {
    if (typeof localStorage !== 'undefined') {
      return localStorage.getItem('jwt-refresh-token') || ''
    }
    return ''
  }
  
  private setToken(token: string): void {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('jwt-token', token)
    }
  }
  
  private setRefreshToken(token: string): void {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('jwt-refresh-token', token)
    }
  }
}

// ==================== 自定义认证策略 ====================

export class CustomAuthStrategy implements AuthStrategy {
  name = 'custom'
  private config: Record<string, any>
  
  constructor(config: Record<string, any> = {}) {
    this.config = config
  }
  
  async authenticate(config: RequestConfig): Promise<RequestConfig> {
    // 根据自定义配置进行认证
    const headers: Record<string, string> = { ...config.headers }
    
    // 示例：API Key认证
    if (this.config.apiKey) {
      headers['X-API-Key'] = this.config.apiKey
    }
    
    // 示例：自定义Header认证
    if (this.config.customHeaders) {
      Object.assign(headers, this.config.customHeaders)
    }
    
    return { ...config, headers }
  }
  
  async handleAuthError(error: any): Promise<void> {
    console.warn('自定义认证失败:', error.message)
    // 自定义错误处理逻辑
  }
  
  clearAuth(): void {
    // 自定义清除认证逻辑
  }
}

// ==================== 无认证策略 ====================

export class NoAuthStrategy implements AuthStrategy {
  name = 'none'
  
  async authenticate(config: RequestConfig): Promise<RequestConfig> {
    return {
      ...config,
      headers: {
        ...config.headers,
        'Content-Type': 'application/json'
      }
    }
  }
  
  async handleAuthError(error: any): Promise<void> {
    console.warn('API请求失败:', error.message)
  }
  
  clearAuth(): void {
    // 无需清除认证
  }
}

// ==================== 认证策略工厂 ====================

export class AuthStrategyFactory {
  private static strategies = new Map<string, new (config?: any) => AuthStrategy>([
    ['device-an', DeviceAnAuthStrategy],
    ['jwt', JWTAuthStrategy],
    ['custom', CustomAuthStrategy],
    ['none', NoAuthStrategy]
  ])
  
  static create(type: string, config?: any): AuthStrategy {
    const StrategyClass = this.strategies.get(type)
    if (!StrategyClass) {
      throw new Error(`不支持的认证策略: ${type}`)
    }
    return new StrategyClass(config)
  }
  
  static register(type: string, strategyClass: new (config?: any) => AuthStrategy): void {
    this.strategies.set(type, strategyClass)
  }
}
