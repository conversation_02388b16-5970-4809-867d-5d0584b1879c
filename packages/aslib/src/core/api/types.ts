/**
 * API架构核心类型定义
 */

// ==================== 基础类型 ====================

export interface APIResponse<T = any> {
  success: boolean
  data: T
  message: string
  code?: number
}

export interface RequestConfig {
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  params?: Record<string, any>
  data?: any
  headers?: Record<string, string>
  timeout?: number
  retry?: RetryConfig
  cache?: CacheConfig
}

export interface RetryConfig {
  times: number
  delay: number
  backoff?: number
}

export interface CacheConfig {
  key: string
  ttl: number
  enabled: boolean
}

// ==================== 认证策略类型 ====================

export interface AuthStrategy {
  name: string
  authenticate(config: RequestConfig): Promise<RequestConfig>
  handleAuthError(error: any): Promise<void>
  refreshToken?(): Promise<void>
  clearAuth(): void
}

export interface AuthConfig {
  type: 'device-an' | 'jwt' | 'custom' | 'none'
  config?: Record<string, any>
}

// ==================== API适配器类型 ====================

export interface APIAdapter {
  name: string
  baseURL: string
  authStrategy: AuthStrategy
  request<T>(config: RequestConfig): Promise<APIResponse<T>>
  healthCheck(): Promise<boolean>
}

export interface AdapterConfig {
  name: string
  baseURL: string
  timeout: number
  auth: AuthConfig
  headers?: Record<string, string>
  interceptors?: {
    request?: (config: RequestConfig) => RequestConfig
    response?: (response: any) => any
    error?: (error: any) => any
  }
}

// ==================== API网关类型 ====================

export interface APIGatewayConfig {
  adapters: Record<string, AdapterConfig>
  defaultAdapter?: string
  globalTimeout?: number
  globalRetry?: RetryConfig
}

export interface RouteConfig {
  pattern: string | RegExp
  adapter: string
  transform?: {
    request?: (config: RequestConfig) => RequestConfig
    response?: (response: any) => any
  }
}

// ==================== 项目配置类型 ====================

export interface ProjectAPIConfig {
  projectId: string
  name: string
  adapters: Record<string, AdapterConfig>
  routes: RouteConfig[]
  globalConfig?: {
    timeout?: number
    retry?: RetryConfig
    cache?: CacheConfig
  }
}

// ==================== 环境配置类型 ====================

export interface EnvironmentConfig {
  development: ProjectAPIConfig
  production: ProjectAPIConfig
  test?: ProjectAPIConfig
}

// ==================== 错误类型 ====================

export class APIError extends Error {
  constructor(
    message: string,
    public code?: number,
    public adapter?: string,
    public originalError?: any
  ) {
    super(message)
    this.name = 'APIError'
  }
}

export class AuthError extends APIError {
  constructor(message: string, adapter?: string) {
    super(message, 401, adapter)
    this.name = 'AuthError'
  }
}

export class NetworkError extends APIError {
  constructor(message: string, adapter?: string) {
    super(message, 0, adapter)
    this.name = 'NetworkError'
  }
}
