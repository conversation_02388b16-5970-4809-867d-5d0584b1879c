/**
 * 安生低代码平台统一库
 * 
 * 将原来的 @lowcode/core、@lowcode/ui、@lowcode/hooks 三个包合并
 * 提供统一的导出接口，避免包名冲突
 */

// ==================== 重新导出所有模块 ====================

// 从 core 模块导出
export * from './core'

// 从 ui 模块导出  
export * from './ui'

// 从 hooks 模块导出
export * from './hooks'

// ==================== 分模块导出 ====================
import * as Core from './core'
import * as UI from './ui'
import * as Hooks from './hooks'

export { Core, UI, Hooks }

// ==================== 默认导出 ====================
export default {
  Core,
  UI,
  Hooks,
  version: '1.2.0'
}

// ==================== 版本信息 ====================
export const VERSION = '1.2.0'
export const PACKAGE_NAME = '@lowcode/aslib'

// ==================== 初始化函数 ====================
export const initAsLib = () => {
  console.log(`🚀 安生低代码平台 v${VERSION} 初始化完成`)
  console.log('📦 包含模块: Core (渲染引擎), UI (组件库), Hooks (工具函数)')
}
