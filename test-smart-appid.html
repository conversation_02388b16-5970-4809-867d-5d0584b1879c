<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能AppID管理器测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .test-section { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { color: green; }
        .error { color: red; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🎯 智能AppID管理器测试</h1>
    
    <div class="test-section">
        <h2>1. 当前状态检查</h2>
        <button onclick="checkCurrentState()">检查当前状态</button>
        <div id="currentState" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. URL参数测试</h2>
        <p>测试不同的URL参数格式：</p>
        <button onclick="testUrlParam('appId', 'test-app-001')">测试 ?appId=test-app-001</button>
        <button onclick="testUrlParam('appid', 'test-app-002')">测试 ?appid=test-app-002</button>
        <button onclick="clearUrlParams()">清理URL参数</button>
        <div id="urlTestResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. 本地存储测试</h2>
        <button onclick="setAppId('mall-app-123')">设置 mall-app-123</button>
        <button onclick="setAppId('device-app-456')">设置 device-app-456</button>
        <button onclick="resetToDefault()">重置为默认</button>
        <button onclick="clearCache()">清除缓存</button>
        <div id="storageTestResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. 事件监听测试</h2>
        <button onclick="startEventTest()">开始监听AppID变化</button>
        <button onclick="stopEventTest()">停止监听</button>
        <div id="eventTestResult" class="result"></div>
    </div>

    <script>
        // 模拟SmartAppIdManager类（简化版本用于测试）
        class TestSmartAppIdManager {
            constructor() {
                this.STORAGE_KEY = 'current-app-id';
                this.DEFAULT_APP_ID = 'default-device';
                this.listeners = [];
            }

            initializeFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                const urlAppId = urlParams.get('appid') || urlParams.get('appId');
                
                if (urlAppId) {
                    console.log('🔄 从URL检测到appId，更新本地缓存:', urlAppId);
                    const oldAppId = this.getCurrentAppId();
                    this.setCurrentAppId(urlAppId, 'url');
                    this.cleanUrlParams();
                    return { updated: true, appId: urlAppId, source: 'url' };
                }

                const currentAppId = this.getCurrentAppId();
                const source = localStorage.getItem(this.STORAGE_KEY) ? 'localStorage' : 'default';
                return { updated: false, appId: currentAppId, source };
            }

            getCurrentAppId() {
                const stored = localStorage.getItem(this.STORAGE_KEY);
                return stored || this.DEFAULT_APP_ID;
            }

            setCurrentAppId(appId, source = 'manual') {
                const oldAppId = this.getCurrentAppId();
                
                if (oldAppId !== appId) {
                    localStorage.setItem(this.STORAGE_KEY, appId);
                    console.log(`✅ AppID已更新: ${oldAppId} → ${appId} (source: ${source})`);
                    this.notifyAppChange({ oldAppId, newAppId: appId, source });
                }
            }

            cleanUrlParams() {
                if (typeof window !== 'undefined' && window.history) {
                    try {
                        const url = new URL(window.location.href);
                        const hasAppIdParam = url.searchParams.has('appid') || url.searchParams.has('appId');
                        
                        if (hasAppIdParam) {
                            url.searchParams.delete('appid');
                            url.searchParams.delete('appId');
                            window.history.replaceState({}, '', url.toString());
                            console.log('🧹 已清理URL中的appId参数');
                        }
                    } catch (error) {
                        console.warn('清理URL参数时出错:', error);
                    }
                }
            }

            addChangeListener(listener) {
                this.listeners.push(listener);
            }

            removeChangeListener(listener) {
                const index = this.listeners.indexOf(listener);
                if (index > -1) {
                    this.listeners.splice(index, 1);
                }
            }

            notifyAppChange(event) {
                this.listeners.forEach(listener => {
                    try {
                        listener(event);
                    } catch (error) {
                        console.error('AppID变化监听器执行出错:', error);
                    }
                });
            }

            resetToDefault() {
                this.setCurrentAppId(this.DEFAULT_APP_ID, 'manual');
            }

            clearCache() {
                const oldAppId = this.getCurrentAppId();
                localStorage.removeItem(this.STORAGE_KEY);
                console.log('🗑️ 已清除AppID缓存');
                this.notifyAppChange({ 
                    oldAppId, 
                    newAppId: this.DEFAULT_APP_ID, 
                    source: 'manual' 
                });
            }

            getDebugInfo() {
                return {
                    currentAppId: this.getCurrentAppId(),
                    hasStoredValue: !!localStorage.getItem(this.STORAGE_KEY),
                    listenersCount: this.listeners.length,
                    defaultAppId: this.DEFAULT_APP_ID
                };
            }
        }

        // 创建测试实例
        const manager = new TestSmartAppIdManager();
        let eventListener = null;

        // 测试函数
        function checkCurrentState() {
            const debugInfo = manager.getDebugInfo();
            const initResult = manager.initializeFromUrl();
            
            document.getElementById('currentState').innerHTML = `
                <h4>调试信息:</h4>
                <pre>${JSON.stringify(debugInfo, null, 2)}</pre>
                <h4>初始化结果:</h4>
                <pre>${JSON.stringify(initResult, null, 2)}</pre>
            `;
        }

        function testUrlParam(paramName, value) {
            const url = new URL(window.location.href);
            url.searchParams.set(paramName, value);
            window.history.replaceState({}, '', url.toString());
            
            const result = manager.initializeFromUrl();
            document.getElementById('urlTestResult').innerHTML = `
                <p><strong>设置参数:</strong> ${paramName}=${value}</p>
                <p><strong>结果:</strong> ${JSON.stringify(result)}</p>
                <p><strong>当前URL:</strong> ${window.location.href}</p>
            `;
        }

        function clearUrlParams() {
            manager.cleanUrlParams();
            document.getElementById('urlTestResult').innerHTML += `
                <p class="success">✅ URL参数已清理</p>
                <p><strong>当前URL:</strong> ${window.location.href}</p>
            `;
        }

        function setAppId(appId) {
            const oldAppId = manager.getCurrentAppId();
            manager.setCurrentAppId(appId);
            
            document.getElementById('storageTestResult').innerHTML = `
                <p><strong>操作:</strong> 设置AppID为 ${appId}</p>
                <p><strong>变化:</strong> ${oldAppId} → ${manager.getCurrentAppId()}</p>
                <p><strong>本地存储:</strong> ${localStorage.getItem('current-app-id')}</p>
            `;
        }

        function resetToDefault() {
            manager.resetToDefault();
            document.getElementById('storageTestResult').innerHTML = `
                <p class="success">✅ 已重置为默认AppID</p>
                <p><strong>当前AppID:</strong> ${manager.getCurrentAppId()}</p>
            `;
        }

        function clearCache() {
            manager.clearCache();
            document.getElementById('storageTestResult').innerHTML = `
                <p class="success">✅ 缓存已清除</p>
                <p><strong>当前AppID:</strong> ${manager.getCurrentAppId()}</p>
            `;
        }

        function startEventTest() {
            if (eventListener) {
                manager.removeChangeListener(eventListener);
            }
            
            eventListener = (event) => {
                const timestamp = new Date().toLocaleTimeString();
                const eventDiv = document.createElement('div');
                eventDiv.innerHTML = `
                    <p><strong>[${timestamp}]</strong> AppID变化事件:</p>
                    <pre>${JSON.stringify(event, null, 2)}</pre>
                `;
                document.getElementById('eventTestResult').appendChild(eventDiv);
            };
            
            manager.addChangeListener(eventListener);
            document.getElementById('eventTestResult').innerHTML = `
                <p class="success">✅ 已开始监听AppID变化事件</p>
                <p>请执行其他操作来触发事件...</p>
            `;
        }

        function stopEventTest() {
            if (eventListener) {
                manager.removeChangeListener(eventListener);
                eventListener = null;
            }
            document.getElementById('eventTestResult').innerHTML += `
                <p class="error">⏹️ 已停止监听事件</p>
            `;
        }

        // 页面加载时自动检查状态
        window.onload = function() {
            checkCurrentState();
        };
    </script>
</body>
</html>